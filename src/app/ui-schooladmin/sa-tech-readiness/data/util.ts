import { SCHOOL_ADMIN_TECH_READ_CHECKLIST_MASTER } from "./checklist-master"
import { SCHOOL_ADMIN_TECH_READ_CHECKLIST_PRIVATE_SCHOOLS } from "./checklist-private-schools";
import { ICheckListItem } from "./types";

interface IChecklistComposeConfig {
    suffix?:string, 
    slugs:Array<{
        id: string,
        isSpecific: boolean,
    }>,
    disableForSchoolType?: string[],
}

const cloneObject = (obj:any) => {
    if (obj != null) {
        return JSON.parse(JSON.stringify(obj));
    }
    
    return {};
};

const checklistItem = (id:string, suffix:string='',isPrivateSchool:boolean):ICheckListItem => {
    if(!isPrivateSchool){
        return {
            ... cloneObject(SCHOOL_ADMIN_TECH_READ_CHECKLIST_MASTER[id]),
            id: id + suffix,
        }
    }
    return {
        ... cloneObject(SCHOOL_ADMIN_TECH_READ_CHECKLIST_PRIVATE_SCHOOLS[id]),
        id: id + suffix,
    }
}

export const composeChecklist = (config:IChecklistComposeConfig,isPrivateSchool:boolean):ICheckListItem[] => {
    return config.slugs.map(slug =>{
        let suffix = '';
        if (slug.isSpecific){
            suffix = config.suffix;
        }
        return checklistItem(slug.id, suffix,isPrivateSchool)
    })
}