
<div class="page-container">
  <div class="header-container">
    <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
  </div>
  <div class="panel-container">
    <div class="router-container is_wide">

      <div>
        <div class="link-to-guides-container" *wlCtx="'IS_LANDING_GUIDE'">
          <tra [slug]="getGuideSlug()"></tra>&nbsp;<a routerLink="/{{lang.c()}}/user-guides"><tra [slug]="getClickText()" ></tra></a>
        </div>
        <div>
          <tra [slug]="getLoginAsSlug()"></tra>
        </div>
        <div>
          <a routerLink="/{{lang.c()}}/login-educator" class="button is-fullwidth" style="height: auto; word-wrap: break-word"><tra [slug]="getAdminLoginSlug()"></tra></a>
        </div>
        <div>
          <a routerLink="/{{lang.c()}}/login-test-taker" class="button is-fullwidth"><tra [slug]="getStudentButtonSlug()"></tra></a>
        </div>
        <div *wlCtx="'IS_LANDING_PUBLIC_PRACTICE'">
          <a routerLink="/{{lang.c()}}/public-practice" class="button is-fullwidth"><tra [slug]="getPracticeTestTxt()"></tra></a>
        </div>
      </div>
    </div>
  </div>
</div>
