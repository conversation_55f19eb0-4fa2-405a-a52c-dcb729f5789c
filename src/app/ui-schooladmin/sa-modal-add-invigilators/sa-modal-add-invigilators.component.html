<div>
    <tra *ngIf="!isABED()" slug="ta_add_remove_invigilators"></tra>
    <tra *ngIf="isABED()" slug="ta_add_remove_invigilators_abed"></tra>
</div>
<hr/>
<tra-md *ngIf="!isABED()" slug="ta_add_remove_invig_desc"></tra-md>
<tra-md *ngIf="isABED()" slug="ta_add_remove_invig_desc_ABED"></tra-md>
<div style = "height:30em; overflow:auto;">
    <table>
        <tr>
            <th style="width:2em;"></th>
            <th><tra slug="ta_teacher_list_name_col"></tra></th>
            <th><tra slug="ta_teacher_list_email_col"></tra></th>
        </tr>
        <tr *ngFor="let teacher of availableTeachers">
            <td><input type ="checkbox" [checked] = "teacher.selected" (change) = "toggleInvigilator(teacher)"></td>
            <td>{{teacher.name}}</td>
            <td>{{teacher.email}}</td>
        </tr>
    </table>
</div> 
<div style="margin-top: 1.5rem; text-align: center;">
    <button class="button is-info invig-ok-btn" (click)="closeModal()"><tra [slug]="'btn_ok'"></tra></button>
</div>  