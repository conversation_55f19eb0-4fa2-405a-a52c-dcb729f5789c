@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/partials/_modal.scss';
.custom-modal { @extend %custom-modal; }

.tech_readi_component {
    table#checklist-items {
        margin-top: 1em;
    }

    .checkbox {
        margin-right: 5px;
        transform: scale(1.1)
    }

    .sa-tech-checklist-label {
        font-size: 1.2em;
    }

    .checkbox-option {
        display: flex;
        flex-direction: row;
        align-items: start;
        .checklist-label {
            cursor: pointer;
            margin-left: 0.5em;
        }
    }

    .sa-tech-readiness-confirmation-div {
        display: flex;
        justify-content: space-between;
        flex-direction: column;


    }
    .success-msg {
        color: white;
        border-radius: 10px;
        padding: 10px;
        background-color: #268826;
    }
    .failure-msg {
        color: white;
        background-color:#E52525;
        border-radius: 10px;
        padding: 10px;
    }
    .warning-msg {
        color: black;
        font-weight: 700;
        background-color: #E5C725;
        border-radius: 10px;
        padding: 10px;
    }
    .tech-readi-status{
        display:flex;
        align-items: center;
    }

    a {
        color: #3d70bd;
        &.disabled {
            color: #657792;
            &:hover {
                cursor: not-allowed;
            }
        }
    }


    button {
        &.is-modal {
            background: #AEEAC3;
            box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
            border-radius: 4px;
        }
    }
}

.collapse {
    border: none;
}
