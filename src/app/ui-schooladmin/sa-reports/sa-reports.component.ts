import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment-timezone';
import { LangService } from 'src/app/core/lang.service';
import { IStudentAccount } from '../data/types';
import { MySchoolService, ClassFilterId } from '../my-school.service';
import * as _ from 'lodash';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AuthService, getFrontendDomain } from '../../api/auth.service';
import { FormControl } from '@angular/forms';
import { G9DemoDataService } from '../g9-demo-data.service';
import { downloadFromExportData, downloadCSVFromExportData, IExportColumn } from 'src/app/ui-testctrl/tc-table-common/tc-table-common.component';
import { formatDate } from '@angular/common';
import { RoutesService } from 'src/app/api/routes.service';
import { OSSLTResultColumns } from '../../ui-dist-admin/sb-board/sb-board.component';
import { DomSanitizer } from '@angular/platform-browser';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import { LoginGuardService } from '../../api/login-guard.service';
import { allowBypassDomains } from '../../domain/qa-bypass'

@Component({
  selector: 'sa-reports',
  templateUrl: './sa-reports.component.html',
  styleUrls: ['./sa-reports.component.scss']
})
export class SaReportsComponent implements OnInit {

  @Output() onSetClassFilter = new EventEmitter();

  studentReports: [];
  studentPJData: any = [];
  studentReportsAfterDate: any [];
  singleStudentUid?:number;
  isReportsLoaded:boolean;
  //REPORT_YEAR:string = "2020 - 2021";
  isPrivateSchool = false;
  report_after_date = new FormControl();
  currentAdminWindow
  adminWindows = []
  public bypassAdminSignOff:boolean;
  isShowingG9Reports =false;
  g9Classes: any[] = [];
  currentG9AdminWindow
  g9AdminWindows = []
  classroomsTable: MemDataPaginated<any>;
  ClassFilterId = ClassFilterId;

  constructor(
    public schoolService:MySchoolService,
    public lang: LangService,
    public auth: AuthService,
    private route:ActivatedRoute,
    private routes: RoutesService,
    private whitelabel: WhitelabelService,
    public mySchool: MySchoolService,
    private g9demoService: G9DemoDataService,
    private sanitizer: DomSanitizer,
    private loginGuard: LoginGuardService,
    private router: Router,
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((queryParams) => {
      this.singleStudentUid = queryParams['uid'];
      this.loadStudentReports()
    });
    this.isPrivateSchool = this.g9demoService.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.mySchool.initStuAsmtSignoff();
    const schoolLang = this.g9demoService.schoolData["lang"];
    this.lang.setCurrentLanguage(schoolLang);

    this.classroomsTable = new MemDataPaginated({
      data: [],
      pageSize: 5,
      filterSettings: {
      },
      sortSettings: {
      }
    })
  }

  getReportTableBody(report: any): any[] {
    const rowTitles = [
      {
        title: report.var_reading ? this.lang.tra('pj_student_report_result_reading_2') : this.lang.tra('pj_student_report_result_reading'), 
        content: report.var_reading ? report.var_reading : null
      },
      {
        title: report.var_writing ? this.lang.tra('pj_student_report_result_writing_2') : this.lang.tra('pj_student_report_result_writing'), 
        content: report.var_writing ? report.var_writing : null
      },
      {
        title: report.var_mathematics ? this.lang.tra('pj_student_report_result_math_2') : this.lang.tra('pj_student_report_result_math'), 
        content: report.var_mathematics ? report.var_mathematics : null
      }
    ];
    const tableRows = [];
    let index = 0;
    rowTitles.forEach((e: any) => {
      let layer_1, layer_2;
      if(index == 0){
        layer_1 = report.reading_layer_1;
        layer_2 = report.reading_layer_2;
      }
      if(index == 1){
        layer_1 = report.writing_layer_1;
        layer_2 = report.writing_layer_2;
      }
      if(index == 2){
        layer_1 = report.math_layer_1;
        layer_2 = report.math_layer_2;
      }
      const row = {
        reportID: report.id,
        id: `${report.id}-row-${index}`,
        columns: [],
        layers: {
          layer_1: layer_1,
          layer_2: layer_2
        }
      };
      for(let j = 0; j < 6; j++) {
        row.columns.push({
          id: `${report.id}-row-${index}-col-${j}`,
          title: e.title,
          content: j === 0 ? e.content : null
        });
      }
      tableRows.push(row);
      index++;
    });
    return tableRows;
  }
  // Table performance for rendering
  byID(index, item){
    return item.id
  }
  
  addSquarePosition(reportID: number,rowNumber: number, columNumber: number, squarePostion: number): void {
    const sectionNum = 5;
    columNumber = columNumber + 1;
    const id = `${reportID}-row-${rowNumber}-col-${columNumber}`;

    const box = document.getElementById(id);

    if (!box) {
      return null;
    }
    box.style.textAlign = "left";
    const level2Spaces = box.clientWidth / ((sectionNum + 1) * 2);
    let marginLeft = (level2Spaces * 2) * (squarePostion-1) + level2Spaces;
    box.innerHTML = `<i style="margin-left:${marginLeft}px; font-size: ${level2Spaces * 2}px;" class="fas fa-square"></i>`;
  }

  clearSingleSudentFocus(){
    this.singleStudentUid = null;
    this.loadStudentReports();
  }
  
  async loadStudentReports(){
    if(this.currentClassFilter == ClassFilterId.OSSLT){
      this.isReportsLoaded = false;
      this.studentReports = await this.schoolService.loadSchoolReports(this.singleStudentUid, this.bypassAdminSignOff)
      this.studentReports = _.orderBy(this.studentReports, ['last_name', 'first_name', 'student_oen']);
      this.isReportsLoaded = true;

      this.auth.apiCreate('public/log', {
        slug: 'SA_REPORT_VIEW',
        data: {
          singleStudentUid: this.singleStudentUid,
          schoolId: this.schoolService.getCurrentSchoolId()
        }
      })
      this.createAdminWindows()
      if(this.adminWindows.length > 0){
        this.currentAdminWindow = this.adminWindows[this.adminWindows.length - 1];
      }
      this.filterReports()
    }
    if(this.currentClassFilter == ClassFilterId.Primary||this.currentClassFilter == ClassFilterId.Junior){
      //this.isReportsLoaded = false;
      this.studentPJData = [];
      this.studentPJData = await this.schoolService.loadSchoolPJReports(this.currentClassFilter, this.singleStudentUid, this.bypassAdminSignOff, true)
      //this.studentReports = _.orderBy(this.studentReports, ['last_name', 'first_name', 'student_oen']);
      this.isReportsLoaded = true;
      this.createPJAdminWindows()
      if(this.adminWindows.length > 0){
        this.currentAdminWindow = this.adminWindows[this.adminWindows.length - 1];
      }
      //this.filterReports()  not implement yet
    }
  }

  isShowingReports:boolean;
  isShowingPJReports:boolean;
  currentClassFilter;
  setClassFilter(filterId){
    this.onSetClassFilter.emit(filterId);

    this.currentClassFilter = filterId;
    this.isShowingG9Reports= false;
    this.isShowingReports = false;
    this.isShowingPJReports= false;
    if (this.currentClassFilter){
      if(this.currentClassFilter === ClassFilterId.OSSLT){
        this.loadStudentReports();
        this.isShowingReports = true;
        this.loadStudentReports()
      }if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior){
        this.isShowingPJReports = true;
        this.loadStudentReports()
      }
      else if(this.currentClassFilter === ClassFilterId.G9){
        this.loadG9StudentClass();
        this.isShowingG9Reports= true;
      }
    }
  }

  isLangActive(langCode: string) {
    return (this.lang.c() == langCode);
  }

  isEnglish(){
    return this.isLangActive('en');
  }

  getWhitelabelUrl(slug:string){
    return this.whitelabel.getSiteText(slug);
  }

  timezone = moment.tz.guess();
  renderAttemptDate(dateTimeStart: string) {
    return moment.tz(dateTimeStart, this.timezone).format(this.lang.tra('datefmt_dashboard_short'));
  }

  print(){
    window.print()
  }

  maskOENWithFourStars(oen){
    let oenArr = oen.split('');
    for(let i =2; i < oenArr.length-3; i++){
      oenArr[i] = "*";
    }
    return oenArr.join('')
  }

  maskOENWithFiveStars(oen){
    let oenArr = oen.split('');
    for(let i =1; i < oenArr.length-3; i++){
      oenArr[i] = "*";
    }
    return oenArr.join('')
  }

  maskOENWithHyphen(oen){
    return oen.replace(/(\d{3})(\d{3})(\d{3})/, '$1-$2-$3');
  }

  generateReportsAfterDate(){
    this.filterReports()
  }

  createAdminWindows(){
    this.adminWindows = [];
    this.studentReports.forEach((report:any) =>{
      const adminWindow = this.adminWindows.find( adminWindow => adminWindow.tw_id == report.tw_id)
      if(!adminWindow){
        this.adminWindows.push({tw_id:report.tw_id, tw_date_end:report.tw_date_end})
      }
    })
    this.adminWindows.sort( (window1:any, window2:any) => {
      const window1StartDate = new Date(window1.tw_date_end)
      const window2StartDate = new Date(window2.tw_date_end)
      if(window1StartDate > window2StartDate){
        return 1
      }else if (window1StartDate < window2StartDate){
        return -1
      }else{
        return 0
      }
    })
  }

  createPJAdminWindows(){
    this.adminWindows = [];
    this.studentPJData.forEach((report:any) =>{
      const adminWindow = this.adminWindows.find( adminWindow => adminWindow.tw_id == report.test_window)
      if(!adminWindow){
        this.adminWindows.push({tw_id:report.test_window, sch_year:report.sch_year})
      }
    })
    this.adminWindows.sort( (window1:any, window2:any) => {
      const window1SchlYear = new Date(window1.sch_year)
      const window2SchlYear = new Date(window2.sch_year)
      if(window1SchlYear > window2SchlYear){
        return 1
      }else if (window1SchlYear < window2SchlYear){
        return -1
      }else{
        return 0
      }
    })
  }

  onAdminWindowChanged(adminWindowIndex){
    this.currentAdminWindow = this.adminWindows[adminWindowIndex];
    this.filterReports()
  }

  onG9AdminWindowChanged(adminWindowIndex){
    this.currentG9AdminWindow = this.g9AdminWindows[adminWindowIndex];
    this.filterReports()
  }

  filterReports(){
    if(this.currentClassFilter === ClassFilterId.OSSLT){
      this.studentReportsAfterDate = this.studentReports.slice();
      if(this.report_after_date.value){
        this.studentReportsAfterDate = this.studentReportsAfterDate.filter( report => 
            Date.parse(report.report_gen_on) > Date.parse(this.report_after_date.value)
        )
      }
      this.studentReportsAfterDate = this.studentReportsAfterDate.filter( report => report.tw_id == this.currentAdminWindow.tw_id)
    }
    if(this.currentClassFilter === ClassFilterId.G9){
      this.g9Classes = this.g9Classes.filter(theClass => theClass.tw_id == this.currentG9AdminWindow.tw_id)
    }  
  }

  currentAdminWindowIndex(){
    return this.adminWindows.indexOf(this.currentAdminWindow);
  }

  currentG9AdminWindowIndex(){
    return this.g9AdminWindows.indexOf(this.currentG9AdminWindow);
  }

  getReportYear(report){
    const written_on = new Date (report.written_on)
    const writtenYear = written_on.getFullYear()
    const writtenMonth = written_on.getMonth() + 1
    const writtenSemester = writtenMonth < 7 ? this.lang.tra('lbl_osslt_report_spring') : this.lang.tra('lbl_osslt_report_term_fall')
    return writtenSemester + ' ' + writtenYear;
  }

  allowBypassDomain(){
    const isBypassDomain = allowBypassDomains.indexOf(getFrontendDomain()) > -1
    return isBypassDomain
  }

  onSetBypassAdminSignOff(value:boolean){
    this.bypassAdminSignOff = value;
    this.loadStudentReports()
  }

  exportOSSLTResult(){
    const user = this.auth.user().value;
    this.auth
      .apiGet(this.routes.SCHOOL_ADMIN_REPORTS, user.uid, this.configureQueryParams()
      ).then(res =>{
        let exportData;
        const columns = this.getOSSLTResultColumns();
        if(res.length > 0){
          exportData = this.getOSSLTResultData(res)
          //downloadFromExportData(exportData, columns, 'osslt-report', this.auth);
          downloadCSVFromExportData('osslt-report.csv',exportData, columns)
        }
      })
  }

  configureQueryParams(){
    let testWindowId
    if(this.currentClassFilter === ClassFilterId.OSSLT){
      testWindowId = this.currentAdminWindow.tw_id
    }
    if(this.currentClassFilter === ClassFilterId.G9){
      testWindowId = this.currentG9AdminWindow.tw_id
    }
    return { query: {schl_group_id: this.schoolService.getCurrentSchoolGroupId(), clientDomain: getFrontendDomain(), testWindowId}}
  }

  getOSSLTResultColumns(){
    const columnsName = OSSLTResultColumns;
    let columns: IExportColumn[] = [];
    columnsName.forEach(column =>{
      columns.push({
        prop: column,
        caption: column,
        isClickable: false
      })
    })
    return columns;
  }

  getOSSLTResultData(data:any){
    let records = [];
    data.forEach(record => {
      //Modify SASN
      if(record.SASN === null){
        record.SASN = '#'
      }

      //Modify EligibilityStatus
      if(record.EligibilityStatus){
        if(record.EligibilityStatus == '1'){
          record.EligibilityStatus = this.lang.tra("sdc_osslt_eligstat_1")
        }
        else if (record.EligibilityStatus == '2'){
          record.EligibilityStatus = this.lang.tra("sdc_osslt_eligstat_2")
        }else{
          record.EligibilityStatus = ''
        } 
      }else{
        record.EligibilityStatus = ''
      }

      //Modify HasStarted
      if(record.HasStarted){
        record.HasStarted = this.lang.tra("lbl_yes")
      }else{
        record.HasStarted = this.lang.tra("lbl_no")
      }

      //Modify HasSubmitted
      if((record.HasSubmitted && record.HasSubmitted == 1)||(record.HasReport && record.ta_closed_on)){
        record.HasSubmitted = this.lang.tra("lbl_yes")
      }else{
        record.HasSubmitted = this.lang.tra("lbl_no")
      }

      if(record.HasSubmitted && record.HasSubmitted == this.lang.tra("lbl_yes") && record.ta_closed_on ){
        record.SubmittedOn = formatDate(new Date(record.ta_closed_on), 'yyyyMMdd', 'en_US')
      }else{
        record.SubmittedOn = ''
      }
      delete record.ta_closed_on;

      //Modify StartedOn
      if(record.StartedOn){
        record.StartedOn = formatDate(new Date(record.StartedOn), 'yyyyMMdd', 'en_US')
      }else{
        record.StartedOn= ''
      }

      //Modify HasReport
      // if(record.HasReport){
      //   record.HasReport = this.lang.tra("lbl_yes")
      // }else{
      //   record.HasReport = this.lang.tra("lbl_no")
      // }

      //Modify Result, Scalescore and HasReport
      if(record.Result){
        switch(record.Result){
          case "0":{
            record.Result = this.lang.tra("sa_report_state_0");
            record.OSSLTScaleScore = ""
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }  
          case "1":{
            record.Result = this.lang.tra("sa_report_state_1");
            record.OSSLTScaleScore = 0
            record.ScaleScoreDescription = this.lang.tra("osslt_report_scalescore_1")
            record.HasReport = this.lang.tra("lbl_yes")
            break;
          }
          case "2":{
            record.Result = this.lang.tra("sa_report_state_2");
            record.ScaleScoreDescription = this.lang.tra("osslt_report_scalescore_2")
            record.HasReport = this.lang.tra("lbl_yes")
            if(record.isLinear == '1'){
              record.OSSLTScaleScore = "ALT"
              record.ScaleScoreDescription = this.lang.tra("osslt_report_scalescore_3")
            }
            break;
          }
          case "3":{
            record.Result = this.lang.tra("sa_report_state_3");
            record.OSSLTScaleScore = ""
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }
          case "4":{
            record.Result = this.lang.tra("sa_report_state_4");
            record.OSSLTScaleScore = ""
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }
          case "5":{
            record.Result = this.lang.tra("sa_report_state_5");
            record.OSSLTScaleScore = ""
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }
          case "6":{
            record.Result = this.lang.tra("sa_report_state_6");
            record.OSSLTScaleScore = ""
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }
          case "10":{
            record.Result = this.lang.tra("sa_report_state_10");
            record.OSSLTScaleScore = ""
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }
          case "11":{
            record.Result = this.lang.tra("sdc_const_osslt_succ_2");
            record.OSSLTScaleScore = 0
            record.ScaleScoreDescription = this.lang.tra("osslt_report_scalescore_1")
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }          
          default:{
            record.Result = this.lang.tra("sa_report_state_2");
            record.OSSLTScaleScore = ""
            record.HasReport = this.lang.tra("lbl_no")
            break;
          }  
        }
      }else{
        record.Result = this.lang.tra("sa_report_state_2");
        record.OSSLTScaleScore = ""
        record.HasReport = this.lang.tra("lbl_no")
      }

      //Modify Note
      if(record.Note === null){
        record.Note = ''
      }
      records.push(record)
    });

    return records
  }
  isAllowBypassDomain(){
    // const clientDomain = getFrontendDomain()
    // const allowBypassDomain = ["http://localhost:4200/", "http://localhost:4401/","https://d3h4m0g2lmrmq6.cloudfront.net/"]
    // // const allowBypassDomain = ["https://d3h4m0g2lmrmq6.cloudfront.net/"]
    // const isBypassDomain = allowBypassDomain.indexOf(clientDomain) > -1
    // return isBypassDomain;
    return true;
  }


  isSignoffAvail(){
    if(!this.currentAdminWindow){
      return false
    }
    const studentSignoffs = this.mySchool.checkStuAsmtSignoffReport(this.currentClassFilter, this.currentAdminWindow.tw_id);
    return this.currentClassFilter && studentSignoffs; // to do, should show alternate version if signed off
  }

  getReportVersion(report){
    const written_on = new Date (report.written_on);
    const writtenYear = written_on.getFullYear();
    if(writtenYear >= 2022) {
      return 'reportVersion1'
    }
    return 'reportVersion0'
  }

  getAdminWindowSlug(adminWindow){
    if(this.currentClassFilter == ClassFilterId.Primary || this.currentClassFilter == ClassFilterId.Junior){
      return adminWindow.sch_year
    }
    if(this.currentClassFilter == ClassFilterId.G9){
      const adminWindowStartDate = new Date (adminWindow.tw_date_start)
      const adminWindowYear = adminWindowStartDate.getFullYear()
      return '' + adminWindowYear + '-' + (adminWindowYear + 1)
    }
    if(this.currentClassFilter == ClassFilterId.OSSLT ){
      const adminWindowStartDate = new Date (adminWindow.tw_date_end)
      const adminWindowYear = adminWindowStartDate.getFullYear()
      const adminWindowMonth = adminWindowStartDate.getMonth()+1
      if(adminWindowMonth < 7){
        return ''+adminWindowYear+' '+this.lang.tra('lbl_osslt_report_term_spring')
      }else{
        return ''+adminWindowYear+' '+this.lang.tra('lbl_osslt_report_term_fall')
      }
    }  
  }
  
  getReportYearVersion1(report){
    if(this.currentClassFilter === ClassFilterId.OSSLT){
      return this.lang.tra("lbl_student_report_2") + ", " + this.getReportYear(report);
    }
    if(this.currentClassFilter === ClassFilterId.Primary|| this.currentClassFilter === ClassFilterId.Junior){
      return this.lang.tra("lbl_student_report_2") + ", " + report.sch_year;
    }
  }

  getScaleScore(report){
    if(this.isLinearFailReport(report) ){
      return 'ALT*'
    }
    return report.scale_score
  }

  isLinearFailReport(report){
    if(report.isLinear && report.isLinear =='1' && report.result_state == '2' ){
      return true;
    }
    return false;
  }

  isFireFox(){
    const agent = window.navigator.userAgent.toLowerCase()
    const isFireFox = agent.includes('firefox');
    return isFireFox;
  }

  generetePJReport(){
    if(!this.currentAdminWindow){
      return; 
    }
    this.schoolService
      .generateSchoolPJReports(this.currentClassFilter, this.currentAdminWindow.tw_id, this.singleStudentUid, this.bypassAdminSignOff)
      .then(res =>{
        if(res.message === 'REPORT_GENERATING'){
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_generating'),
            confirm: () => {}
          });
        }
        if(res.message === 'REPORT_REGENERATING'){
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_regenerating'),
            confirm: () => {}
          });
        }
      })
      .catch(err => {
        if (err.message === 'REPORT_GENERATING') {
          this.loginGuard.confirmationReqActivate({
            caption: this.lang.tra('sa_report_generating'),
            confirm: () => {}
          });
        }
        if (err.message === 'NO_DATA_FOR_REPORT') {
          //do nothing
        }
      })
  }

  async printPJReport(){
    if(!this.currentAdminWindow){
      return; 
    }
    let noAvailableReport = false
    const res = await this.schoolService
       .loadSchoolPJReports(this.currentClassFilter, this.singleStudentUid, this.bypassAdminSignOff, false, this.currentAdminWindow.tw_id)
       .catch(err => {
          noAvailableReport = true;
          if (err.message === 'REPORT_GENERATING') {
            this.loginGuard.confirmationReqActivate({
              caption: this.lang.tra('sa_report_generating'),
              confirm: () => {}
            });
          }
          if (err.message === 'NO_DATA_FOR_REPORT') {
            //do nothing
          }
       })

    const message = res.message
    if(message === 'REPORT_GENERATING'){ // shouldn't come into this line, but put here just incase
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('sa_report_generating'),
        confirm: () => {}
      });
    } 
    if(message === 'REPORT_REGENERATING'){
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('sa_report_regenerating'),
        confirm: () => {}
      });
    } 
    const fileURL = res.reportURL
    if(message === 'REPORT_GENERATED' && fileURL && !noAvailableReport){
      let a = document.createElement('a');
      a.href = fileURL;
      a.download = 'StudentResponseSheets.pdf';
      a.dispatchEvent(new MouseEvent('click'));
    }   
  }

  getPJReportGenerateOn(){
    const currentTestWindowReport = this.studentPJData.find( (report:any) => +report.test_window == +this.currentAdminWindow.tw_id)
    if(currentTestWindowReport){
      const reportGenerateOn = currentTestWindowReport.report_gen_on
      if(reportGenerateOn && reportGenerateOn!= ''){
        return (new Date(reportGenerateOn)).toLocaleString()
      }  
    }
    return ''
  }  
  async loadG9StudentClass(){
    this.g9Classes = await this.mySchool.loadSchoolG9ClassesWithReport()
    this.createG9AdminWindows()
    if(this.g9AdminWindows.length > 0){
      this.currentG9AdminWindow = this.g9AdminWindows[this.g9AdminWindows.length - 1];
    }
    this.classroomsTable.injestNewData(this.g9Classes)
  }

  createG9AdminWindows(){
    this.g9AdminWindows = [];
    this.g9Classes.forEach((theClass:any) =>{
      const g9AdminWindow = this.g9AdminWindows.find(adminWindow => adminWindow.tw_id == theClass.tw_id)
      if(!g9AdminWindow){
         this.g9AdminWindows.push({tw_id:theClass.tw_id, tw_date_start:theClass.tw_date_start})
      }
    })
    this.g9AdminWindows.sort( (window1:any, window2:any) => {
      const window1StartDate = new Date(window1.tw_date_start)
      const window2StartDate = new Date(window2.tw_date_start)
      if(window1StartDate > window2StartDate){
         return 1
      }else if (window1StartDate < window2StartDate){
         return -1
      }else{
         return 0
      }   
    })
  }

  onClickG9Report(classroom){
    if(!this.isPrivateSchool  || this.isPrivateSchool && +classroom.allow_ISR == 1){
      const class_id = classroom.sc_id;
      const schl_group_id = this.g9demoService.schoolData.group_id;
      this.auth.apiPatch(this.routes.SCHOOL_ADMIN_INVIGILATE_CLASS, 0, {class_id, schl_group_id}, this.configureQueryParams())
      .then(result =>{
        this.g9demoService.setIsFromSchoolAdmin(true)
        const queryParams = { ...this.route.snapshot.queryParams};
        const route = `/${this.lang.c()}/educator/assessment-report/${classroom.sc_id}/${classroom.sc_group_id}/`;
        this.router.navigate([route], {
          relativeTo: this.route,
          queryParams
        });
      });
    }else{
      this.loginGuard.quickPopup(this.lang.tra("ta_msg_g9_report_unavailable"))
    }
  }

  pageChanged(){
  }
}
