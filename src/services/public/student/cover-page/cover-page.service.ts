// Initializes the `public/dist-admin/summary` service on path `/public/dist-admin/summary`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { CoverPage } from './cover-page.class';
import hooks from './cover-page.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/cover-page': CoverPage & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/cover-page', new CoverPage(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/cover-page');

  service.hooks(hooks);
}
