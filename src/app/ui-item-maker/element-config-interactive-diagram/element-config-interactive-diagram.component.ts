import { Component, Input, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { AuthService } from 'src/app/api/auth.service';
import { IContentElementInteractiveDiagram } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { bindFormControls } from '../services/data-bind';
import { DgIntConstructionType, DgIntConstructionTypeCaption, disabledConstructionTypes } from 'src/app/ui-testrunner/element-render-interactive-diagram/common';
import { generateDefaultDgIntElement } from 'src/app/ui-testrunner/element-render-interactive-diagram/generator';
import { styleProfilesMap, constructionTypeStyle } from 'src/app/ui-testrunner/element-render-interactive-diagram/style-profile';
import { AssetLibraryService, IAssetLibraryConfig } from '../services/asset-library.service';
import { Subscription } from 'rxjs';

export interface IInteractiveDiagramHiddenConfigs {
  hideConfig?: boolean,
  hideStyle?: boolean,
  hideStyleProfile?: boolean,
  hideTypeDropdown?: boolean,
  simpleStyleMode?: boolean,
}

@Component({
  selector: 'element-config-interactive-diagram',
  templateUrl: './element-config-interactive-diagram.component.html',
  styleUrls: ['./element-config-interactive-diagram.component.scss']
})
export class ElementConfigInteractiveDiagramComponent implements OnInit, OnDestroy {

  @Input() element: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs: IInteractiveDiagramHiddenConfigs = {}
  @Input() isSimplified: boolean = false;
  isShowAdvancedOptions = new FormControl(false);
  // set id and caption to be the values of DiagramaticsVersion enum
  // diagramaticsVersions = Object.values(DiagramaticsVersion).map(v => ({ id: v, caption: v }))
  
  constructionTypes = Object.values(DgIntConstructionType)
    .filter(v => !disabledConstructionTypes.includes(v))
    .map(v => ({ id: v,  caption: DgIntConstructionTypeCaption[v] }));
  constructionTypeForm = new FormControl(DgIntConstructionType.DND_TABLE);
  prevConstructionType : DgIntConstructionType;
  DgIntConstructionType = DgIntConstructionType;
  styleProfileList : Array<{id:string, caption:string}>;
  assetLibrarySub: Subscription;

  constructor(
    private auth: AuthService,
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService,
    private assetLibraryService: AssetLibraryService,
  ) { }

  ngOnInit(): void {
    this.prevConstructionType = this.element.constructionType;
    this.updateStyleProfileList();
    
    if (this.element._changeCounter == undefined) this.element._changeCounter = 0;
    
    if (this.element.scoreWeight == undefined)
      this.element.scoreWeight = 1;
    
    const formControls = [
      {f: this.isShowAdvancedOptions, p:'isShowAdvancedOptions'},
      {f: this.constructionTypeForm, p:'constructionType'},
    ];
    bindFormControls(this.element, formControls);
    this.constructionTypeForm.valueChanges.subscribe( () => {
      if (this.prevConstructionType == this.constructionTypeForm.value) return;
      const confirm = window.confirm('Changing the construction type will reset the element. Are you sure you want to continue?');
      if (!confirm) {
        this.constructionTypeForm.setValue(this.prevConstructionType);
        // this.element.constructionType = this.prevConstructionType;
        return;
      }
      
      this.element.constructionType = this.constructionTypeForm.value;
      this.prevConstructionType = this.constructionTypeForm.value;
      this.element.constructionElement = generateDefaultDgIntElement(this.element.constructionType);
      this.rerender();
      this.updateStyleProfileList();
    });
    
    this.assetLibrarySub = this.assetLibraryService.onElementUpdated.subscribe(() => {
      this.rerender();
    });
  }

  ngAfterViewInit(){
  }

  ngOnDestroy(): void {
    this.assetLibrarySub.unsubscribe();
  }
  
  rerender() {
    this.element._changeCounter += 1;
  }
  
  getStyleProfileCaption(id: string) : string {
    return styleProfilesMap[id]?.caption ?? id;
  }
  
  updateStyleProfileList(){
    const constructionStyles = constructionTypeStyle[this.element.constructionType];
    this.styleProfileList = constructionStyles.map(id => ({ id, caption: this.getStyleProfileCaption(id) }));
  }
  
  addStyleProfile(id: string){
    if (this.element.styleProfiles == undefined) this.element.styleProfiles = [];
    if (this.element.styleProfiles.includes(id)) return;
    this.element.styleProfiles.push(id);
    this.rerender();
  }
  
  removeStyleProfile(id: string){
    const confirm = window.confirm('Are you sure you want to remove this style profile?');
    if (!confirm) return;
    const index = this.element.styleProfiles.indexOf(id);
    if (index < 0) return;
    this.element.styleProfiles.splice(index, 1);
    this.rerender();
  }
  
  isShowingAdvanced(){
    return this.isShowAdvancedOptions.value
  }

}
