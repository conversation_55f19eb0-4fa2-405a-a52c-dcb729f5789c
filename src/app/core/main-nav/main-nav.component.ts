import { Component, OnInit, OnDestroy } from '@angular/core';
import { IPanelLink } from './panels/type';
import { teacherPanels } from './panels/teacher';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { SidepanelService } from '../sidepanel.service';
import { UserSiteContextService } from '../usersitecontext.service';
import { schoolAdminPanels, schoolAdminPanels_NBED, schoolAdminPanels_ABED } from './panels/school-admin';
import { fsaSchoolAdminPanels, fsaSchoolAdminScoreEntryPanels } from './panels/fsa-school-admin';
//import { scoringLeaderPanels } from './panels/scoring-leader';
import { getScoringLeaderPanels, scoringRangeFinderPanels } from './panels/scoring-leader';
import { scoringSupervisorPanels } from './panels/scoring-supervisor';
import fsaMinistryAdminPanels from './panels/fsa-ministry-admin';
import gradMinistryAdminPanels from './panels/grad-ministry-admin';
import gradSchoolAdminPanels from './panels/grad-school-admin';
import fsaDistrictAdminPanels from './panels/fsa-district-admin';
import { ChatpanelService } from '../chatpanel.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { Subject, Subscription } from 'rxjs';
import { AuthService } from '../../api/auth.service';
import { AccountType } from '../../constants/account-types';
import { LangService } from '../lang.service';
import {LoginGuardService} from "../../api/login-guard.service";
import { StudentG9ConnectionService } from 'src/app/ui-student/student-g9-connection.service';
import { RoutesService } from 'src/app/api/routes.service';

export interface Item { name: string; }

@Component({
  selector: 'main-nav',
  templateUrl: './main-nav.component.html',
  styleUrls: ['./main-nav.component.scss']
})
export class MainNavComponent implements OnInit, OnDestroy {

  sidePanelOptions:IPanelLink[] | any = [];
  activePanelLink:IPanelLink;

  isAccountLoaded:boolean;
  displayName:string;
  displayEmail:string;
  accountType:string;
  isLanguageInitialized: boolean = false;
  currentQueryParams
  
  private signinSub:Subscription;
  private routerSub:Subscription;

  constructor(
    public whiteLabelService: WhitelabelService,
    private auth: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    public sidePanel: SidepanelService,
    public lang: LangService,
    private chatPanel: ChatpanelService,
    private loginGuard: LoginGuardService,
    private userSiteContext: UserSiteContextService,
    public studentG9Connection: StudentG9ConnectionService,
    private routes: RoutesService
  ) { }


  ngOnInit(){
    this.assignPanelLinks();
    this.signinSub = this.auth.user().subscribe(userInfo => {
      if (userInfo){
        this.isAccountLoaded = this.sidePanel.isAccountLoaded = true;
        this.displayName = [userInfo.firstName, userInfo.lastName].join(' ');
        this.displayEmail = userInfo.email;
        this.accountType = userInfo.accountType;
      }
      else{
        this.isAccountLoaded = this.sidePanel.isAccountLoaded = false;
        this.accountType = null;
      }
      this.assignPanelLinks();
    });
    this.sidePanel.forcedSub.subscribe(isForcedPanels => {
      if (isForcedPanels){
        this.assignPanelLinks();
      }
    })
    // this.route.params.subscribe(params => {
    //   this.classroomId = params['classroomId'];
    //   console.log ('classroomId', this.classroomId, params)
    // });

    this.routerSub =  this.router.events.subscribe(val => {
      if (val instanceof NavigationEnd){
        this.checkForActivePanel(val.url);
        setTimeout(()=> { // because the router processing takes a moment to update the active classroom id from the route itself... TO DO: have a mechanism that can extract (and update) the classroom id from here
          this.checkForActivePanel(val.url);
        }, 100);
      }
    });

    this.lang.languageInitialized.subscribe(() => {
      this.isLanguageInitialized = true;
    });

    this.route.queryParams.subscribe((queryParams) => {
      // todo: generalize to all props
      this.currentQueryParams = {
        uid: queryParams['uid'],
        school: queryParams['school']
      }
    });
  }

  ngOnDestroy() {
    if (this.signinSub) {this.signinSub.unsubscribe();}
    if (this.routerSub) {this.routerSub.unsubscribe();}
  }

  assignPanelLinks(){
    this.sidePanelOptions = [];
    this.getUiPanels().forEach(panel => {
      this.sidePanelOptions = this.sidePanelOptions.concat(panel);
    });
    this.checkForActivePanel(this.router.url);
  }

  getUiPanels(){
    if (this.sidePanel.forcedPanels){
      return this.sidePanel.forcedPanels;
    }
    const ui = this.accountType;
    switch(ui){
      case AccountType.EDUCATOR:        return [teacherPanels]
      case AccountType.SCHOOL_ADMIN:    
        if(this.whiteLabelService.isNBED()){
          return [schoolAdminPanels_NBED];
        }
        if(this.whiteLabelService.isABED()){
          return [schoolAdminPanels_ABED];
        }
        return [schoolAdminPanels];
      case AccountType.BC_FSA_SCHOOL_ADMIN:    return [fsaSchoolAdminPanels];
      case AccountType.BC_FSA_SCHOOL_ADMIN_SCORE_ENTRY:    return [fsaSchoolAdminScoreEntryPanels];
      case AccountType.BC_FSA_DIST_ADMIN_SCORE_ENTRY:     return [fsaDistrictAdminPanels];
      case AccountType.SCOR_RAFI:    return [ [] ]; // scoringRangeFinderPanels
      case AccountType.SCOR_LEAD:    return [ [] ]; // scoringLeaderPanels
      case AccountType.SCOR_SUPR:    return [ [] ]; // scoringSupervisorPanels
      case AccountType.BC_GRAD_MINISTRY_ADMIN:  return [gradMinistryAdminPanels];
      case AccountType.BC_GRAD_SCHOOL_ADMIN: return [gradSchoolAdminPanels];
      case AccountType.BC_FSA_MINISTRY_ADMIN: return [fsaMinistryAdminPanels];
      case AccountType.BC_FSA_DIST_ADMIN: return [fsaDistrictAdminPanels];
      default: return [];
    }
  }

  isOptionHidden(option:IPanelLink){
    if (option.requiresClassroom){
      if (!this.userSiteContext.hasActiveClassroom()){
        return true;
      }
    }
    return false;
  }

  openSupportChat(){
    this.chatPanel.expand();
  }

  checkForActivePanel(currentUrl:string){

    const checkLinkForRoute = (panelLink) => {
      const routeParts:string[] = this.processRouteParts(panelLink.routerLink);
      if (routeParts[1]){
        routeParts[1] = this.lang.c(); // temp, hardcoding the lang
      }
      let url = routeParts.join('/');
      url.split(':classroomId').join(this.sidePanel.classroomId); // todo: should be happening in processRouteParts
      panelLink.routerLinkProcessed = url;
      if (currentUrl.indexOf(url) === 0){
        this.activePanelLink = panelLink;
      }
    }

    this.activePanelLink = null;

    // If nested - iterate over subpanels in each category
    if (this.sidePanel.isForcedPanelsNested){
      this.sidePanelOptions.forEach(sidePanelGroup => {
        sidePanelGroup.subpanels.forEach(panelLink => {
          checkLinkForRoute(panelLink)
        })
      })
    }
    // If not nested - iterate over only list of panels 
    else {
      this.sidePanelOptions.forEach( panelLink =>{
        checkLinkForRoute(panelLink)
      })
    }
  }
  logout() {
    if (this.studentG9Connection.userConnectionId) {
      this.studentG9Connection.logOffUser();
    }
    this.auth
      .logout()
      .then( r => {
        if (this.whiteLabelService.getSiteFlag('IS_BCED')){
          this.router.navigate(['/en/bced-landing/admin']);
        }
        else{
          this.router.navigate(['/en/login-router-st']);
        }
        setTimeout(() => {
          window.location.reload();
        }, 300);
      });
  }

  getLogoAlt(){
    if (this.whiteLabelService.getSiteFlag('IS_EQAO')){
      return this.lang.tra('lbl_eqao_logo')
    }
    else if (this.whiteLabelService.getSiteFlag('IS_BCED')){
      return 'Logo'
    }
    else {
      return 'Logo'
    }
  }


  // getUsername(){
  //   return this.auth.getUsername();
  // }

  // getAccountTypeDisplayName(){
  //   switch(this.auth.getUi()){
  //     case UserUi.TEACHER: return 'Teacher';
  //     case UserUi.STUDENT: return 'Student';
  //     case UserUi.PARENT: return 'Parent';
  //     case UserUi.ADMIN: return 'Administrator';
  //     case UserUi.TRIAL_COORD: return 'Trial Coordinator';
  //     case UserUi.DISTRICT_COORD: return 'District Coordinator';
  //   }
  // }

  isUserCoordinator(){

  }
  // isUserTeacher(){
  //   switch(this.auth.getUi()){
  //     case UserUi.TEACHER:
  //     case UserUi.TRIAL_COORD:
  //     case UserUi.ADMIN:
  //       return true;
  //   }
  //   return false;
  // }
  // isUserParent(){
  //   return this.auth.getUi() === UserUi.PARENT;
  // }
  // isUserAdministrator(){
  //   return this.auth.getUi() === UserUi.ADMIN;
  // }

  // logout(){
  //   this.auth.signOut();
  //   this.router.navigate(['/']);
  // }

  // getUserPhotoUrl(){
  //   return this.auth.getPhotoUrl();
  // }

  isAccountDropupActive() {
    return this.sidePanel.getAccountDropupActive();
  }

  toggleAccountDropdown(){
    this.sidePanel.toggleAccountDropdown();
  }

  // getUserDisplayname(){
  //   return this.auth.getDisplayName()
  // }

  isSidePanelExpanded():boolean{
    return this.sidePanel.getExpandedStatus();
  }

  expandSidePanel(){
    this.sidePanel.expand();
  }
  collapseSidePanel(){
    this.sidePanel.unexpand();
  }

  isRouteActive(panelLink:IPanelLink) {
    if(panelLink === this.activePanelLink){
      return true;
    }
    return false;
  }

  processRouteParts(routeTemplate:string):string[]{
    let routeParts:string[] = routeTemplate.split('/');
    routeParts.forEach( (routePart,i) => {
      if (i > 0){
        routeParts[i] = this.userSiteContext.processRoutePart(routePart);
      }
    })
    return routeParts;
  }

  navToOptionRoute(option:IPanelLink){
    // console.log('navToOptionRoute', option.requiresClassroom, this.userSiteContext.hasActiveClassroom())
    // return;
    this.navToRoute(option.routerLink);
  }

  navToRoute(routeTemplate:string){
    let routeParts:string[] = this.processRouteParts(routeTemplate);
    this.router.navigate(routeParts);
  }

  pageUnavailable(e: Event) {
    if (e) {
      (e.target as HTMLElement).blur();
    }
    this.loginGuard.quickPopup('This page is currently unavailable.')
  }

  navItemClicked(e: Event, item: IPanelLink){
    if (!item.disabled) {
      this.router.navigate([item.routerLinkProcessed],{queryParams: this.currentQueryParams})
    } else {
      this.pageUnavailable(e)
    }
  }

  /** Expand/unexpand a panel group, if expanding then unexpand any previously expanded one */
  togglePanelGroup(targetPanelGroup){
    targetPanelGroup.isExpanded = !targetPanelGroup.isExpanded
    if (targetPanelGroup.isExpanded){
      this.sidePanelOptions
      .filter(option => option !== targetPanelGroup)
      .forEach(option => {
        option.isExpanded = false;
      })
    }

  }


}
