import { Component, Input, OnInit } from '@angular/core';
import { AuthService, getFrontendDomain } from "../../api/auth.service";
import { RoutesService } from "../../api/routes.service";
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import * as _ from 'lodash';
import * as moment from 'moment-timezone';
import { LangService } from "../../core/lang.service";
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { G9DemoDataService } from 'src/app/ui-schooladmin/g9-demo-data.service';
import { IValidationError } from 'src/app/ui-schooladmin/data/types';
import { REGEX_VALID_EMAIL } from 'src/app/constants/regex';

const STANDARD_TIMEZONE = 'America/Toronto'; // todo:WHITELABEL

enum AdminAccountsModal {
  NEW = 'NEW',
  EDIT = 'EDIT',
}

@Component({
  selector: 'panel-sb-sd-accounts',
  templateUrl: './panel-sb-sd-accounts.component.html',
  styleUrls: ['./panel-sb-sd-accounts.component.scss']
})
export class PanelSbSdAccountsComponent implements OnInit {

  @Input() schoolDistrictGroupId:number;

  constructor(
    public lang: LangService,
    private auth: AuthService,
    private loginGuard: LoginGuardService,
    public whiteLabel: WhitelabelService,
    private routes: RoutesService,
    private g9DemoData: G9DemoDataService,
    private pageModalService: PageModalService,
  ) { }

  isAnySelected = false;
  isAllSelected = false;
  schoolAdminsTable: MemDataPaginated<any>;
  schoolAdmins = [];
  public pageModal: PageModalController;
  AdminAccountsModal = AdminAccountsModal;
  selectedAdmin;

  ngOnInit(): void {
    this.loadSchoolAdminAccesses();
    this.schoolAdminsTable = new MemDataPaginated({
      data: this.schoolAdmins,
      configurablePageSize: true,
      filterSettings: {
        schoolAdmin: (admin: any, val: string) => 
        (admin.name == null ? "" : admin.name.toLocaleLowerCase()).includes(val.toLocaleLowerCase())
      },
      sortSettings: {
        schoolAdmin: (admin: any) => _.orderBy((admin.name == null ? "" : admin.name).trim().toLowerCase(), ['asc']),
        hasAccess: (admin: any) => admin.isConfirmed,
        startTime: (admin: any) => _.orderBy(admin.created_on, ['asc']),
      }
    });
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  loadSchoolAdminAccesses(){
    this.auth.apiFind(this.routes.SCHOOL_DIST_SD_ACCESS, this.configureQueryParams())
      .then((result) => 
      {
        this.formatTime(result);
        this.schoolAdmins = result;
        this.schoolAdminsTable.injestNewData(this.schoolAdmins);
      });
  }

  formatTime(result: any[]): void {
    result.forEach(row => 
    {
      row.expire_on_formatted = this.auth.formatDateForWhitelabel(row.expire_on, undefined, this.lang.getCurrentLanguage());
      row.created_on_formatted = this.auth.formatDateForWhitelabel(row.created_on, undefined, this.lang.getCurrentLanguage());
      row.startTimeformatted = this.auth.formatDateForWhitelabel(row.startTime, undefined, this.lang.getCurrentLanguage());
      row.endTime_formatted = this.auth.formatDateForWhitelabel(row.endTime, undefined, this.lang.getCurrentLanguage());
    });
  }

  configureQueryParams(){
    return {
      query: {
        schl_dist_group_id: this.schoolDistrictGroupId,
      }
    }
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }

  newAdminModalStart(){
    const config: any = {};
    this.pageModal.newModal({
      type: AdminAccountsModal.NEW,
      config,
      finish: config => this.newAdminModalFinish(config)
    });
  }

  private getSdRole(){
    const roles = this.whiteLabel.getRolesSd();
    if (!roles.length){
      alert('SD Roles note defined in this WL context.')
      throw new Error('NO_SD_ROLE_WL_CONTEXT')
    }
    return roles[0];
  }

  newAdminModalFinish(config){
    const validationErrors: any[] = this.validateAdmin(config);
    const roleType = this.getSdRole(); // this is validated on the API too
    const data = 
    {
      first_name: config.payload.firstName,
      last_name: config.payload.lastName,
      email:config.payload.email,
      emailLinkDomain: getFrontendDomain(),
      whiteLabelContext: this.whiteLabel.getWhitelabelFlag(),
      roleType,
    }

    if (validationErrors.length === 0) {
      return this.auth
      .apiCreate(this.routes.SCHOOL_DIST_SD_ACCESS, data, this.configureQueryParams())
      .then(res => {
        this.schoolAdmins = this.schoolAdmins.concat(res);
        this.schoolAdminsTable.injestNewData(this.schoolAdmins);
        const message = 'New ' + this.getSchoolDistrictRoleLabel() + ' Account Created'
        setTimeout(() => this.loginGuard.quickPopup(message), 0);
      })
      .catch(error =>{
        switch(error.message){
          case 'ACCOUNT_ALREADY_EXISTED':
            const message = ` This user already has an account with this ${this.getSchoolDistrictRoleLabel()}.`
            setTimeout(() => this.loginGuard.quickPopup(this.lang.tra("sa_school_admin_invite_account_existed")), 0);
            break
          default:
            setTimeout(() => this.loginGuard.quickPopup(error.message), 0);
            break  
        }
      })
    } else {
      setTimeout(() => this.loginGuard.quickPopup(validationErrors[0].message), 0);
    }
  }

  private validateAdmin(config, isEditCheck = false){
    const createValidationError = (prop: string, message: string): IValidationError => { return { prop, message } }
    const admin = config.payload;
    const errors: any[] = [];
    let requiredFields = [
      { prop: 'firstName', label: 'lbl_first_name' },
      { prop: 'lastName', label: 'lbl_last_name' },
    ];
    if(!isEditCheck){
      requiredFields.push({prop: 'email', label: 'lbl_email'})
    }

    for (let i = 0; i < requiredFields.length; i++) {
      const field = requiredFields[i];
      const prop = field.prop;
      const val = admin[prop];
      if (typeof val === 'undefined' || val === null || val.toString().trim() === '') {
        const fieldName = this.lang.tra(field.label);
        const errorMessage = this.lang.tra('sa_required_field', null, { fieldName });
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        return errors;
      } else if (prop === "firstName" && val.length > 35) {
        const fieldName = this.lang.tra(field.label);
        const errorMessage = this.lang.tra('brc_teacher_firstname_char_len_range', null, { fieldName });
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        return errors;
      } else if (prop === "lastName" && val.length > 35) {
        const fieldName = this.lang.tra(field.label);
        const errorMessage = this.lang.tra('brc_teacher_lastname_char_len_range', null, { fieldName });
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        return errors;
      }
    }
    //email validation
    if (!isEditCheck ) {
      // todo:CONST store this in a central place in the repo
      const re = REGEX_VALID_EMAIL
      if (admin.email !== undefined && admin.email !== null && !re.test(String(admin.email).trim().toLowerCase())) {
        const prop = 'email';
        const errorMessage = this.lang.tra('sa_valid_email_prompt');
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        return errors;
      }
    }
    return []
  }

  editAdminModalStart(){
    const admins: any = this.getSelected(false)
    const config: any = {
      admins,
    };
    this.pageModal.newModal({
      type: AdminAccountsModal.EDIT,
      config,
      finish: config => this.editAdminModalFinish(config)
    });
  }

  editAdminModalFinish(config){
    const validationErrors: IValidationError[] = this.validateAdmin(config, true);
    if (validationErrors.length === 0) {
      const data = {
        first_name:  config.payload.firstName,
        last_name: config.payload.lastName,
        whiteLabelContext: this.whiteLabel.getWhitelabelFlag()
      }
      this.auth.apiPatch(this.routes.SCHOOL_DIST_SD_ACCESS, this.selectedAdmin.uid, data, this.configureQueryParams())
        .then(res => {
          //remove the old one and insert the new record
          const theAdmin = this.schoolAdmins.find(sa => +sa.uid === +res[0].uid)
          if(theAdmin){
            const i = this.schoolAdmins.indexOf(theAdmin);
            this.schoolAdmins.splice(i, 1);
            this.schoolAdmins = this.schoolAdmins.concat(res);
            this.schoolAdminsTable.injestNewData(this.schoolAdmins);
          }
        })
        //reset selection
        this.resetSelection();
    }else{
      setTimeout(() => this.loginGuard.quickPopup(validationErrors[0].message), 0);
      this.resetSelection();
    }  
  }

  setSelectAdmin(admin){
    this.selectedAdmin = admin;
  }

  revokeSelectedAdmins(){
    const targets: any = this.getSelected(false);
    const confirmationMsg = this.lang.tra('sd_revoke_schl_admin_warning', undefined, { admin_count: targets.length }); // You have selected ${targets.length} school admintrators to be revoked. Are you sure that you would like to remove the school admintrator(s) ability to administer the assessment?
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      confirm: () => {
        targets.map(entry => {
          this.archiveAdmin(entry.id || entry.uid)
            .then(res => {
              const i = this.schoolAdmins.indexOf(entry);
              this.schoolAdmins.splice(i, 1);
              this.schoolAdminsTable.injestNewData(this.schoolAdmins);
              this.resetSelection();
            })
        })
      }
    })
  }

  getSelected(throwNullSet: boolean) {
    const schoolAdmins = [].concat(this.schoolAdmins).filter(entry => entry.__isSelected);

    if (throwNullSet && schoolAdmins.length === 0) { // shouldn't run into this if statement at all because edie button is disable if no entries are selected
      alert('Please select a '+this.getSchoolDistrictRoleLabel()+' account before proceeding with this action.');
      throw new Error('No account selected');
    }
    return schoolAdmins;
  }

  archiveAdmin(uid) {
    return this.auth.apiRemove(this.routes.SCHOOL_DIST_SD_ACCESS, uid, this.configureQueryParams())
  }

  pageChanged(){
    if (!this.isAllSelected) {
      this.schoolAdmins.forEach(student => student.__isSelected = false);
    }
  }

  toggleSelectAll(){
    this.setSelectAll(this.isAllSelected);
  }

  setSelectAll(state: boolean) {
    this.isAllSelected = state;
    this.schoolAdmins.forEach(schoolAdmin => schoolAdmin.__isSelected = state);
    this.isAnySelected = state;
  }

  resetSelection() {
    this.isAnySelected = false;
    this.isAllSelected = false
    this.schoolAdmins.forEach(student => {
      student.__isSelected = false
    });
  }

  checkSelection() {
    this.isAnySelected = false;
    this.isAllSelected = false
    this.schoolAdmins.forEach(student => {
      if (student.__isSelected) {
        this.isAnySelected = true;
      }
    });
  }  

  getSchoolDistrictRoleLabel(){
    return this.whiteLabel.getSiteText('lbl_sd')
  }


  showInvitationLink(admin){
    this.loginGuard.quickPopup(this.renderAdminInvitationToClipboard(admin))
  }

  // logInvitation(admin){
  //   // TODO
  // }

  renderAdminInvitationToClipboard(admin){
    const homepage = window.location.host;
    const hostname = window.location.hostname;
    const urlPrefix = (hostname !== 'localhost') ? 'https://' : ''
    const url = urlPrefix+ `${homepage}/#/${this.lang.getCurrentLanguage()}/test-admin/accept-invitation/${admin.invit_email}/${admin.invit_id}X${admin.secret_key}/${admin.firstName}/${admin.lastName}`
    return url;
  }

  onAdminInviteLinkCopy(admin){
    alert(this.lang.tra('sa_school_admin_invite_link'));
    //alert('The invitation link has been copied to your clipboard. You can paste it into an email to share it with the school admin.  ')
  }

  invitationExpired(expireOn: string){
    const expireDate = moment(expireOn).format('YYYY-MM-DD'); 
    const wlTimezone = this.whiteLabel.getTimeZone()
    const currentDate = moment.tz(moment(), wlTimezone).utc().format('YYYY-MM-DD');  

    // Compare current date and invitation expire date -- if current date > expire date, return true; otherwise, return false
    if (moment(currentDate).isAfter(expireDate)){
      return true;
    } else {
      return false;
    }
  }

  extendInvitation(admin){
    const data = {
      emailLinkDomain: getFrontendDomain(),
      whiteLabelContext: this.whiteLabel.getWhitelabelFlag()
    }
    this.auth.apiUpdate(this.routes.SCHOOL_DIST_SD_ACCESS, admin.invit_id, data, this.configureQueryParams())
     .then(res =>{
        const theAdmin = this.schoolAdmins.find(sa => +sa.uid === +res[0].uid)
        if(theAdmin){
          const i = this.schoolAdmins.indexOf(theAdmin);
          this.schoolAdmins.splice(i, 1);
          this.schoolAdmins = this.schoolAdmins.concat(res);
          this.schoolAdminsTable.injestNewData(this.schoolAdmins);
        }
     })
  }

  notScholAdmin(){
    const uid = this.auth.getUid()
    const confirmationMsg = this.lang.tra('sa_not_schl_admin_warning');
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      confirm: () => {
        //revoke self as school admin
        this.archiveAdmin(uid);

        //logout school admin
        this.auth.logout();
      }
    })
  }

}
