import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import {  } from '../../../../sql/scoring';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {
  [key: string]: any;
}

interface INewScoringPair {
  mwi_id: number,
  users: IScoringPairUser[]
}

interface IScoringPairUser {
  uid: number,
  task_id: number,
  is_read_only: boolean
}
interface ServiceOptions {}

export class ScoringPairs implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');
    const uid = await currentUid(this.app, params);
    const newPairs: INewScoringPair[] = data.newPairs;

    for (const newPair of newPairs){
      const newPairRecord = await this.app.service('db/write/marking-pairs').create({
        group_to_mwi_id: newPair.mwi_id,
        created_by_uid: uid,
        created_on: dbDateNow(this.app)
      })
      for (let user of newPair.users){
        await this.app.service('db/write/marking-pairs-users').create({
            pair_id: newPairRecord.id,
            uid: user.uid,
            is_read_only: user.is_read_only ? 1 : 0
        })
        await this.app.service('db/write/marking-item-marker-tasks').patch(user.task_id, {
          pair_id: newPairRecord.id
        })
      }
    }
    return {}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');
    const { pairIdsToRevoke } = data;
    if (!pairIdsToRevoke?.length) throw new Errors.BadRequest('MISSING_DATA');

    const uid = await currentUid(this.app, params);

    // Revoke pair records
    for (const pairId of pairIdsToRevoke){
      await this.app.service('db/write/marking-pairs').patch(pairId, {
        is_revoked: 1,
        revoked_by_uid: uid,
        revoked_on: dbDateNow(this.app)
      })
    }

    // Unlink pair records from tasks
    const itemTasks = await dbRawRead(this.app, {pairIdsToRevoke}, `
      select id from marking_item_marker_tasks
      where pair_id in (:pairIdsToRevoke);
    `)
    for (const itemTask of itemTasks){
      await this.app.service('db/write/marking-item-marker-tasks').patch(itemTask.id, {
        pair_id: null
      })
    }
    return {}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
