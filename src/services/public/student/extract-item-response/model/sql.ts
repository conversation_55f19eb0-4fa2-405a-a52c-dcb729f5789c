export const SQL_ITEM_USE_WINDOW = ` /* SQL_ITEM_USE_WINDOW */ 
    select twtar.test_window_id
         , tw.title 
         , max(twtar.is_active) twtar_is_active
    from test_question_register tqr 
    join test_window_td_alloc_rules twtar 
        on tqr.test_design_id  = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
    join test_windows tw 
        on tw.id = twtar.test_window_id 
    where tqr.question_id = :item_id
    group by twtar.test_window_id
`

export const SQL_ITEM_TW_STATS = ` /* SQL_ITEM_TW_STATS */ 
select 
       pis.lang
     , pis.NR
     , pis.NF
     , pis.omit
     , pis.p
     , pis.rpb 
     , pis.crpb
     , pis.export_id 
     , pis.test_design_id 
     , pis.form_code 
     , pis.95p_lower
	 , pis.95p_upper
	 , pis.p_high
	 , pis.p_mid
	 , pis.p_low
	 , pis.total
	 , pis.high
	 , pis.mid
	 , pis.low
	 , pis.iri
	 , pis.rbis
	 , pis.crbis
     , pis.imported_on 
from psych_item_stats pis 
where item_id = :item_id
    and test_window_id = :tw_id
`

export const SQL_ITEM_EXP_ANS = (test_window_id:number) => ` /* SQL_ITEM_EXP_ANS */ 
    select tqer.formatted_response
         , tqer.id
         , tqer.coded_response
         , tqer.score
         , tqer.weight
         , tqer.response_raw
         , count(tqer.id) total
         , tqer.lang
         , tqer.created_on
         , tqer.is_item_score_exceptions
         , tqer.score_override
         , tqer.is_from_admin -- everything here and below is from admin
         , tqer.export_id
         , tqer.test_window_id
         , tqer.test_design_id
         , tqer.asmt_code
         , tqer.form_code
         , tqer.proportion_of_total
         , tqer.proportion_of_high
         , tqer.proportion_of_mid
         , tqer.proportion_of_low
         , tqer.total_score
         , tqer.n_total
         , tqer.n_high
         , tqer.n_mid
         , tqer.n_low
         , tqer.DP
         , tqer.se_of_dp
         , tqer.sample_taqr_id
    from test_question_expected_responses tqer
    where tqer.item_id = :test_question_id
        and tqer.is_revoked = 0
        ${ test_window_id ? `and tqer.test_window_id = :test_window_id `: `and tqer.is_from_admin = 0 `}
    group by tqer.formatted_response
           , tqer.score
           , tqer.lang
           , tqer.id -- until we can confirm no duplicates
`