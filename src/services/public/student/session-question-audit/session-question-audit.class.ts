import { Id, NullableId, <PERSON><PERSON>ated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Knex } from 'knex';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { ITestAttempt, ITestAttemptInfo } from '../../../db/schemas/test_attempts.schema';
import { ITestFormQuesConfig } from '../../educator/report-results/report-results.class';
import { TestFormConstructionMethod } from '../../test-auth/test-design-question-versions/test-design-question-versions.class';
import { ILanguage, UpsertResult } from '../../test-question-register/tqr-publish/types';
import { QuestionContent } from '../../test-taker/invigilation/question-content/question-content.class';
import { IAttemptPayload } from '../../test-taker/invigilation/test-attempt/test-attempt.class';
import { IExpectedResponseData } from '../extract-item-response/extract-item-response.class';
import { ITestAttemptQuestionResponses } from '../session/session.class';
const _ = require('lodash');

interface Data extends ISqaAuditConfig{
  test_window_id : number
  resolve_note? : string
  is_resolved? : boolean 
}

interface ISqaAuditConfig {
  attemptProcessingInterval? : number, 
  attemptProcessingLimit?: number, 
  includeProcessedAttempts: boolean, 
  includeProcessedTaqrs: boolean, 
  studentOEN?: number,
}

interface IAuditCreationData {
  uid?: number
  test_attempt_id: number,
  test_question_id: number,
  audit_slug: EAuditSlugs,
  audit_description: string,
  score?: number
  response_raw? : any
  response? : string
}

interface IAuditRequiredData { 
  test_attempt_id: number, 
  test_question_id: number, 
  response_raw: string,
  module_id?: number,
  section_index?: number,
  response?: string
}

interface IFormattedResponse {
  formatted_response: any;
  score: number;
  weight: number;
}

export enum EAuditSlugs {
  POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED = 'POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED',
  QUESTION_SCORE_NOT_ALIGNED_WITH_EA = 'QUESTION_SCORE_NOT_ALIGNED_WITH_EA',
  QUESTION_SCORE_OUT_OF_RANGE = 'QUESTION_SCORE_OUT_OF_RANGE',
  QUESTION_NOT_ASSOCIATED_TO_TEST_FORM = 'QUESTION_NOT_ASSOCIATED_TO_TEST_FORM'  ,
  MSCAT_IMPOSSIBLE_PANEL_PATHWAY = 'MSCAT_IMPOSSIBLE_PANEL_PATHWAY',
}

export const auditSlugDescription  = {
  'POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED' : 'Student response is not aligned to the Possible Expected Answers captured by EQAO Assessment team',
  'QUESTION_SCORE_NOT_ALIGNED_WITH_EA' : 'Student response score is not aligned to the Possible Expected Answers captured by EQAO Assessment team',
  'QUESTION_SCORE_OUT_OF_RANGE' : 'Student response contains a score value greater than the maximum score points indicated by EQAO Assessment team',
  'QUESTION_NOT_ASSOCIATED_TO_TEST_FORM': 'Student response relates to an item that is not associated with the test form in which they are assigned',
  'MSCAT_IMPOSSIBLE_PANEL_PATHWAY': "Student responses represent an impossible panel pathway (this can happen in the case of student un-submissions)."
}


interface ServiceOptions {}

const MINUTE_TO_MS = 60000;

export class SessionQuestionAudit implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    
    if(!params || !params.query) throw new Errors.BadRequest('MISSING_PARAMS');

    const { test_window_id } = params.query

    const records = await dbRawRead(this.app, [test_window_id], `
      select sqa.id as audit_id 
        , ta.id  as attempt_id
        , twtar.slug 
        , sqa.created_on audit_created_on
        , um.value as StudentOEN
        , ta.uid 
        , u.first_name 
        , u.last_name      
        , tq.question_label 
        , tq.question_set_id as item_set_id
        , sqa.test_question_id 
        , tw.id as test_window_id
        , sc.access_code 
        , ta.test_form_id  
        , sqa.audit_description 
        , taqr.score
        , sqa.resolve_note
        , twtar.is_sample
        , tf.lang
        , taqr.response_raw
        , taqr.weight
        , taqr.is_nr 
        , taqr.created_on taqr_created_on
        , taqr.updated_on taqr_updated_on
        , taqr.id taqr_id
        , taqr.module_id 
        , taqr.section_id
        , sqa.is_resolved
        , sqa.audit_slug
        , sqa.response
      from test_windows tw
      join test_sessions ts on ts.test_window_id = tw.id
      join test_attempts ta on ta.test_session_id = ts.id and ta.is_invalid = 0 and ta.started_on is not null
      join school_class_test_sessions scts on scts.test_session_id = ts.id
      join session_question_audit sqa on sqa.uid = ta.uid and sqa.test_attempt_id = ta.id 
      join user_metas um on um.uid = ta.uid and um.key in ('StudentOEN') 
      join users u on u.id = ta.uid  
      left join test_questions tq on tq.id = sqa.test_question_id 
      join school_classes sc on sc.id = scts.school_class_id 
      left join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
      left join test_forms tf on tf.id = ta.test_form_id
      left join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id and sqa.test_question_id = taqr.test_question_id  and taqr.is_invalid != 1
      join test_question_register tqr on tqr.question_id = tq.id and tqr.test_form_id = tf.id
      where tw.id = ?
      GROUP BY sqa.id 
    ;`);

    return records;

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<any> {
    // used to run verify question exist in test form audit :        - Depricated
    if(!id) throw new Errors.BadRequest();
    const test_window_id = +id
    return await this.verifyQuestionsExistInTestForm(test_window_id);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<any> {
    
    const { test_window_id, attemptProcessingInterval, attemptProcessingLimit, includeProcessedAttempts, includeProcessedTaqrs, studentOEN} = data;
    if(!data?.test_window_id) throw new Error("MISSING_TEST_WINDOW_ID");

    const processedAttemptIds = await this.runRealTimeBatchAuditsOnTw(data);

    return processedAttemptIds;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!id || !data || !params) throw new Errors.BadRequest();

    const { resolve_note, is_resolved } = data

    const patch = {
      resolve_note,
      is_resolved : is_resolved ? 1 : 0,
      updated_on: dbDateNow(this.app),
      updated_by_uid: await currentUid(this.app, params)
    }

    return await this.app.service('db/write/session-question-audit').patch(id, patch)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async _createAuditLog(data: IAuditCreationData){
    if(!data)  throw new Errors.BadRequest();

    let {
      test_attempt_id,
      test_question_id,
      audit_slug,
      audit_description,
      score,
      response_raw,
      response,
      uid
    } = data


    if(!test_attempt_id ) throw new Errors.BadRequest("MISSING_DATA");

    let currentAttempt: ITestAttempt;
    
    if(!uid){
      // get associated user with the test_attempt
      currentAttempt = await this.app.service('db/read/test-attempts').get(test_attempt_id);
      uid = currentAttempt.uid;
    }

    // check if the issue already exists in the log 

    const issueExist = await this.app
      .service('db/read/session-question-audit')
      .db()
      .where('test_attempt_id', test_attempt_id)
      .andWhere('test_question_id', test_question_id)
      .andWhere('audit_slug', audit_slug)
      .select();

    
    if(issueExist && issueExist.length) return ;

    return await this.app
      .service('db/write/session-question-audit')
      .create({
        uid,
        test_attempt_id,
        test_question_id,
        audit_slug,
        audit_description,
        score,
        response_raw,
        response,
        created_on: dbDateNow(this.app),
        created_by_uid: uid,
      })  
  }

  async runRealTimeBatchAuditsOnTw(data:Data) {
    const { test_window_id, attemptProcessingInterval, attemptProcessingLimit, includeProcessedAttempts, includeProcessedTaqrs, studentOEN} = data;
  
    // fetch student attempt(s) 
    const test_attempts = await this.findTestAttemptsForTw(test_window_id, attemptProcessingLimit, includeProcessedAttempts, studentOEN);

    const test_form_cache: Map<number, any> = new Map();
    const test_design_cache: Map<number, any> = new Map();

    const processedAttempts: number[] = [];
    const LOG_INTERVAL = 50;

    for(let i=0; i<test_attempts.length; i++){

      if ( i % LOG_INTERVAL == 0){
        console.log('RT Audit processed ', i , 'of', test_attempts.length)
      }

      const test_attempt = test_attempts[i]
      const startTime = new Date().valueOf();      
      const processedId = await this.processAttempt(test_attempt, test_form_cache, test_design_cache, includeProcessedTaqrs);
      
      if(processedId) processedAttempts.push(processedId);
      const totalExecutionTime = new Date().valueOf() - startTime;
      
      if(attemptProcessingInterval ){
        const ms = attemptProcessingInterval * MINUTE_TO_MS;
        if(totalExecutionTime < ms){
          // wait for additional time before processing next
          const waitingTime = ms - totalExecutionTime
          await this.gutterTimer(waitingTime)
        }
      }

    }
    return processedAttempts;

  }

  async processAttempt(test_attempt:ITestAttempt, test_form_cache:Map<number, any>, test_design_cache: Map<number, any>, includeProcessedTaqr: boolean){
    
    const { id: test_attempt_id, test_form_id, twtdar_id, lang, is_closed, closed_on, is_submitted, is_real_time_audit_processed: ta_processed} = test_attempt;    
    
    const currentAttempt: ITestAttempt = await this.app.service('db/read/test-attempts').get(test_attempt_id);
    const taqrRecords = await this.findTestAttemptQuestionResponses(test_attempt_id);
    
    if(!taqrRecords || !taqrRecords.length) return;
    const filteredTaqrRecords = includeProcessedTaqr ? taqrRecords : taqrRecords.filter(r => !r.is_real_time_audit_processed) 
    
    if(!filteredTaqrRecords || filteredTaqrRecords?.length === 0 ) {
      return ; // all the Taqrs are processed for this attempt
    }          
    
    // download and cache test_form
    let test_form = test_form_cache.get(test_form_id);
    if(!test_form) {
      test_form =  await this.app.service('public/student/session').loadAttemptTestFormData(test_attempt);
      test_form_cache.set(+test_form_id, test_form)
    }
    
    
    const tf_record = await this.app.service('db/read/test-forms').get(test_form_id);
    let framework = test_design_cache.get(+tf_record.test_design_id);
    // cache test design
    if(!framework){
      const td_record = await this.app.service('db/read/test-designs').get(tf_record.test_design_id);    
      framework = JSON.parse(td_record.framework || '');
      test_design_cache.set(+td_record.id, framework);
    }
    

    let testLang = lang // based on currentAttempt 
    if(!testLang) testLang = tf_record.lang  // based on tf_record
    if(!testLang) {  // based on twtdar
      if(twtdar_id){
        const twtdar_record = await this.app.service('db/read/test-window-td-alloc-rules').get(+twtdar_id);
        testLang = twtdar_record.lang
      }
    }

    // generate questions Map from test_form
    const questionsMap = this.objectToNumMap(test_form.questionDb);

    // process TAQRs
    for(const currentTaqrRecord of filteredTaqrRecords){
      await this.runAudits(currentAttempt, currentTaqrRecord, { testLang, test_form, framework, questionsMap, taqrRecords});   
      // update status for taqr record
      await this.app
      .service('db/write/test-attempt-question-responses')
      .patch(+currentTaqrRecord.id, { is_real_time_audit_processed: 1 });
    }

    //update test_attempts
    await this.app
      .service('db/write/test-attempts')
      .patch(+test_attempt_id, { is_real_time_audit_processed: 1 });
    
    return test_attempt_id;

  }

  async runAudits(currentAttempt: ITestAttempt, currentTaqrRecord: any, assessmentData: {testLang:string, test_form:any, framework: any, questionsMap: Map<number, any>, taqrRecords: any[]}) {
    
    const {id: test_attempt_id, uid} = currentAttempt;
     const { test_question_id, response_raw } = currentTaqrRecord;     
     const {test_form, framework, testLang, questionsMap, taqrRecords} =  assessmentData;
      
      const data: IAuditRequiredData = {
        response_raw,
        test_attempt_id,
        test_question_id
      }

      let qConfig = questionsMap.get(test_question_id)
      
      if(!qConfig){
        const question =  await this.app.service('db/read/test-questions').get(test_question_id); 
        qConfig = JSON.parse(question.config);

        // question doesn't exist in test_form -  create session-question-audit log
        await this._createAuditLog({
          uid,
          test_attempt_id,
          test_question_id,
          audit_slug: EAuditSlugs.QUESTION_NOT_ASSOCIATED_TO_TEST_FORM,
          audit_description: auditSlugDescription[EAuditSlugs.QUESTION_NOT_ASSOCIATED_TO_TEST_FORM]
        })
      }


      let isEligible = true
      // Determine eligibility to run the audit
      if(this.isQuestionnaire(framework, qConfig, testLang)) isEligible = false;
      if(this.isReadingSelectionPage(qConfig, testLang)) isEligible = false;
  
      if(!isEligible)  return Promise.resolve();

      const formatted_response_record:IFormattedResponse = await this.app
      .service("public/student/extract-item-response")
      .processResponse(response_raw, +test_question_id);

      data.response = formatted_response_record.formatted_response;

      // run audits
      await this.runExpectedAnswerAudit(data, currentAttempt, test_form, framework, qConfig, formatted_response_record, testLang);
      await this.checkForImpossiblePathWay(data, currentAttempt, framework, currentTaqrRecord, taqrRecords);
  }


  async runExpectedAnswerAudit(data:IAuditRequiredData, currentAttempt:ITestAttempt, test_form:any, framework:any, qConfig:any, formatted_response_record:IFormattedResponse, testLang: string) {   
    const {test_attempt_id, test_question_id, response_raw} = data;

    const { formatted_response : currentAnswer, score : currentScore, weight: currentweight } = formatted_response_record
    // get all possible EAs for the questions
    const possibleExpectedAnswers  = <any[]>await this.app
      .service("public/student/extract-item-response")
      .find({ query : { test_question_id } , paginate : false } )

    // const ta_record:ITestAttempt = await this.app.service('db/read/test-attempts').get(test_attempt_id);
    
    const qScoreInfo = await this.app
    .service('public/test-auth/test-question-scoring-info')
    .get(test_question_id, { query: { lang: testLang }});
    
    if(qScoreInfo && qScoreInfo.length && qScoreInfo[0].is_human_scored) return;

    const is_sample = await dbRawReadSingle(this.app, [test_attempt_id], `
      SELECT twtar.is_sample
      FROM test_attempts ta
      JOIN test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id
      where ta.id = ?
    ;`)
   
    const { meta } = qConfig;

    let scorePoint = (meta && meta['SP'] && !isNaN(meta['SP']) ) ? +meta['SP'] : 0;

    let isPossibleEaFound = false;
    let isScoreAligned = false;
    let isScoreOutOfBounds = false;

    possibleExpectedAnswers.forEach((expectedAns: IExpectedResponseData) => {
        const { formatted_response: formatted_ea,  score, weight} = expectedAns

        if(formatted_ea === currentAnswer) {
            if(+currentScore === +score) isScoreAligned = true                
            isPossibleEaFound = true;
        }

      });
      
    if(currentAnswer && !isPossibleEaFound){
        await this._createAuditLog({
            ...data,
            uid: currentAttempt.uid,
            score: currentScore,
            audit_slug : EAuditSlugs.POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED, 
            audit_description: auditSlugDescription[EAuditSlugs.POSSIBLE_EXPECTED_ANSWER_NOT_ALIGNED]
          });
    } 

    if(currentAnswer && !isScoreAligned) {
        await this._createAuditLog({
          ...data, 
          uid: currentAttempt.uid,
          score: currentScore,
          audit_slug : EAuditSlugs.QUESTION_SCORE_NOT_ALIGNED_WITH_EA, 
          audit_description: auditSlugDescription[EAuditSlugs.QUESTION_SCORE_NOT_ALIGNED_WITH_EA], 
        });
    }

    if(!is_sample){
      if(+currentScore > scorePoint ) isScoreOutOfBounds = true;
      
      if(isScoreOutOfBounds) {
          await this._createAuditLog({
            ...data,
            uid: currentAttempt.uid,
            score: currentScore,
            audit_slug : EAuditSlugs.QUESTION_SCORE_OUT_OF_RANGE, 
            audit_description: auditSlugDescription[EAuditSlugs.QUESTION_SCORE_OUT_OF_RANGE], 
          });
      }    
    }
  }


  // for all the TAQRs - could be ran as periodical script
  async verifyQuestionsExistInTestForm(test_window_id : number) { 

    const slug = EAuditSlugs.QUESTION_NOT_ASSOCIATED_TO_TEST_FORM
    const processedTaqrs = this.generateUpsertResObj();
    const auditLogs = this.generateUpsertResObj();

    const taqrPromises: Promise<any>[] = [];
    const logPromises: Promise<any>[] = [];

    const test_attempts = await this.findTestAttemptsForTw(test_window_id);

    // cache test_form
    const test_form_map: Map<number, any>  = new Map()

    for(const ta of test_attempts){
      const test_attempt_id = +ta.id;
      const uid = +ta.uid;

      let test_form = test_form_map.get(+ta.test_form_id)

      if(!test_form) {
        test_form =  await this.app.service('public/student/session').loadAttemptTestFormData(ta);
        test_form_map.set(+ta.test_form_id, test_form)
      }

      // fetch all TAQRs
      const taqr_records = <any[]>await this.app
      .service('db/write/test-attempt-question-responses')
      .find({
        query: {
          $select: ['id', 'test_question_id'],
          test_attempt_id,
          is_invalid: 0,
          is_processed_tf_verify: 0,  
        },
        paginate: false
      });
  
      if(taqr_records && taqr_records.length) {

        const questionsSet = this.objectToNumSet(test_form.questionDb);
  
        for(const taqr of taqr_records){
  
          const test_question_id = +taqr.test_question_id
          if(!questionsSet.has(test_question_id)){
  
            // create log
            const log = this._createAuditLog({
              uid,
              test_attempt_id,
              test_question_id,
              audit_slug: slug,
              audit_description: auditSlugDescription[slug]
            })

            logPromises.push(log);
  
          }  
          // create record in TAQR
          const taqrProcessLog = this.app
            .service('db/write/test-attempt-question-responses')
            .patch(+taqr.id, { is_processed_tf_verify: 1 })
            .then( (res) =>  processedTaqrs.updated.push(+taqr.id))
            .catch( (e) => processedTaqrs.failed.push(+taqr.id))
          taqrPromises.push(taqrProcessLog);

        }
      } 
      
    }

    await Promise.all(logPromises);
    await Promise.all(taqrPromises);
    return processedTaqrs;    
  }

  async checkForImpossiblePathWay(data: IAuditRequiredData, currentAttempt:ITestAttempt, framework:any, currentTaqrRecord:any, taqrRecords: any[]) {
    
    // Only for MSCAT
    const {test_attempt_id, test_question_id, response_raw} = data;

    const { testFormType } = framework;

    if(testFormType !== TestFormConstructionMethod.MSCAT) return;

    const moduleToStage: Map<number, number> = new Map();
    const seenStageWithModule : Map<number, Set<number>> = new Map();

    // generate module to stage map
    framework.panelAssembly.allModules.forEach((m:any) => moduleToStage.set(+m.id, +m.stageNumber));

    const currentModuleId = currentTaqrRecord.module_id; 
    const currentStage: any = moduleToStage.get(+currentModuleId);
   
    // const uniqueModules = [ ...new Set([...taqrRecords.map((m:any) => +m.module_id)])]

    let isImpossiblePanelPathWay = false;
    const impossibleModuleIds: number[][] = [];


    // construct seenStageToModule map
    taqrRecords?.forEach( record => {
      const { module_id} = record      
      const relatedStageNumber = <number>(moduleToStage.get(module_id))
      if(relatedStageNumber){
        if(seenStageWithModule.has(relatedStageNumber)) {
          seenStageWithModule.get(relatedStageNumber)?.add(+module_id);            
        } else {
          seenStageWithModule.set(relatedStageNumber, new Set([+module_id]))
        }
      }
    });

    const totalUniqueModulesAssociatedWithStageFromTaqr = seenStageWithModule.get(currentStage)
    if( totalUniqueModulesAssociatedWithStageFromTaqr ){
      const modules = Array.from(totalUniqueModulesAssociatedWithStageFromTaqr)
      if(modules.length > 1){  // students can only access one module from one stage
        isImpossiblePanelPathWay = true;
        impossibleModuleIds.push(modules);
      }
    }

    

    // uniqueModules.forEach(module_id => {
    //   const relatedStageNumber = moduleToStage.get(module_id)     
      
    //   if(relatedStageNumber) {

    //     if(seenStageWithModule.has(relatedStageNumber)) {
    //       const preExistedModule = seenStageWithModule.get(relatedStageNumber);
    //       if(preExistedModule != null && preExistedModule != +module_id) {
    //         isImpossiblePanelPathWay = true;
    //         impossibleModuleIds.push([preExistedModule, +module_id]);
    //       } 
    //     } else {
    //       seenStageWithModule.set(+relatedStageNumber, +module_id)
    //     }

    //   }
    // });

    if(isImpossiblePanelPathWay && impossibleModuleIds.length) {
      const slug = EAuditSlugs.MSCAT_IMPOSSIBLE_PANEL_PATHWAY
      await this._createAuditLog({
        ...data,
        uid: currentAttempt.uid,
        audit_slug: slug,
        audit_description: auditSlugDescription[slug] + ` Impossible Module ids: ${JSON.stringify(impossibleModuleIds)}`
      })
    }
    
  }

  async findTestAttemptsForTw(test_window_id: number , limit?: number, includeProcessedAttempts?: boolean , StudentOEN?: number) {
    
    const db:Knex = await this.app.get('knexClientReadReporting');

    let query = db('test_windows as tw')
    .join('test_sessions as ts', 'ts.test_window_id', 'tw.id')
    .join('test_attempts as ta', 'ta.test_session_id', 'ts.id')
    .join('test_forms as tf', 'tf.id', 'ta.test_form_id')
    .where('tw.id', test_window_id)
    .andWhereNot('ta.is_invalid', 1)
    .andWhereNot('ta.started_on', null);

    if(!includeProcessedAttempts){
      query.andWhereNot('ta.is_real_time_audit_processed', 1)
    } 

    if(StudentOEN){
      const studentUid = await db('user_metas')
      .where('key', 'StudentOEN')
      .andWhere('key_namespace', 'eqao_sdc')
      .andWhere('value', StudentOEN)
      .select("uid");

      if(studentUid && studentUid.length && studentUid[0].uid){
        query.andWhere('ta.uid', studentUid[0].uid);
      } else {
        // no uid found
        return [];
      }

    }

    if(limit) query.limit(limit)
    
    const test_attempts = <ITestAttempt[]>await query.select(['ta.*', 'tf.test_design_id']);

    return test_attempts;

  }

  async findTestAttemptQuestionResponses(test_attempt_id: number, includeProcessedTaqr?: boolean) {
    const taqr_records = <any[]>await this.app
      .service('db/write/test-attempt-question-responses')
      .find({
        query: {
          $select: ['id', 'test_question_id', 'response_raw', 'score', 'weight', 'section_id', 'module_id', 'is_real_time_audit_processed'],
          test_attempt_id,
          is_invalid: 0,  
        },
        paginate: false
      });

      return taqr_records;
  }

  isQuestionnaire = (framework: any, qConfig: any, lang: string) => {
    const isQuestionnaireAssessment = framework.isQuestionnaireAssessment;
    const isCurrQuestionQuestionnaire = lang === 'en' ? qConfig.isQuestionnaire : qConfig.langLink?.isQuestionnaire
    if(isQuestionnaireAssessment || isCurrQuestionQuestionnaire) return true;
    return false;
  }

  isReadingSelectionPage = (qConfig: any, lang: string) => lang === 'en' ? qConfig.isReadingSelectionPage : qConfig.langLink?.isReadingSelectionPage;

  // util
  private objectToNumMap(obj: { [key: string | number]: any }) {
    const map = new Map();
    Object.keys(obj).forEach((key:string | number) => {
      map.set(+key, obj[key]);
    })
    return map;
  }
  
  private objectToNumSet(obj: { [key: string | number]: any }) {
    const set: Set<number> = new Set();
    Object.keys(obj).forEach((key:string | number) => {
      set.add(+key)
    })
    return set;
  }

  private generateUpsertResObj = () => {
    return <UpsertResult>  {
      created: [],
      failed: [],
      updated: [],
    }  
  }

  async gutterTimer(rate: number){
    return new Promise((resolve, _) => {
      setTimeout(resolve, rate)
    })
  }




}
