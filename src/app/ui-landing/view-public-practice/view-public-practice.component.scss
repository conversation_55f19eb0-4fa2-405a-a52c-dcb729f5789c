@import "../../../styles/page-types/custom-login.scss";
@import '../../../styles/partials/_modal.scss';


.public-practice-title {
    color: #fff;
    font-size:1.4em; 
    .public-practice-title-big {
        margin-bottom: 0.5em;
        font-size:1.4em; 
        font-weight:bold
    }
    margin-bottom: 2em;
}

.public-practice-links {
    background-color:#fff;
    border-radius: 1em;
    font-size: 1.2em;
    padding: 1em 2em;
    line-height: 1.3em;
    padding-bottom: 2em;
}

.public-practice-links-row {
    display: flex;
    justify-content: space-between;
    margin-left: 1em;
    margin-right: 1em;
    margin-bottom: 1.3em;
}

.flex {
    display: flex;
    flex-direction: row-reverse; // temp until we figure out ordering to force Grad 9 to be the first
    flex-wrap: wrap;
    justify-content: center;
    align-content: center;
}

.card {
    @extend .flex;
    flex-direction: column;
    margin: 1em;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.3);
    width: 15em;
    height: 15em;
    transition: background-color 500ms ease-out 30ms;
    cursor: pointer;

    &:hover{
        background-color: #e8e8e8;
    }

    .text {
        align-self: center;
        margin-left: 1em;
        margin-right: 1em;
    }

    .title {
        @extend .text;
        margin-top: 1em;
        font-size: 1.1em;
        font-weight: bold;
        color: #2c82b0;    
    }

    .sub-title {
        @extend .text;
        margin-bottom: 1em;
    }
}

.back-button {
    border: none;
    background-color: transparent;
    color: #4a4a4a;
    cursor: pointer;
    &:hover {
        color: black;
        background-color: #fefefe;
        transition: background-color 500ms ease-out 30ms;
    }
}
