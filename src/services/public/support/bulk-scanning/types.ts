export const dateTimeFiltercolumns = new Set([
  'created_on', 'started_on',
])

export const textFilterColumns = new Set([
  'id',
  , 'test_window_id'
  , 'schl_name'
  , 'class_name'
  , 'uploader'
])

export enum DateFilters {
  equals = "equals",
  notEqual = "notEqual",
  greaterThan = "greaterThan",
  lessThan = "lessThan",
  inRange = "inRange",
}

export enum TextFilters {
  contains = "contains",
  notContains = "notContains",
  equals = "equals",
  notEqual = "notEqual",
  startsWith = "startsWith",
  endsWith = "endsWith"
}