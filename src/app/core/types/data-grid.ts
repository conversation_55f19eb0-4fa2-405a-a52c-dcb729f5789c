export enum EColDataType {
    SLUG = 'SLUG',
    SLUG_LINK = 'SLUG_LINK',
    STRING = 'STRING',
    COLOR = 'COLOR',
    DATETIME = 'DATETIME',
    JSON = 'JSON',
    ID = 'ID',
    ID_LINK = 'ID_LINK',
    BOOLNUM = 'BOOLNUM',
    NUMBER = 'NUMBER',
}

export interface IColDef { 
    caption: string,
    field: string,
    dataType: EColDataType,
    isNotEditable?: boolean,
    isCreationKey?: boolean,
}
