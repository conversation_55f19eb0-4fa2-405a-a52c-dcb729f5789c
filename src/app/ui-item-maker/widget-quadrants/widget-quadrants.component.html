<div class="pre-table-strip">
  <div>
    <button 
      [disabled]="isReadOnly()"
      class="button is-small has-icon" 
      (click)="quadrantCtrl.refreshQuadrantItems()"
    >
      <span class="icon"><i class="fa fa-refresh" aria-hidden="true"></i> </span>
      <span>Refresh Quadrants</span>
    </button>
    <button 
      [disabled]="isReadOnly()"
      class="button is-small has-icon" 
      (click)="panelCtrl.exportQuadrantsTable()"
    >
      <span class="icon"><i class="fa fa-table" aria-hidden="true"></i> </span>
      <span>Export Quadrant Table</span>
    </button>
    <span>| <input type="checkbox" [(ngModel)]="frameworkCtrl.asmtFmrk.isAutoTestletMode"> Auto Testlets?  |</span>
  </div>
</div>
<table *ngIf="frameworkCtrl.asmtFmrk.quadrantItems" style="margin-bottom:2em;">
  <tr>
    <th>Quadrant ID</th>
    <th>Description</th>
    <th>Item Per Testlet</th>
    <th>Items Mapped</th>
    <th>Review Questions</th>
    <th>Generate Testlets</th>
    <th>Number of Testlets</th>
    <th>Items Used</th>
    <th>Q.U.%</th>
    <th>Estimated Per-item Exposure</th>
    <th>Duplication</th>
  </tr>
  <tr *ngFor="let quadrant of frameworkCtrl.asmtFmrk.quadrantItems">
      <td>{{quadrant.id}}</td>
      <td>{{quadrant.description}}</td>
      <td>{{quadrant.minQRequired}}</td>
      <td>{{quadrant.questions.length}}</td>
      <td> 
        <div *ngIf="quadrant.questions.length" style="white-space: nowrap;">
              <button 
              [disabled]="isReadOnly()"
              (click)="quadrantCtrl.activateQuadrantQuestions(quadrant.id)" 
              class="button is-small has-icon" 
              [class.is-info]="quadrantCtrl.activeQuadrant && quadrantCtrl.activeQuadrant.id===quadrant.id"
              >
              <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
              <span>List</span>
            </button> 
            <button 
            [disabled]="isReadOnly()"
            (click)="quadrantCtrl.printQuadrantQuestions(quadrant.id)" 
            class="button is-small has-icon" 
            >
            <span class="icon"><i class="fa fa-print" aria-hidden="true"></i> </span>
            <span>Print</span>
          </button> 
        </div>
  </td>
  <td>
    <ng-container *ngIf="quadrant.questions.length " >
      <button 
        *ngIf="frameworkCtrl.asmtFmrk.isAutoTestletMode"
        [disabled]="isReadOnly()"
        (click)="testletCtrl.generateTestlets(quadrant.id)" 
        class="button is-small is-success" 
      >
        Generate Testlets
      </button> 
      <button 
        *ngIf="!frameworkCtrl.asmtFmrk.isAutoTestletMode"
        [disabled]="isReadOnly()"
        (click)="customTestletStart(quadrant.id)" 
        class="button is-small is-success" 
      >
        Create Custom Testlet
      </button> 
    </ng-container> 
  </td>
  <td>
    {{quadrant.numTestlets}}
    <a [class.is-disabled]="isReadOnly()" class="close" (click)="testletCtrl.removeAllTestletFromQuadrant(quadrant.id)">
      <i class="fa fa-times" aria-hidden="true"></i>
    </a>
  </td>
  <td>{{quadrant.numItemsUsed}}</td>
  <td>{{quadrantCtrl.computePerItemsUsedQuad(quadrant)}}</td>
  <td style="white-space: nowrap;">
    <span *ngIf="quadrant.exposureMin || quadrant.exposureMax">
      {{quadrant.exposureMin}} to {{quadrant.exposureMax}}
    </span>
  </td>
  <td style="color:#f00;">
    <div *ngFor="let crossLink of quadrant.crossLinkedItems">
      <strong>{{crossLink.label}}</strong> : 
      <span *ngFor="let quadrantId of quadrantCtrl.quadrantsCrossLinks">{{quadrantId}} ;</span>
    </div>
    <div *ngIf="!quadrant.crossLinkedItems || quadrant.crossLinkedItems.length == 0" style="color:#ccc;">
      None
    </div>
  </td>
  </tr>
</table>
<div *ngIf="itemBankCtrl.isCustomTestlet()">
  <h4>Custom Testlet</h4>
  <p>Select items from the quadrant below to create your testlet (use the "+" button to add the items like you would with a linear test design).</p>
  <div style="margin:1em 0em; padding: 1em; border:1px solid #ccc; border-radius: 1em;     max-width: 32em;">
    <div 
      cdkDropList  
      [cdkDropListDisabled]="isReadOnly()"
      [cdkDropListData]="getCustomTestletItems()"
    >    
      <div 
        *ngFor="let item of getCustomTestletItems()" 
        cdkDrag 
        class="section-question-row"
      >
        <!-- [class.is-question-nav-open]="itemBankCtrl.isCurrentQuestionNavigationOpen(questionIdentifier)" -->
        <button [disabled]="isReadOnly()" [class.no-pointer-events]="isReadOnly()" class="stack-edit-element-header" cdkDragHandle>
          <div class="icon"><i class="fa fa-bars" aria-hidden="true"></i></div>
        </button>
        <div class="question-label">
          {{item.label}}
        </div>
        <button [disabled]="isReadOnly()" (click)="itemBankCtrl.removeItemFromCustomTestlet(item)"  class="button is-danger">
          <i class="fa fa-minus" aria-hidden="true"></i>
        </button>
      </div>
    </div>
  </div>
  <div>
    <button class="button is-small" (click)="customTestletSave()">Create Testlet</button>
    <button class="button is-small is-danger" (click)="customTestletCancel()">Cancel</button>
  </div>
  <hr>
</div>