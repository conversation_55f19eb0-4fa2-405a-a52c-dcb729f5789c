<div class="header-container" *ngIf="isThisFSA">
    <bc-header-layout 
        [title]="'sa_technical_readiness'"
        [imgSrc]="'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/Group_15__1_/1666112755552/Group_15__1_.png'"
        (school)="onSelectedSchoolChange($event)" 
        (testWindow)="onSelectedTestWindowChange($event)"
    ></bc-header-layout>
</div>

<div class="content">
    <div *ngIf="isThisFSA" class="box">
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">
                <tra slug="technicalGuide_adminHeader"></tra>
            </h2>
        </div>
        <div class="main-content">
            <ul class="techGuideLinks">
                <li>
                    <h3>
                        <a target="_blank" [href]="lang.tra('admin_technical_tips_link')">
                            <tra slug="admin_technical_tips"></tra>
                        </a>
                    </h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('admin_fsa_admin_manual_link')">
                            <tra slug="admin_fsa_admin_manual"></tra>
                        </a></h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('admin_text_reader_tips_FSA_link')">
                            <tra slug="admin_text_reader_tips"></tra>
                        </a></h3>
                </li>
            </ul>
        </div>
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">
                <tra slug="technicalGuide_technicianHeader"></tra>
            </h2>
        </div>
        <div class="main-content">
            <ul class="techGuideLinks">
                <li>
                    <h3><a target="_blank" [href]="lang.tra('admin_technical_tips_link')">
                            <tra slug="tech_tech_tips"></tra>
                        </a></h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('admin_fsa_admin_manual_link')">
                            <tra slug="tech_admin_manual"></tra>
                        </a></h3>
                </li>
                <li>
                    <h3><a (click)="openPreAdminTest()">
                            <tra slug="tech_preadmin_test"></tra>
                        </a></h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('admin_text_reader_tips_FSA_link')">
                            <tra slug="tech_text_reader_tips"></tra>
                        </a></h3>
                </li>
            </ul>
        </div>
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">
                <tra slug="technicalGuide_securityHeader"></tra>
            </h2>
        </div>
        <div class="main-content">
            <tra style="padding: 15px" slug="fsa_txt_security_info"></tra>
            <ul class="techGuideLinks">
                <li>
                    <h3><a target="_blank" [href]="lang.tra('security_policy_link')">
                            <tra slug="grad_title_pushing_kiosk_policy"></tra>
                        </a></h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('security_seb_link')">
                            <tra slug="grad_title_seb"></tra>
                        </a></h3>
                </li>
            </ul>
        </div>
    </div>
    <div *ngIf="isThisGrad" class="box">
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">
                <tra slug="technicalGuide_preAdminHeader"></tra>
            </h2>
        </div>
        <div class="main-content">
            <ul class="techGuideLinks">
                <li>
                    <h3><a target="_blank" [href]="lang.tra('grad_preadmin_test_ew_link')">
                            <tra whitelabel="grad_preadmin_test_ew" slug="grad_preadmin_test_ew"></tra>
                        </a></h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('grad_preadmin_test_fo_link')">
                            <tra whitelabel="grad_preadmin_test_fo" slug="grad_preadmin_test_fo"></tra>
                        </a></h3>
                </li>
            </ul>
        </div>
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">
                <tra slug="technicalGuide_adminHeader"></tra>
            </h2>
        </div>
        <div class="main-content">
            <ul class="techGuideLinks">
                <li>
                    <h3><a target="_blank" [href]="lang.tra('grad_user_guide_link')">
                            <tra whitelabel="grad_user_guide" slug="grad_user_guide"></tra>
                        </a></h3>
                </li>
            </ul>
        </div>
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">
                <tra slug="technicalGuide_technicianHeader"></tra>
            </h2>
        </div>
        <div class="main-content">
            <ul class="techGuideLinks">
                <li>
                    <h3><a target="_blank" [href]="lang.tra('grad_admin_technical_tips_link')">
                            <tra whitelabel="tech_tech_tips" slug="tech_tech_tips"></tra>
                        </a>
                    </h3>
    
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('caching_prevention_tip_link')">
                            <tra whitelabel="caching_prevention_tip" slug="caching_prevention_tip"></tra>
                        </a>
                    </h3>
    
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('security_policy_link')">
                            <tra whitelabel="grad_title_pushing_kiosk_policy" slug="grad_title_pushing_kiosk_policy"></tra>
                        </a>
                    </h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('guided_access_ipda_link')">
                            <tra whitelabel="guided_access_ipda" slug="guided_access_ipda"></tra>
                        </a>
                    </h3>
                    <!-- Once we have translation we can add an isFrench Condition and display the wanted text with proper styles -->
                    <p style="font-weight: bold;">Technical Information for iPad</p>
                    <p>
                        iOS version: 9.3+ - Recommended Browser: Safari
                    </p>
                    <p *ngIf="false">
                        Note: Pop-up blocker must be turned off in Safari. Go to "Settings --> Safari --> Block Pop-ups" and
                        turn off.
                    </p>
                </li>
            </ul>
        </div>
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">
                <tra slug="technicalGuide_secContHeader"></tra>
            </h2>
        </div>
        <div class="main-content">
            <ul class="techGuideLinks">
                <li>
                    <h3><a target="_blank" [href]="lang.tra('security_seb_link')">
                            <tra slug="grad_title_seb"></tra>
                        </a></h3>
                </li>
            </ul>
        </div>
        <div class="assessment-level-intro guide-intro">
            <h2 style="float: left">Adaptations Setup Information
            </h2>
        </div>
        <div class="main-content">
            <ul class="techGuideLinks">
                <li>
                    <h3><a target="_blank" [href]="lang.tra('admin_text_reader_tips_GRAD_link')">
                            <tra slug="tech_text_reader"></tra>
                        </a></h3>
                </li>
                <li>
                    <h3><a target="_blank" [href]="lang.tra('text_speech_setup_link')">
                            <tra slug="text_speech_setup"></tra>
                        </a></h3>
                </li>
            </ul>
        </div>
    </div>
</div>