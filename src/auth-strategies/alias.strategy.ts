import { ServiceAddons, Params,Paginated, Query } from '@feathersjs/feathers';
import { AuthenticationBaseStrategy } from '@feathersjs/authentication';
import { Application } from '../declarations';
import { Errors } from '../errors/general';
import { dbDateOffsetHours } from '../util/db-dates';
import { Knex } from 'knex';
import { dbRawRead, dbRawReadSingle } from '../util/db-raw';
import logger from '../logger';

interface Data {
  strategy:string,
  id:number,
  secret:string,
}

export class LoginAliasStrategy extends AuthenticationBaseStrategy {

    app: Application;

    constructor ( app: Application) {
      super();
      this.app = app;
    }
    async getIdentity(data: Data){
      const {id, secret} = data;
      const timeLimitHours = -0.5;

      /*
       * The secret is a hex key generated when register with login service function by generateSecretCode(42)
       * which is used to authenticate user with loginAs routeParams, using loginAlias Method at auth service
       *
       * A route parameter string is passed to the client at its loginAs function, with an 'x' splitting id and
       * secret, if the parameter is not correct or without an x, secret will be undefined.
       *
       * Once the client receive this error or 500 error, an "Invalid login alias" alert will be prompted to users
       * the users wouldn't be redirect since userInfo is not updated.
       *
       */

      if(!secret) {
        throw new Errors.BadRequest('MISSING_SECRET_KEY')
      }

      const loginAsRecords = await dbRawRead(this.app, [id, secret, timeLimitHours],`
        select *
        from auth_as
        where id = ?
          and secret = ?
          and created_on > date_add(NOW(), INTERVAL ? hour)
      ;`)
      if (loginAsRecords.length === 0){
          throw new Error();
      }
      const uid = loginAsRecords[0].target_uid;
      const userInfoService = this.app.service('public/auth/user-info-core');
      const { accountType, accountTypes, firstName, lastName, isTotpUser } = await userInfoService.retrieveUserInfo(uid)
      return {
        id,
        uid,
        accountType,
        accountTypes,
        firstName: firstName+' (LOGIN AS)',
        lastName,
        isTotpUser,
      }
    }
    async authenticate (data:Data) {
      logger.silly('login key auth. method entered');
      const user = await this.getIdentity(data)
      return {
        authentication: { strategy: this.name },
        user
      };
    }
  }
