import { ISelectionData } from "src/app/ui-testrunner/element-render-template/model";

export const templateSelectionOptionsPreset: {id: string, caption: string, options: ISelectionData['options']}[] = [
  {
    id: 'text_alignment',
    caption: 'Text Alignment',
    options: [
      {id:'left', icon:'fa-align-left'},
      {id:'center', icon:'fa-align-center'},
      {id:'right', icon:'fa-align-right'},
      {id:'justify', icon:'fa-align-justify'}
    ]
  }
]