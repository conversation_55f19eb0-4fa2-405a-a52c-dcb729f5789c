import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { dbRawReadSingle } from '../../../../util/db-raw';

interface Data {
  password?:string
  response_raw?:string
  isValid?:boolean,
}

interface ServiceOptions {}

export class Debug implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const {password} = data;
    return this.validatePassword(password || '');
    throw new Errors.BadRequest();
  }

  private async validatePassword(password:string){
    const record = await dbRawReadSingle(this.app, [], `select value from sys_constants_string scs where scs.key = 'ASMT_RUNNER_DEBUG' limit 1;`)
    if (password?.trim() === record.value.trim()){
      return { isValid: true }
    }
    else {
      throw new Errors.Forbidden();
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const test_question_id = id;
    const {response_raw, password} = data;
    await this.validatePassword(password || '');
    if (test_question_id && response_raw){
      const {
        formatted_response,
        score,
        weight,
      } = await this.app
        .service('public/student/extract-item-response')
        .processResponse(
          response_raw,
          +test_question_id,
        );
      logger.debug('DEBUG student response', {formatted_response, score, weight})
      return <any> {
        formatted_response,
        score,
        weight
      }
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
