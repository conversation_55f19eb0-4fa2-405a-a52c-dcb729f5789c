.page-break {
  page-break-after: always;
}
// .page-break:first-child {
// }
.page-break:last-child {
  page-break-after: avoid;
}
.grey-text{
  color: rgba(160, 159, 159, 0.986);
  padding:0.5em;
}
.ind-student-report{
  background:rgba(241, 238, 238, 0.959);
  padding:1em;
  min-width:50rem;
  min-height: 90vh;
  margin-bottom: 4em;
  background-color: #fff;
  box-shadow: 0.5em 2em 3em rgba(0,0,0,0.2);
  @media print {
      box-shadow: none;
  }
  color: black;
  
  .ind-student-report-header {
      border-top: 1px solid #000;
      border-bottom: 1px solid #000;
      margin-top: 0.5em;
      margin-bottom: 1em;
      padding: 1em;
      text-align: center;
  }
  .ind-student-report-section-container {
      padding: 1em;
      padding-left: 2em;
      border-bottom: 1px solid #ccc;
      table tr  {
          td,th {
              border: none;
          }
      }
      .ind-student-report-section-header {
          font-size: 1.4em;
          margin-bottom:0.5em;
          margin-left: 0.5em;
          text-transform: uppercase;
      }
  }

}
.top-level{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 50rem;
  margin:1em;
}

.school-year-select {
  margin-left:1em;
  background-color: white;
  border-color: #dbdbdb;
  border-width: 1px;
  border-radius: 4px;
  color: #363636;
  justify-content: center;
  padding-bottom: calc(0.5em - 1px);
  padding-left: 0.8em;
  padding-right: 0.8em;
  padding-top: calc(0.5em - 1px);
  text-align: left;
  outline: none;
  font-size: 1rem;
  height: 2.5em;
}

.btn_export_report {
  font-weight: 600; 
  margin-left: 1em; 
  height: 3em; 
  line-height: 3em; 
  text-align: center;
}

.v2_table {
  tr  {
      td,th {
          border: none;
          padding-bottom: 0;
          padding-top: 0;
      }

      th {
          width: 25%;
          font-weight: 500;
          padding-left: 0;
      }
  }
}

.ind-student-report-v1{
  width: 8.5in;
  padding: 0em;
  height: 11.4in;
  @media print {
      height: 11in;
      margin-bottom: 0em;
  }
}

// .report-version-1 * {      
//     font: 400 14px/20px Arial;
// }


.report-version-1 {      
  padding-top:  0.5rem;
  padding-bottom:  0.5rem;
  padding-left:  1.2rem;
  padding-right: 2.8rem;
  width: 8.5in;
  height: 11in;
  position:relative;

  @media print {
      @page {
          margin: 0in;
      } 
  }

  .image-container {
      flex: 1; 
      max-width: 11em; 
      text-align: left;
      // @media print {
      //     max-width: 7em; 
      // }
      img {
          max-width: 6rem; 
          display: block;
          // @media print {
          //     max-width: 5rem; 
          // }
      }
  }
  .header-container{
      margin-top: 1.5em; 
      margin-bottom: 2.5em;
      .report-header{
          width: 50%;
          line-height:1.2em;
      }
      .report-header-1{
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: 0;
      }
      .report-header-2{
          margin-top: 0;
          margin-bottom: 0;
          padding-top: 0;
          border-top: 0;
      }
      // @media print {
      //     margin-top: 0.5em; 
      //     margin-bottom: 2em;
      // }
  }
  .report-details {
      margin-top: 0.2em; 
      .student-full-name{
          font: 400 1.2em Arial;
          text-transform: uppercase;
          margin-bottom: 0.1em;
      }
      table {
          tr  {
              td,th {
                  border: none;
                  padding-bottom: 0.25em;
                  padding-top: 0.25em;
              }
      
              th {
                  font-weight: 400;
                  padding-right: 0em;
                  padding-left: 0;
              }
          }
      }
  }
  .report-details-en {
      table {
          tr  {
              th {
                  width: 25%;
              }
          }
      }
  }

  .report-details-fr {
      table {
          tr  {
              th {
                  width: 40%;
              }
          }
      }
  }   

  .report-student-result {
      margin-top: 2em; 
      margin-bottom: 0.7em; 
      width: 50%; 
      .result-title {
          font: 400 14px/20px Arial;
          margin-bottom: 0.5em;
      }
      & > p {
          border-bottom: 1px solid #000; 
          padding-bottom: 1em;
      }
  }

  .result-explaination {
      margin-top: 3em; 
      margin-bottom: 2em; 
      // @media print {
      //     margin-top: 2em; 
      //     margin-bottom: 0.4em; 
      // }
      .result-explaination-caption {
          margin-bottom: 0em;
          // text-align: justify;
          // text-justify: inter-word;
          //line-height:  2.5em;
      }
      .result-1-explaination-body {
          display: flex;
          min-height: 20rem;
          .reading-container, .writing-container {
              flex: 1; 
              max-width: 50%; 
              flex-wrap: nowrap;
              // .container-header {
              //     font: 700 14px/20px Arial;
              // }
              .container-body {
                  margin-top: 0.5em;
                  padding-right: 1.5em;
              }
          }
      }
      .result-2-explaination-body {
          display: flex;
          min-height: 23rem;
          .reading-container, .writing-container {
              flex: 1; 
              max-width: 50%; 
              flex-wrap: nowrap;
              // .container-header {
              //     font: 700 14px/20px Arial;
              // }
              .container-body {
                  margin-top: 0.5em;
                  padding-right: 1.5em;
              }
          }
      }
  }

  .result-explaination-1-fr {
      margin-top: 2em; 
  }

  .result-explaination-1-en {
      margin-top: 4.2em; 
  }
  
  .result-explaination-2 {
      margin-top: 6em; 
      // @media print {
      //     margin-top: 10em; 
      // }
  }
  
  .report-footer {
      position:absolute;
      bottom:0.5em;
      border-top: 0.05em solid #000;
      margin-right: 2rem; 
      justify-content: center;
      padding-top: 0.9rem;
      line-height: 1.2;
      text-align: justify;
      text-justify: inter-word;
  }
  .report-unsuccessful-details {
      margin-top: 0.9rem;
      // @media print {
      //     margin-top: 0.5rem;
      // }
      tr  {
          border-top: 1px solid black;
          border-bottom: 1px solid black;
          td,th {
              border: none;
              padding-bottom: 0.23rem;
              padding-top: 0.23rem;
              padding-left: 0 !important;
              // @media print {
              //     padding-bottom: 0;
              //     padding-top: 0;
              // }
          }
  
          th {
              font-weight: 500;
              padding-left: 0 !important;
          }
      }
  }

  .report-unsuccessful-details-en {
      width: 20em;
      tr {
          th {
              width: 70%;
          }
      }
  }

  .report-unsuccessful-details-fr {
      width: 30em;
      tr {
          th {
              width: 74%;
          }
      }
  }  
  
  & * {
      font: 400 14px/20px Arial;
  }
  &.report-details-fr-alt * {
      font: 400 13px/20px Arial;
      line-height: 1.4;
  }
  &.report-details-fr-alt.firefox * {
      line-height: 1.3;
  }
  &.report-details-fr-alt.firefox, &.report-details-fr-alt {
      .report-unsuccessful-details-fr-alt {
          tr {
              th {
                  width: 80%;
              }
          }
      }
  }
}

.report-version-1-non-page1{
  padding-top:  2.7rem;
  padding-bottom:  0rem;
  height: 11.2in;
  .report-footer {
      bottom:0em;
  }
}

.report-version-1-firefox-page1{
  height: 11.3in;
}

.report-version-1-firefox{
  //padding-left:  1.2rem;
  //padding-right: 2.8rem;
  padding-left:  1.25rem;
  padding-right: 2.75rem;
}


.pj-ind-student-report-en{
  width: 8.5in;
  padding: 0em;
  height: 12.5in;
}

.pj-ind-student-report-fr{
  width: 8.5in;
  padding: 0em;
  height: 13.1in;
}

.pj-ind-student-report-en-firefox{
  width: 8.5in;
  padding: 0em;
  height: 13in;
}

.pj-ind-student-report-fr-firefox{
  width: 8.5in;
  padding: 0em;
  height: 13.5in;
}

.report-version-pj {      
  padding: 0.5rem 1.2rem;
  position:relative;

  .pj-image-container {
      flex: 1; 
      max-width: 11em; 
      text-align: left;
      img {
          max-width: 6rem; 
          display: block;
      }
  }

  .pj-header-container{
      margin-top: 1.5em; 
      margin-bottom: 2.5em;
      .report-header-pj{
          line-height:1.2em;
          .report-header-pj-font{
              font: 700 18pt Arial;
          }
      }
      .report-header-pj-title-1{
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: 0;
      }
      .report-header-pj-title-2{
          margin-top: 0;
          margin-bottom: 0;
          padding-top: 0;
          border-top: 0;
      }
  }

  .pj-report-details {
      margin-top: 0.2em; 
      .pj-student-full-name{
          font: 400 1.2em Arial;
          text-transform: uppercase;
          margin-bottom: 0.25em;
      }
      table {
          tr  {
              td,th {
                  border: none;
                  padding-bottom: 0.25em;
                  padding-top: 0.25em;
              }
      
              th {
                  &.pj-report-details-en {
                      width: 11rem !important; 
                  }
                  &.pj-report-details-fr {
                      width: 18.5rem !important; 
                  }
                  font-weight: 400;
                  padding-right: 0em;
                  padding-left: 0;
              }

              td {
                  font-size: 0.92em;
              }
          }
      }
  }

  .pj-report-student-result {
      margin-top: 2em; 
      margin-bottom: 0.7em; 
      .pj-result-title {
          font: 400 1.2em Arial;
      }
      .pj-result-content-1 {
          font: 400 1em Arial;
      }
      .pj-result-content-2 {
          font: 400 0.92em Arial;
          line-height: 1.3em;
      }
      table {
          border-top: 0.5px solid #000;
          margin-bottom: 1em;
          margin-top: 0.5em;
          tr {
              display:flex; 
              flex-direction:row; 
              justify-content:center;
              border-bottom: 0.5px solid #000;
              th,td {
                  display:flex; 
                  flex-direction:column; 
                  justify-content:center;
                  
                  &.pj-report-student-result-wider-col{     
                      flex-grow:2; 
                      min-width: 30%;
                      padding: 0;
                  }
                  &.pj-report-student-result-reg-col{
                      text-align:center;  
                      flex-grow:1; 
                      min-width: 14%;
                      padding: 0;
                      border-right: none;
                      .pj-report-student-result-reg-col-font{
                          font-size: 0.7em;
                          line-height: 0em;
                      }
                  }
              }
              td{
                  border-left: 0.5px dotted #363636;
              }
          }
      }
  }

  .pj-result-explaination {
      margin-bottom: 2em; 
      border-top: 0.05em solid #000;
      .pj-result-explaination-caption {
          margin-bottom: 0em;
          margin-top: 1.2em;
          .pj-result-explaination-caption-font{
              font: 550 1em Arial;
          }
      }
      .pj-result-explaination-body {
          display: flex;
          margin-top: 0.5em;
          .pj-reading-container, .pj-writing-container, .pj-math-container {
              flex: 1; 
              max-width: 33.33%; 
              flex-wrap: nowrap;
              .pj-container-header-font{
                  font: 700 14px/20px Arial;
              }
              .pj-container-body {
                  margin-top: 0.5em;
                  padding-right: 1.5em;
                  .pj-container-body-font{
                      font: 400 9.5pt Arial; 
                      line-height: 1.3em;
                  }
              }
          }
      }
      .pj-result-explaination-note {
          .pj-result-explaination-note-font{
              font: 300 8.5pt Arial; 
          }
      }
      .pj-result-detail-info{
          margin-top: 1em;
          .pj-result-detail-info-font{
              font: 400 9.5pt Arial; 
              line-height: 1.3em;
          }
      }
  }

  .pj-report-footer {
      // position:absolute;
      bottom:0.5em;
      border-top: 0.05em solid #000;
      padding-top: 0.9rem;
      line-height: 1.2;
      text-align: justify;
      text-justify: inter-word;
      .pj-footer-font {
          font: 300 8.5pt Arial;
      }
  }
  
  & * {
      font: 400 14px/20px Arial;
  }
  &.report-details-fr-alt * {
      font: 400 13px/20px Arial;
      line-height: 1.4;
  }
  &.report-details-fr-alt.firefox * {
      line-height: 1.3;
  }
  &.report-details-fr-alt.firefox, &.report-details-fr-alt {
      .report-unsuccessful-details-fr-alt {
          tr {
              th {
                  width: 80%;
              }
          }
      }
  }
}