import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { IBoardItInviteEmail } from '../../../dist-admin/district/district.class';
import { dbRawRead, dbRawWrite } from '../../../../../util/db-raw';
import { renderInvitationCode } from '../../../../../util/secret-codes';
import { dbDateOffsetDays } from '../../../../../util/db-dates';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class AccountInvite implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: any, params?: Params): Promise<Data> {
    if (params){
      const created_by_uid = await currentUid(this.app, params);
      const {
        sd_name,
        sd_group_id,
        lang,
        first_name,
        last_name,
        offsetDays,
        emailLinkDomain,
        isAutoEmail,
      } = <any> data;

      let email = (<any> data).email;
      if (!email){
        throw new Errors.BadRequest('NEED_EMAIL');
      }
      email = email.trim();

      let role_type = data.role_type
      if(!role_type){
        role_type = 'schl_dist_it_admin'
      }

      let existingUid;
      const authRecords = <Paginated<any>> await this.app.service('db/read/auths').find({query: {email}});
      const authRecord = authRecords.data[0];
      if (authRecord){
        existingUid = authRecord.uid;
      }
      else {
        const userRecords = <Paginated<any>> await this.app.service('db/read/users').find({query: {contact_email: email}});
        const userRecord = userRecords.data[0];
        if (userRecord){
          existingUid = userRecord.id;
        }
      }

      if (existingUid){
        // ensure invitation
        const invitation = <Paginated<any>> await this.app
          .service('db/read/u-invites')
          .find({query: {uid: existingUid}})
        if (!invitation){
          const invite = await this.app
            .service('auth/invitation')
            .createInvitation({
              uid: existingUid,
              created_by_uid,
              isAutoEmail: false,
              contact_email: email,
              invit_meta:'{}', 
              offsetDays: 3,
              isRevoked: true,
            });
        }
        // assign role
        await this.app
          .service('public/dist-admin/district')
          .createDistAdminUserRoles(
            existingUid, 
            sd_group_id, 
            created_by_uid,
            role_type
          )
        return this.getUserRecord(existingUid);
      }
      else{
        const userAccount = {
          first_name, 
          last_name, 
          email
        }
        // const invite = <any> await this.app
        //       .service('public/dist-admin/district')
        //       .createDistAdminAccount(sd_group_id, created_by_uid, lang, 'eqao-api.vretta.com', userAccount, sd_name, offsetDays);

        const invite = <any> await this.app
          .service('public/dist-admin/district')
          .createDistAdminAccount(sd_group_id, created_by_uid, lang, 'eqao-api.vretta.com', userAccount, sd_name, offsetDays, offsetDays, role_type);      

        // return this.getUserRecord(invite.uid);
        return this.getUserRecord(invite.uid, role_type);
      }
    }
    throw new Errors.BadRequest();
  }

  async getUserRecord(uid:number, role_type = 'schl_dist_admin'){
    const accounts = await dbRawRead(this.app, [uid, role_type], `
        SELECT ur.id
            , ur.uid
            , ur.group_id
            , ur.role_type
            , ur.is_revoked
            , ur.created_on
            , ur.revoked_on
            , u.first_name
            , u.last_name
            , u.contact_email
            , u.is_claimed
            , a.email
            , a.enabled
            , a.failed_login_attempts
            , i.id as invite_id
            , i.secret_key
            , i.created_on  as invite_created_on
            , i.expire_on  as invite_expire_on
            , i.is_revoked as invite_is_revoked
            , i.revoked_on as invite_revoked_on
            , i.used_on as invite_used_on
            , i.invit_email
          FROM user_roles ur
          join users u
            on u.id = ur.uid
          left join auths a
            on a.uid = ur.uid
          left join u_invites i
            on i.uid = ur.uid
          where ur.uid = ?
            and ur.role_type IN (?)
      `)
    return accounts[0];
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async getInvitationRecords(uid:NullableId){
    return dbRawRead(this.app, [uid], `
      select sd.id as sd_id, sd.brd_lang as lang, sd.name as sd_name, ur.uid, u.first_name, u.last_name, u.contact_email, i.id as invite_id, i.secret_key , i.expire_on
      from school_districts sd
      join user_roles ur on ur.group_id = sd.group_id and ur.role_type = 'schl_dist_admin' and ur.is_revoked != 1
      join users u on ur.uid = u.id
      join u_invites i on i.uid = u.id
      where u.id = ?
      and i.is_revoked != 1
      group by sd.id, ur.role_type
    ;`);
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const res = [];
    const records = await this.getInvitationRecords(id);
    for (let i=0; i<records.length; i++){
      console.log(i);
      const record = records[i];
      const langCode = record.lang;
      const domain = 'eqao-api.vretta.com'; // there is some confusion around domain and whitelabel at the moment...
      let contact_email = record.contact_email;
      // contact_email = '<EMAIL>'; // debug
      const invitationCode = renderInvitationCode(<number>record.invite_id, record.secret_key);
      const EMAIL_ENCODED = encodeURIComponent(contact_email);
      const emailTemplateParams:IBoardItInviteEmail = {
        FIRST_NAME: record.first_name,
            LAST_NAME: record.last_name,
            BOARD_NAME: record.sd_name,
            LANG_CODE: langCode,
            EMAIL_ENCODED,
            INVITATION_CODE: invitationCode
      }
      const mailRes = await this.app
        .service('auth/invitation')
        .sendEmail(
          contact_email,
          'subj_email_acct_invite_sd_admin',
          'email_acct_invite_sd_admin',
          emailTemplateParams,
          invitationCode,
          langCode,
          domain
        );
        await this.app
          .service('db/write/u-invites')
          .patch(record.invite_id, {
            is_auto_email: 1,
            expire_on: dbDateOffsetDays(this.app, 7),
          })
        res.push(mailRes);
    }
    return res;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    if(!params?.query?.user_roles_id) {
      throw new Errors.BadRequest();
    }
    const user_roles_id = params?.query?.user_roles_id;
    const findUserRole = `SELECT * FROM user_roles WHERE id=?;`
    const revokeUserRole = `
      UPDATE user_roles SET 
        is_revoked = 1, 
        revoked_on = CURRENT_TIMESTAMP, 
        revoked_by_uid = ? 
      WHERE 
        role_type = ?
        AND uid = ?
        AND group_id = ?
        AND is_revoked = 0`;
    const checkNonDistAdmin = `
    SELECT * FROM user_roles 
    WHERE uid = ? AND group_id = ? 
      AND is_revoked = 0 
      AND role_type != "schl_dist_admin";`

    // Find user role with id and get role_type, uid, and group_id
    const userRole = (await dbRawRead(this.app, [user_roles_id], findUserRole))[0];
    const userInformation = {
      role_type: userRole.role_type,
      uid: userRole.uid,
      group_id: userRole.group_id,
    }
    const uid = await currentUid(this.app, params);

    // Revoke all rows associated with role_type, uid, and group_id
    await dbRawWrite(this.app, [uid, userInformation.role_type, userInformation.uid, userInformation.group_id], revokeUserRole);

    // Check if there are any other rows that aren't schl_dist_admin. If none are found, revoke schl_dist_admin
    const nonDistAdmin = (await dbRawRead(this.app, [uid, userInformation.group_id], checkNonDistAdmin));

    if(nonDistAdmin.length == 0) {
      await dbRawWrite(this.app, [uid, "schl_dist_admin",userInformation.uid, userInformation.group_id], revokeUserRole);
    }

    return [];
  }
}
