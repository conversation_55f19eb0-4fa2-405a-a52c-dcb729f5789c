<div>
    <div style="display:flex; justify-content:center; align-items:center;">
        <button class="button dont-print" (click)="printInvoice()">
            <i style="margin-right:0.5em" class="fas fa-print"></i>
            <tra slug="btn_print"></tra>
        </button>
    </div>
    <hr/>
    <div id="invoice-title">
        <div style="display: flex; flex-direction: row; margin-bottom: 1em;">
            <div style="flex: 1; max-width: 8rem;">
                <img *ngIf="isEnglish()" style="width: 8rem; height: auto;" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/517206/authoring/payment_invoice_eqao_logo/1661888407083/payment_invoice_eqao_logo.png"> 
                <img *ngIf="!isEnglish()" style="width: 8rem; height: auto;" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/517206/authoring/payment_invoice_oqre_logo/1661888532568/payment_invoice_oqre_logo.png">
            </div>
            <div style="display: flex; flex-direction: column; margin-left: 2em; flex: 1;"> 
                <tra-md slug="sa_purchase_invoice_eqao_info"></tra-md>
            </div>
        </div>
    </div>  
    <div>
        <br/>
    </div> 
    <div *ngFor="let item of porcessedList; let i = index" [id]="'print-container-'+i">
        <div>
            <table class="invoice-table">
                <thead>
                    <th><tra slug="sa_lbl_purchase_invoice_number"></tra></th>            <!-- Invoice No. -->     
                    <th><tra slug="sa_lbl_purchase_invoice_transaction_num"></tra></th>        <!-- Transaction No. -->  
                    <th><tra slug="sa_lbl_purchase_invoice_assessment_name"></tra></th>     <!-- Name of Assessment --> 
                </thead>
                <tbody>
                    <tr>
                        <td style="text-align: center;">{{item.invoice_number}}</td>
                        <td style="text-align: center;">{{item.purchase_trans_num}}</td>
                        <td style="text-align: center;">{{item.assessment_name}}</td>
                    </tr>
                </tbody>
            </table>
            <div>
                <br/>
            </div>
            <table class="invoice-table">
                <thead>
                    <th><tra slug="sa_lbl_purchase_invoice_school_name"></tra></th>     <!-- Name of School --> 
                    <th><tra slug="pc_lbl_schl_mident"></tra></th>                      <!-- School Mident --> 
                    <th><tra slug="sa_lbl_purchase_invoice_class_name"></tra></th>      <!-- Class Name --> 
                    <th><tra slug="sa_lbl_purchase_invoice_admin_window"></tra></th>      <!-- Administration Window --> 
                </thead>
                <tbody>
                    <tr>
                        <td style="text-align: center;">{{item.schl_name}}</td>
                        <td style="text-align: center;">{{item.schl_mident}}</td>
                        <td style="text-align: center;" *ngFor="let className of item.class_name">{{className}}</td>
                        <td style="text-align: center;">{{item.admin_window}}</td>
                    </tr>
                </tbody>
            </table>
            <div>
                <br/>
            </div>
            <table class="invoice-table">
                <tbody>
                    <tr>
                        <th class="limit-width"><tra slug="sa_lbl_purchase_invoice_num_of_sessions_purchased"></tra></th>     <!-- Number of Assessment Sessions Purchased -->
                        <td>{{item.class_name.length}}</td>
                    </tr>
                    <tr>
                        <th class="limit-width"><tra slug="sa_lbl_purchase_invoice_num_of_attempts"></tra></th>   <!-- Number of Student Attempts Purchased -->
                        <td>{{item.num_student_attempts_purchased}}</td>
                    </tr>
                    <tr>
                        <th class="limit-width"><tra slug="sa_lbl_purchase_invoice_list_paid_students"></tra></th>   <!-- List of Paid Students -->
                        <td class="no-padding">
                            <div class="students-container">
                                <div class="students-container-flex">
                                    <div class="student-header dynamic-col"><tra slug="sa_lbl_purchase_invoice_student_name"></tra></div>     <!-- Name -->
                                    <div class="student-header dynamic-col"><tra slug="sa_lbl_purchase_invoice_student_oen"></tra></div>    <!-- OEN -->
                                </div>
                                <div class="students-container-flex" *ngFor="let student of item.student_attempts">
                                    <div class="dynamic-col">{{student.student_first_initial}} {{student.student_last_name}}</div>
                                    <div class="dynamic-col">{{student.student_oen}}</div>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <th class="limit-width"><tra slug="sa_lbl_purchase_invoice_payment_method"></tra></th>    <!-- Payment Method -->
                        <td>{{item.purchase_method}}</td>
                    </tr>
                    <tr>
                        <th class="limit-width"><tra slug="sa_lbl_purchase_invoice_total_cost"></tra></th>         <!-- Total Cost -->
                        <td>{{convertTotalAmount(item.total_amount)}}</td>
                    </tr>
                </tbody>
            </table>
            <div>
                <br/>
            </div>
            <div style="margin-bottom: 0.25em;">
                <span><b><tra slug="sa_lbl_purchase_invoice_refund_policy"></tra></b></span>
                <span style="margin-left: 1em;"><tra slug="sa_lbl_purchase_invoice_refund_policy_from_eqao"></tra></span>
            </div>
            <div>
                <br/>
            </div>
        </div>
        <!-- Refund Invoice Table -->
        <ng-container *ngIf="item.is_refunded == 1">
            <div class="pagebreak d-none"> </div>
            <div >
                <div>
                    <br/>
                </div>
                <div style="text-align: center;">
                    <tra-md slug="invoice_lbl_refunded"></tra-md>
                </div>
                <div>
                    <br/>
                </div>
                <table class="invoice-table">
                    <tbody>
                        <tr>
                            <th class="limit-width"><tra slug="sa_lbl_refund_invoice_num_of_assessment"></tra></th>     <!-- Number of Assessment Sessions Refunded -->
                            <td>{{item.class_name.length}}</td>
                        </tr>
                        <tr>
                            <th class="limit-width"><tra slug="sa_lbl_refund_invoice_num_of_attempts"></tra></th>   <!-- Number of Student Attempts Refunded -->
                            <td>{{item.refunded_student_count}}</td>
                        </tr>
                        <tr>
                            <th class="limit-width"><tra slug="sa_lbl_refund_invoice_list_students"></tra></th>   <!-- List of Refunded Students -->
                            <td class="no-padding">
                                <div class="students-container">
                                    <div class="students-container-flex">
                                        <div class="student-header dynamic-col"><tra slug="sa_lbl_purchase_invoice_student_name"></tra></div>     <!-- Name -->
                                        <div class="student-header dynamic-col"><tra slug="sa_lbl_purchase_invoice_student_oen"></tra></div>    <!-- OEN -->
                                    </div>
                                    <div class="students-container-flex" *ngFor="let student of item.student_attempts">
                                        <div *ngIf="student.payment_status == 'Refunded'" class="dynamic-col">{{student.student_first_initial}} {{student.student_last_name}}</div>
                                        <div *ngIf="student.payment_status == 'Refunded'" class="dynamic-col">{{student.student_oen}}</div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="limit-width"><tra slug="sa_lbl_purchase_invoice_payment_method"></tra></th>    <!-- Payment Method -->
                            <td>{{item.purchase_method}}</td>
                        </tr>
                        <tr>
                            <th class="limit-width"><tra slug="sa_lbl_refund_invoice_total_cost"></tra></th>         <!-- Refunded Amount -->
                            <td>{{convertTotalAmount(item.recovery_value)}}</td>
                        </tr>
                    </tbody>
                </table>
                <div>
                    <br/>
                </div>
            </div>
        </ng-container>
        <div>
            <span style="font-weight: bold;"><tra slug="lbl_date"></tra></span>
            <span>: {{ getCurrentDate() }}</span>
        </div>  
        <div>
            <br/> <br/>
        </div>
    </div>
</div>