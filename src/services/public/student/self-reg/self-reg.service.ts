// Initializes the `public/student/self-reg` service on path `/public/student/self-reg`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SelfReg } from './self-reg.class';
import hooks from './self-reg.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/self-reg': SelfReg & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/self-reg', new SelfReg(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/self-reg');

  service.hooks(hooks);
}
