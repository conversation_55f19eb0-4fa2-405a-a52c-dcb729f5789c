.personal-info-columns {
    // display:flex;
    // flex-direction: row;
    .column { 
        // margin-top: 1.5em; // CLOSER_LOOK_20210807
    }
}

.modal-links {
    font-weight: 700;
    text-decoration: underline;
    color: blue;
    padding-top: 2em;
}

.modal-button {
    background-color: blue; color: white; height: 20%; width: 25%; border: none; border-radius: 0.2em; padding: 0.5em;
}

.fsa-results {
    display: flex;
    flex-direction: column;
    .fsa-label {
        color: teal;
        font-weight: 700;
        padding-top: 32px;
    }
}

.fsa-student-info {
    > div {
        flex: 1;
    }
    display: flex;
    .fsa-label {
        color: teal;
        font-weight: 700;
        padding-left: 32px;
    }
    .course-code-container {
        display: flex;
        padding: 5px 0 40px 55px;
        sa-widget-form-entry {
            border: 1px solid #dbdbdb;
            border-radius: 5px;
            margin-right: 10px;
            padding: 0px 5px;
        }
    }
    .online-component-status {
        padding-left: 25px;
        margin-top: 50px;
        .fsa-label {
            padding-left: 0;
            padding-bottom: 5px;
        }
        th {
            background-color: #F9F9F9;
            border: 1px solid #b5b5b5;
        }
    }
    .date-table {
        th:nth-child(3), td:nth-child(3) {
            display: none;
        }
        
    }
}
.grad-special-formats {
    .intro {
        font-weight: 700;
    }
    .request-status {
        .field.is-vertical {
            display: flex;
            .field-label {
                flex-grow: unset;
                width: 110px;
                flex-basis: unset;
            }
        }
        .mat-radio-group {
            height: 75px;
            width: 620px;
            flex-wrap: wrap !important;
        }
    }
    .descriptions-container {
        > div {
            flex: 1;
        }
        display: flex;
    }
    .mat-radio-label-content {
        font-weight: bold;
    }
}

.fsa-special-formats {
    .intro {
        font-weight: 700;
    }
    .request-status {
        .field.is-vertical {
            display: flex;
            .field-label {
                flex-grow: unset;
                width: 110px;
                flex-basis: unset;
            }
        }
        .mat-radio-group {
            height: 75px;
            width: 620px;
            flex-wrap: wrap !important;
        }
    }
    .fields-container {
        > div {
            flex: 1;
        }
        display: flex;
    }
    .mat-radio-label-content {
        font-weight: bold;
    }
    .fsa-label {
        color: teal;
        font-weight: 700;
        padding-top: 32px;
        width: 150px;
        text-align: right;
        white-space: normal;
        .required-field-container{
            display:flex;
            flex-direction: row;
            // margin-left:0.5em;
            position: absolute;
            .fa-asterisk {
                color:red;
                font-size: 0.6em;
                padding:0.2em;
            }
        }
    }
}

.required-field-container{ // CLOSER_LOOK_20210807 sub to above class
    // display:flex;
    // flex-direction: row;
    // // margin-left:0.5em;
    // position: absolute;
    .fa-asterisk{
        color:red;
        font-size: 0.6em;
        // padding:0.2em;
        margin-left: 1.8em;
    }
}

.is-select{
    background-color: #297cb3;
    font-weight: bold;
    color: white; 
}

.sdc_conflict_radio_sch_btn{
    .mat-radio-inner-circle{
	    background-color: black!important;   /*inner circle color change*/
    };
    display: inline-block;
    padding-top: 0px;
}

.sdc_conflict_radio_board_btn{
    .mat-radio-inner-circle{
	    background-color: black!important;   /*inner circle color change*/
    };
    display: inline-block;
    position:absolute; 
    right:0px; 
    width:40%;
}

.sdc_field_name {
    padding-top: 1.5em;
    resize: none;
    background-color:moccasin;
    border: none;
    width: 20em;
    text-align: left;
}

.sdc_field_value {
    padding-top: 1.5em;
    resize: none;
    background-color:moccasin;
    border: none;
    width: 18em;
    text-align: left;
}

.tab-container{
    .lable-container{
        color: teal;
    }
    .label{
        margin-right: 1em;
    }
}

.header-note{
    width: 100%; 
    text-align: center; 
    font-weight: bold;
    color: teal;
  }

  .alt-version-not-require {
    display: flex;
    justify-content: center;
    align-items: center;
    //background-color:#94beda;
    padding: 0.5em;
    width: 45em;
    border-width: 0.1em;
    border-style: solid;
    border-color: rgb(116, 113, 113);
}
.submit-alt-request-buttons-container {
    margin-top:2em;
    display: flex; 
    flex-direction: row;
}
.submit-alt-request-buttons {
    width: 10em;
    background-color: teal;
    color: white;
    padding: 1em 1.2em;
    padding-left: 1em;
    padding-right: 1em;
    border-radius: 0.5em;
    font-size: small;
    font-weight: 500;
    border: none;
    cursor: pointer;
    &:hover {
        background-color: rgb(4, 121, 121);
    };
    &:disabled{
      background: rgb(220, 220, 220)
    }
}

.cancel-alt-request-buttons {
    width: 10em;
    padding: 1em 1.2em;
    padding-left: 1em;
    padding-right: 1em;
    border-radius: 0.5em;
    font-size: small;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.submit-alt-request-buttons-container-modal {
    margin-top:2em;
    display: flex; 
    flex-direction: row;
    justify-content: space-between;
}

.ack-alt-request-buttons-modal{
    background-color: rgb(255, 255, 255);
    color: rgb(0, 0, 0);
    border:none;
}

.cancel-alt-request-buttons-modal{
    width: 10em;
    background-color: rgb(223, 223, 223);
    color: rgb(0, 0, 0);
    width: 10em;
    padding: 1em 1.2em;
    padding-left: 1em;
    padding-right: 1em;
    border-radius: 0.5em;
    font-size: small;
    font-weight: 500;
    border: none;
    cursor: pointer;
    &:hover {
        background-color: rgb(212, 212, 212);
    }
}

.submit-alt-request-buttons-modal {
    width: 10em;
    padding: 1em 1.2em;
    padding-left: 1em;
    padding-right: 1em;
    border-radius: 0.5em;
    font-size: small;
    font-weight: 500;
    border: none;
    cursor: not-allowed;
    background-color: rgba(0, 128, 128, 0.356);
    color: white;
    &:enabled {
        background-color: teal;
    }
    &:hover:enabled {
        cursor: pointer;
        background-color: rgb(4, 121, 121);
    }
}

.alt_request_invalid_entry {
    color: rgb(160, 0, 0);
}