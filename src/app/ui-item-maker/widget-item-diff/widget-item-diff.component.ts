import { Component, Input, OnInit, EventEmitter, Output, OnDestroy} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { checkElementIsEntry, IQuestionConfig } from '../item-set-editor/models';
import { getElementEntryByPath } from './util';
import { ElementType, IContentElement } from 'src/app/ui-testrunner/models';
import { getValueByPath, MCQ_OPTION_MAP } from '../element-config-template/util';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { diffWordsWithSpace, diffWords } from 'diff';
import { BehaviorSubject, Subscription } from 'rxjs';

export enum ExpansionPanelContentType {
  JSON_VIEW = 'JsonView',
  TEXT_AREA = 'TextArea',
  SPAN_LIST = 'SpanList',
  TEXT_LIST = 'TextList'
}
export const KindMap = {
  A: 'Array',
  N: 'New',
  D: 'Delete',
  E: 'Edit',
  DI: 'Item Deleted'
}
export interface IdeepDiffData {
  kind: 'A' | 'N' | 'D' | 'E' | 'DI',
  path: any[],
  rhs?: any,
  lhs?: any,
  item?: IdeepDiffData,
  index?: number,
  responseEntryElement?: IContentElement
}
export interface CustomSpanData {
  text: string,
  style: any
}
export interface IExpansionPanelContent {
  type: ExpansionPanelContentType;
  label?: string;
  data: any;
}

interface IFormattedDataText {
  responseKeys: string[], 
  voiceover: string[], 
  layout: string[], 
  other: string[], 
  answerKeys: string[]
}

interface IFormattedData {
  responseKeys: IdeepDiffData[], 
  voiceover: IdeepDiffData[], 
  layout: IdeepDiffData[], 
  other: IdeepDiffData[], 
  answerKeys: IdeepDiffData[]
}

export interface IDiffContentParsed {config: string, last_updated_on?: string, name: string, tqv_id?: number}

@Component({
  selector: 'widget-item-diff',
  templateUrl: './widget-item-diff.component.html',
  styleUrls: ['./widget-item-diff.component.scss']
})
export class WidgetItemDiffComponent implements OnInit, OnDestroy {


  @Input() questionId : string;
  @Input() questionLabel: string
  @Input() description: string
  @Input() content: IExpansionPanelContent[];
  @Input() isNgContent:boolean;
  @Input() style: any;
  @Input() signalOpenClose: boolean = false;
  @Input() isExpanded: boolean = false;
  @Input() readOnly: boolean = false;
  @Input() itemBankCtrl: ItemBankCtrl;
  @Input() currentContent: IDiffContentParsed;
  @Input() previousContent: IDiffContentParsed;
  @Input() isSimpleView: boolean = false;
  @Input() lang: string;

  data: IdeepDiffData[];
  parsedCurrentContent: IQuestionConfig;
  parsedPreviousContent: IQuestionConfig;

  twiddleToggleMap: Map<string, boolean> = new Map();

  formattedData: IFormattedData;
  formattedDataText: IFormattedDataText = {responseKeys: [], voiceover: [], layout: [], other: [], answerKeys: []};

  isItemDeleted = false;

  @Output() opened =  new EventEmitter<boolean>();
  @Output() closed =  new EventEmitter<boolean>();

  ExpansionPanelContentType = ExpansionPanelContentType;

  private showLeftHand:BehaviorSubject<boolean> = new BehaviorSubject(false); // gets overridden upon registration
  private subscription = new Subscription();
  constructor(
    private editingDisabled: EditingDisabledService,
    private sanitizer: DomSanitizer,
  ) {
  }

  ngOnInit(): void {
    if(this.content.find((cont) => cont.type = ExpansionPanelContentType.JSON_VIEW)) {
      try {
        this.parsedCurrentContent = JSON.parse(this.currentContent.config)
        this.parsedPreviousContent = JSON.parse(this.previousContent.config)
      } catch (err) {
        console.error(err);
      }
      this.data = this.content[0].data;
      this.formattedData = this.formatData();
      this.initFormattedText(this.formattedData);

      this.subscription.add(this.showLeftHand.subscribe(this.onLeftHandToggle))
    }
  }

  onLeftHandToggle = (val: boolean) => {
    this.initFormattedText(this.formattedData);
  }

  toggleLeftHand(val: any) {
    this.showLeftHand.next(val.checked);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  setTwiddleToggleInfo(key: string, toggle: boolean) {
    this.twiddleToggleMap.set(key, toggle);
  }

  getTwiddleToggleInfo(key: string) {
    return this.twiddleToggleMap.get(key);
  }

  selectQuestion(id: string | number) {
    this.itemBankCtrl.selectQuestionById(+id).then(() => {
      this.itemBankCtrl.switchToEditorView();
    })
  }

  /**
   * 
   * @returns the diff data categorized and filtered based on business rules.
   */
  formatData() {
    const data: IdeepDiffData[] = this.data;
    const constructedData = {responseKeys: [], voiceover: [], layout: [], other: [], answerKeys: []}
    const layoutKeys = ['caption', 'text', 'latex', 'value', 'format', 'elementType', 'x', 'y', 'height', 'width', 'elementMode', 'content', 'templateName', 'templateVersion'];
    const responseIdKeys =['optionId'];
    const answerKeys = ['value', 'scoreWeight', 'isCorrect', 'weight', 'isReadingSelectionPage'];
    const voiceoverKeys = ['url', 'script'];

    if(!data) {
      return constructedData;
    }

    data.forEach((diff) => {
      const responseEntry = this.getResponseEntry(diff.path);

      // Check if the path has a response entry, and store it in the diff.
      if(responseEntry) { 
        diff.responseEntryElement = responseEntry;
      }
      
      // If there is no path, push it in other just in case.
      if(diff.path.length <= 0) {
        constructedData.other.push(diff);
        return;
      }

      const pathLength = diff.path.length;
      const lastPath = diff.path[pathLength-1];

      // ** Ignored Cases **
      // Ignore New type changes that are null.
      if(diff.kind == 'N' && diff.rhs === null) {
        return constructedData.other.push(diff);
      }      
      // Ignore changes to Entry ID if no response entry
      if((lastPath == 'entryId' && !responseEntry)) {
        return;
      }
      // Ignore null to "" type changes
      if(diff.kind == 'E' && (!diff.rhs && !diff.lhs)) {
        return;
      }

      // ** Business Rules **
      // Handle deleted items
      if(diff.kind == 'DI') {
        return this.isItemDeleted = true;
      }
      // Handle Array changes
      if(diff.path.includes('content') && diff.kind == 'A' && diff.item) {
        return constructedData.layout.push({
          ...diff.item,
          index: diff.index,
          path: diff.path
        })
      }
      if(diff.path.includes('meta')) {
        return constructedData.answerKeys.push(diff);
      }

      // voiceover checks
      if(voiceoverKeys.includes(lastPath)) {
        return constructedData.voiceover.push(diff);
      }
      if(responseEntry && responseEntry.elementType == 'validator') {
        return constructedData.answerKeys.push(diff);
      }
      // answer key checks
      if(answerKeys.includes(lastPath)) {

        return constructedData.answerKeys.push(diff);
      }
      // layout checks
      if(layoutKeys.includes(lastPath)) {

        return constructedData.layout.push(diff);
      }

      // responseKey checks
      if(responseIdKeys.includes(lastPath)) {
        return constructedData.responseKeys.push(diff);
        
      }
      // other checks
      constructedData.other.push(diff);
    })

    return constructedData;
  }

  /**
   * 
   * Helper function for sorting the formatted data and pushing them into {@link formattedDataText}
   * @param data the formatted data
   */
  initFormattedText(data: IFormattedData) {
    this.formattedDataText = {responseKeys: [], voiceover: [], layout: [], other: [], answerKeys: []};

    Object.keys(data).forEach((key) => {
      // Sort arrays alphabetically by path
      (data[key] as IdeepDiffData[]).sort(function (a, b) {
        const aPath = a.path.join('.');
        const bPath = b.path.join('.');
        if (aPath < bPath) {
          return -1;
        }
        if (aPath > bPath) {
          return 1;
        }
        return 0;
      });

      (data[key] as IdeepDiffData[]).forEach((diff) => {
        const text = this.getFormattedText(diff);
        this.formattedDataText[key].push(text);
      })
    })
  }

  /**
   * 
   * @param diff the diff to format
   * @returns a formatted version of the diff in readable text
   */
  getFormattedText(diff: IdeepDiffData) {
    const RIGHT_HAND_COLOR = 'style="color: #32a632"';
    const LEFT_HAND_COLOR = 'style="color: #f71b1b"';
    const NEUTRAL_COLOR = 'style="color: #ec9f3b"';
    const EDIT_COLOR = 'style="color: #ff8519"';

    const RIGHT_HAND_COLOR_NO_BG = 'style="color: #32a632; background-color: #0000;"';
    const LEFT_HAND_COLOR_NO_BG = 'style="color: #f71b1b; background-color: #0000;"';
    const NEUTRAL_COLOR_NO_BG = 'style="color: #ec9f3b; background-color: #0000;"';
    const EDIT_COLOR_NO_BG = 'style="color: #ff8519; background-color: #0000;"';
    const PATH_COLOR = 'style="color: #a0a0a0; background-color: #0000;"';

    const path = diff.path.join(' -> ')
    const pathLength = diff.path.length;
    const actionedKey = diff.path[pathLength-1];

    const diffType = KindMap[diff.kind]
    let contextText = `<b>Path:</b> <code ${PATH_COLOR}>${path}</code> <br>`;

    let diffColor;
    switch(diff.kind) {
      case 'D':
        diffColor = LEFT_HAND_COLOR_NO_BG;
        break;
      case 'N':
        diffColor = RIGHT_HAND_COLOR_NO_BG;
        break;
      case 'E':
        diffColor = NEUTRAL_COLOR_NO_BG;
        break;
      default: 
        diffColor = EDIT_COLOR_NO_BG;
        break;
    }
    let actionText = `<b>Action</b> <code ${diffColor}>(${diffType})</code>: `

    const responseEntry = diff.responseEntryElement;

    if(responseEntry) {
      contextText = `<b>Block:</b> <code ${PATH_COLOR}>${responseEntry.elementType} ID ${responseEntry.entryId ? responseEntry.entryId : 'N/A'}</code>`;
      // If MCQ response entry, check if current change is in an option.
      if(responseEntry.elementType == ElementType.MCQ) {
        const poppedPath = diff.path.slice(0, pathLength-1);
        const stringPath = poppedPath.join('.');
        const currObj = getValueByPath(stringPath, this.parsedCurrentContent);
        
        if(currObj && currObj.optionType && currObj.optionType == ElementType.MCQ_OPTION) {
          const optionIndex = diff.path[pathLength-2];
          const optionLabel = MCQ_OPTION_MAP[optionIndex];
          contextText += `<code ${NEUTRAL_COLOR_NO_BG}>Option ${optionLabel}</code>`;
        }
      }

      contextText += '<br>'
    }

    if(diff.kind == 'D') {
      let lhs = JSON.stringify(diff.lhs);
      if(diff.lhs && typeof diff.lhs == 'object' && !Array.isArray(diff.lhs) && diff.lhs.hasOwnProperty('elementType')) {
        lhs = diff.lhs['elementType']
        actionText += `Removed <code ${LEFT_HAND_COLOR}>${lhs}</code> block from <code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code>.`;
      } else if(Array.isArray(diff.lhs)) {
        lhs = `${diff.lhs.length}`;
        actionText += `Removed <code ${LEFT_HAND_COLOR}>${lhs}</code> blocks from <code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code>.`;
      } else {
        actionText += `Removed <s><code ${LEFT_HAND_COLOR}>${lhs}</code></s> from <code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code>.`;
      }
    } else if(diff.kind == 'E') {
      let lhs = JSON.stringify(diff.lhs);
      let rhs = JSON.stringify(diff.rhs);
      if(diff.lhs && typeof diff.lhs == 'object' && !Array.isArray(diff.lhs) && diff.lhs.hasOwnProperty('elementType')) {
        lhs = diff.lhs['elementType']
      }
      if(diff.rhs && typeof diff.rhs == 'object' && !Array.isArray(diff.rhs) && diff.rhs.hasOwnProperty('elementType')) {
        rhs = diff.rhs['elementType']
      }

      if(typeof diff.lhs == 'string' && typeof diff.rhs == 'string') {
        try {
          actionText+= `<code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code> changed to ${this.getParsedDiff(lhs, rhs)}`
        } catch {
          actionText += `<code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code> changed from <s><code ${LEFT_HAND_COLOR}>${lhs}</code></s> to <code ${RIGHT_HAND_COLOR}>${rhs}</code>.`
        }
      } else {
        actionText += `<code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code> changed from <s><code ${LEFT_HAND_COLOR}>${lhs}</code></s> to <code ${RIGHT_HAND_COLOR}>${rhs}</code>.`
      }
    } else if(diff.kind == 'N') {
      let rhs = JSON.stringify(diff.rhs);
      if(diff.rhs && typeof diff.rhs == 'object' && !Array.isArray(diff.rhs) && diff.rhs.hasOwnProperty('elementType')) {
        rhs = diff.rhs['elementType'];

        actionText += `<code ${RIGHT_HAND_COLOR}">${rhs}</code> block added to <code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code> for the first time.`
      } else {
        actionText += `<code ${NEUTRAL_COLOR_NO_BG}>${actionedKey}</code> set to <code ${RIGHT_HAND_COLOR}">${rhs}</code>.`
      }
    }

    return this.sanitizer.bypassSecurityTrustHtml(contextText + actionText);
  }

  getParsedDiff(lhs, rhs) {
    lhs = lhs ?? '';
    rhs = rhs ?? '';
    const RIGHT_HAND_COLOR = 'style="color: #32a632; background-color: #d4f8d4; padding-left: 0.2em; padding-right: 0.2em; border-radius: 3px;"';
    const LEFT_HAND_COLOR = 'style="color: #f71b1b; background-color: #ffdddd; padding-left: 0.2em; padding-right: 0.2em; border-radius: 3px;"';
    // Perform the diff operation using diffWordsWithSpace
    const differences = diffWordsWithSpace(lhs, rhs);
    // Map over the differences and construct the result string
    const result = differences.map(part => {
        if (part.added) {
            // Right-hand changes
            return `<code ${RIGHT_HAND_COLOR}>${part.value}</code>`;
        } else if (part.removed) {
            // Left-hand changes
            return '';
        } else {
            // Unchanged text
            return part.value;
        }
    }).join('');


    if(this.showLeftHand.value) {
      return `<s><code ${LEFT_HAND_COLOR}>${lhs}</code></s>` + ' ' + result;
    }
    return result;
  }

  /**
   * 
   * @param path the path array
   * @returns the first response entry element found in the path. If none is found, undefined is returned.
   */
  getResponseEntry(path: any[]) {
    const currentContent = this.parsedCurrentContent;
    for(let i = 1; i<=path.length; i++) {
      const currPath = path.slice(0, i).join('.');
      const entryElement = getElementEntryByPath(currPath, currentContent);
      if(entryElement) {
        return entryElement;
      }
    }

    return undefined;
  }

  isReadOnly() {
    return this.editingDisabled.isReadOnly()
  }

  getBorderColor(){
    if(this.style && this.style['borderColor']){
      return this.style['borderColor']
    }
    return '#d3d3d3'
  }


  panelOpened(): void {
    if (this.signalOpenClose) this.opened.emit()
  }

  panelClosed(){
    if (this.signalOpenClose) this.closed.emit()
  }

  isOnlyOther() {
    let isOnlyOther = true;
    // If other category has no values, return false.
    if(!this.formattedData.other.length) {
      return false;
    }

    Object.keys(this.formattedData).forEach((category) => {
      if(this.formattedData[category].length && category != 'other') {
        isOnlyOther = false;
      }
      
    })

    return isOnlyOther
  }

}
