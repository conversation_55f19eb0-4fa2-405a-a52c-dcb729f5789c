@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/registration-or-login-form.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/page-types/accounts.scss';
@import '../../../styles/page-types/standard.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/partials/_table.scss';
@import '../../../styles/partials/_modal.scss';
@import '../../../styles/pseudo-objects/form-input.scss';

.page-body {
    @extend %page-body;
    @extend %page-body-accounts;
    .page-content {
    }
}

.input-row { @extend %input-row; }
.custom-modal { @extend %custom-modal; }

.is-selected {
    min-height: 25em;
}

.config-header {
    color: #363636;
    font-weight: 700;
}

.card {
    background-color: #fff;
    box-shadow: 0 .5em 1em -.125em rgba(10,10,10,.1),0 0 0 1px rgba(10,10,10,.02);
    color: #4a4a4a;
    max-width: 100%;
    position: relative;
    padding: 1.5em;
    border-radius: .5em;
    border: none;
}

.surface-card {
    background-color: #fff;
    box-shadow: 0 .25em 0.5em -.125em rgba(10,10,10,.1),0 0 0 1px rgba(10,10,10,.02);
    color: #4a4a4a;
    max-width: 100%;
    position: relative;
    padding: 1.5em;
    border-radius: .5em;
    border: none;
}

.row {
    display: flex;
    flex-direction: row;
    gap: 1em;
}

.page-body {
    background-color: #f5f5f5;
}

.flex-column {
    display: flex; 
    flex-direction: column; 
}

.button {
    margin-right: 0px;
    box-shadow: 0 .25em 0.5em -.125em rgba(10,10,10,.1),0 0 0 1px rgba(10,10,10,.02);
}

h3 {
    font-weight: bold;
}

.is-full {
    height: 92%;
}

.is-yellow {
    background-color: #ffd65a;
}

.is-over-flow {
    overflow: auto;
    max-height: 25em;
    padding: 0.1em;
}

.is-error {
    color: #ff5656;
    font-weight: bold;
    font-size: small;
}

.input-container {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.is-red {
    background-color: #fb7272;
    color: #fff;
}

.is-light-yellow {
    background-color: #ffd46f;
    color: #fff;
}

.selection-options-container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 0.5em;
  padding: 0.5em;
  border: 1px solid #ccc;
  border-radius: 0.5em;
  
  input {
    max-width: 5em;
  }
}

.disabled {
    pointer-events: none;
    opacity: 60%;
}

.cursor-disabled {
    cursor: not-allowed;
}

.deselected {
    pointer-events: none;
    opacity: 60%;
}

.tip {
    color: #ff5656;
    font-size: small;
}