import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Knex } from 'knex';
import { currentUid } from '../../../../util/uid';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { dbDateNow } from '../../../../util/db-dates';
import { LDBConfigLevel, LDBSecurityLevel } from '../../../auth/lock-down-browser/data/types';
import { hasRoleAction } from '../../../../hooks/has-role-action';

interface Data {}

interface ServiceOptions {}


const MAX_LIST_CHARACTER_LIMIT = 155;

export class LdbConfig implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

 /**
   * Find LDB configuration for the given school_class_id, school_id, school_board_id
   */
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params) {
      throw new Errors.BadRequest('ERR_PARAMS_REQ');
    }

    const uid = await currentUid(this.app, params);

    const { school_class_id, school_id, school_board_id } = <any>params.query;

    return this.getLdbConfig({school_class_id, school_id, school_board_id});
  }

  /** 
   * Get LDB configuration for the given school_class_id, school_id, school_board_id
   * - If any of the params are present, it should get all of them by orWhere statement
   * - If none of the params are present, it should get the default configuration
   */
  public async getLdbConfig(params: {school_class_id: number, school_id: number, school_board_id: number}) {
    const knex: Knex = this.app.get('knexClientRead');

    // Should get a config with is_default = 1 and is_revoked = 0
    // If any of the params are present it should get all of them by orWhere statement
    const ldbConfigs = await knex('ldb_launch_configuration')
      .where('is_revoked', 0)
      .andWhere(function() {
        this.orWhere('is_default', 1)
        if(params.school_class_id) {
          this.orWhere('school_class_id', params.school_class_id)
        }
        if(params.school_id) {
          this.orWhere('school_id', params.school_id)
        }
        if(params.school_board_id) {
          this.orWhere('school_board_id', params.school_board_id)
        }
      })

    // Determine correct configuration level for each config
    for(const ldbConfig of ldbConfigs) {
      ldbConfig.level = 
        ldbConfig.school_id ? LDBConfigLevel.SCHOOL : 
        ldbConfig.school_class_id ? LDBConfigLevel.CLASS : 
        ldbConfig.school_board_id ? LDBConfigLevel.BOARD :
        ldbConfig.is_default ? LDBConfigLevel.SYSTEM : null;

      if(!ldbConfig.level) {
        throw new Errors.BadRequest('ERR_INVALID_LDB_CONFIG');
      }
    }
      
    return ldbConfigs;
  }

  /**
   * Compile LDB configuration from multiple configurations based on priority
   *  - Security Level is fetched from System Configuration
   *  - Whitelist, Blacklist and Domain Whitelist are combined from all configurations
   * @param ldbConfigs - list of ldbConfigs from getLdbConfig function
   * @returns compiled LDB configuration
   */
  public async compileLdbConfig(ldbConfigs: any[]) {
    const compiledConfig = {
      security_level: LDBSecurityLevel.rldbsp,
      whitelist: [],
      blacklist: [],
      domain_whitelist: [],
      xml_config: {}
    }

    for(const ldbConfig of ldbConfigs) {
      if(ldbConfig.level === LDBConfigLevel.SYSTEM) {
        compiledConfig.security_level = ldbConfig.security_level;
        // Get XML configuration only from system (default) configuration
        if (ldbConfig.xml_config) {
          compiledConfig.xml_config = ldbConfig.xml_config;
        }
      }

      compiledConfig.whitelist = compiledConfig.whitelist.filter(wl=>wl).concat(ldbConfig.whitelist);
      compiledConfig.blacklist = compiledConfig.blacklist.filter(bl=>bl).concat(ldbConfig.blacklist);
      compiledConfig.domain_whitelist = compiledConfig.domain_whitelist.filter(wld=>wld).concat(ldbConfig.domain_whitelist);
    }

    return compiledConfig;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  /**
   * Create or update LDB configuration based on the level
   * - If the configuration does not exist, create a new one
   * - If the configuration is the default one, it cannot be updated
   * - If the configuration is different, update the existing one by:
   *  - Revoking the old one
   *  - Creating a new one
   */
  async create (data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('ERR_MISSING_PARAMS');
    }

    const uid = await currentUid(this.app, params);

    const { school_class_id, school_id, school_board_id, is_default } = <any>params.query;
    const { level, whitelist, blacklist, domain_whitelist, xml_config } = <any>data;

    if(!is_default) {
      this.validateParams(level, school_board_id, school_id, school_class_id);
    }

    const ldbConfigs = await this.getLdbConfig({school_class_id, school_id, school_board_id});

    if (this.getTotalListSize(ldbConfigs, level, whitelist, blacklist, domain_whitelist) > MAX_LIST_CHARACTER_LIMIT){
      throw new Errors.BadRequest('MAX_LIST_CHARACTER_LIMIT_EXCEEDED')
    }
    const ldbConfig = ldbConfigs.find((config: any) => config.level === level);

    // In case the configuration does not exist, create a new one
    if(!ldbConfig) {
      return await this.createLdbConfig(level, whitelist, blacklist, domain_whitelist, school_id, school_board_id, school_class_id, uid);
    }

    // If the configuration is the default one, check if the user has the support role
    if(ldbConfig.is_default) {
      await this.checkSupportRole(uid);
    }

    // Compare whitelist and blacklist with existing config, if they are the same, return true
    if(ldbConfig.whitelist === whitelist && ldbConfig.blacklist === blacklist && ldbConfig.domain_whitelist == domain_whitelist) {
      logger.info('LDB_CONFIG_NO_CHANGE', { level, whitelist, blacklist, domain_whitelist, uid, school_board_id, school_class_id, school_id });
      return true;
    }

    // Update existing configuration (revoke the old one and create a new one)
    const knex: Knex = this.app.get('knexClientWrite');
    logger.log('LDB_CONFIG_UPDATE', { level, whitelist, blacklist, uid, school_board_id, school_class_id, school_id, oldConfig: ldbConfig });
    await knex('ldb_launch_configuration').where('id', ldbConfig.id).update({ is_revoked: 1, revoked_by_uid: uid, revoked_on: dbDateNow(this.app) });

    return await this.createLdbConfig(level, whitelist, blacklist, domain_whitelist, school_id, school_board_id, school_class_id, uid, xml_config, is_default);
  }

  /**
   * Create a new LDB configuration for the given level
   * - school_id, school_board_id, school_class_id are mutually exclusive
   */
  private async createLdbConfig(level: LDBConfigLevel, whitelist: string[], blacklist: string[], domain_whitelist: string[], school_id: number, school_board_id: number, school_class_id: number, uid: number, xml_config?: any, is_default: boolean = false) {
    const knex: Knex = this.app.get('knexClientWrite');
    logger.log('LDB_CONFIG_CREATE', { level, whitelist, blacklist, uid, school_board_id, school_class_id, school_id, xml_config });
    
    return await knex('ldb_launch_configuration').insert({
      school_id: level === LDBConfigLevel.SCHOOL ? school_id : undefined,
      school_board_id: level === LDBConfigLevel.BOARD ? school_board_id : undefined,
      school_class_id: level === LDBConfigLevel.CLASS ? school_class_id : undefined,
      whitelist: JSON.stringify(whitelist),
      blacklist: JSON.stringify(blacklist),
      domain_whitelist: JSON.stringify(domain_whitelist),
      xml_config: JSON.stringify(xml_config || {}),
      is_revoked: 0,
      is_default,
      created_by_uid: uid
    })
  }

  private validateParams(level: LDBConfigLevel, school_board_id: number, school_id: number, school_class_id: number) {
    const isBoardValid = school_board_id && level === LDBConfigLevel.BOARD;
    const isSchoolValid = school_id && level === LDBConfigLevel.SCHOOL;
    const isClassValid = school_class_id && level === LDBConfigLevel.CLASS;

    if(!isBoardValid && !isSchoolValid && !isClassValid) {
      throw new Errors.BadRequest('ERR_INVALID_PARAMS');
    }

    return true
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();  
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();  
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();  
  }

  getTotalListSize(ldbConfigs: any[], updatedLevel: string, newWhitelist: any[], newBlacklist: any[], newDomainWhiltelist: any[]){
    let currSize = 0;
    for (const ldbConfig of ldbConfigs){
      const whiteListTags = ldbConfig.level == updatedLevel ? newWhitelist.join('') : ldbConfig.whitelist?.join('') || '';
      currSize += whiteListTags.length;
      const blacklistTags =  ldbConfig.level == updatedLevel ? newBlacklist.join('') : ldbConfig.blacklist?.join('') || '';
      currSize += blacklistTags.length;
      const domainWhiteListTags =  ldbConfig.level == updatedLevel ? newDomainWhiltelist.join('') : ldbConfig.domain_whitelist?.join('') || '';
      currSize += domainWhiteListTags.length;
    }
    return currSize;
  }

  /**
   * Checks if user has the support role required to modify default configurations
   *  - not using hooks because method is reused in other roles
   * @param uid - User ID to check
   * @throws Forbidden error if user doesn't have the required role
   */
  private async checkSupportRole(uid: number): Promise<void> {
    const mptSysGroupId = await this.app
      .service('auth/user-role-actions')
      .getSingularGroupId('mpt_sys');
    
    if (mptSysGroupId === undefined) {
      throw new Errors.Forbidden('GROUP_NOT_FOUND');
    }
    
    const hasAccess = await this.app
      .service('auth/user-role-actions')
      .checkUserRole('/public/anon/ldb-config', 'create', [mptSysGroupId], uid);
    
    if (!hasAccess) {
      throw new Errors.Forbidden('GROUP_ROLE_REQ_FOR_ROUTE');
    }
  }

}
