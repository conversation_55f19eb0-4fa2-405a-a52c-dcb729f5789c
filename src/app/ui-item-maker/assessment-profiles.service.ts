import { Injectable } from '@angular/core';
import { AuthService } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { IAuthGroupOption } from './view-im-group-access/model/types';
import { IReportingProfileNew, IReportingProfileView } from './view-im-reporting-profiles/model/types';

@Injectable({
  providedIn: 'root'
})
export class AssessmentProfilesService {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
  ) { }


  ///////////////////
  ////// UTIL ///////
  ///////////////////

  /**
   * Convert an array of IAuthGroupOption to an array of group IDs
   */
  private mapAuthGroupIds(authGroupOptions: IAuthGroupOption[]) {
    return authGroupOptions.map(r => r.group_id);
  }

  ///////////////////
  ///// MODELS //////
  ///////////////////

  /**
  * TW_TYPE_SLUGS
  */
  async findTwTypeSlugs(authGroupOptions: IAuthGroupOption[]):Promise<any[]> {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return await this.auth.apiFind(this.routes.TEST_AUTH_AP_TW_TYPE_SLUGS, {
      query: {
        authoring_group_ids
      }
    });
  }

  async createTwTypeSlugs(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_AP_TW_TYPE_SLUGS, payload);
  }
  
  async patchTwTypeSlugs(authGroupId: number, id: number, payload: Partial<any>) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_AP_TW_TYPE_SLUGS, id, payload);
  }

  /**
   * TW_PROFILES
   */
  async findTwProfiles(authGroupOptions: IAuthGroupOption[]):Promise<any[]> {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return await this.auth.apiFind(this.routes.TEST_AUTH_AP_TW_PROFILES, {
      query: {
        authoring_group_ids
      }
    });
  }

  async createTwProfiles(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_AP_TW_PROFILES, payload);
  }
  
  async patchTwProfiles(authGroupId: number, id: number, payload: Partial<any>) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_AP_TW_PROFILES, id, payload);
  }

  /**
   * TWTAR_TYPE_SLUGS
   */
  async findTwtarTypeSlugs(authGroupOptions: IAuthGroupOption[]):Promise<any[]> {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return await this.auth.apiFind(this.routes.TEST_AUTH_AP_TWTAR_TYPE_SLUGS, {
      query: {
        authoring_group_ids
      }
    });
  }

  async createTwtarTypeSlugs(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_AP_TWTAR_TYPE_SLUGS, payload);
  }
  
  async patchTwtarTypeSlugs(authGroupId: number, id: number, payload: Partial<any>) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_AP_TWTAR_TYPE_SLUGS, id, payload);
  }

  /**
   * TWTAR_COURSES
   */
  async findTwtarCourses(authGroupOptions: IAuthGroupOption[]):Promise<any[]> {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return await this.auth.apiFind(this.routes.TEST_AUTH_AP_TWTAR_COURSES, {
      query: {
        authoring_group_ids
      }
    });
  }

  async createTwtarCourses(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_AP_TWTAR_COURSES, payload);
  }
  
  async patchTwtarCourses(authGroupId: number, id: number, payload: Partial<any>) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_AP_TWTAR_COURSES, id, payload);
  }

  /**
   * TWTAR_FORM_CARDINALITY
   */
  async findTwtarFormCardinality(authGroupOptions: IAuthGroupOption[]):Promise<any[]> {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return await this.auth.apiFind(this.routes.TEST_AUTH_AP_TWTAR_FORM_CARDINALITY, {
      query: {
        authoring_group_ids
      }
    });
  }

  async createTwtarFormCardinality(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_AP_TWTAR_FORM_CARDINALITY, payload);
  }
  
  async patchTwtarFormCardinality(authGroupId: number, id: number, payload: Partial<any>) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_AP_TWTAR_FORM_CARDINALITY, id, payload);
  }

  /**
   * STYLE_PROFILES
   */
  async findStyleProfiles(authGroupOptions: IAuthGroupOption[]):Promise<any[]> {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return await this.auth.apiFind(this.routes.TEST_AUTH_AP_STYLE_PROFILES, {
      query: {
        authoring_group_ids
      }
    });
  }

  async createStyleProfiles(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_AP_STYLE_PROFILES, payload);
  }
  
  async patchStyleProfiles(authGroupId: number, id: number, payload: Partial<any>) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_AP_STYLE_PROFILES, id, payload);
  }

}
