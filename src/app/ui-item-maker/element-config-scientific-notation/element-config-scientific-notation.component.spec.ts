import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ElementConfigScientificNotationComponent } from './element-config-scientific-notation.component';

describe('ElementConfigScientificNotationComponent', () => {
  let component: ElementConfigScientificNotationComponent;
  let fixture: ComponentFixture<ElementConfigScientificNotationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ElementConfigScientificNotationComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ElementConfigScientificNotationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
