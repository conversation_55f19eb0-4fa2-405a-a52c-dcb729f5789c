

export const SQL_TS_CLASS_LOCK = ` /* SQL_TS_CLASS_LOCK */
  select sccf.twtdar_id 
  from school_class_test_sessions scts 
  join school_class_common_forms sccf 
    on sccf.school_class_id  = scts.school_class_id 
    and sccf.is_revoked = 0 
  where scts.test_session_id = :test_session_id
    and type_slug = :slug
`

export const SQL_TS_CLASS_LOCK_TWTAR_FULL = ` /* SQL_TS_CLASS_LOCK_TWTAR_FULL */
  select twtar.* 
  from school_class_common_forms sccf 
  join school_class_test_sessions scts
    on scts.school_class_id = sccf.school_class_id
  join test_window_td_alloc_rules twtar 
    on sccf.twtdar_id = twtar.id 
  where scts.test_session_id = :test_session_id
    and sccf.type_slug = :slug
    and sccf.is_revoked != 1
`

export const SQL_CLASS_TWTAR_AVAIL = (options:{isScoreEntry:boolean, isSampleSchool:boolean, hasSpecificIds:boolean}) => {

  let query = ` /* SQL_CLASS_TWTAR_AVAIL */
    select twtdar.*
    from test_window_td_alloc_rules twtdar
    where twtdar.test_window_id = :test_window_id
      and twtdar.order = :twtdar_order
  `

  let where_slug = 'and twtdar.slug = :slug';
  // todo: all contexts should be using type_slug,
  if (options.isScoreEntry){
    where_slug = 'and twtdar.type_slug = :slug';
  }
  query += '\n'+where_slug

  let chunk_where_active = '(twtdar.is_active = 1)';
  if (options.isSampleSchool){
    chunk_where_active = '(twtdar.is_active = 1 or twtdar.is_active_for_qa = 1)';
  }
  let where_active_or_specific = 'and '+chunk_where_active
  if (options.hasSpecificIds){
    where_active_or_specific = `and (
      (twtdar.id in (:locked_twtdar_ids)) or ${chunk_where_active}
    )`
  }

  query += '\n'+where_active_or_specific

  return query;
}



export const SQL_ATTEMPT_TS_TWTAR_TW_META = ` -- SQL_ATTEMPT_TS_TWTAR_TW_META
    SELECT ta.id AS id
         , ta.uid AS uid
         , ta.test_session_id AS test_session_id
         , ta.lang AS lang
         , ta.booking_lang AS booking_lang
         , ta.section_index AS section_index
         , ta.question_index AS question_index
         , ta.attempt_key AS attempt_key
         , ta.test_form_cache AS test_form_cache
         , ta.created_on AS created_on
         , ta.started_on AS started_on
         , ta.is_closed AS is_closed
         , ta.closed_on AS closed_on
         , ta.is_present AS is_present
         , ta.is_absent AS is_absent
         , ta.is_identity_verified AS is_identity_verified
         , ta.is_identity_missing AS is_identity_missing
         , ta.is_submitted AS is_submitted
         , ta.time_ext_m AS time_ext_m
         , ta.is_seb_downloaded AS is_seb_downloaded
         , ta.is_open_tether AS is_open_tether
         , ta.is_zoom_setup AS is_zoom_setup
         , ta.is_zoom_installed AS is_zoom_installed
         , ta.is_seb_app_downloaded AS is_seb_app_downloaded
         , ta.is_mic_tested AS is_mic_tested
         , ta.is_video_tested AS is_video_tested
         , ta.is_seb_setup AS is_seb_setup
         , ta.is_seb_tested AS is_seb_tested
         , ta.twtdar_order AS twtdar_order
         , ta.is_paused AS is_paused
         , ta.unpaused_on AS unpaused_on
         , ta.is_checklist_reminder_sent AS is_checklist_reminder_sent
         , ta.has_supported_device AS has_supported_device
         , ta.is_results_released AS is_results_released
         , ts.is_paused AS is_session_paused
         , ts.is_cancelled AS is_session_cancelled
         , ts.time_ext_m AS session_time_ext_m
         , ts.is_closed AS is_session_closed
         , ts.date_time_start AS date_time_start
         , twtar.test_duration
         , twtar.test_date_end
         , ts.duration_m ts_duration_m
         , twtar.is_sample
         , twtar.is_sample_time_enforced
         , (now() < twtar.hard_close_on) is_hard_closed
         , tw.hardstop_offset_h
         , ts.instit_group_id AS instit_group_id
         , ts.delivery_format AS delivery_format
         , ts.is_video_conference AS is_video_conference
         , ac.is_no_td_order AS is_no_td_order
         , tw.is_closed is_tw_closed
  FROM test_attempts ta
  JOIN test_sessions ts ON ts.id = ta.test_session_id
  JOIN test_windows tw ON tw.id = ts.test_window_id
  LEFT JOIN school_class_test_sessions scts ON scts.test_session_id = ts.id
  LEFT JOIN test_window_td_alloc_rules twtar ON twtar.id = ta.twtdar_id
  LEFT JOIN assessment_components ac ON (ac.assessment_code = scts.slug
    AND ac.test_window_id = ts.test_window_id )
  WHERE ta.id = :attempt_id
`

export const SQL_ATTEMPT_TIME_CONTROLS = `
    SELECT ta.started_on AS ta_started_on
         , ts.date_time_start AS ts_started_on
         , ta.time_ext_m AS ta_time_ext_m
         , ts.time_ext_m AS ts_time_ext_m
         , twtar.test_duration twtar_duration_m
         , ts.duration_m ts_duration_m
         , twtar.is_sample twtar_is_sample
         , twtar.is_sample_time_enforced
         , tw.hardstop_offset_h
  FROM test_attempts ta
  JOIN test_sessions ts ON ts.id = ta.test_session_id
  JOIN test_windows tw ON tw.id = ts.test_window_id
  LEFT JOIN school_class_test_sessions scts ON scts.test_session_id = ts.id
  LEFT JOIN test_window_td_alloc_rules twtar ON twtar.id = ta.twtdar_id
  WHERE ta.id = :attempt_id
`

export const SQL_STU_COVER_PAGE = ` /* SQL_STU_COVER_PAGE */
select twtdar.test_window_id
  , scts.test_session_id
  , twtdar.lang
  , twtdar.type_slug
  , twtdar.cover_page_configs AS cover_page_configs_overrides
  , tw.window_date_human
  , tw.title_persistent 
  , twtt.course_code
  , ac.course_name_full 
  , twtt.part_code
  , twtt.part_description_short 
  , twtt.cover_color 
  , twtt.resource_td_id
  , td_resource.source_item_set_id resource_item_set_id
  , twtt.is_simple_cover
  , twtt.cover_security_msg
  , twtt.cover_page_configs
from school_classes sc
join school_class_test_sessions scts 
  on sc.id = scts.school_class_id
join test_attempts ta 
  on ta.test_session_id = scts.test_session_id
join test_window_td_alloc_rules twtdar 
  on ta.twtdar_id = twtdar.id
join test_windows tw
  on tw.id = twtdar.test_window_id
join test_window_td_types twtt 
  on twtt.type_slug = twtdar.type_slug 
  and twtt.test_window_id is null
  and twtt.is_revoked = 0
left join test_designs td_resource
  on td_resource.id = twtt.resource_td_id
join test_sessions ts 
  on ts.id = scts.test_session_id 
left join assessment_courses ac
  on twtt.course_code = ac.course_code
where scts.school_class_id = :school_class_id
  and ta.uid = :uid
  and (ta.is_invalid = 0 or ta.is_invalid is null)
  and ts.is_closed = 0
  and ts.is_cancelled = 0
ORDER by ts.date_time_start asc
`

export const SQL_STU_COVER_PAGE_PREVIEW = ` /* SQL_STU_COVER_PAGE_PREVIEW */
select twtdar.test_window_id
  , -1 test_session_id -- undefined for preview
  , twtdar.lang
  , twtdar.type_slug
  , twtdar.cover_page_configs AS cover_page_configs_overrides
  , tw.window_date_human
  , tw.title_persistent 
  , twtt.course_code
  , ac.course_name_full 
  , twtt.part_code
  , twtt.part_description_short 
  , twtt.cover_color 
  , twtt.resource_td_id
  , td_resource.source_item_set_id resource_item_set_id
  , twtt.is_simple_cover
  , twtt.cover_security_msg
  , twtt.cover_page_configs
from test_window_td_alloc_rules twtdar 
join test_windows tw
  on tw.id = twtdar.test_window_id
join test_window_td_types twtt 
  on twtt.type_slug = twtdar.type_slug 
  and twtt.test_window_id is null
  and twtt.is_revoked = 0
left join test_designs td_resource
  on td_resource.id = twtt.resource_td_id
left join assessment_courses ac
  on twtt.course_code = ac.course_code
where twtdar.id = :twtar_id
group by twtdar.id 
`