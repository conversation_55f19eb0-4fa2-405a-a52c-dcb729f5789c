import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { G9DemoDataService } from '../g9-demo-data.service';
import { ClassFilterId, MySchoolService } from '../my-school.service';
import { formatDate } from '@angular/common';
import { LangService } from "../../core/lang.service";
import { AuthService } from 'src/app/api/auth.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import * as moment from 'moment-timezone';
@Component({
  selector: 'sa-test-window-filter',
  templateUrl: './sa-test-window-filter.component.html',
  styleUrls: ['./sa-test-window-filter.component.scss']
})
export class SaTestWindowFilterComponent implements OnInit {

  @Output() setTestWindowEvent = new EventEmitter();
  @Input() currentClassFilter: ClassFilterId;
  @Input() restrictToSaSignoff: boolean;
  
  currentTestWindow
  testWindows: any[] = [];

  constructor(
    private g9DemoData: G9DemoDataService,
    public lang: LangService,
    public mySchool: MySchoolService,
    public whiteLabelService: WhitelabelService,
    private auth: AuthService,
  ) { }

  ngOnInit(): void {
    //this.updateTestWindow()
    console.log('restrictToSaSignoff', this.restrictToSaSignoff, this.g9DemoData.testWindows)
  }

  ngOnChanges(){
    this.updateTestWindow()
  }

  updateTestWindow(){
    if(!this.currentTestWindow){
      this.currentTestWindow = this.mySchool.getCurrentTestWindow()
    }

    if(this.currentClassFilter){
      //Only show current class test windows 
      const filteredTestWindows = this.g9DemoData.testWindows.filter( tw => tw.type_slug === this.getTwFilter());
      this.testWindows = this.filterDuplicateObjects(filteredTestWindows);
      if (this.restrictToSaSignoff){
        this.testWindows = this.testWindows.filter(testWindow => testWindow.is_sa_signoff_required == 1)
      }
   }

   let currentTWinClass =false
    if(this.currentTestWindow){
      currentTWinClass = this.currentTestWindow.type_slug === this.getTwFilter()
    }

    console.log("  ", this.testWindows, currentTWinClass)
    if(!currentTWinClass){ 
      //Set default test window to valid test window
      const validTestWindow = this.testWindows.find(tw => new Date(tw.date_end) > new Date ())
      const newTestWindow = validTestWindow? validTestWindow:this.testWindows[0]
      this.mySchool.setCurrentTestWindow(newTestWindow);
      this.currentTestWindow = newTestWindow;

      //trigger setTestWindowEvent
      this.setTestWindowEvent.emit(newTestWindow)
    }
  } 

  filterDuplicateObjects = (array) => {
    return array.filter((value, index, array) => array.findIndex(v2 => (v2.id === value.id)) === index);
  }

  getTwFilter = () => {
    return this.currentClassFilter;
    // todo:DB_DATA_MODEL before merging this into other environments, we need to make sure this has been addressed
    // switch(this.currentClassFilter){
    //   case ClassFilterId.Primary    :  return 'EQAO_G3P'
    //   case ClassFilterId.Junior     :  return 'EQAO_G6J'
    //   case ClassFilterId.G9         :  return 'EQAO_G9M'
    //   case ClassFilterId.OSSLT      :  return 'EQAO_G10L'
    //   case ClassFilterId.TCLE       :  return 'NBED_TCLE'
    //   case ClassFilterId.TCN        :  return 'NBED_TCN'
    //   case ClassFilterId.SCIENCES8  :  return 'NBED_SCIENCES8' 
    //   case ClassFilterId.MBED_SAMPLE:  return 'MBED_SAMPLE'  
    //   case ClassFilterId.SMCS_G7_EN :  return 'SMCS_G7_EN'
    //   default                       :  return this.currentClassFilter   // ABED is aligned
    // }
  }

  onTestWindowChanged(testWindowIndex){
    this.mySchool.setCurrentTestWindow(this.testWindows[testWindowIndex]);
    this.currentTestWindow = this.testWindows[testWindowIndex];
    this.setTestWindowEvent.emit(this.testWindows[testWindowIndex])
  }

  getTestWindowViewText(tw){
    if(tw){
      const startDate = this.auth.convertUTCToWhitelabelTZ(tw.date_start).format("MMM YYYY")
      const endDate = this.auth.convertUTCToWhitelabelTZ(tw.date_end).format("MMM YYYY")
      const isActive = (new Date(tw.date_end) > new Date ())? "lbl_active":"lbl_inactive"
      let caption =  startDate+" "+this.lang.tra("lbl_date_to") +" "+endDate+" ("+this.lang.tra(isActive)+")";
      try {
        const title = JSON.parse(tw.title) || {};
        const titleStr = title[this.lang.c()] || title['en'] || '';
        if (titleStr){
          caption = titleStr;
        }
      }
      catch (e) { }
      return caption;
    }
  }

  currentTestWindowIndex(){
    if(this.currentTestWindow){
      return this.testWindows.indexOf(this.currentTestWindow)
    }
  }

  getTestWindowLabelSlug(){
    if (this.whiteLabelService.isABED()){
      return 'sa_class_test_window_lb_ABED'
    }
    return 'sa_class_test_window_lb'
  }
}
