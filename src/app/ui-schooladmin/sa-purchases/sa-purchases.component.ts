import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { PurchaseModal } from '../data/types';
import { G9DemoDataService } from '../g9-demo-data.service';
import { LangService } from 'src/app/core/lang.service';
import { RoutesService } from 'src/app/api/routes.service';
import { AuthService } from 'src/app/api/auth.service';
import { ClassFilterId, MySchoolService } from '../my-school.service';

const coldef = (field: string, headerName: string, options?) => {
  return {
    field,
    headerName,
    sortable: true,
    filter: true,
    ... (options || {})
  }
}
@Component({
  selector: 'sa-purchases',
  templateUrl: './sa-purchases.component.html',
  styleUrls: ['./sa-purchases.component.scss']
})
export class SaPurchasesComponent implements OnInit {

  @Output() onSetClassFilter = new EventEmitter();

  constructor(
    private pageModalService: PageModalService,
    private g9demoService: G9DemoDataService,
    private routes: RoutesService,
    private auth: AuthService,
    public mySchool: MySchoolService,
    public lang: LangService
  ) { }

  ClassFilterId = ClassFilterId;
  currentClassFilter: string;
  isPrivateSchool = false;
  pageModal: PageModalController;
  PurchaseModal = PurchaseModal;

  purchases: any[] = [];
  selectedPurchases:any[] = [];
  studentAttempts: any[] = [];

  purchasesDefaultColDef = {
    filter: true,
    sortable: true,
    resizable: true,
  }

  studentAttemptsDefaultColDef = {
    filter: true,
    sortable: true,
    resizable: true,
  }

  purchasesColumnDefs = [
    { field: 'invoice_number', headerName: this.lang.tra("pc_lbl_invoice_number"), width: 150, checkboxSelection: true },
    { field: 'purchase_method', headerName: this.lang.tra("pc_lbl_purchase_method"), width: 200, },
    { field: 'payment_status', headerName: this.lang.tra("pc_lbl_payment_status"), width: 150, },
    { field: 'purchase_trans_num', headerName: this.lang.tra("pc_lbl_purchase_trans_num"), width: 180, },
    { field: 'purchased_on', headerName: this.lang.tra("pc_lbl_purchased_on"), width: 180, },
    { field: 'class_name', headerName: this.lang.tra("pc_lbl_class_name"), width: 180, },
    { field: 'schl_name', headerName: this.lang.tra("pc_lbl_schl_name"), width: 180, },
    { field: 'schl_mident', headerName: this.lang.tra("pc_lbl_schl_mident"), width: 150, },
    { field: 'assessment_name', headerName: this.lang.tra("pc_lbl_assessment_name"), width: 180, },
    { field: 'admin_window', headerName: this.lang.tra("pc_lbl_admin_window"), width: 200, },
    { field: 'num_student_attempts_purchased', headerName: this.lang.tra("pc_lbl_num_stu_purchased"), width: 250, },
    { field: 'num_stu_attempt_used', headerName: this.lang.tra("pc_lbl_num_stu_attempt_used"), width: 200, },
    { field: 'purchase_price', headerName: this.lang.tra("pc_lbl_purchase_price"), width: 150, },
    { field: 'recovery_value', headerName: this.lang.tra("pc_lbl_recovery_value"), width: 150, },
  ];

  studentAttemptsColumnDefs = [
    { field: 'purchase_order_num', headerName: this.lang.tra("pc_lbl_purchase_order_num"), width: 150, checkboxSelection: true },
    { field: 'purchase_method', headerName: this.lang.tra("pc_lbl_purchase_method"), width: 200, },
    { field: 'payment_status', headerName: this.lang.tra("pc_lbl_payment_status"), width: 200, },
    { field: 'purchased_student_attempt_id', headerName: this.lang.tra("pc_lbl_purchased_student_attempt_id"), width: 300, },
    { field: 'purchased_on', headerName: this.lang.tra("pc_lbl_purchased_on"), width: 200, },
    { field: 'name_of_assessment', headerName: this.lang.tra("pc_lbl_name_of_assessment"), width: 250, },
    { field: 'admin_window', headerName: this.lang.tra("pc_lbl_admin_window"), width: 250, },
    { field: 'test_session_id', headerName: this.lang.tra("pc_lbl_test_session_id"), width: 300, },
    { field: 'student_uid', headerName: this.lang.tra("pc_lbl_student_uid"), width: 200, },
    { field: 'student_oen', headerName: this.lang.tra("pc_lbl_student_oen"), width: 200, },
    { field: 'student_first_initial', headerName: this.lang.tra("sp_lbl_student_first_name"), width: 300, },
    { field: 'student_last_name', headerName: this.lang.tra("pc_lbl_student_last_name"), width: 300, },
    { field: 'used_on', headerName: this.lang.tra("pc_lbl_used_on"), width: 200, },
    { field: 'assessment_accessed', headerName: this.lang.tra("pc_lbl_assessment_accessed"), width: 400, },
  ];

  ngOnInit(): void {
    this.isPrivateSchool = this.g9demoService.schoolData["is_private"];
    this.pageModal = this.pageModalService.defineNewPageModal();

    this.loadPurchases();
  }

  purchasesGridApi;
  purchasesGridColumnApi;
  async onPurchasesGridReady(params) {
    this.purchasesGridApi = params.api;
    this.purchasesGridColumnApi = params.columnApi;
    params.columnApi.autoSizeColumns();

    // params.columnApi.autoSizeAllColumns();
  }

  studentAttemptsGridApi;
  studentAttemptsGridColumnApi;
  async onStudentAttemptsGridReady(params) {
    this.studentAttemptsGridApi = params.api;
    this.studentAttemptsGridColumnApi = params.columnApi;
    params.columnApi.autoSizeColumns();

    // params.columnApi.autoSizeAllColumns();

  }

  translatePaymentStatus(sessions) {
    sessions.forEach(session => {
      session.payment_status = this.lang.tra(session.payment_status);
      if(session.is_refunded) session.payment_status = this.lang.tra("pc_lbl_refund");
    })
  }

  processStudentAttempts() {
    this.translatePaymentStatus(this.studentAttempts);
  }

  processPurchases() {
    this.translatePaymentStatus(this.purchases);
  }

  async loadPurchases() {
    this.auth.apiFind(
      this.routes.SCHOOL_ADMIN_PAYMENT,
      this.configureQueryParams()
    ).then(result=>{
      // if(result.length > 0){     
      //   result = result.map(attempts => {
      //     let firstInitial = attempts.student_first_initial;
      //     attempts.student_first_initial = (firstInitial != null && firstInitial != undefined) ? firstInitial.substring(0, 1).toUpperCase() : ""
      //     return {
      //       ...attempts
      //     }
      //   })
      // }
      this.studentAttempts = result[0].studentAttempts;
      this.processStudentAttempts()
      this.purchases = result[0].testSessionPurchases;
      this.processPurchases();
    })
  }

  async onPurchasesRowClick(params) {
    if(params){
      const selectedRow = params.rowIndex;
      const index = this.selectedPurchases.findIndex(purchase => purchase.rowIndex == selectedRow);
      if(index < 0){
        this.selectedPurchases.push({
          invoice_number: params.data.invoice_number,
          rowIndex: params.rowIndex
        })
      } else {
        this.selectedPurchases.splice(index, 1);
      }
    }
  }

  async onStudentAttemptsRowClick(params) {
    console.log('Student Attempts row clicked!');
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }
  
  setClassFilter(filterId) {
    this.onSetClassFilter.emit(filterId);

    const { payment_req_g9, payment_req_osslt, payment_req_pj } = this.g9demoService.schoolData;
    this.currentClassFilter = filterId;
    if (this.currentClassFilter) {
      if (this.currentClassFilter === ClassFilterId.OSSLT) {
        // payment_req_osslt ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
        // payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.G9) {
        // payment_req_g9 ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
    }
  }

  setTestWindowFilter(e) {

  }

  showRefundPolicy() {
    const config = {};
    this.pageModal.newModal({
      type: PurchaseModal.PURCHASE_REFUND_POLICY,
      config,
      finish: () => { }
    })
  }

  noInvoiceSelected(){
    return this.selectedPurchases.length < 1;
  }

  viewInvoiceModalStart() {
    // Loop through this.purchases and find all records match each invoice number of this.selectedPurchases
    const purchasesByInvoiceNumber = [];
    const studentsByInvoiceNumber = [];
    this.selectedPurchases.forEach(purchase => {
      const invoiceNumberString = purchase.invoice_number.toString();
      const allInvoicePurchases = this.purchases.filter(p => p.invoice_number === purchase.invoice_number);
      const allPaidStudents = this.studentAttempts.filter(s => s.purchase_order_num === purchase.invoice_number);
      const invoicePurchases = purchasesByInvoiceNumber[invoiceNumberString];
      const studentPurchases = studentsByInvoiceNumber[invoiceNumberString];
      if (invoicePurchases === null || invoicePurchases === undefined) {
        purchasesByInvoiceNumber[invoiceNumberString] = [...allInvoicePurchases];
      }
      if (studentPurchases === null || studentPurchases === undefined) {
        studentsByInvoiceNumber[invoiceNumberString] = [...allPaidStudents];
      }
    });
    
    
    const config = { 
      purchasesByInvoiceNumber,
      studentsByInvoiceNumber,
    }; 
    this.pageModal.newModal({
      type: PurchaseModal.VIEW_INVOICE,
      config,
      finish: this.viewInvoiceModalFinish
    })
  }

  viewInvoiceModalFinish = () => {
    this.pageModal.closeModal();
  }

  purchasesGridOptions: any = {};
  exportPurchases() {
    this.purchasesGridOptions.api.exportDataAsCsv();
  }

  studentAttemptsGridOptions: any = {};
  exportStudentAttempts() {
    this.studentAttemptsGridOptions.api.exportDataAsCsv();
  }

  getClassFilterToggles() {
    const { payment_req_g9, payment_req_osslt, payment_req_pj } = this.g9demoService.schoolData;
    return this.mySchool.getClassFilterToggles().filter(toggle => {
      if (payment_req_g9 && toggle.id == ClassFilterId.G9) {
        return toggle;
      }
      else if (payment_req_osslt && toggle.id == ClassFilterId.OSSLT) {
        return toggle;
      }
      else if (payment_req_pj && (toggle.id == ClassFilterId.Primary || toggle.id == ClassFilterId.Junior)) {
        return toggle;
      }
    });
  }

  getPrivateSchoolClassFilterToggles() {
    const { payment_req_g9, payment_req_osslt, payment_req_pj } = this.g9demoService.schoolData;
    return this.mySchool.getPrivateSchoolClassFilterToggles().filter(toggle => {
      if (payment_req_g9 && toggle.id == ClassFilterId.G9) {
        return toggle;
      }
      else if (payment_req_osslt && toggle.id == ClassFilterId.OSSLT) {
        return toggle;
      }
      else if (payment_req_pj && (toggle.id == ClassFilterId.Primary || toggle.id == ClassFilterId.Junior)) {
        return toggle;
      }
    });
  }

  configureQueryParams(){
    return {
      query: {
        schl_group_id: this.g9demoService.schoolData.group_id
      }
    }
  }
}
