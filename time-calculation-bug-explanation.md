# Time Calculation Bug: Student Session End Times

## What Was the Problem?

When students take online assessments, the system needs to calculate when their test session should end. This involves two important time limits:

1. **Regular Time Limit**: Based on how long the test is supposed to take (plus any extra time given)
2. **Hard Stop Time**: An absolute deadline when the test must end, regardless of when it started

## The Bug

The system was calculating these two time limits incorrectly. Here's what was happening:

### Step-by-Step Breakdown

1. **Starting Point**: The system correctly identified when the student's session began
2. **First Calculation**: It calculated the regular time limit by adding the test duration to the start time
3. **Second Calculation**: It tried to calculate the hard stop time by adding the hard stop hours to the start time

**The Problem**: When doing the first calculation, the system accidentally changed the original start time. So when it did the second calculation, it wasn't using the real start time anymore - it was using the already-modified time from step 2.

### Real-World Example

Imagine a student starts a test at **2:00 PM**:

- **Test Duration**: 2 hours
- **Hard Stop Deadline**: 4 hours from start

**What Should Happen**:
- Regular end time: 2:00 PM + 2 hours = **4:00 PM**
- Hard stop time: 2:00 PM + 4 hours = **6:00 PM**
- Final end time: Earlier of the two = **4:00 PM**

**What Was Actually Happening**:
- Regular end time: 2:00 PM + 2 hours = **4:00 PM** ✓
- Hard stop time: 4:00 PM + 4 hours = **8:00 PM** ❌ (using wrong start time!)
- Final end time: Earlier of the two = **4:00 PM** (accidentally correct in this case)

## Impact on Students

This bug could cause:

- **Incorrect session end times** displayed to students
- **Confusion about how much time is remaining**
- **Potential unfair advantages or disadvantages** depending on the specific timing
- **Inconsistent behavior** across different test configurations

## The Fix

The solution was simple but important: make sure each calculation uses the original, unchanged start time. Think of it like making photocopies - instead of making a copy of a copy (which can degrade), we always go back to the original document.

## Why This Matters

Accurate time calculations are crucial for:
- **Fair testing conditions** for all students
- **Proper enforcement of time limits**
- **Clear communication** about remaining time
- **Consistent user experience** across the platform

This type of bug demonstrates why careful attention to detail is essential in educational software, where precision and fairness directly impact student outcomes.
