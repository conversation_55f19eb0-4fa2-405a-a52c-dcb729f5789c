import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';

@Component({
  selector: 'panel-sb-s-accounts',
  templateUrl: './panel-sb-s-accounts.component.html',
  styleUrls: ['./panel-sb-s-accounts.component.scss']
})
export class PanelSbSAccountsComponent implements OnInit {

  @Input() schoolDistrictGroupId: number;

  records?:any[]
  gridOptions:any = {
    rowSelection: 'single',
    columnDefs: [
      { headerName:'School_Code', field:'s_code', checkboxSelection:true,  width:200 }, // 
      { headerName:'School_Name', field:'s_name',  width:300 }, // 
      { headerName:'Lang', field:'lang',  width:100 }, //
      { 
        headerName:'Status', field:'is_active', width:120,
        cellRenderer: (params) => params.value ? 'Active' : 'Inactive',
        filterValueGetter: (params) => params.data.is_active ? 'Active' : 'Inactive'
      },
      { headerName:'School_Administrators', field:'emails',  width:500 }, // 
    ],
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
  };


  constructor(
    private auth: AuthService,
  ) { }

  ngOnInit(): void {
    this.loadRecords()
  }

  async loadRecords(){
    this.records = await this.auth.apiFind('public/dist-admin/school', this.configureQueryParams())
  }

  exportCsv(){
    this.gridOptions.api.exportDataAsCsv();
  }

  configureQueryParams(){
    return {
      query: {
        schl_dist_group_id: this.schoolDistrictGroupId,
      }
    }
  }

}
