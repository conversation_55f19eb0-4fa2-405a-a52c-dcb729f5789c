import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { WidgetAssessmentStructuresComponent } from './widget-rp-assessment-structures.component';

describe('WidgetAssessmentStructuresComponent', () => {
  let component: WidgetAssessmentStructuresComponent;
  let fixture: ComponentFixture<WidgetAssessmentStructuresComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ WidgetAssessmentStructuresComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(WidgetAssessmentStructuresComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
