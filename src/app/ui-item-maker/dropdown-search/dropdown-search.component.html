<!-- <select (change)="selectItem($event.target.value)">
  <option *ngFor="let item of list" [value]="item.id">
    {{ item.caption }}
  </option>
</select> -->
<div class="dropdown-search">
  <input 
    type="text" 
    (input)="filterList($event.target.value)" 
    (focus)="onFocus($event.target.value)"
    (blur)="onBlur()"
    placeholder="Search..."
  />
  <div 
    *ngIf="isShowDropdown" 
    class="dropdown-item-container" 
    (mouseenter)="setMouseHoverState(true)"
    (mouseleave)="setMouseHoverState(false)"
  >
    <div *ngFor="let item of filteredList" (click)="selectItem(item.id)" class="dropdown-item">
      {{ item.caption }}
    </div>
  </div>
</div>
