// Initializes the `public/student/walk-in` service on path `/public/student/walk-in`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { WalkIn } from './walk-in.class';
import hooks from './walk-in.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/walk-in': WalkIn & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/walk-in', new WalkIn(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/walk-in');

  service.hooks(hooks);
}
