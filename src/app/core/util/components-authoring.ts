import { SimpleChanges } from "@angular/core";
import { IAuthGroupOption } from "src/app/ui-item-maker/view-im-group-access/model/types";
import { IColDef } from "../types/data-grid";
import { RowNodeEvent } from "ag-grid-community/dist/lib/entities/rowNode";
import { objDeepClone } from "./obj";

const IS_ALLOW_CREATION_KEY_EDIT = true

export interface IViewAuthGroupProfileLoader {
    authGroupOptions: IAuthGroupOption[]
    profiles: any[];
    selectedAuthoringGroupId: number;
    selectedProfileId: number | string;
    isLoading:boolean;
    isLoadingError:boolean;
    isLoaded:boolean;
    currentAuthGroupHash:string;
    newProfile:any;
    profileEdit:any;
    isPreserveFormAfterCreation:boolean;
    isMultiEdit:boolean;
    reloadProfiles:() => any;
}

export interface IAgGridHolder {
    assetName: string,
    gridOptions: any //  {columnDefs: any[], api:any}
}

export const authGroupAsmtProfileReload = async (self:IViewAuthGroupProfileLoader, serviceFind:() => any) => {
    self.isLoading = true;
    self.isLoaded = false;
    self.isLoadingError = false;
    try {
      // Load profiles
      const result = await serviceFind();
      self.profiles = result || [];
      self.isLoaded = true;
    } catch (e) {
      self.isLoadingError = true;
    }
    self.isLoading = false;
}

export const isMultiAuthGroupScope = (self:IViewAuthGroupProfileLoader) => {
    return self.authGroupOptions.length > 1
}

export const onAuthGroupSelection = (self:IViewAuthGroupProfileLoader) => {
    if (self.authGroupOptions && self.authGroupOptions.length > 0) {
        // Auto-select if only one group is available
        if ( ! isMultiAuthGroupScope(self) ) {
            self.selectedAuthoringGroupId = self.authGroupOptions[0].group_id;
        }
        self.reloadProfiles();
      }
}

export const onAuthGroupOptionsChange = (self:IViewAuthGroupProfileLoader, changes: SimpleChanges) => {
    if (changes.authGroupOptions) {
        if (self.authGroupOptions && self.authGroupOptions.length){
          const authGroupHash = self.authGroupOptions.map(r => r.group_id).join();
          if (self.currentAuthGroupHash !== authGroupHash) {
            self.currentAuthGroupHash = authGroupHash;
            onAuthGroupSelection(self);
          }
        }
        else {
          self.currentAuthGroupHash = null
          self.isLoaded = false;
          self.isLoading = false;
          self.isLoadingError = false;
        }
      }
}

export const applyInputSlugConstraints = (self:IViewAuthGroupProfileLoader, fields:string[]) => {
    const form = self.newProfile
    for (let field of fields){
        if (form[field]){
            form[field] = form[field].replace(/[^a-zA-Z0-9_]/g, '');
        }
    }
}

interface IOptionsCreateNewAsmtProfile {
    requiredFields: string[], // to deprecate
    fields?: IColDef[],
    create: (payload:any) => Promise<any>
}
interface IOptionsPatchAsmtProfile {
    fields?: IColDef[],
    patch: (id, payload:any) => Promise<any> // todo: should be passing along the payload and current record as well
}

export const patchAsmtProfile = async (self:(IViewAuthGroupProfileLoader & IAgGridHolder), config:IOptionsPatchAsmtProfile) => {
    const persistedRecord = asmtProfileRecordCurrent(self);
    // todo: we should be sending along the original value that was pulled so that we can check for delta before writing in the new record
    const form = self.profileEdit;
    const recordId = form.id;
    // todo: should we be more explicit about the nullification of fields
    const payload:any = {
    };
    if (config.fields){
        for (let field of config.fields){
            if (!field.isNotEditable && (!field.isCreationKey || IS_ALLOW_CREATION_KEY_EDIT)){
                payload[field.field] = form[field.field]
            }
        }
    }

    try {
        const patched = await config.patch(recordId, payload);
        for (let prop in payload){
            persistedRecord[prop] = payload[prop]
        }
        refreshSelectedGridRowData(self);
        if (!self.isPreserveFormAfterCreation){
            self.newProfile = {};
        }
    } catch (e) {
        alert('Failed to create record. ' + e.message);
    }

}

export const refreshSelectedGridRowData = (self:IAgGridHolder) => {
    const selectedNodes = self.gridOptions.api.getSelectedNodes();
    self.gridOptions.api.refreshCells({
        rowNodes: selectedNodes, // Specify which nodes to refresh
        force: true, // Optional: Force full refresh
    });
}

export const createNewAsmtProfile = async (self:IViewAuthGroupProfileLoader, config:IOptionsCreateNewAsmtProfile) => {
    if (!self.selectedAuthoringGroupId) {
      alert('Select an authoring group to continue');
      throw new Error();
    }

    const isOnlyApplyingRequired = (!config.fields); // todo: fallback if we are not feeding in the fields... this should be deprecated

    const payload:any = {
        authoring_group_id: self.selectedAuthoringGroupId,
    };

    const form = self.newProfile;

    if (config.requiredFields){
        for (let fieldProp of config.requiredFields){
            if (!self.newProfile[fieldProp]) {
              alert('Missing Field: '+fieldProp);
              throw new Error();
            }
            if (isOnlyApplyingRequired){
                payload[fieldProp] = form[fieldProp]
            }
        }
    }

    if (config.fields){
        for (let field of config.fields){
            if (!field.isNotEditable || field.isCreationKey){
                payload[field.field] = form[field.field]
            }
        }
    }

    try {
        const created = await config.create(payload);
        self.profiles.push(created);
        if (!self.isPreserveFormAfterCreation){
            self.newProfile = {};
        }
    } catch (e) {
        alert('Failed to create record. ' + e.message);
    }
}

export const asmtProfileProfileIdKey = (self:IViewAuthGroupProfileLoader) => {
    return 'id'; // todo: allow for overrides?
}

export const asmtProfileRecordCurrent = (self:IViewAuthGroupProfileLoader) => {
    const profileIdKey = asmtProfileProfileIdKey(self)
    const record = self.profiles.find(r => r[profileIdKey] == self.selectedProfileId)
    return record;
}

export const asmtProfileRecordEditStart = (self:IViewAuthGroupProfileLoader) => {
    const record = asmtProfileRecordCurrent(self);
    if (record){
        self.profileEdit = objDeepClone(record);
    }
    else {
        self.profileEdit = null;
    }
}

export const initGridOptions = (self:(IAgGridHolder & IViewAuthGroupProfileLoader), cols:IColDef[]):any => {
    const columnDefs = cols.map(col => {
        return <any> {
            headerName: col.caption,
            field: col.field,
            // editable: !col.isNotEditable,
        }
    })
    columnDefs[0].checkboxSelection = true

    const exportCsv = () => {
        const timestamp = new Date().toISOString().split('T')[0];
        self.gridOptions.api.exportDataAsCsv(self.assetName+'-'+timestamp+'.csv');
    }
    const onRowSelected = ($event: RowNodeEvent) => {
        const selectedRows = self.gridOptions.api.getSelectedRows();
        const record = selectedRows[0]
        self.profileEdit = null;
        if (record){ // selectedRows.length > 0
            self.selectedProfileId = record.id;
            if (self.isMultiEdit){
                asmtProfileRecordEditStart(self);
            }
        }
        else {
            self.selectedProfileId = null;
        }
    }
    self.gridOptions = {
        columnDefs,
        defaultColDef: {
          filter: true,
          sortable: true,
          resizable: true,
          // editable: true,
        },
        exportCsv,
        onRowSelected,
    }
}
