<div class="page-container" [class.is-abed]="isABED()">
  <div class="header-container" [class.is-abed]="isABED()">
    <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
  </div>
  <div class="panel-container" [class.is-abed]="isABED()">
    <div class="form-card" [class.is-wide]="isInSelfRegistrationMode">
      <div class="form-heading">
        <!-- <img src="assets/icons/student_diverse_icon_2.png"> -->
        <tra slug="student_access"></tra>
      </div>
      <div class="form-container">
        <form [formGroup]="formGroup" (ngSubmit)="gotoDashboard()">
          <div class="field">
            <label class="label">
              <tra *ngIf="whiteLabelService.isABED()" slug="abed_access_code"></tra>
              <tra *ngIf="!whiteLabelService.isABED()" slug="access_code"></tra>
            </label>
            <div class="control">
              <input id="accesscode" autocomplete="username" [formControl]="formGroup.controls.accessCode" class="input is-success" type="text">
            </div>
          </div>
          <div class="field">
            <label class="label">
              <tra [slug]="getStudentIdentificationNumber()"></tra>
            </label>
            <div class="control">
              <input id="sin" autocomplete="username" [formControl]="formGroup.controls.userId" class="input is-success" type="text">
            </div>
          </div>
          <div *ngIf="isDobReq()" class="field">
            <label class="label">
              <tra slug="abed_dob_lbl"></tra>
            </label>
            <div class="control">
              <dob-input id="dob" autocomplete="bday" [formControlModel]="formGroup.controls.date_of_birth"> </dob-input>
            </div>
            <!-- <div class="control">
              <input [formControl]="formGroup.controls.date_of_birth" class="input is-success" type="date">
            </div> -->
          </div>
          <div class="control" *ngIf="!isInSelfRegistrationMode">
            <input 
              type="submit"
              value={{signInText}}
              id="signIn"
              class="button is-link is-fullwidth"
              [disabled]="isLoggingIn"
            />
            <!-- <button (click)="gotoDashboard()"  class="button is-link is-fullwidth">Sign In</button> -->
          </div>
        </form>
        <div *ngIf="isInSelfRegistrationMode">
          <div style="margin:1em">
            <!-- todo: css styling, use more specific classes for the other buttons so that I don't have to override to get the default color https://bubo.vretta.com/vea/project-management/vretta-project-notes/vea-abed/-/issues/2532 --> 
            <button (click)="selfRegCancel()" class="button is-warning" style="background-color: #ffdb4a;">
              Re-enter ASN
            </button>
          </div>
          <div class="notification is-small">
            <!-- todo:TRANS -->
            You are entering a practice written response test session. Please enter your name so that your teacher can identify you. (Note that this will be different when you login to write a Diploma Exam or a Provincial Achievement Test. For these assessments you will need to enter your ASN and DOB which must match what is in PASI.)
          </div>
          <div *ngIf="whiteLabelService.isABED()" class="field">
            <label class="label">
              <!-- todo:TRANS -->
              Name
            </label>
            <div class="control" style="    display: flex;">
               <!-- todo:TRANS -->
              <input [(ngModel)]="selfRegForm.first_name" placeholder="First Name" class="input is-success" type="text">
              <input [(ngModel)]="selfRegForm.last_name"  placeholder="Last Name" class="input is-success" type="text">
            </div>
          </div>
          <div class="control">
            <button 
              class="button is-link is-fullwidth" 
              [disabled]="isLoggingIn || !(selfRegForm.first_name || selfRegForm.last_name)"
              (click)="selfRegSubmit()"
            >
              <!-- todo:TRANS -->
              Join Class
            </button>
            <!-- <button (click)="gotoDashboard()"  class="button is-link is-fullwidth">Sign In</button> -->
          </div>
        </div>
      </div>
    </div>
    <ng-container *ngIf="!isSimpleTestTaker">
      <div *wlCtx="'IS_LOGIN_AGR'" class="post-agreement-container">
        <br>
        <p class="sub-header">
          <tra slug="abed_usage_agreement_priv_header"></tra>
        </p><br>
        <tra-md [isCondensed]="true" slug="abed_usage_agreement_priv_body"></tra-md>
        <p class="agreement-footer"><tra-md [isCondensed]="true" slug="abed_usage_agreement_priv_footer"></tra-md>
        </p>
      </div>
    </ng-container>
  </div>
  <div class="troubleshooting-container">
    <button class="button troubleshoot-btn" (click)="goToTroubleshootAssesssment()" [title] = "lang.tra('stu_btn_troubleshooting_desc')"><tra slug="stu_btn_troubleshooting"></tra></button>
  </div>
</div>
<div class="custom-modal" *ngIf="agreementPopUp">
  <div class="modal-contents">
      <span class="main-header"><tra-md [isCondensed]="true" slug="abed_usage_agreement_header"></tra-md></span>
      <tra-md [isCondensed]="true" slug="abed_usage_agreement_body"></tra-md>
      <p class="sub-header agreement-footer small"><tra slug="abed_usage_agreement_footer"></tra></p>
      <div style="margin-top: 1.5em;">
        <button class="button" (click)="handleIDoNotAgree()"><tra slug="abed_usage_agreement_reject"></tra></button>
        <button class="button is-info" (click)="handleProceedAgreement()"><tra slug="abed_usage_agreement_accept"></tra></button>
      </div>
    </div>
</div>