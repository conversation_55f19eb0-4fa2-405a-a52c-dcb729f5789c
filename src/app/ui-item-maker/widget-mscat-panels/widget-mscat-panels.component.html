<div>
    <div class="pre-table-strip">
      <div>
          <button 
          class="button  has-icon" 
          (click)="previewCtrl.previewMsCat();" 
          >
          <span class="icon"><i class="fa fa-desktop" aria-hidden="true"></i> </span>
          <span>Sample as Test Taker</span>
        </button>
        <button
          class="button has-icon"
          (click)="toggleItemExposure()"
          [class.is-info]="isItemExposureActive()"
        >
          <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
          <span>Item Exposure</span>
        </button>
      </div>
      <div>
        <button 
        [disabled]="isReadOnly()"
        class="button is-small has-icon" 
        (click)="panelCtrl.createNewPanels(frameworkCtrl.asmtFmrk)"
        >
        <span class="icon"><i class="fa fa-refresh" aria-hidden="true"></i> </span>
        <span>Create New Panels (Adv)</span>
      </button>
    </div>
  </div>

  <div *ngIf="isItemExposureActive()">
    <table style="width:auto">
      <tr>
        <th>Item ID</th>
        <th>Label</th>
        <th>Panel Presence</th>
        <th>Est. Exposure</th>
        <th>Panels</th>
      </tr>
      <tr *ngFor="let item of itemStats">
        <td>{{item.item_id}}</td>
        <td>{{item.label}}</td> 
        <td>{{item.panelPresence}}</td> 
        <td>{{formatPerc(item.estimatedExposure)}}</td>
        <td>
          <div class="tags">
            <span 
              *ngFor="let panelModule of item.panelModules"
              class="tag is-dark"
            >{{panelModule}}</span>
          </div>
        </td>
      </tr>
    </table>
    <!-- <button (click)="downloadItemExposureCSV()">Download CSV</button> -->
  </div>

  <div *ngIf="!isItemExposureActive()" style="display:flex; flex-direction: row;">
    <!-- class="space-between" -->
    <table *ngIf="frameworkCtrl.asmtFmrk.panels" style="margin-bottom:2em; width:auto;">
      <tr>
        <th>Panel</th>
        <th>Created On</th>
        <th>Items Mapped</th>
        <th *ngIf="!isSplitView()">Preview</th>
        <th *ngIf="!isSplitView()">Disable</th>
        <th *ngIf="!isSplitView()">Remove</th>
      </tr>
      <tr *ngFor="let panel of frameworkCtrl.asmtFmrk.panels">
        <td>{{panel.id}}</td>
        <td>{{panel.dateCreated}}</td>
        <td> 
          <button 
            (click)="inspectPanel(panel)" 
            class="button is-small has-icon" 
            [class.is-info]="inspectedPanel && inspectedPanel.id===panel.id"
          >
            <span class="icon"><i class="fa fa-crosshairs" aria-hidden="true"></i> </span>
            <span>Edit</span>
          </button>
          <ng-container *ngIf="!isSplitView()">
            <button 
              (click)="panelCtrl.activatePanelQuestions(panel.id)" 
              class="button is-small has-icon" 
              [class.is-info]="panelCtrl.activePanel && panelCtrl.activePanel.id===panel.id && !panelCtrl.activePanel.moduleId"
            >
              <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
              <span>List All</span>
            </button> 
            &rarr;
            <button 
              *ngFor="let module of panel.modules"
              (click)="panelCtrl.activatePanelQuestions(panel.id, module.moduleId)" 
              class="button is-small has-icon" 
              [class.is-info]="panelCtrl.activePanel && panelCtrl.activePanel.id===panel.id && panelCtrl.activePanel.moduleId===module.moduleId"
            >
              <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
              <span>Module {{module.moduleId}}</span>
            </button> 
          </ng-container>
        </td>
        <td *ngIf="!isSplitView()">
          <button (click)="previewCtrl.previewMsCat(panel)" class="button is-small has-icon">
            <span class="icon"><i class="fa fa-desktop" aria-hidden="true"></i> </span>
            <span>Preview</span>
          </button>
        </td>
        <td *ngIf="!isSplitView()">
          <mat-slide-toggle 
            [(ngModel)]="panel.isDisabled" 
            color="danger"
          >
            Disable
          </mat-slide-toggle>
        </td>
        <td *ngIf="!isSplitView()">
          <button (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.panels, panel)" class="button is-small is-danger has-icon">
            <span class="icon"><i class="fa fa-archive" aria-hidden="true"></i> </span>
            <span>Delete</span>
          </button>
        </td>
      </tr>
    </table>
    <div *ngIf="isSplitView()" style="border-left:1px solid #000; ">
      <div style="display:flex; flex-direction:row; align-items:center; padding:0.5em; font-size:1.2em;">
        <strong>Panel {{inspectedPanel.id}}</strong>
        <!-- <button class="button is-small" [disabled]="true">Rename</button> -->
        <button class="button is-small" (click)="duplicateInspectedPanel()">Duplicate</button>
      </div>
      <div style="max-height:70vh; overflow: auto;">
        <div *ngFor="let module of inspectedPanel.modules" style="padding-left:0.5em; border-top:1px solid #333;">
          <div style="padding:0.5em;" class="space-between">
            <div style="display:flex; flex-direction:row; align-items:center;">
              <button (click)="toggleModuleExpand(module)" class="button is-small" style="margin-right:1em;" [ngSwitch]="isModuleExpanded(module)">
                <i *ngSwitchCase="false" class="fa fa-chevron-right" aria-hidden="true"></i>
                <i *ngSwitchCase="true" class="fa fa-chevron-down" aria-hidden="true"></i>
              </button>
              <span style="margin-right:2em">
                <strong>Module {{module.moduleId}} | </strong>
                <!-- <span style="margin-left:1em;">{{module.name || '---'}}</span> -->
                <span style="margin-left:1em;">({{module.itemLabels.length}} items)</span>
              </span>
              <!-- <button class="button is-small" [disabled]="true">Change ID</button> -->
              <!-- <button class="button is-small" [disabled]="true">Rename</button> -->
              <button class="button is-small" *ngIf="!module.posOverrides" (click)="module.posOverrides = {}">Activate Position Overrides</button>
              <button (click)="addItemToModule(inspectedPanel, module)" class="button is-small is-inverted is-success">Add Item</button>
            </div>
          </div>
          <div *ngIf="isModuleExpanded(module)" style="padding:0.5em; padding-left:2em; border-top:1px solid #ccc;">
            <p *ngIf="module.posOverrides" >Use the input boxes to set a fixed position for the items in the form (starting with 1, then 2,3,4, etc.). To randomize position of 2 or more items being positioned at the start of the form, simply assign them to the same number.</p>
            <div *ngFor="let itemLabel of module.itemLabels; let index=index;" style="margin:0.2em; background-color: #f1f1f1; border-radius: 0.2em; padding:0.2em; " class="space-between">
              <span>
                <span class="tag is-dark">
                  {{getModuleItemId(module, index)}}
                </span>
                <span>
                  {{itemLabel}}
                </span>
              </span>
              <span>
                <span style="margin-right:0.5em;">
                  <input 
                    *ngIf="module.posOverrides" 
                    [(ngModel)]="module.posOverrides[getModuleItemId(module, index)]"
                    type="number" 
                    placeholder="Position Override"
                  >
                </span>
                <span style="margin-right:3em;">
                  <button 
                    class="button is-small is-info" 
                    [class.is-inverted]="!isItemExclRouting(module, index)"
                    (click)="toggleItemExclRouting(module, index)"
                  >
                    {{'Exclude'}} from Routing
                  </button>
                </span>
                <button class="button is-small" [disabled]="true">Preview</button>
                <button (click)="swapModuleItem(inspectedPanel, module, index)" class="button is-small">Swap</button>
                <button class="button is-small is-inverted is-danger" (click)="removeModuleItem(module, index)">Remove</button>
              </span>
            </div>
          </div>
        </div>
      </div>
      
    </div>
    <div>

    </div>
  </div>
</div>