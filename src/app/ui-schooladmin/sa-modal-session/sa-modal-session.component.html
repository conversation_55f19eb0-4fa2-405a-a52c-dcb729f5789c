<div *ngIf="isSelectingSession">
    <div><tra slug="select_session_edit"></tra></div>
    <div><tra slug="txt_ongoing_sessions"></tra></div>
    <ul>
        <li *ngFor="let session of sessions">
            <button class="button" (click)="selectSession(session)" *ngIf="session.status === 'pending'">
                {{session.startTime}}
            </button>
            <div  *ngIf="session.status === 'active'">
                {{session.startTime}} {{lang.tra('sa_ongoing')}}
            </div>
        </li>
    </ul>
</div>
<div *ngIf="!isSelectingSession && formEntriesList && (!isActive || isPartialActive)">
    <sa-widget-form-entry
        *ngFor="let entry of saWidgetFormEntries()"
        [formEntries]="formEntriesList"
        [formEntry]="entry"
        (selected)="checkSelection(entry, $event)"
    ></sa-widget-form-entry>
   
    <ng-container *ngIf="showDatePicker">
        <div>
            <ng-container *ngIf="isG9||isOsslt || whitelabelService.isABED()">
                <ng-container *ngIf="showSessionA()">  
                    <div *ngIf="!removesessionB"><strong><tra slug="session_a"></tra></strong></div>
                    <div *ngIf="removesessionB"><strong>
                        <tra 
                            slug="{{getSessionHeaderSlug()}}"
                            [class.ABED-header]="whitelabelService.isABED()">
                        </tra></strong></div>
                    <div  class="times">
                        <div class="label"><tra slug="lbl_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionAStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)"></input-date>
                        </div>       
                    </div>
                    <div *ngIf="showDatePicker" class="times">
                        <!-- <tra-md slug="g9_operational_test"></tra-md> -->
                        <div class="label"><tra slug="lbl_time"></tra></div>
                        <div class="values">
                            <input-time [fc]="formEntries.sessionAStartTime.formControlRef" type="time"></input-time>
                        </div>   
                        <div class="TZAbbr">
                            {{whitelabelService.getTimezoneAbbr()}}
                        </div>        
                    </div>
                </ng-container>
                <div *ngIf="!showSessionA() && showSessionB() && !whitelabelService.isABED()">
                    <tra slug="msg_session_a_submitted"></tra>
                </div>
                <ng-container *ngIf="showSessionB()">
                    <hr/>
                    <strong><tra slug="session_b"></tra></strong>
                    <div  class="times">
                        <div class="label"><tra slug="lbl_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionBStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)"></input-date>
                        </div>        
                    </div>
                    <div *ngIf="showDatePicker" class="times">
                        <!-- <tra-md slug="g9_operational_test"></tra-md> -->
                        <div class="label"><tra slug="lbl_time"></tra></div>
                        <div class="values">
                            <input-time [fc]="formEntries.sessionBStartTime.formControlRef" type="time"></input-time>
                        </div>      
                    </div>
                </ng-container>
                <div *ngIf="!showSessionA() && !showSessionB() && !whitelabelService.isABED()">
                    <tra [slug]="getCompletedSlug()"></tra>
                </div>
            </ng-container>

            <ng-container *ngIf="isPrimary||isJunior">  
                <ng-container *ngIf="canEditDatetimePicker(datePickerType.Language) && isLanguageVisibleForFIClass()">
                    <div style="margin-bottom: 1.25em;"><strong><tra slug="pj_lang"></tra></strong></div>    <!-- Primary && Junior Session Language -->
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_start_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionLangStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>        
                    </div>
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_end_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionLangEndDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>        
                    </div>
                    <hr />
                </ng-container> 
                <ng-container *ngIf="!canEditDatetimePicker(datePickerType.Language)">
                    <div>
                        <tra slug="msg_session_language_start"></tra>
                    </div>
                    <hr />
                </ng-container>
                <!-- <ng-template #languageActive> 
                    <div>
                        <tra slug="msg_session_language_start"></tra>
                    </div>
                </ng-template> -->

                <ng-container *ngIf="canEditDatetimePicker(datePickerType.Math); else mathActive">
                    <div style="margin-bottom: 1.25em;"><strong><tra slug="pj_math"></tra></strong></div>    <!-- Primary && Junior Session Math -->
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_start_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionMathStartDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>        
                    </div>
                    <div  class="times">
                        <div class="label"><tra slug="pj_lbl_end_date"></tra></div>
                        <div class="values">
                            <input-date [fc]="formEntries.sessionMathEndDate.formControlRef" type="date" (isDateValid)="setDateValidity($event)" [isRequired]="false"></input-date>
                        </div>        
                    </div>
                </ng-container>
                <ng-template #mathActive> 
                    <div>
                        <tra slug="msg_session_math_start"></tra>
                    </div>
                </ng-template>
            </ng-container>
        </div>
    </ng-container>
</div>
<div *ngIf="isEditing && isActive && !isPartialActive">
   <tra slug="txt_ongoing_sessions_2"></tra>
</div>
