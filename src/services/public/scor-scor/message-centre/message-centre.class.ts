import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { generateS3DownloadUrl } from '../../../upload/upload.listener';

interface Data {}

interface ServiceOptions {}

export class MessageCentre implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    if(!params?.query) return []

    const recipient_uid = await currentUid(this.app, params);

    if(params.query.getDetails) {
      let data = <any> await dbRawRead(this.app, [recipient_uid], `select smcr.id as recipientRecordID, smcm.*, smcr.read, u.account_type as sender_account_type from scoring_message_centre_recipients smcr
      left join scoring_message_centre_messages smcm on smcm.id = smcr.message_id
      left join users u on u.id = smcm.sender_uid
      where smcr.recipient_uid = ? order by timestamp desc`);
      for(let rec of data) {
        if(rec.img_url) {
          rec.img_url = generateS3DownloadUrl(rec.img_url, 600, 'storage.mathproficiencytest.ca');
        }
      }

      return data;


    }
    else {
      return (await dbRawRead(this.app, [recipient_uid], `select count(*) as unread from scoring_message_centre_recipients smcr
      left join scoring_message_centre_messages smcm on smcm.id = smcr.message_id
      left join users u on u.id = smcm.sender_uid
      where smcr.recipient_uid = ? and smcr.read = 0`))[0].unread;
    } 

    
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: any, params?: Params): Promise<Data> {
   return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return await this.app.service('db/write/scoring-message-centre-recipients').patch(id, data);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
