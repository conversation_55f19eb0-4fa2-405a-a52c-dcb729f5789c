import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { LangService } from 'src/app/core/lang.service';
import { MySchoolService } from '../my-school.service';
import * as _ from 'lodash';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AuthService } from '../../api/auth.service';
import { G9DemoDataService } from '../g9-demo-data.service';
import { RoutesService } from 'src/app/api/routes.service';
import { DomSanitizer } from '@angular/platform-browser';
import { LoginGuardService } from '../../api/login-guard.service';
import { ColumnApi, GridApi } from 'ag-grid-community';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { ALT_VERSION_REQUEST_STATUS, REQUEST_STATUS_SLUGS, TestWindowType } from 'src/app/ui-alt-version-ctrl/alt-version-ctrl-req-table/types';

enum Modal {
  ALT_REQUEST = 'ALT_REQUEST',
}

@Component({
  selector: 'sa-requests',
  templateUrl: './sa-requests.component.html',
  styleUrls: ['./sa-requests.component.scss']
})

export class SaRequestsComponent implements OnInit {

  @Output() onSetClassFilter = new EventEmitter();
  @Output() unPaidStudentCurrentFilter = new EventEmitter<string>();

  currentClassFilter;

  constructor(
    private pageModalService: PageModalService,
    public schoolService:MySchoolService,
    public lang: LangService,
    public auth: AuthService,
    private routes: RoutesService,
    public mySchool: MySchoolService,
    private g9demoService: G9DemoDataService,
    private whiteLabel: WhitelabelService
  ) {}

  currentSchId = this.mySchool.getCurrentSchoolGroupId();
  currentTestWindow:any;
  isSASNSchool = this.mySchool.isSASNLogin();
  isPrivateSchool = false;
  
  // Alt Request ag-grid information
  altRequestGridColumnApi: ColumnApi;
  altRequestGridApi: GridApi;
  altRequests = []; 
  altRequestsLoading:boolean;
  altRequestsLoaded:boolean;
  altRequestsLoadFailed:boolean;
  altRequestGridcolumnDefs = [
    {width:50, checkboxSelection:true },

    //ID - Request Number
    { headerName:'ID', field:'id', width:75},

    //Student name
    { headerName: this.lang.tra('student_name_alt_version'), field:'student_name', width:175, },

    //Student OEN
    { headerName: this.lang.tra(this.getStudentIdSlug()), field:'student_id_number', width:175, },

    //Student SASN
    { headerName: this.lang.tra('sasn_alt_version'), field:'student_sasn', width:175, hide:!this.isSASNSchool},

    //Request Status
    { headerName: this.lang.tra('request_status_alt_version'), field:'status', width:175, valueGetter: this.getAltRequestSlug.bind(this)},

    // Assessment date
    { headerName: this.lang.tra('op_admin_date_alt_version'), field:'op_administration_date', width:225, cellRenderer: this.parseDate.bind(this)},

    //Date of Request
    { headerName: this.lang.tra('request_date_alt_version'), field:'created_on', width:180, cellRenderer: this.parseDate.bind(this)},

    //class name
    { headerName: this.lang.tra('class_name_alt_version'), field:'class_name', width:175, },

    //Request Format(s)
    { headerName: this.lang.tra('req_formats_alt_version'), field:'requested_formats', width:400, cellRenderer: function(params){
      // Translate the list of slugs based on the language of the request
      return params.value.split(',').map(slug => this.lang.tra(slug, params.data.lang)).join(', ')
    }.bind(this)},

    //sample links
    // { headerName: this.lang.tra('sample_links_alt_version'), width:175, cellRenderer: this.parseSampleLinks.bind(this)},

    //Assessment Links
    { headerName: this.lang.tra('assessment_links_alt_version'), width:175, cellRenderer: this.parseOperationalLinks.bind(this)},

    //Individual links are hidden but are included in the CSV export instead of the combined list of hyperlinks
    // { headerName: this.lang.tra('alt_version_access_link_pdf_regular_sample'), field:'sample_link_pdf_regular', hide: true, cellRenderer: this.renderLinkAbsolute},
    // { headerName: this.lang.tra('alt_version_access_link_pdf_large_sample'), field:'sample_link_pdf_large', hide: true, cellRenderer: this.renderLinkAbsolute},
    // { headerName: this.lang.tra('alt_version_access_link_mp3_sample'), field:'sample_link_mp3', hide: true, cellRenderer: this.renderLinkAbsolute},
    // { headerName: this.lang.tra('alt_version_access_link_asl_sample'), field:'sample_link_asl', hide: true, cellRenderer: this.renderLinkAbsolute},
    // { headerName: this.lang.tra('alt_version_access_link_ebraille_sample'), field:'sample_link_ebraille', hide: true, cellRenderer: this.renderLinkAbsolute},
    { headerName: this.lang.tra('alt_version_access_link_pdf_regular_assessment'), field:'operational_link_pdf_regular', hide: true, cellRenderer: this.renderLinkAbsolute},
    { headerName: this.lang.tra('alt_version_access_link_pdf_large_assessment'), field:'operational_link_pdf_large', hide: true, cellRenderer: this.renderLinkAbsolute},
    { headerName: this.lang.tra('alt_version_access_link_mp3_assessment'), field:'operational_link_mp3', hide: true, cellRenderer: this.renderLinkAbsolute},
    // { headerName: this.lang.tra('alt_version_access_link_asl_assessment'), field:'operational_link_asl', hide: true, cellRenderer: this.renderLinkAbsolute},
    { headerName: this.lang.tra('alt_version_access_link_ebraille_assessment'), field:'operational_link_ebraille', hide: true, cellRenderer: this.renderLinkAbsolute},
   
    //Reason
    { headerName: this.lang.tra('reason_alt_version'), field:'reason', width:175, },

    //Date Approved
    { headerName: this.lang.tra('date_approved_alt_version'), field:'approved_on', width:180, cellRenderer: this.parseDate.bind(this)},

    //teacher name
    { headerName: this.lang.tra('teacher_alt_version'), field:'teacher_name', width:175, },

    //teacher email
    { headerName: this.lang.tra('teacher_email_col_alt_version'), field:'teacher_email', width:175, },

    // Date Cancelled
    { headerName: this.lang.tra('canceled_on_alt_version'), field:'canceled_on', width:180, cellRenderer: params => {
      if(params.data.canceled_on) return this.parseDate({value: params.data.canceled_on})
      else if (params.data.rejected_on) return this.parseDate({value: params.data.rejected_on})
      else return null
    }},
    
    // // Is FI
    // { headerName: this.lang.tra('is_fi_col_alt_version'), field:'is_fi_current', width:175, valueGetter: this.formatYesNo.bind(this)},

    //  // FI Option (hidden unless Primary filter)
    // { headerName: this.lang.tra('fi_option_col_alt_version'), field:'fi_option_current', width:175},

    // Sch Mident
    { headerName: this.lang.tra(this.getSchlForeignIdSlug()), field:'schl_foreign_id', width:175, },

    //Sch Name
    { headerName: this.lang.tra('sch_name_alt_version'), field:'schl_name', width:175, },
    
    //Started test?
    { headerName: this.lang.tra('is_started_alt_version'), field:'is_started', width: 175, valueGetter: this.formatYesNo.bind(this)},

    //sch admin name
    { headerName: this.lang.tra('sch_admin_name_alt_version'), field:'schl_admin_name', width:175, },

    //sch admin email
    { headerName: this.lang.tra('sch_admin_email_col_alt_version'), field:'schl_admin_email', width:175, },

    //NON-BRAILLE sch admin phone
    { headerName: this.lang.tra('sch_admin_phone_non_braille_alt_version'), field:'schl_admin_phone_non_braille', width:175, },

    //BRAILLE sch contact
    { headerName: this.lang.tra('sch_contact_braille_alt_version'), field:'schl_contact_braille', width:175, },

     //BRAILLE sch email
    { headerName: this.lang.tra('sch_email_braille_alt_version'), field:'schl_email_braille', width:175, },

     //BRAILLE sch phone
    { headerName: this.lang.tra('sch_phone_alt_version'), field:'schl_phone_braille', width:175, },

     //BRAILLE sch address
    { headerName: this.lang.tra('sch_address_braille_alt_version'), field:'schl_address_braille', width:175, },
    
    // //Shipping Company (sample)
    // { headerName: this.lang.tra('ship_company_sample_alt_version'), field:'shipping_company_sample', width:175, },

    // //Tracking num (sample)
    // { headerName: this.lang.tra('ship_track_sample_alt_version'), field:'tracking_number_sample', width:175, cellRenderer: this.parseSampleTracking.bind(this)},

    // //Tracking URL Sample - Hidden column for export
    // { headerName: this.lang.tra('ship_track_url_sample_alt_version'), field:'tracking_url_sample', width:175, hide: true},

    //  //Delivery Date (sample)
    //  { headerName: this.lang.tra('ship_deliver_date_sample_alt_version'), field:'shipping_delivered_date_sample', width:175, },

    //Shipping Company (assessment)
    { headerName: this.lang.tra('ship_company_op_alt_version'), field:'shipping_company_operational', width:175, },

    //Tracking num (assessment)
    { headerName: this.lang.tra('ship_track_op_alt_version'), field:'tracking_number_operational', width:175, cellRenderer: this.parseOpTracking.bind(this)},

    //Tracking URL assessment - Hidden column for export
    { headerName: this.lang.tra('ship_track_url_op_alt_version'), field:'tracking_url_operational', width:175, hide: true},

    //Date Delivered to School (Assessment)
    { headerName: this.lang.tra('ship_deliver_date_op_alt_version'), field:'shipping_delivered_date_operational', width:175, },

    //Tracking num (Return)
    { headerName: this.lang.tra('ship_track_return_alt_version'), field:'tracking_number_return', width:175, cellRenderer: this.parseReturnTracking.bind(this)},

    //Tracking URL return - Hidden column for export
    { headerName: this.lang.tra('ship_track_url_return_alt_version'), field:'tracking_url_return', width:175, hide: true},
    

    //Hidden fields
    {field: 'tw_is_active', hide: true},
    { headerName: this.lang.tra('test_window_alt_version'), field:'test_window_id', width:175, hide: true},
  ];

  parseSampleLinks(params){
    let linkList ="";
    if(params.data.sample_link_pdf_regular) linkList += `<a href=${params.data.sample_link_pdf_regular} target=_blank>${this.lang.tra('alt_version_link_title_pdf_regular')}</a>, `
    if(params.data.sample_link_pdf_large) linkList += `<a href=${params.data.sample_link_pdf_large} target=_blank>${this.lang.tra('alt_version_link_title_pdf_large')}</a>, `
    if(params.data.sample_link_mp3) linkList += `<a href=${params.data.sample_link_mp3} target=_blank>${this.getMp3AccessLinkName(params)}</a>, `
    if(params.data.sample_link_asl) linkList += `<a href=${params.data.sample_link_asl} target=_blank>${this.lang.tra('alt_version_link_title_asl')}</a>, `
    if(params.data.sample_link_ebraille) linkList += `<a href=${params.data.sample_link_ebraille} target=_blank>${this.lang.tra('alt_version_link_title_ebraille')}</a>, `
    return linkList.slice(0,-2);
  }

  parseOperationalLinks(params){
    let linkList ="";
    if(params.data.operational_link_pdf_regular) linkList += `<a href=${params.data.operational_link_pdf_regular} target=_blank>${this.lang.tra('alt_version_link_title_pdf_regular')}</a>, `
    if(params.data.operational_link_pdf_large) linkList += `<a href=${params.data.operational_link_pdf_large} target=_blank>${this.lang.tra('alt_version_link_title_pdf_large')}</a>, `
    if(params.data.operational_link_mp3) linkList += `<a href=${params.data.operational_link_mp3} target=_blank>${this.getMp3AccessLinkName(params)}</a>, `
    if(params.data.operational_link_asl) linkList += `<a href=${params.data.operational_link_asl} target=_blank>${this.lang.tra('alt_version_link_title_asl')}</a>, `
    if(params.data.operational_link_ebraille) linkList += `<a href=${params.data.operational_link_ebraille} target=_blank>${this.lang.tra('alt_version_link_title_ebraille')}</a>, `
    return linkList.slice(0,-2);
  }

   /**
   * Get the wording of the mp3 access link, because in OSSLT it specifies large or regular depending on the PDF type in the request
   * (Note that these are different from the alt controller view)
   * @param params 
   * @returns The translated string
   */
   getMp3AccessLinkName(params){
    const {assessment_type, requests_pdf_large, requests_pdf_regular} = params.data
    const isOsslt = assessment_type == TestWindowType.EQAO_G10L
    let slug = 'alt_version_link_title_mp3';
    if (isOsslt) {
      if (requests_pdf_large) slug = 'alt_version_link_title_mp3_large'
      else if (requests_pdf_regular) slug = 'alt_version_link_title_mp3_regular'
    }
    return this.lang.tra(slug)
  }

  parseSampleTracking(params){
    if(params.data.tracking_number_sample) return `<a href=${params.data.tracking_url_sample} target=_blank>${params.data.tracking_number_sample}</a>`
    else return null
  }

  parseOpTracking(params){
    if(params.data.tracking_number_operational) return `<a href=${params.data.tracking_url_operational} target=_blank>${params.data.tracking_number_operational}</a>`
    else return null
  }

  parseReturnTracking(params){
    if(params.data.tracking_number_return) return `<a href=${params.data.tracking_url_return} target=_blank>${params.data.tracking_number_return}</a>`
    else return null
  }

  altRequestGridOptions:any = {
    columnDefs: this.altRequestGridcolumnDefs,
    defaultColDef: {
      filter: true,
      sortable: true,
      resizable: true,
    },
    enableCellTextSelection: true,
  };
  selectedAltId: number;


  /**",
   * convert is_graduating to translation string
   * @param is_graduating
   * @returns transtioned string
   */
  convertIsGraduating(params){
    switch(params.data.is_graduating){
      case 'Yes':
        return this.lang.tra('lbl_yes')
      case 'No':
      default:
        return this.lang.tra('lbl_no')
    }
  }

  pageModal: PageModalController;
  Modal = Modal;
  
  ngOnInit(): void {
    this.isPrivateSchool = this.g9demoService.schoolData["is_private"];
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.loadAltRequests();
  }

  /**",
   * Get the Assessment(Class) filter list
   * @param null
   * @returns IFilterToggle:  List of Assessment(Class) filter list
   */
  getClassFilterState(){
    if(this.isPrivateSchool){
      return this.mySchool.getPrivateSchoolClassFilterToggles()
    }
    return this.schoolService.getClassFilterToggles()
  }

  /**",
   * check if there's any alternative request
   * This is used to tell if ClassFilter and test window filter need to show up or not
   * @param null
   * @returns boolean
   */
  haveRequests(){
    let haveRequests = false;

    //check if haveAltRequest
    if(this.altRequestsLoaded && !this.altRequestsIsEmpty()){ 
      haveRequests = true
    }

    return haveRequests
  }

  cModal() { return this.pageModal.getCurrentModal(); }

  cmc() { return this.cModal().config; }

  onAltGridReady(params) {
    this.altRequestGridColumnApi = params.columnApi;
    this.altRequestGridApi = params.api;
    this.setTestWindowFilter(this.mySchool.getCurrentTestWindow());
  }

  /**",
   * when Alt Request Record is selected
   * @param null
   * @returns null   
   */
  onAltRowSelected(event) {
    const request = event.api?.getSelectedRows()[0];
    this.selectedAltId = request?.id || null;
  }

  /**",
   * Check if alt version requests is empty
   * @param null
   * @returns boolean  
   */
  altRequestsIsEmpty() {
    return this.altRequests.length == 0 ? true : false;
  }

  /**",
   * when if alt version request is selected
   * @param null
   * @returns boolean   
   */
  checkAltSelected() {
    if(this.selectedAltId != null) {
      return false;
    } else {
      return true;
    }
  }

  /**",
   * when alt version request is selected and show request button is click, show the  alt version request detail
   * @param null
   * @returns null   
   */
  viewAltRequestModalStart() {
    const config = {
      rowData: this.altRequestGridApi.getSelectedRows()[0]
    }; 
    this.pageModal.newModal({
      type: Modal.ALT_REQUEST,
      config,
      finish: this.viewAltRequestModalFinish
    })
  }

  /**",
   * when alt version request detail is closed
   * @param null
   * @returns null   
   */
  viewAltRequestModalFinish() {
    this.pageModal.closeModal();
  }
  
  /**",
   * load alt version requests from API
   * @param null
   * @returns null   
   */
  async loadAltRequests(){
    this.altRequestsLoading = true;
    this.altRequestsLoaded = false;
    this.altRequestsLoadFailed = false;
    const params = this.configureQueryParams()
    this.auth.apiFind(this.routes.SCHOOL_ADMIN_ALTERNATIVE_VERSION_REQUEST, params)
    .then((res) => {
      this.altRequests = res;
      this.altRequestsLoading = false;
      this.altRequestsLoaded = true;
    })
    .catch((e) => {
      this.altRequestsLoading = false;
      this.altRequestsLoadFailed = true;
    });
    
  }

  /**",
   * User click different class filter, need to update grid columns and related the test window and the requests filter result.
   * @param ClassFilter  selected class
   * @returns null   
   */
  setClassFilter(filterId){
    this.selectedAltId = null;
    this.currentTestWindow = null
    this.onSetClassFilter.emit(filterId);
    this.currentClassFilter = filterId;

    //change grid columns
    if (this.currentClassFilter == "Primary"){
      this.altRequestGridColumnApi?.setColumnsVisible(['is_fi_current', 'fi_option_current'], true);
    } else {
      this.altRequestGridColumnApi?.setColumnsVisible(['is_fi_current', 'fi_option_current'], false);
    }

    //if no class is selected  
    if(!this.currentClassFilter) {
      this.setTestWindowFilter(this.currentTestWindow)
    }  
  }

  /**",
   * Use in export to render alt version request cell render's value
   * @param cell
   * @returns cellValue   
   */
  processAltCells(cell) {
    let cellVal = cell.value;
    if ( cell.column.colDef.cellRenderer ) {
        const field = cell.column.colDef.field
        if (!['tracking_number_sample', 'tracking_number_return', 'tracking_number_operational'].includes(field)){
            cellVal = cell.column.colDef.cellRenderer({value: cell.value, data: cell.node.data, plainTextMode: true});
        }
    }
    return cellVal;
  }

    /** New access links are relative, get the current homepage and append to it to get the url in the exported file 
   * But don't do it if it's an old link that was not relative
  */
    renderLinkAbsolute(params){
      const linkRecord = params.value
      if (!linkRecord) return null
      if (linkRecord[0] !== "/") return linkRecord
      const host = window.location.host
      return (host + linkRecord)
    }

  /**",
   * Use in export to render alt version request column's value
   * @param cell
   * @returns cellValue   
   */
  getColumnKeysForAltExport(columnApi: ColumnApi){
    //Columns which are lists of hyperlinks will be broken up into individual columns with links on export (individual columns are hidden on web page)
    const colHeadersToRemoveFromExport = [this.lang.tra('sample_links_alt_version'), this.lang.tra('assessment_links_alt_version')]
    const colFieldsToAddToExport = [
      'sample_link_pdf_regular', 'sample_link_pdf_large', 'sample_link_mp3', 'sample_link_asl', 'sample_link_ebraille',
      'operational_link_pdf_regular', 'operational_link_pdf_large', 'operational_link_mp3', 'operational_link_asl', 'operational_link_ebraille',
      'tracking_url_sample', 'tracking_url_operational', 'tracking_url_return'
    ]
    const columnKeysForExport = columnApi
    .getAllColumns()
    .filter(col => {
      const colDef = col.getColDef()
      const isVisible = col.isVisible()

      if(colDef.checkboxSelection) return false
      else if (colFieldsToAddToExport.includes(colDef.field)) return true
      else if (colHeadersToRemoveFromExport.includes(colDef.headerName)) return false
      else if (!isVisible) return false
      else return true
    })
    return columnKeysForExport
  }

  /**",
   * Export Alt version records as CSV
   * @param null
   * @returns null   
   */
  exportAltRequests() {
    this.altRequestGridApi.exportDataAsCsv({
      fileName: `alt_version_exports ${new Date()}`,
      processCellCallback: this.processAltCells,
      columnKeys: this.getColumnKeysForAltExport(this.altRequestGridColumnApi)
    });
  }

  /**",
   * User choose different test windows after choosed a classfilter, need to update requests filter result.
   * @param tw  selected test window
   * @returns null   
   */
  setTestWindowFilter(test_window){
    this.currentTestWindow = test_window;
    //set Alt Request filter
    const altRequestModel = {
      test_window_id: {
        filterType: 'number',
        type: 'equals',
        filter: this.currentTestWindow?.id || 0
      }
    }
    this.altRequestGridApi?.setFilterModel(altRequestModel);
    this.selectedAltId = null;
  }

  formatYesNo(params){
    return params.data[params.colDef.field] == 1 ? this.lang.tra('lbl_yes') : this.lang.tra('lbl_no')
  }

  /**",
   * convert datetime format
   * @param params.value
   * @returns string  
   */
  parseDate(params) {
    if(!params.value) {
      return null;
    }
    const current = new Date(Date.parse(params.value));
    const parsedDate = current.toLocaleString('default', {month: '2-digit', year: 'numeric', day: 'numeric'});
    const parsedTime = current.toLocaleTimeString('default');
    return `${parsedDate} ${parsedTime}`;
  }

  /**",
   * convert ALT_VERSION_REQUEST_STATUS to translated string
   * @param params.data.status
   * @returns string  
   */
  getAltRequestSlug(params): string {
    let slug = "";
    switch(params.data.status) {
      case ALT_VERSION_REQUEST_STATUS.Pending:
        slug = REQUEST_STATUS_SLUGS.Pending
        break;
      case ALT_VERSION_REQUEST_STATUS.Approved: 
        slug = REQUEST_STATUS_SLUGS.Approved
        break;
      case ALT_VERSION_REQUEST_STATUS.Canceled: 
        slug = REQUEST_STATUS_SLUGS.Canceled
        break;
      case ALT_VERSION_REQUEST_STATUS.Rejected: 
        slug = REQUEST_STATUS_SLUGS.Rejected
        break;
      case ALT_VERSION_REQUEST_STATUS.Shipment_Send: 
        slug = REQUEST_STATUS_SLUGS.Shipment_Send
        break;
      case ALT_VERSION_REQUEST_STATUS.Operational_Link_Send: 
        slug = REQUEST_STATUS_SLUGS.Operational_Link_Send
        break;
    }

    return this.lang.tra(slug);
  }

   /**",
   * API call default params
   * @param additonalParams
   * @returns {query:{}}
   */
  configureQueryParams(){
    const params = {
      query: { 
        schl_group_id: this.currentSchId
      }
    }
    return params
  }

  isNBED() {
    return this.whiteLabel.getSiteFlag('IS_NBED');
  }
  isABED() {
    return this.whiteLabel.getSiteFlag('IS_ABED');
  }
  isMBED() {
    return this.whiteLabel.getSiteFlag('IS_MBED');
  }
  isEQAO() {
    return this.whiteLabel.getSiteFlag('IS_EQAO');
  }

  getStudentIdSlug(){
    if (this.isABED()){
      return 'lbl_student_asn'
    }
    return 'oen_alt_version';
  }

  getSchlForeignIdSlug(){
    if (this.isABED()){
      return 'sa_sr_school_code'
    }
    return 'sch_mident_alt_version';
  }
}
