import { Component, Input, OnInit } from '@angular/core';
import { IContentElementScientificNotation } from 'src/app/ui-testrunner/element-render-scientific-notation/model';
import { EditingDisabledService } from '../editing-disabled.service';


export interface IScientificNotationHiddenConfigs {
  hideAdvancedOptions?: boolean,
  hideAllowMultipleAnswers?: boolean,
  hideFixedOptions?: boolean,
}

@Component({
  selector: 'element-config-scientific-notation',
  templateUrl: './element-config-scientific-notation.component.html',
  styleUrls: ['./element-config-scientific-notation.component.scss']
})
export class ElementConfigScientificNotationComponent implements OnInit {
  @Input() element: IContentElementScientificNotation;
  @Input() hiddenConfigs: IScientificNotationHiddenConfigs = {}
  @Input() isSimplified: boolean = false;
  
  answerStrings: string[] = [];
  nonUniqueAnswerStrings: Set<string> = new Set();
  isNonUniqueAnswerString: boolean[] = [];

  constructor(
    private editingDisabled: EditingDisabledService,
  ) { }

  ngOnInit(): void {
    this.updateWarning();
  }

  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  validateIntegerInput(event: any): void {
    const input = event.target.value;
    const isValid = /^[0-9]*$/mg.test(input);
    if (!isValid) {
      event.target.value = input.replace(/[^0-9]/g, '');
      // trigger change detection
      event.target.dispatchEvent(new Event('input'));
    }
  }
  
  get answerSets() {
    return this.element.answerSets ?? [];
  }
  
  addAnswerSet() {
    if(this.element.answerSets === undefined) {
        this.element.answerSets = [];
    }
    this.element.answerSets.push({
      whole: this.element.whole,
      fractional: this.element.fractional,
      exponent: this.element.exponent,
      isNegativeCoefficient: this.element.isNegativeCoefficient,
      isNegativeExponent: this.element.isNegativeExponent,
    });
    this.update();
  }
  deleteAnswerSet(index: number) {
    const confirm = window.confirm('Are you sure you want to delete this answer?');
    if (!confirm) return;
    this.element.answerSets.splice(index, 1);
    this.update();
  }
  
  
  isEditingEntryId:boolean;
  startEditingEntryId(){
    if(confirm('Edit Entry ID? You should avoid doing this unless the entry is blank or duplicated due to an import.')){
      this.isEditingEntryId = true;
    }
  }
  
  update(){
    this.updateWarning();
    this.updateChangeCounter();
  }
  updateChangeCounter() {
    if (!this.element._changeCounter) {
      this.element._changeCounter = 0;
    }
    this.element._changeCounter ++;
  }
  
  
  getNonUniqueElement<T>(arr: T[]): Set<T> {
    const seen = new Set<T>();
    const nonUnique = new Set<T>();
    arr.forEach(x => {
      if (!seen.has(x)) {
        seen.add(x);
      } else {
        nonUnique.add(x);
      }
    });
    return nonUnique;
  }
  updateAnswerString(){
    const answerSets = [this.element, ...this.element.answerSets];
    this.answerStrings = answerSets.map(x => `${x.whole},${x.fractional},${x.exponent},${!!x.isNegativeCoefficient},${!!x.isNegativeExponent}`);
    this.nonUniqueAnswerStrings = this.getNonUniqueElement(this.answerStrings);
    this.isNonUniqueAnswerString = this.answerStrings.map(x => this.nonUniqueAnswerStrings.has(x));
  }
  updateWarning(){
    if (!this.element.isAllowMultipleAnswers) return;
    this.updateAnswerString();
  }
  

}
