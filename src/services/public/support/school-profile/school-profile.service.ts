// Initializes the `public/school-admin/school-profile` service on path `/public/school-admin/school-profile`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SchoolProfile } from './school-profile.class';
import hooks from './school-profile.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/support/school-profile': SchoolProfile & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/support/school-profile', new SchoolProfile(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/support/school-profile');

  service.hooks(hooks);
}
