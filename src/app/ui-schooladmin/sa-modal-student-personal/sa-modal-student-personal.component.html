<div *ngIf="isSelectingRecord">
  <tra-md slug="txt_instr_select_student_rec"></tra-md>
  <table>
    <tr>
      <th></th>
      <th><tra slug="{{getMultiSelectStudentSlug('studentIdentifier')}}"></tra></th>
      <th><tra slug="{{getMultiSelectStudentSlug('firstName')}}"></tra></th>
      <th><tra slug="{{getMultiSelectStudentSlug('lastName')}}"></tra></th>
      <th *ngIf="!whitelabelService.isABED()"><tra slug="sa_students_col_course"></tra></th>
      <th><tra slug="{{getMultiSelectStudentSlug('className')}}"></tra></th>
      <th *ngIf="whitelabelService.isABED()"><tra slug="abed_lbl_accommodation_support"></tra></th>
    </tr>
    <tr *ngFor="let acct of studentRecords">
      <td >
        <!-- <button [disabled]="disabled" (click)="selectRecord(acct)" class="button is-info"> -->
        <button [disabled]="disabled" (click)=" selectSingleRcord(acct)" class="button is-info">
          <tra slug="btn_select"></tra>
        </button>
      </td>
      <td>{{acct[getMultiSelectStudentKey('studentIdentifier')]}}</td>
      <td>{{acct.first_name}}</td>
      <td>{{acct.last_name}}</td>
      <td *ngIf="!whitelabelService.isABED()">{{getCourseLabelFromId(acct.eqao_g9_course)}}</td>
      <td>{{getClassLabelFromId(acct[getMultiSelectStudentKey('className')])}}</td>
      <td *ngIf="whitelabelService.isABED()"> {{renderAccommodations(acct)}} </td>
    </tr>
  </table>
</div>
<div *ngIf="!isSelectingRecord">

  <div *ngIf ="!isCreatingNew">
    <div style="display: table">  
      <div style="display: table-cell">
        <div style = "font-weight:bold;">{{getFormEntryVal('eqao_student_gov_id')}} - {{getFormEntryVal('first_name')}} {{getFormEntryVal('last_name')}}</div>  <!-- - {{studentRecord.id}} --> 
        <div>
          <img  (click)="clickPreRecord()" style ="margin:0.1em; cursor: pointer;"
            src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515512/authoring/left/1632165841258/left.png" />
          <img  (click)="clickNextRecord()" style ="margin:0.1em; cursor: pointer;"
            src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515512/authoring/right/1632165867980/right.png" />
          <tra slug='sm_student_record'></tra> {{selectedStudentIndex+1}} <tra slug='of_enum'></tra> {{totalRecords}}
        </div>
      </div>
      <div style="display: table-cell; position: absolute; right:0.1em;" >
        <button (click)="clickConfirmStudentInformation()" class="button is-small is-select" [disabled]="isEditDisable"><span></span><tra slug="btn_confirm_student_information"></tra></button>
      </div>
    </div> 
    <hr style="margin: 0.5em;">
    <!--  School Board Discrepancy -->
    <div *ngIf ="SDC_conflict.length > 0" style = "position: relative; margin:0.1em; background-color:moccasin;">
      <div *ngIf="!isImportUpdate"><tra slug='sm_schoolboard_discrepancy' style = "font-weight:bold;"></tra></div>
      <div *ngIf="isImportUpdate"><tra slug='sm_school_import_discrepancy' style = "font-weight:bold;"></tra></div>
      <div *ngIf="!isImportUpdate">
        <tra-md slug='sm_schoolboard_discrepancy_disc' [props]="{getUploadDate:convertimportDateTime(studentRecord)}"></tra-md>
      </div>
      <div *ngIf="isImportUpdate">
        <tra-md slug='sm_school_import_discrepancy_disc' [props]="{getUploadDate:convertimportDateTime(studentRecord)}"></tra-md>
      </div>
      <table style="border:none; width:100%">
        <tr style="border:none;">
          <th style = "font-weight:bold; border:none;"></th>
          <th style = "font-weight:bold; border:none; display: inline-block"><tra slug='sm_th_school'></tra></th>
          <th style = "font-weight:bold; border:none; display: inline-block; position:absolute; right:0px; width:40%" *ngIf="!isImportUpdate"><tra slug='sm_th_board'></tra></th>
          <th style = "font-weight:bold; border:none; display: inline-block; position:absolute; right:0px; width:40%" *ngIf="isImportUpdate"><tra slug='sm_school_import'></tra></th>
        </tr>
        <tr style="border:none;" *ngFor="let conflict of SDC_conflict"> 
          <td style="border:none;"><div class = "sdc_field_name">{{conflictFieldText(conflict.fieldname)}}</div></td>
          <td colspan="2" style="border:none; width:100%;">
            <mat-radio-group  style="width:100%;" [class.disabled]="disabled">
              <mat-radio-button class ="sdc_conflict_radio_sch_btn" value = 'school' (change)="selectedConflicts(conflict, $event)" [checked] = "true" ><textarea class = "sdc_field_value">{{FieldValue(conflict.fieldname,studentRecord[get_DATA_MAPPING_EQAO_G9_STUDENT(conflict.fieldname)])}}</textarea></mat-radio-button>
              <mat-radio-button class ="sdc_conflict_radio_board_btn" value='board' (change)="selectedConflicts(conflict, $event)"><textarea class = "sdc_field_value">{{FieldValue(conflict.fieldname,conflict.value)}}</textarea></mat-radio-button>
            </mat-radio-group>
          </td>  
        </tr>
      </table>  
      <div style="position: absolute; right:1.0em; bottom: 0.5em" >
        <button (click)="clickApplyBtn()" class="button is-select"><tra slug="btn_schbrd_disc_apply"></tra></button>
      </div>
      <br><br><br>
    </div>
    <!-- Business Rule Validate -->
    <div *ngIf ="errMsgArray.length > 0 && SDC_conflict.length == 0" style = "margin:0.1em; background-color:pink; font-weight:bold;">
      <tra slug='sm_invalid_information'></tra>
      <ul *ngFor="let err of errMsgArray">
        <li>{{fetchErrMsg(err)}}</li>
      </ul>
    </div>       
  </div> 

  <!-- New Student popup-->
  <tra-md 
  style="font-size:1.3rem; font-weight: 700; text-transform: uppercase;" 
  [slug]="this.isCreatingNew ? 'new-student-ABED' : 'edit-student-ABED'">
</tra-md>

  <!-- Personal Information, Accommodation -->
  <menu-bar 
    [menuTabs]="views"
    [tabIdInit]="selectedView"
    (change)="selectView($event)">
  </menu-bar>

  <div [ngSwitch]="selectedView">
    <div *ngSwitchCase="View.PERSONAL_INFO" class="personal-info-columns">
      <div *ngIf="studentRecord.IsDeceased" class="header-note"><tra slug="abed_deceased_student"></tra></div>
      <div class="required-field-container">
        <b><tra slug="lbl_required_field"></tra></b>
        <i class="fa fa-asterisk" aria-hidden="true"></i>
      </div>
      <br>
      <div 
        *ngFor="let personalInfoEntries of personalInfoColumns" 
      >
        <sa-widget-form-entry 
          *ngFor="let entry of personalInfoEntries, let i = index"
          [disabled]="entry.isLocked && !isCreatingNew" 
          [formEntry]="entry"
          [formEntries]="formEntriesList"
        ></sa-widget-form-entry>
      </div>
    </div>
    <div *ngSwitchCase="View.ABED_ACCOMM">
      <!-- [isDisabled]="!editMode" -->
      <h3>Built-in Accommodations:</h3>
      <student-accommodations [isDisabled]="disabled" [accommodationList]="getBuiltInAccommodationList()"></student-accommodations>
      <h3>Other Accommodations:</h3>
      <student-accommodations [isDisabled]="disabled" [accommodationList]="getOtherAccommodationList()"></student-accommodations>
    </div>
    <div *ngSwitchCase="View.SECONDARY_INACTIVE_ASN" class="tab-container">
      <ng-container *ngIf="studentRecord">
        <div style="display: flex; flex-direction: row;">
          <label class="label">  
            <div class="lable-container">
              <tra-md slug="sdc_1_student_gov_id_full_abed" [isCondensed]="true"></tra-md>
            </div>
          </label>
          <input [class.disabled]="true" disabled="true" style="width: 15em" class="input is-small" value="{{renderASN('StudentIdentificationNumber')}}" type="text" />
        </div>
        <ng-container *ngIf="allowPASIUpdates">
          <br>
          <label class="label">  
            <div class= "form-entry-label" style="display: flex;">
              <div class="lable-container">
                <tra-md slug="student_account_num_abed_sec" [isCondensed]="true"></tra-md>
              </div>
            </div>
          </label>
        </ng-container>
      </ng-container>
    </div>
    <div *ngSwitchCase="View.ACCOMMODATION">
      <table>
        <tr>
          <th><tra slug="sdc_1_acc_oen"></tra></th> <!--OEN-->
          <th><tra slug="sdc_1_acc_last_name"></tra></th><!--Last Name-->
          <th><tra slug="sdc_1_acc_first_name"></tra></th><!--First Name-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_term_format"></tra></th><!--Course-->
          <th *ngIf="!isOSSLT && !isPrimary &&!isJunior"><tra slug="sdc_1_acc_fre_imm_ext"></tra></th> <!--Fre. Imm./ Ext. Fre.-->
          <th *ngIf="isPrimary || isJunior"><tra slug="sdc_1_acc_fre_imm_ext"></tra></th> <!--Fre. Imm. Fre.-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_class_code"></tra></th> <!--Class Code-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_teacher_name"></tra></th><!--Teacher Name-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_eligibility_status"></tra></th><!--EligibilityStatus-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_grouping"></tra></th><!--Grouping-->
          <th><tra slug="sdc_1_acc_iep"></tra></th><!--IEP-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_ESLELD"></tra></th><!--ESLELD-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_NonParticipation_Status"></tra></th><!--NonParticipation_Status-->
        </tr>
        <tr>
          <td>{{getFormEntryVal('eqao_student_gov_id')}}</td>
          <td>{{getFormEntryVal('last_name')}}</td>
          <td>{{getFormEntryVal('first_name')}}</td>
          <td  *ngIf="!isOSSLT"><tra [slug]="getCourseLabel()"></tra></td>
          <td *ngIf="!isOSSLT && !isPrimary &&!isJunior"> <i *ngIf="getFormEntryValYesNo('eqao_g9_french')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="isPrimary||isJunior"><i *ngIf="getFormEntryValYesNo('eqao_pj_french')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="!isOSSLT">{{getClassLabel()}}</td>
          <td *ngIf="!isOSSLT">{{getStudentTeacherName()}}</td>
          <td *ngIf="isOSSLT"><tra [slug]="getEligibilityStatus()"></tra></td>
          <td *ngIf="isOSSLT">{{getClassLabel()}}</td>
          <td> <i *ngIf="getFormEntryValYesNo('eqao_iep')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="isOSSLT"> <i *ngIf="getELL('_ell')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="isOSSLT"> <i *ngIf="haveNonParticipationStatus()" class="fa fa-check" aria-hidden="true"></i></td>
        </tr>
      </table>
      <div>
        <div *ngIf="isPrimary || isJunior">
          <tra-md slug="sdc_1_acc_decisions_g3_g9"></tra-md>
        </div>
        <div *ngIf="!isPrimary && !isJunior && !isOSSLT"> <!--is G9 -->
          <tra-md slug="sdc_1_acc_decisions_g9"></tra-md>
        </div >
        <div *ngIf="isOSSLT">
          <tra-md slug="sdc_1_acc_decisions_g10"></tra-md>
        </div>
        <div>
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_iep" ></sa-widget-form-entry>
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._identification_placement" ></sa-widget-form-entry>
          <!-- <ng-container *ngIf="isOSSLT">
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.SpecPermIEP" ></sa-widget-form-entry>
          </ng-container> -->
        </div>
        <hr/>
        <div *ngIf="!isPrimary && !isJunior">
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_1" ></sa-widget-form-entry>
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_2" ></sa-widget-form-entry>
          <sa-widget-form-entry [disabled]="!isOtherTechSelected()" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_custom" ></sa-widget-form-entry>
          <div *ngIf="isOSSLT"><tra-md slug="eqao_acc_assistive_tech_custom_note_osslt"></tra-md></div>
          <!-- <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_3" ></sa-widget-form-entry> -->
        </div>
        <hr/>
        <div *ngIf="isG9">
          <div><tra-md slug="sdc_1_acc_indicate_acc"></tra-md></div><!--Indicate Accommodations Provided by the School-->
          <sa-widget-form-entry  [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_braille" ></sa-widget-form-entry>
          <div><tra-md slug="sdc_gr9_audio_note"></tra-md></div>
          <!-- <sa-widget-form-entry *ngIf="this.lang.c() =='fr'" [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_braille_fr" ></sa-widget-form-entry> -->
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.AccAudioVersion" ></sa-widget-form-entry>
          <div><tra-md slug="sdc_gr9_order_special_versions"></tra-md></div>
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._extended_time" ></sa-widget-form-entry>
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_pres_format" ></sa-widget-form-entry>
          <!--
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._audio_recorsding_of_resp" ></sa-widget-form-entry>
          <ng-container *ngIf="isOSSLT">
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.AccVideotapeResponse" ></sa-widget-form-entry>
          </ng-container>
          -->
          <!--
          <ng-container *ngIf="!isOSSLT">
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._audio_recording_of_resp" ></sa-widget-form-entry>
          </ng-container>
          -->
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_scribing" ></sa-widget-form-entry>
        </div>
        <div *ngIf="isOSSLT">
          <ng-container>
            <div><tra-md slug="sdc_1_acc_indicate_acc"></tra-md></div><!--Indicate Accommodations Provided by the School-->
            <sa-widget-form-entry  [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_braille" ></sa-widget-form-entry>
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._extended_time" ></sa-widget-form-entry>
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_pres_format" ></sa-widget-form-entry>
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_scribing" ></sa-widget-form-entry>
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.AccOther" ></sa-widget-form-entry>
          </ng-container>
          <hr style="border:0; width: 96%; height: 1px; background-color:#dbdbdb;"/>
          <ng-container>
            <div><tra-md slug="sa_alternative_version_test_desc"></tra-md></div>
            <sa-widget-form-entry  [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.osslt_alternative_version_test" ></sa-widget-form-entry>
            <div><tra-md slug="sa_alternative_version_test_note"></tra-md></div>
          </ng-container>
        </div>
        <div *ngIf="isPrimary || isJunior">
          <menu-bar [menuTabs]="Accom_Sub_Views" [tabIdInit]="selected_Accom_Sub_View" (change)="selectAccomSubView($event)"></menu-bar>
          <tra-md slug="sdc_pj_assistive_note"></tra-md>
          <div [ngSwitch]="selected_Accom_Sub_View">
            <div *ngSwitchCase="Accom_Sub_View.Reading">
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_1_pj_reading" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_2_pj_reading" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="!isOtherTechSelected()" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_custom_pj_reading" ></sa-widget-form-entry>
            </div>
            <div *ngSwitchCase="Accom_Sub_View.Writing">
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_1_pj_writing" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_2_pj_writing" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="!isOtherTechSelected()" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_custom_pj_writing" ></sa-widget-form-entry>
            </div>
            <div *ngSwitchCase="Accom_Sub_View.Mathematics">
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_1_pj_mathematics" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_2_pj_mathematics" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="!isOtherTechSelected()" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_assistive_tech_custom_pj_mathematics" ></sa-widget-form-entry>
            </div>
          </div>
          <menu-bar [menuTabs]="Accom_Sub_Views" [tabIdInit]="selected_Accom_Sub_View" (change)="selectAccomSubView($event)"></menu-bar>
          <div [ngSwitch]="selected_Accom_Sub_View">
            <div *ngSwitchCase="Accom_Sub_View.Reading">
              <div><tra-md slug="sdc_1_acc_indicate_acc"></tra-md></div><!--Indicate Accommodations Provided by the School-->
              <sa-widget-form-entry  [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_braille_pj_reading" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_audio_note_2"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.AccAudioVersion_pj_reading" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_order_special_versions"></tra-md></div>
              <div><tra-md slug="sdc_pj_osv_approved_lbl"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_alternate_form_pj_reading" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_osv_approved_description"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_pres_format_pj_reading" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_scribing_pj_reading" ></sa-widget-form-entry>
            </div>
            <div *ngSwitchCase="Accom_Sub_View.Writing">
              <div><tra-md slug="sdc_1_acc_indicate_acc"></tra-md></div><!--Indicate Accommodations Provided by the School-->
              <sa-widget-form-entry  [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_braille_pj_writing" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_audio_note_2"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.AccAudioVersion_pj_writing" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_order_special_versions"></tra-md></div>
              <div><tra-md slug="sdc_pj_osv_approved_lbl"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_alternate_form_pj_writing" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_osv_approved_description"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_pres_format_pj_writing" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_scribing_pj_writing" ></sa-widget-form-entry>
            </div>
            <div *ngSwitchCase="Accom_Sub_View.Mathematics">
              <div><tra-md slug="sdc_1_acc_indicate_acc"></tra-md></div><!--Indicate Accommodations Provided by the School-->
              <sa-widget-form-entry  [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_braille_pj_mathematics" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_audio_note_3"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.AccAudioVersion_pj_mathematics" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_order_special_versions_2"></tra-md></div>
              <div><tra-md slug="sdc_pj_osv_approved_lbl"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_alternate_form_pj_mathematics" ></sa-widget-form-entry>
              <div><tra-md slug="sdc_pj_osv_approved_description"></tra-md></div>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_pres_format_pj_mathematics" ></sa-widget-form-entry>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_acc_scribing_pj_mathematics" ></sa-widget-form-entry>
            </div>
          </div>
          <div><tra-md slug="sdc_pj_exemption_title"></tra-md></div>
          <menu-bar [menuTabs]="Accom_Sub_Views" [tabIdInit]="selected_Accom_Sub_View" (change)="selectAccomSubView($event)"></menu-bar>
          <div [ngSwitch]="selected_Accom_Sub_View">
            <div *ngSwitchCase="Accom_Sub_View.Reading">
              <tra-md slug="sdc_pj_exemption_content"></tra-md>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_exemption_pj_reading" ></sa-widget-form-entry>
            </div>
            <div *ngSwitchCase="Accom_Sub_View.Writing">
              <tra-md slug="sdc_pj_exemption_content"></tra-md>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_exemption_pj_writing" ></sa-widget-form-entry>
            </div>
            <div *ngSwitchCase="Accom_Sub_View.Mathematics">
              <tra-md slug="sdc_pj_exemption_content"></tra-md>
              <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.eqao_exemption_pj_mathematics" ></sa-widget-form-entry>
            </div>
          </div>
        </div>
        <hr style="border:0; width: 96%; height: 1px; background-color:#dbdbdb;"/>
        <div>
          <div><tra-md slug="sdc_1_acc_spec_circ"></tra-md></div><!--Special Circumstances-->
          <ng-container *ngIf="isOSSLT">
            <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries.SpecPermIEP" ></sa-widget-form-entry>
          </ng-container>  
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._no_iep_temp_inj" ></sa-widget-form-entry>
          <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._no_iep_recent_arriv" ></sa-widget-form-entry>
        </div>
        <hr/>
        
      </div>
    </div>
    <div *ngSwitchCase="View.PROVISIONS">
      <table>
        <tr>
          <th><tra slug="sdc_1_acc_oen"></tra></th> <!--OEN-->
          <th><tra slug="sdc_1_acc_last_name"></tra></th><!--Last Name-->
          <th><tra slug="sdc_1_acc_first_name"></tra></th><!--First Name-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_term_format"></tra></th><!--Course-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_fre_imm_ext"></tra></th> <!--Fre. Imm./ Ext. Fre.-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_class_code"></tra></th> <!--Class Code-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_teacher_name"></tra></th><!--Teacher Name-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_eligibility_status"></tra></th><!--EligibilityStatus-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_grouping"></tra></th><!--Grouping-->
          <th><tra slug="sdc_1_acc_iep"></tra></th><!--IEP-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_ESLELD"></tra></th><!--ESLELD-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_NonParticipation_Status"></tra></th><!--NonParticipation_Status-->
        </tr>
        <tr>
          <td>{{getFormEntryVal('eqao_student_gov_id')}}</td>
          <td>{{getFormEntryVal('last_name')}}</td>
          <td>{{getFormEntryVal('first_name')}}</td>
          <td  *ngIf="!isOSSLT"><tra [slug]="getCourseLabel()"></tra></td>
          <td *ngIf="!isOSSLT"> <i *ngIf="getFormEntryValYesNo('eqao_g9_french')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="!isOSSLT">{{getClassLabel()}}</td>
          <td *ngIf="!isOSSLT">{{getStudentTeacherName()}}</td>
          <td *ngIf="isOSSLT"><tra [slug]="getEligibilityStatus()"></tra></td>
          <td *ngIf="isOSSLT">{{getClassLabel()}}</td>
          <td> <i *ngIf="getFormEntryValYesNo('eqao_iep')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="isOSSLT"> <i *ngIf="getELL('_ell')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="isOSSLT"> <i *ngIf="haveNonParticipationStatus()" class="fa fa-check" aria-hidden="true"></i></td>
        </tr>
      </table>
      <div class="column">
        <sa-widget-form-entry 
          *ngFor="let entry of provisionInfoColumns, let i = index"
          [disabled]="columnsDisabled[i] && !isCreatingNew" 
          [formEntry]="entry"
          [formEntries]="formEntriesList"
        ></sa-widget-form-entry>
      </div>
    </div>
    <div *ngSwitchCase="View.NON_PARTICIPANT_STATUS">
      <table>
        <tr>
          <th><tra slug="sdc_1_acc_oen"></tra></th> <!--OEN-->
          <th><tra slug="sdc_1_acc_last_name"></tra></th><!--Last Name-->
          <th><tra slug="sdc_1_acc_first_name"></tra></th><!--First Name-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_term_format"></tra></th><!--Course-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_fre_imm_ext"></tra></th> <!--Fre. Imm./ Ext. Fre.-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_class_code"></tra></th> <!--Class Code-->
          <th *ngIf="!isOSSLT"><tra slug="sdc_1_acc_teacher_name"></tra></th><!--Teacher Name-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_eligibility_status"></tra></th><!--EligibilityStatus-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_grouping"></tra></th><!--Grouping-->
          <th><tra slug="sdc_1_acc_iep"></tra></th><!--IEP-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_ESLELD"></tra></th><!--ESLELD-->
          <th *ngIf="isOSSLT"><tra slug="sdc_1_acc_NonParticipation_Status"></tra></th><!--NonParticipation_Status-->
        </tr>
        <tr>
          <td>{{getFormEntryVal('eqao_student_gov_id')}}</td>
          <td>{{getFormEntryVal('last_name')}}</td>
          <td>{{getFormEntryVal('first_name')}}</td>
          <td  *ngIf="!isOSSLT"><tra [slug]="getCourseLabel()"></tra></td>
          <td *ngIf="!isOSSLT"> <i *ngIf="getFormEntryValYesNo('eqao_g9_french')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="!isOSSLT">{{getClassLabel()}}</td>
          <td *ngIf="!isOSSLT">{{getStudentTeacherName()}}</td>
          <td *ngIf="isOSSLT"><tra [slug]="getEligibilityStatus()"></tra></td>
          <td *ngIf="isOSSLT">{{getClassLabel()}}</td>
          <td> <i *ngIf="getFormEntryValYesNo('eqao_iep')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="isOSSLT"> <i *ngIf="getELL('_ell')" class="fa fa-check" aria-hidden="true"></i></td>
          <td *ngIf="isOSSLT"> <i *ngIf="haveNonParticipationStatus()" class="fa fa-check" aria-hidden="true"></i></td>
        </tr>
      </table>
      <!--
      <div>
        <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._ell" ></sa-widget-form-entry>
        <sa-widget-form-entry [disabled]="disabled" [formEntries]="formEntriesList" [formEntry]="formEntries._periodic_breaks" ></sa-widget-form-entry>
      </div>
      -->
      <div class="column">
        <sa-widget-form-entry 
          *ngFor="let entry of participationInfoColumns, let i = index"
          [disabled]="columnsDisabled[i] && !isCreatingNew" 
          [formEntry]="entry"
          [formEntries]="formEntriesList"
        ></sa-widget-form-entry>
      </div>
    </div>
    <div *ngSwitchCase="View.ALT_VERSION_REQUEST">
      <tra-md slug = 'alt_version_request_instruction_abed'></tra-md>
      <!--alt_version_require-->
      <sa-widget-form-entry [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.alt_version_require" ></sa-widget-form-entry>
      <div *ngIf="getAltVersionInfoValue('alt_version_require')==='#'" class = 'alt-version-not-require'><tra-md slug = 'lbl_alt_version_not_require'></tra-md></div>
      <!--Possible errors-->
      <!-- TODO -->
      <!-- <div *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && !haveAltReqPrereq() && !altVersionSubmit()"><tra-md slug = 'lbl_alt_version_require_iep_spec'></tra-md></div>
      <div *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && haveAltReqPrereq() && !haveAltVersionAccomCheck() && !altVersionSubmit()"><tra-md slug = 'lbl_alt_version_require_accom_alt_version'></tra-md></div>
      <div *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && haveAltReqPrereq() && haveAltVersionAccomCheck() && invalidIepSpecGetError() && !altVersionSubmit()"><tra-md slug = {{invalidIepSpecGetError()}}></tra-md></div> -->

      <div *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && (showAltQuestions() || altVersionSubmit())"><tra slug = 'lbl_alt_version_follow_prompts'></tra></div>
      <!--tent_session_date when alt_version_require === 1 AND student is not in a class scheduled for an operational session -->
      <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && ((showAltQuestions() && !altVersionSubmit()) || submittedAndAnswered('tent_session_date'))" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.tent_session_date" ></sa-widget-form-entry>
      <div *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && ((showAltQuestions() && !altVersionSubmit()) || submittedAndAnswered('tent_session_date')) && !validTentSessionDate()" style = "color: rgb(160, 0, 0);"><tra slug = 'lbl_alt_version_invalid_tent_date_prompt'></tra></div>
      <!--alt_version_braille_require when alt_version_require === 1 -->
        <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && (showAltQuestions() || submittedAndAnswered('alt_version_braille_require'))" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.alt_version_braille_require" ></sa-widget-form-entry>
        <!-- <div *ngIf="showBlindBrailleError()"><tra slug="lbl_alt_version_require_iprc_blind_braille"></tra></div> -->
        <!--braille_format when alt_version_braille_require === 1 -->
          <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_braille_require')==='1' && ((showAltQuestions()) || submittedAndAnswered('braille_format'))" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.braille_format" ></sa-widget-form-entry>
            <!--schl_contact when braille_format === 1, 2, 3, 4-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_contact" ></sa-widget-form-entry>
            <!--schl_email when braille_format === 1, 2, 3, 4-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_email" ></sa-widget-form-entry>            
            <!--schl_phone when braille_format === 1, 2, 3, 4, 16, 17, 18, 19-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_phone" ></sa-widget-form-entry>
            <!--schl_street_number_name when braille_format === 1, 2, 3, 4-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_street_number_name" ></sa-widget-form-entry>
            <!--schl_city when braille_format === 1, 2, 3, 4-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_city" ></sa-widget-form-entry>
            <!--schl_province when braille_format === 1, 2, 3, 4-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_province" ></sa-widget-form-entry>
            <!--schl_country when braille_format === 1, 2, 3, 4-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_country" ></sa-widget-form-entry>
            <!--schl_postal_code when braille_format === 1, 2, 3, 4-->
            <sa-widget-form-entry *ngIf="showaltRequestSchoolAddress()" [disabled]="disabled|| altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_postal_code" ></sa-widget-form-entry>
          <!--pdf_format when alt_version_braille_require === 1-->
          <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_braille_require')==='1' && ((showAltQuestions()) || submittedAndAnswered('pdf_format'))" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.pdf_format" ></sa-widget-form-entry>
          <!--audio_delivery_format when alt_version_braille_require === 1-->
          <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_braille_require')==='1' && ((showAltQuestions()) || submittedAndAnswered('audio_delivery_format'))" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.audio_delivery_format" ></sa-widget-form-entry>
      <!--alt_version_asl_require when when alt_version_require === 1 and alt_version_braille_require === 0-->
      <!-- ASL not asked anymore but should display if was answered before -->
      <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_require')==='1'  &&  getAltVersionInfoValue('alt_version_braille_require')==='#' && showAltQuestions() && submittedAndAnswered('asl_format')" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.alt_version_asl_require" ></sa-widget-form-entry>
      <!--alt_version_pdf_require when when alt_version_require === 1  and alt_version_braille_require === 0-->
      <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_require')==='1' &&  getAltVersionInfoValue('alt_version_braille_require')==='#' && showAltQuestions()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.alt_version_pdf_require" ></sa-widget-form-entry>
        <!--pdf_format when alt_version_pdf_require ===1 -->
        <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_pdf_require')==='1' && showAltQuestions()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.pdf_format" ></sa-widget-form-entry>
        <!--audio_delivery_format when alt_version_pdf_require ===1 -->
        <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_pdf_require')==='1' && showAltQuestions()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.audio_delivery_format" ></sa-widget-form-entry>
        <!--reason when alt_version_pdf_require ===1 -->
        <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_pdf_require')==='1' && showAltQuestions()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.reason" ></sa-widget-form-entry>
      <!--alt_version_online_require when alt_version_require === 1  and alt_version_braille_require === 0 and alt_version_pdf_require === 0 -->
      <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_require')==='1' &&  getAltVersionInfoValue('alt_version_braille_require')==='#' && getAltVersionInfoValue('alt_version_pdf_require')==='#' && showAltQuestions()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.alt_version_online_require" ></sa-widget-form-entry>
        <!--reason when alt_version_online_require ===1 -->
        <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_online_require')==='1' && showAltQuestions()" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.reason" ></sa-widget-form-entry>
        <!-- schl_admin_phone (for non-braille) -->
        <sa-widget-form-entry *ngIf="getAltVersionInfoValue('alt_version_require')==='1' && showAltQuestions() && getAltVersionInfoValue('alt_version_braille_require')==='#' && (showAltQuestions || submittedAndAnswered('schl_admin_phone'))" [disabled]="disabled || altVersionSubmit()" [formEntries]="altVersionFormEntriesList" [formEntry]="altVersionFormEntries.schl_admin_phone" ></sa-widget-form-entry>
      <!--all require question is no-->
      <div *ngIf="getAltVersionInfoValue('alt_version_require')==='1' &&  getAltVersionInfoValue('alt_version_braille_require')==='#' && getAltVersionInfoValue('alt_version_pdf_require')==='#' && getAltVersionInfoValue('alt_version_online_require')==='#'" class = 'alt-version-not-require'><tra-md slug = 'lbl_alt_version_not_require'></tra-md></div>
      <div class ='submit-alt-request-buttons-container'>
        <div mat-flat-button [matTooltip]='lang.tra("submit_alt_request_btn_tooltip_gen")' matTooltipPosition="after" [matTooltipDisabled]="!showSubmitAltVersionBtn() || altQuestionsCompleted()">
          <button *ngIf="showSubmitAltVersionBtn()" [disabled]="!altQuestionsCompleted()" class="submit-alt-request-buttons" (click)="startSubmitAltRequestModal()"> <tra slug = 'btn-submit-alt-req-gen'></tra></button>
        </div>
        <button *ngIf="showCancelAltVersionBtn()" class="cancel-alt-request-buttons" (click)="cancelAltRequest()"> <tra slug = 'btn-cancel-alt-req-gen'></tra></button>
      </div>
    </div>

  </div>
  
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents" style="width:30em; padding: 3em;">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="StudentPersonalModal.SUBMIT_ALT_REQUEST">
        <div>
          <b><tra-md slug = "lbl_alt_version_modal_submit_title_gen"></tra-md></b>
        </div>
        <div>
          <tra-md slug = "lbl_alt_version_modal_submit_format_req"></tra-md>
        </div>
        <ul *ngFor="let requestFormat of getRequestFormatList()">
          <li>{{requestFormat}}</li>
        </ul>
        <br>
        <div>
          <button class ='ack-alt-request-buttons-modal' (click)="confirmAcknowledge()">
            <input type="checkbox" [checked]="requestAltVersionAcknowledge" style ="margin-right: 0.5em; margin-top: 0.5em;">
            <tra slug="lbl_alt_version_modal_submit_ack"></tra>
          </button>
        </div> 
        <div class ='submit-alt-request-buttons-container-modal'>
          <button (click)="closeStudentPersonalModal()" 
                  class="cancel-alt-request-buttons-modal"
          > 
            <tra slug = 'btn_cancel'></tra>
          </button>
          <button [disabled] ="!requestAltVersionAcknowledge" 
                  (click)="submitAltRequest()"
                  class="submit-alt-request-buttons-modal"
          >
            <tra slug = 'btn-submit-alt-req-gen'></tra>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>