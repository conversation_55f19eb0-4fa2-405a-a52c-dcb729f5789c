import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SaTwStatementComponent } from './sa-tw-statement.component';

describe('SaTwStatementComponent', () => {
  let component: SaTwStatementComponent;
  let fixture: ComponentFixture<SaTwStatementComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SaTwStatementComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SaTwStatementComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
