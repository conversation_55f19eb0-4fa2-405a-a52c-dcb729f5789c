<div class="content">
    <div class="header-container">
        <div class="step2-header">
            <img
                src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/504237/authoring/bc_logo_transparent/1623353565771/bc_logo_transparent.png">
        </div>
        <div class="step2-header-text">
            <p class="header">FSA System Compatibility Test</p>
            <p *ngIf="!isStep2()" class="sub-header">Step 1: Automated Checks</p>
            <p *ngIf="isStep2()" class="sub-header" >Step 2: User Verifications</p>
        </div>
    </div>
    <div *ngIf="!isStep2()" class="box">
        <div class="main-content">
            <div class="test-list">
                <div class="single-test">
                    <span class="pass">PASS</span>
                    <span class="test-text">1.1) Compatible Browser</span>
                </div>
                <div class="single-test" *ngFor="let singleTest of tests; let i = index;">
                    <div *ngIf="singleTest.status === 'pass'" class="pass">PASS</div>
                    <div *ngIf="singleTest.status === 'fail'" class="fail">FAIL</div>
                    <div *ngIf="singleTest.status === 'warn'" class="warn">WARN</div>
                    <span class="test-text">1.{{i+2}}) {{singleTest.test}}</span>
                </div>
            </div>
            <div *ngIf="incompatible">
                <p class="header">Click on the failed tests above to read more about the compatibility issues that were
                    detected.
                </p>
                <a class="test-text warning-link" (click)="moveToStep2()">Continue the FSA System Compatibility Test
                    despite known incompatibilities.</a>
            </div>
            <button *ngIf="!incompatible" class="continue" (click)="moveToStep2()">
                Continue the FSA System Compatibility Test
            </button>
        </div>
    </div>
    <div *ngIf="isStep2()" class="box box2">
        <div class="main-content-container">
            <div *ngIf="currentStep === 1" class="main-content">
                <p>
                    If this window is small, or if you are unable to see the Prev/Next buttons in the lower-right-hand
                    corner, then hit "Alt + F4" at the same time to exit and then turn off your popup blocker.
                </p><br>
                <p>
                    Otherwise, click "Next" to continue.
                </p>
            </div>
            <div *ngIf="currentStep === 2" class="main-content">
                <p>
                    If you can read this, then this computer is ready for the assessment. Click "Next" to exit the
                    system compatibility test.
                </p>
                <div class="check-container">
                    <img class="check"
                        src="https://bubo.vretta.com/vea/project-management/pm-bc-docs/uploads/8c77c1c42a132c800fbfe3a188fdf07c/iconmonstr-check-mark-1_1__1_.png" />
                </div>
            </div>
            <div class="next-prev-container">
                <a class="next-prev prev" (click)="prevStep()"><i class="fas fa-arrow-left"></i> Prev</a>
                <a class="next-prev next" (click)="nextStep()"><i class="fas fa-arrow-right"></i> Next</a>
            </div>
        </div>
    </div>
</div>