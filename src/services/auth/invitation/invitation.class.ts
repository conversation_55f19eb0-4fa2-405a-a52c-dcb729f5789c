import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
const _ = require('lodash');
import { Application } from '../../../declarations';
import { AccountType } from '../../../types/account-types';
import { IUser } from '../../db/schemas/users.schema';
import { IUserRole } from '../../db/schemas/user-roles.schema';
import { generateSecretCode } from '../../../util/secret-codes';
import { INVITATION_SECRET_KEY_LEN } from '../../../constants/db-constants';
import { IUInvites } from '../../db/schemas/u_invites.schema';
import { dbDateNow, dbDateOffsetDays } from '../../../util/db-dates';
import { renderInvitationCode } from '../../../util/roles';
import { defaultDomain } from '../../../constants/mail-constants';
import { DATETIME } from '../../../types/db-types';

interface Data extends INewUserInfo {}

interface ServiceOptions {}

export interface ICreationResponse {
  uid: number,
  invitationCode: string,
  created_on: DATETIME,
  expire_on: DATETIME,
}

export interface INewUserInfo {
  first_name?: string,
  last_name?: string,
  contact_email?: string,
  langCode: string,
  domain: string,
  offsetDays?:number,
  isAutoEmail?: boolean,
  emailSubjectSlug: string,
  emailTemplateSlug: string,
  emailTemplateParams: {
    [index:string]: string | undefined
  },
  account_type:AccountType,
  roles?:IRoleDef[],
  created_by_uid?: number
}
export interface IRoleDef {
  role_type: string,
  group_id: number,
  expires_on?: string
}

export interface INewAccountInfo  {
}

export class Invitation implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Error();
  }

  async create (data: Data, params?: Params): Promise<Data> {

    const {
      account_type,
      created_by_uid,
      first_name,
      last_name,
      contact_email,
      roles,
      isAutoEmail,
      emailSubjectSlug,
      emailTemplateSlug,
      emailTemplateParams,
      langCode,
      domain
    } = <INewUserInfo> <any> data;

    let offsetDays = data.offsetDays || 1;

    // create the user
    const userInsertFields: Partial<IUser> = {
      account_type,
      first_name,
      last_name,
      contact_email,
      is_claimed :   0
    }

    // console.log('new user', first_name,last_name, roles);

    const newUserRecord : IUser = await this.app
      .service('db/write/users')
      .create(userInsertFields);
    const uid = newUserRecord.id;

    // assign new roles
    if (roles){
      await Promise.all(
        roles.map(role => {
          const {role_type, group_id, expires_on} = role;
          const roleCreateFields:IUserRole = {
            role_type,
            uid,
            group_id,
            created_by_uid,
            is_revoked : 0,
            expires_on
          }
          return this.app
            .service('db/write/user-roles')
            .create(roleCreateFields);
        })
      )
    }

    // create invitation
    const invit_meta = JSON.stringify({ emailSubjectSlug, emailTemplateSlug, emailTemplateParams, langCode });
    const newInvitation = await this.createInvitation({
      uid, 
      created_by_uid, 
      isAutoEmail, 
      contact_email, 
      invit_meta, 
      offsetDays
    })
    const {
      invitationCode, 
      created_on, 
      expire_on
    } = newInvitation;
    
    // send email
    if(isAutoEmail && contact_email){
      if (emailSubjectSlug && emailTemplateSlug && emailTemplateParams && langCode){
        await this.sendEmail(contact_email, emailSubjectSlug, emailTemplateSlug, emailTemplateParams, invitationCode, langCode, domain);
      }
      else{
        throw new Error('insufficient config to send email')
      }
    }
    // response
    const res:ICreationResponse = {
      uid,
      invitationCode,
      created_on,
      expire_on,
    };
    return <any> res;
  }

  async createInvitation(config:{uid:number, created_by_uid?:number, isAutoEmail?:boolean, contact_email?:string, invit_meta?:string, isRevoked?:boolean, offsetDays:number}, ){
    const {uid, created_by_uid, isAutoEmail, contact_email, invit_meta, offsetDays, isRevoked} = config;
    const secret_key = generateSecretCode(INVITATION_SECRET_KEY_LEN);
    const invitationCreateFields:Partial<IUInvites> = {
      uid,
      secret_key,
      expire_on: dbDateOffsetDays(this.app, offsetDays),
      is_auto_email: (isAutoEmail && !isRevoked) ? 1 : 0,
      is_revoked: isRevoked ? 1 : 0,
      revoked_on: isRevoked ? dbDateNow(this.app) : undefined,
      created_by_uid,
      invit_email: contact_email,
      invit_meta,
    }
    const invitationRecord:IUInvites = await this.app
      .service('db/write/u-invites')
      .create(invitationCreateFields);
    const invitationCode = renderInvitationCode(<number>invitationRecord.id, invitationRecord.secret_key);
    const {created_on, expire_on} = invitationRecord;
    return {
      invitationCode,
      created_on, 
      expire_on
    }
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error();
  }

  async sendEmail (
    emailAddress:string,
    emailSubjectSlug:string,
    emailTemplateSlug:string,
    emailTemplateParams:{ [index:string]: string | undefined },
    invitationCode:string,
    langCode:string,
    domain?:string,
  ) {
    // console.log('entry into sendemail; ', emailSubjectSlug, emailTemplateSlug, langCode, domain);
    const translation = this.app.service('public/translation');
    const mailCore = this.app.service('mail/core');
    // TODO preexisting whitelabel?
    const whitelabel = domain || defaultDomain;
    return mailCore.sendEmail({
        whitelabel,
        emailAddress,
        subject: await translation.getOneBySlug(emailSubjectSlug, langCode),
        emailTemplate: await translation.getOneBySlug(emailTemplateSlug, langCode),
        parameterMapping: _.merge({}, emailTemplateParams, { INVITATION_CODE: invitationCode })
      });
  }

  async sendNonSlugEmail (
    emailAddress:string,
    emailSubject:string,
    emailTemplate:string,
    emailTemplateParams:{ [index:string]: string | undefined },
    invitationCode:string,
    domain?:string,
  ) {
    const mailCore = this.app.service('mail/core');
    const whitelabel = domain || defaultDomain;
    
    return mailCore.sendEmail({
        whitelabel,
        emailAddress,
        subject: {
          template: emailSubject, 
          parameterMapping: _.merge({}, emailTemplateParams)
        },
        emailTemplate: emailTemplate,
        parameterMapping: _.merge({}, emailTemplateParams, { INVITATION_CODE: invitationCode })
      });
  }
}
