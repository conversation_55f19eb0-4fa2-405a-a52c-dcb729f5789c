import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_03_ASMT_CONTENT_FORM_MODULE_ITEMS:IExportQueryDef = {
    requiredInputs: [
        'twtar_ids'
    ],
    queryGen: (config:IQueryConfig) => `
        select tfmi.test_design_id 
            , tfmi.test_panel_id 
            , tfmi.question_id  
            , tfmi.module_id 
            , tfmi.section_num 
            , tfmi.lang 
        from test_window_td_alloc_rules twtar 
        join test_form_module_items tfmi 
            on tfmi.test_design_id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
        where tfmi.is_revoked = 0
            and twtar.id in (:twtar_ids)
        group by tfmi.id 
    `
}