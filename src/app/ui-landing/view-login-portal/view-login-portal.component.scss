.login-portal {
  width: 100vw;
  min-height: 100vh;
  padding: 6em;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .portal-link-mosaic {
    display: flex;
    flex-wrap: wrap;
    .portal-link {
      width: 20em;
      height: 6em;
      //padding:1em;
      margin: 1.8em;
      transition: 200ms;
      box-shadow: 0.1em 0.2em 0.5em rgba(0, 0, 0, 0.2);
      position: relative;
      &:hover {
        filter: brightness(95%) contrast(110%);
        transform: scale(1.05);
        box-shadow: 0.1em 0.2em 0.8em rgba(0, 0, 0, 0.2);
      }
      a {
        width: 100%;
        height: 100%;
        position: absolute;
        padding: 1em;
        color: #333;
        font-size: 1.2em;
      }

      button {
        //background-color: rgb(93, 145, 204);
        font-size: 1em;
        color: rgb(255, 255, 255);
        border: none;
        // border-radius: 5px;
        height: 2.5em;
        width: 10em;
        &:hover {
          filter: brightness(95%) contrast(110%);
          transform: scale(1.05);
          box-shadow: 0.1em 0.2em 0.8em rgba(0, 0, 0, 0.2);
        }
      }
      button.confirm {
        background-color: rgb(93, 145, 204);
        border-bottom-left-radius: 9px;
      }
      button.not-my-school {
        background-color: rgb(207, 74, 74);
        border-bottom-right-radius: 9px;
      }
    }
  }
}

.pre-button-strip {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  margin-top: 6em;
  box-shadow: 0.1em 0.2em 0.5em rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.disabled-link {
  pointer-events: none;
}
