<div *ngIf="!testWindows">Loading...</div>
<div *ngIf="testWindows">
    <p>Select a test window...</p>
    <div class="select">
        <select [(ngModel)]="selectedTestWindowId" (change)="selectTestWindow()">
            <option *ngFor="let tw of testWindows" [value]="tw.id">
                {{tw.title_str}}
            </option>
        </select>
    </div>
</div>
<div *ngIf="selectedTestWindowId">
    <panel-tw-statement 
        [testWindowId]="selectedTestWindowId"
        [schoolDistrictId]="schoolDistrictGroupId">
    </panel-tw-statement>
</div>