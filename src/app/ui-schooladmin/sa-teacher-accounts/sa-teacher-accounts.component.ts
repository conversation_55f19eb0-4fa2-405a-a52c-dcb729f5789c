import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { randInt, randId, generateEntries, randArrEntry, randDate, coinFlip } from '../../ui-testadmin/demo-data.service';
import { randomName } from '../../constants/fakenames';
import * as moment from 'moment-timezone';
import { LangService } from "../../core/lang.service";
import { ISession, IValidationError } from "../data/types";
import { MemDataPaginated } from "../../ui-partial/paginator/helpers/mem-data-paginated";
import { AccountType } from "../../constants/account-types";
import { LoginGuardService } from '../../api/login-guard.service';
import { ListSelectService } from '../../ui-partial/list-select.service';
import { G9DemoDataService } from '../g9-demo-data.service';
import { FormControl } from '@angular/forms';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { AuthService, getFrontendDomain} from '../../api/auth.service';
import { IMappedList, renderMappedValue, initMappedList } from '../data/util';
import { RoutesService } from '../../api/routes.service';
import { IModalExport, IModalImport } from '../sa-students/model/types';
import { downloadFromExportData, IExportColumn, saveDataAsTsv } from 'src/app/ui-testctrl/tc-table-common/tc-table-common.component';
import { APITeacherColumnExportList } from './export/config';
import { ClassFilterId, MySchoolService } from '../my-school.service';
import { v4 as uuidv4 } from 'uuid';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { ROLES } from '../sa-modal-teacher-accounts/sa-modal-teacher-accounts.component';
import { cloneDeep } from 'lodash';

enum Overlays {
  NEW_TEACHER = 'NEW_TEACHER',
  EDIT_TEACHER = 'EDIT_TEACHER'
}

enum UserMetaKeyMapping {
  ABED = 'StudentIdentificationNumber',
}
enum TeacherAccountsModal {
  NEW = 'NEW',
  EDIT = 'EDIT',
  EXPORT = 'EXPORT',
  IMPORT = 'IMPORT'
}

const STANDARD_TIMEZONE = 'America/Toronto';

@Component({
  selector: 'sa-teacher-accounts',
  templateUrl: './sa-teacher-accounts.component.html',
  styleUrls: ['./sa-teacher-accounts.component.scss']
})

export class SaTeacherAccountsComponent implements OnInit {
  userInfo: any;
  selectedTeacher = false;
  constructor(
    public lang: LangService,
    public loginGuard: LoginGuardService,
    private listSelectService: ListSelectService,
    private g9DemoData: G9DemoDataService,
    private pageModalService: PageModalService,
    private auth: AuthService,
    public mySchool: MySchoolService,
    private routes: RoutesService,
    public whitelabel: WhitelabelService
  ) { }
  
  public teachersTable: MemDataPaginated<ISession>;
  public teachers: ISession[] = [];
  teacher: any;
  isInvitationSent = false;
  isRevokeConfirmed = false;
  newInvigilatorName = new FormControl(null);
  newInvigilatorClassCode = 'MPM1D - AC - 0';
  newInvigilatorStudents = new FormControl(null);
  newInvigilatorEmail = new FormControl(null);
  public isAllSelected = false;
  public isAnySelected = false;
  public baseFilter:string;
  public isLoaded: boolean;
  public isInited: boolean;
  OVERLAYS = Overlays;
  isPrivateSchool = false;
  public columnLabels;
  public columnStatusLabel;
  public availableClassrooms;
  classCodes = ['MPM1D - AC - 0', 'MFM1P - AP - 1', 'MPM1D - AC - 2', 'MPM1D - AC - 3', 'MPM1D - AC - 4', 'MPM1D - AC - 5']
  public pageModal: PageModalController;
  TeacherAccountsModal = TeacherAccountsModal;
  public isShowingSemesterFilter: boolean;
  currentClassFilter:ClassFilterId;
  private editCheck: boolean = false;

  currentTestWindow

  semesterValues = {
    "Full year": "lbl_full_year", "Semester":"sdc_student_math_class_semester", "Quadmester":"sdc_student_math_class_quadmester", "Octomester":"sdc_student_math_class_octomester"
  }

  @Output() onSetClassFilter = new EventEmitter();

  ngOnInit() 
  {
    this.initRouteView();
    this.isPrivateSchool = this.g9DemoData.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.setTestWindowFilter(this.mySchool.getCurrentTestWindow());
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.getUserData();
    this.wipeSelections()
  }

  initRouteView() {
    this.columnLabels = {
      id: this.lang.tra('sa_sessions_col_id'),
      invigilators: this.whitelabel.isABED() ? this.lang.tra('abed_teacher_invigilator')
      : 'sa_sessions_col_invig',
      classCode: this.lang.tra('sa_teachers_class_code'),
      hasAccess: this.whitelabel.isABED() ? this.lang.tra('abed_text_has_access') : this.lang.tra('sa_teachers_has_access'),
      students: this.lang.tra('sa_sessions_col_stud'),
      startTime: this.lang.tra('sa_teachers_col_times'),
      status: this.lang.tra('sa_sessions_col_stat')
    };

    if (!this.isInited) {
      console.log("here")
      this.isInited = true;
      this.loadTeachers();
    }    
  }

  getRelevantClassrooms(classrooms: any) {
    // get non placeholder classrooms, then get only the classrooms associated with the current test window
    return classrooms.
    filter(classroom => +classroom.is_placeholder !== 1). 
    filter(classroom => this.g9DemoData.filterClassroomTestWindow(classroom.id, this.currentTestWindow.id));
  }

  toggleSemesterFilter(){
    if (this.isShowingSemesterFilter){
      this.isShowingSemesterFilter = false; // to do: clear filter as well
      this.changeCategory(null)
    }
    else{
      this.isShowingSemesterFilter = true
    }
  }

  toggleSelectAll() {
    this.setSelectAll(this.isAllSelected);
  }

  activeOverlay: string;
  openModal(overlay) {
    this.activeOverlay = overlay;
  }

  closeActiveOverlay() {
    this.activeOverlay = null;
  }

  renderOverlayName(overlayId: string) {
    switch (overlayId) {
      case Overlays.NEW_TEACHER: return "Create New Account"
      case Overlays.EDIT_TEACHER: return "Edit Account"
      default: return overlayId;
    }
  }

  setSelectAll(state: boolean) {
    this.isAllSelected = state;
    this.teachers.forEach(session => session.__isSelected = state);
    this.isAnySelected = state;
  }
  checkSelection() {
    this.isAnySelected = false;
    this.isAllSelected = false
    this.teachers.forEach(session => {
      if (session.__isSelected) {
        this.isAnySelected = true;
      }
    });
  }
  wipeSelections() {
    this.teachers.forEach(student => {
      student.__isSelected = false
    });
  }

  renderTeacherInvitationToClipboard(teacher) {
    const homepage = window.location.host;
    const hostname = window.location.hostname;
    // if (this.isInvitationSent) {
    //   if (hostname !== 'localhost') {
    //     return `https://${homepage}/#/${this.lang.getCurrentLanguage()}/test-admin/accept-invitation/${this.teacher.invit_email}/${this.teacher.invit_id}X${this.teacher.secret_key}/${this.teacher.firstName}/${this.teacher.lastName}`;
    //   } else {
    //     return `${homepage}/#/${this.lang.getCurrentLanguage()}/test-admin/accept-invitation/${this.teacher.invit_email}/${this.teacher.invit_id}X${this.teacher.secret_key}/${this.teacher.firstName}/${this.teacher.lastName}`;
    //   }
    // }
    const url = hostname !== 'localhost' ? 
        `https://${homepage}/#/${this.lang.getCurrentLanguage()}/test-admin/accept-invitation/${teacher.invit_email}/${teacher.invit_id}X${teacher.secret_key}/${teacher.firstName}/${teacher.lastName}`
      :
        `${homepage}/#/${this.lang.getCurrentLanguage()}/test-admin/accept-invitation/${teacher.invit_email}/${teacher.invit_id}X${teacher.secret_key}/${teacher.firstName}/${teacher.lastName}`;
    return url;
  }

  onTeacherInviteLinkCopy(teacher) {
    alert(this.lang.tra('sa_invite_link'));
    //alert('The invitation link has been copied to your clipboard. You can paste it into an email to share it with the teacher.')
  }


  getSelected(throwNullSet: boolean) {
    const teachers = [].concat(this.teachers).filter(entry => entry.__isSelected);

    if (throwNullSet && teachers.length === 0) {
      alert('Please select a teacher before proceeding with this action.');
      throw new Error('No account selected');
    }

    return teachers;
  }

  loadTeachers() {
    this.teachers = []
    this.g9DemoData.teachers.list.forEach(teacher => this.processNewTeachers(teacher));
    this.teachersTable = new MemDataPaginated({
      data: this.teachers,
      pageSize: 5,
      sortSettings: {}
    });
    this.getSemester(this.teachers)
    this.isLoaded = true;
  }

  getInvigilatedClasses(teacher) {
    if(teacher && this.whitelabel.isABED()) {
      let classes = teacher.classCode || '';
      const teacherUid = teacher.id;
      const invigilatorAccounts = this.g9DemoData.invigilators.filter((inv) => inv.uid == teacherUid);
      invigilatorAccounts.forEach(inv => {
        const classroom = this.g9DemoData.teacherClassrooms.list.find(classroom => classroom.group_id == inv.group_id);
        if(classroom) {
          classes = classes.length == 0 ? classroom.name : classes + ', ' + classroom.name;
        }
      })
      return classes;
    } else {
      return teacher.classCode
    }
  }

  processNewTeachers(teacher) {
    teacher.semester_label = [];
    if(teacher.semester){
      teacher.semester.forEach(element => {
        teacher.semester_label.push(this.renderSemester(element))
      });
    }
    teacher.classCode = this.getInvigilatedClasses(teacher);
    this.teachers.push(teacher);
  }

  semesterFilters = []
  filteredStudents = []
  filteredStudentsList = []
  processFilterConditions(config) {
    this.filteredStudentsList = []
    //this.semesterFilters.push(value)
    if (config.checked) {
      this.semesterFilters.push(config.semester_group)
    }
    else {
      const i = this.semesterFilters.indexOf(config.semester_group)
      this.semesterFilters.splice(i, 1);
    }
    let filterCondition
    let filteredStudents
    if (this.semesterFilters.length > 0) {
      this.semesterFilters.forEach(item => {
        filterCondition = (teacher) => teacher.semester_label.includes(item)
        filteredStudents = this.filteredStudentsList;
        this.filteredStudentsList = filteredStudents.concat(this.teachers.filter(filterCondition))
      })
      this.teachersTable.injestNewData(this.filteredStudentsList)
    }
    else{
      this.teachersTable.injestNewData(this.teachers)
    }
  }
  renderSemester(id) {
    return renderMappedValue(this.g9DemoData.semesters, id);
  }
  
  changeCategory($event) {
    //this.processFilterConditions({checked:true,semester_group:this.baseFilter})
    this.semesterFilters = []
    this.teachersTable.injestNewData(this.teachers)
  }
  getSemester(students) {
    var numMapping = {};
    for (var i = 0; i < students.length; i++) {
        students[i].semester_label.forEach(semester => {
          if (numMapping[semester] === undefined) {
            if (semester !== undefined) {
              numMapping[semester] = 0;
            }
          }
          numMapping[semester] += 1;
        })
 
    }
    var greatestFreq = 0;
    var mode;
    for (var prop in numMapping) {
      if (numMapping[prop] > greatestFreq) {
        greatestFreq = numMapping[prop];
        mode = prop;
      }
    }
    //console.log(numMapping, mode)
    this.baseFilter = mode;
    // this.processFilterConditions({checked:true,semester_group:mode})
    // this.semesterFilters=[];
  }

  invitationExpired(expireOn: string) {
    const expireDate = moment(expireOn).format('YYYY-MM-DD');   
    const currentDate = moment.tz(moment(), STANDARD_TIMEZONE).utc().format('YYYY-MM-DD');  

    // Compare current date and invitation expire date -- if current date > expire date, return true; otherwise, return false
    if (moment(currentDate).isAfter(expireDate)){
      return true;
    } else {
      return false;
    }
  }

  createNewAccount() 
  {
    let time = "";
    let time2 = "";

    if (this.whitelabel.isABED())
    {
      time = this.auth.formatDateForWhitelabel(moment().format());
      time2 = this.auth.formatDateForWhitelabel(moment().add(1, 'days').format());
    } 

    else
    {
      time = moment.tz(moment(), moment.tz.guess()).format(this.lang.tra('datefmt_dashboard_short'));
      time2 = moment.tz(moment().add(1, 'days'), moment.tz.guess()).format(this.lang.tra('datefmt_dashboard_short'));
    }

    let newSession: any = 
    {
      id: this.teachers.length + 1,
      invigilator: this.newInvigilatorName.value,
      classroom_id: "",
      email: this.newInvigilatorEmail.value,
      classCode: this.newInvigilatorClassCode,
      description: "",
      students: this.newInvigilatorStudents.value,
      //times:string[];
      startTime: time,
      endTime: time2,
      isConfirmed: false,
      status: "",
      __isSelected: false
    };

    this.teachers.push(newSession);
    this.teachersTable.injestNewData(this.teachers);
    this.clearNewAccountFormAndCloseModal();
  }

  getClassCodes(event) {
    this.newInvigilatorClassCode = event;
  }

  clearNewAccountFormAndCloseModal() {
    this.newInvigilatorClassCode = this.classCodes[0];
    this.newInvigilatorName.reset()
    this.newInvigilatorEmail.reset();
    this.newInvigilatorStudents.reset();
    alert("New Teacher/Invigilator Created")
    this.closeActiveOverlay();
  }


  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }

  editTeacher(data, teacherId, classId) {
    const payload = { ...this.configurePayload(data), class_id: classId }
    return this.auth
      .apiUpdate(this.routes.SCHOOL_ADMIN_TEACHERS, teacherId, payload,this.configureQueryParams())
  }

  findSemester() {
    let type_slug = ''
    switch (this.currentClassFilter) {
      case ClassFilterId.ABED_SAMPLE:
        type_slug = 'ABED_SAMPLE'
        break;
      case ClassFilterId.ABED_GRADE_6:
        type_slug = 'ABED_GRADE_6'
        break;
      case ClassFilterId.ABED_GRADE_9:
        type_slug = 'ABED_GRADE_9'
        break;
      case ClassFilterId.ABED_GRADE_12:
        type_slug = 'ABED_GRADE_12'
        break;
      default:
        break
    }
    
    const tw = this.g9DemoData.testWindows.find(tw => tw.type_slug == type_slug && new Date(tw.date_end) > new Date());
    if(tw) return this.g9DemoData.semesters.list.find(semester => semester.testWindowId == tw.id);
  }

  configureGroupingPayload(data) {
    const _getKey = () => {
      if(this.whitelabel.isABED()) return UserMetaKeyMapping.ABED;
    }
    let schoolData: any = this.g9DemoData.schoolData
    const class_id = this.findClassIdByName(data.class_code) 
    const semester = this.findSemester();
    if(semester){
      const semester_id = semester.id;
      const is_fi = data.is_fi?1:0
      const key = _getKey()
      return {
        schl_group_id: schoolData.group_id,
        schl_dist_group_id: schoolData.schl_dist_group_id,
        name: data.class_code,
        semester_id: semester_id,
        is_grouping: data.is_grouping,
        course_type: this.currentTestWindow.type_slug,
        class_id,
        educator_id: data.teacher_uid,
        is_fi,
        key
      }
    }
  }


  configurePayload(data) {
    let schoolData: any = this.g9DemoData.schoolData
    let name = "";
    let classId;
    let whiteLabelContext;
    let role;
    if (data.classCode) {
      name = data.classCode;
      classId = this.findClassIdByName(data.classCode)
    }
    if (this.whitelabel.isABED()) {
      whiteLabelContext = this.whitelabel.getWhitelabelFlag();
      role = data.role;
    }
    return {
      first_name: data.firstName,
      last_name: data.lastName,
      email: data.email,
      schl_group_id: schoolData.group_id,
      class_id: classId || null,
      schl_dist_group_id: schoolData.schl_dist_group_id,
      name,
      whiteLabelContext,
      foreign_id: '',
      role: role || null
    }
  }
 
  configureQueryParams(){
    const schoolData: any = this.g9DemoData.schoolData
    if (schoolData){
      return {
        query: {
          schl_group_id: schoolData.group_id,
        }
      }
    }
    return null;
  }

  //having an optional config for when the input fails validation and we do not want to close the popup.
  newTeacherModalStart(leftOverConfig?) {
    this.selectedId = false;
    let config: any;
    if(leftOverConfig) {
      config = {
        teachers: [leftOverConfig.payload],
        classroomSelected: {}
      };
    }
    else {
      config = { classroomSelected: {} };
    }
    this.pageModal.newModal({
      type: TeacherAccountsModal.NEW,
      config,
      finish: config => this.confirmNewTeacher(config)
    });
  }

  confirmNewTeacher(config: { payload: ISession, classroomSelected: { [classroomId: number]: boolean } }){
    if(this.whitelabel.isABED() && config.payload?.role == ROLES.TEACHER && Object.keys(config.classroomSelected).length) {
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('create_new_teacher_abed'),
        confirm: () => this.newTeacherModalFinish(config)
      })
    } else {
      this.newTeacherModalFinish(config);
    }
  }

  async newTeacherModalFinish(config: { payload: ISession, classroomSelected: { [classroomId: number]: boolean } }) {
    const firstPassConfig = JSON.parse(JSON.stringify(config));
    const validationErrors: any[] = this.validateTeacher(config);
    if (validationErrors.length === 0) {
      // create new grouping (school class) if the user specified one
      const newGrouping = config.payload.grouping;
      const isNewClassCreated = newGrouping && newGrouping.trim();

      if(isNewClassCreated){
        const matchesOtherName = this.g9DemoData.classrooms.find(classroom =>{
          if (classroom.class_code.toLowerCase().replace(/\W/g, '') === newGrouping.toLowerCase().replace(/\W/g, '')){
            return true;
          }
        })
  
        // if (matchesOtherName){
        //   setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('sa_duplicate_classcode')), 0);
        //   return;
        // }

        const newClass = {class_code: newGrouping, is_grouping: 1}
        const groupingPayload = this.configureGroupingPayload(newClass);
        if(!groupingPayload){
          this.loginGuard.quickPopup("Please select a filter when creating a new grouping.")
          return;
        }
        const res = await this.auth
        .apiCreate(
            this.routes.SCHOOL_ADMIN_CLASSES,
            [groupingPayload],
            this.configureQueryParams()
        ); 
        if(res.length){

          if (res[0].id == -1){
            this.loginGuard.quickPopup('Could not create new grouping. (there is likely already grouping with the same name)')
            throw new Error();
          }
          
          const theSemester = this.findSemester();
          if(theSemester){
            const newClassroomRecord: any = {
              ...newClass,
              semester: theSemester.id,
              id: res[0].id,
              group_id: res[0].group_id,
              students: [],
              onboarding: 0,
              assessment: 0,
              currentStudents: initMappedList([]),
              semester_label: theSemester? theSemester.label:'',
            }
  
            var classRecordMod = cloneDeep(newClassroomRecord);
            classRecordMod.students = 0;
            classRecordMod.course_type = this.currentClassFilter;
            classRecordMod.curricShort = this.currentClassFilter;
            classRecordMod.is_placeholder = 0;
            classRecordMod.is_fi = 0;
            classRecordMod.__isSelected = false;
            classRecordMod.class_code = newClassroomRecord.class_code;
            classRecordMod.name = newClassroomRecord.class_code;
            this.g9DemoData.classrooms.push(classRecordMod); 
            this.g9DemoData.teacherClassrooms.map[classRecordMod.id] = classRecordMod;
            config.classroomSelected[res[0].id] = true;
          }
        }
      }

      // add new teachers, invite them, and add them to the classes specified
      await this.addNewTeachers([config]).then((res) => {
        if (res && res.isDuplicate) {
          this.loginGuard.quickPopup('sa_dupli_email');
        } else if (res && res.isAdminUsed) {
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('sa_teacher_account_duplicate_email')));
        }
        this.gotoFirstPage();
      });

      if (isNewClassCreated) {
        const relatedTeacher = this.teachers.find(teacher => teacher.classCode === classRecordMod.class_code);
        if (relatedTeacher != null) {
          classRecordMod.educator = relatedTeacher.firstName + " " + relatedTeacher.lastName;
          classRecordMod.teacher_uid = relatedTeacher.id;
        }
  
        const classroomIdx = this.g9DemoData.classrooms.findIndex(classroom => classroom.id === classRecordMod.id);
        if (classroomIdx != null) {
          this.g9DemoData.classrooms[classroomIdx] = classRecordMod;
        }
        this.g9DemoData.teacherClassrooms.list.push(classRecordMod);
        this.g9DemoData.teacherClassrooms.map[classRecordMod.id] = classRecordMod;
      }

      // console.log(this.g9DemoData.teacherClassrooms.list);
      // console.log(this.g9DemoData.teacherClassrooms.map);
      // console.log(this.g9DemoData.classrooms);
      // console.log(this.g9DemoData.invigilators);
    } 
    
    else {
      setTimeout(() => {
        this.loginGuard.quickPopup(validationErrors[0].message);
        this.newTeacherModalStart(firstPassConfig);
      }, 0);
    }
  }

  gotoFirstPage() {
    this.teachersTable.getPaginatorCtrl().currentPage = 1;
    this.teachersTable.refresh();
  }

  getRequiredFields(bulkCheck:boolean = false, editCheck:boolean) {
    /*
    if (!this.selectedId) {
      return [
        { prop: 'firstName', label: 'lbl_first_name' },
        { prop: 'lastName', label: 'lbl_last_name' },
        { prop: 'email', label: 'lbl_email' }
      ]
    }
    */
    let requiredFields = [
      { prop: 'firstName', label: 'lbl_first_name' },
      { prop: 'lastName', label: 'lbl_last_name' },
    ];
    if(this.whitelabel.isABED() && !editCheck) { 
      requiredFields.push({ prop: 'role', label: 'sa_abed_role'})
    }

    if(!bulkCheck && !editCheck){
      requiredFields.push({prop: 'email', label: 'lbl_email'})
    }
    return requiredFields;
  }

  private validateTeacher(
    data: { payload: ISession, classroomSelected: { [classroomId: number]: boolean } },
    bulkCheck?: boolean,
    editCheck?: boolean,
  ): any[] 
  {
    const createValidationError = (prop: string, message: string): IValidationError => { return { prop, message } }
    const teacher = data.payload;
    //const errors: IValidationError[] = [];
    const errors: any[] = [];
    // Required fields validation 
    const requiredFields = this.getRequiredFields(bulkCheck, this.editCheck);

    for (let i = 0; i < requiredFields.length; i++) {
      const field = requiredFields[i];
      const prop = field.prop;
      const val = teacher[prop];
      if (typeof val === 'undefined' || val === null || val.toString().trim() === '') {
        const fieldName = this.lang.tra(field.label);
        const errorMessage = this.lang.tra('sa_required_field', null, { fieldName });
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        if (!bulkCheck) {
          return errors;
        }else{
          break;
        }
      } else if (prop === "firstName" && val.length > 35) {
        const fieldName = this.lang.tra(field.label);
        const errorMessage = this.lang.tra('brc_teacher_firstname_char_len_range', null, { fieldName });
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        if (!bulkCheck) {
          return errors;
        }else{
          break;
        }
      } else if (prop === "lastName" && val.length > 35) {
        const fieldName = this.lang.tra(field.label);
        const errorMessage = this.lang.tra('brc_teacher_lastname_char_len_range', null, { fieldName });
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        if (!bulkCheck) {
          return errors;
        }else{
          break;
        }
      }
    }

   

    //email validation
    if (!this.selectedId ) {
      const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
      if (teacher.email !== undefined && teacher.email !== null && !re.test(String(teacher.email).trim().toLowerCase())) {
        //const error: IValidationError = createValidationError('email', this.lang.tra('sa_valid_email_prompt'));
        //errors.push(error);
        const prop = 'email';
        const errorMessage = this.lang.tra('sa_valid_email_prompt');
        const error = createValidationError(prop, errorMessage);
        errors.push(error);
        
        if (!bulkCheck) {
          return errors;
        }
      }
    }

    // Class Codes validation
    // const classIDs = Object.entries(data.classroomSelected).filter(el => el[1]);
    // if (classIDs.length === 0) {
    //   const prop = 'classCode';
    //   const fieldName = this.lang.tra('sa_teachers_class_code');
    //   const message = this.lang.tra('sa_required_field', null, { fieldName });
    //   const error: IValidationError = createValidationError(prop, message);
    //   errors.push(error);
    //   if (!bulkCheck) {
    //     return errors;
    //   }
    // }


    if (bulkCheck) {
      return errors
    } else {
      return [];
    }
  }

  getClassrooms(classrooms) {
    let classes = []
    const entries = Object.entries(classrooms)
    for (const [id, value] of entries) {
      if (value) {
        const currentClass = this.g9DemoData.classrooms.find(classroom => classroom.id == parseInt(id));
        classes.push(currentClass.class_code)
      }
    }
    return classes.join(', ');

  }
  getStudents(classrooms) {
    let numStudents = 0;
    const entries = Object.entries(classrooms)
    for (const [id, value] of entries) {
      if (value) {
        const currentClass = this.g9DemoData.classrooms.find(classroom => classroom.id == parseInt(id));
        numStudents += currentClass.students || 0;
       // console.log(currentClass)
      }
    }
    return numStudents
  }

  async addNewTeachers(configs: { payload: any, classroomSelected: any }[], isSingleTeacher: boolean = true) {
    const newTeachers = [];
    //let teacher;
    var is_grouping = -1;
    if(this.currentClassFilter !== undefined && this.currentClassFilter !== null){
      is_grouping = this.currentClassFilter==ClassFilterId.OSSLT? 1:0
    }  
    for (let config of configs) {
       const uuid = uuidv4();
       const teacher = {
        ...config.payload,
        invigilator: `${config.payload.firstName} ${config.payload.lastName}`,
        id: randId(),
        classroom_id: 'classroom_id',
        description: 'description',
        students: this.getStudents(config.classroomSelected),
        invit_email:config.payload.email,
        classCode: this.getClassrooms(config.classroomSelected),
        status: 'status',
        uuid: uuid,
        className: config.payload.classCode
      }
      config.payload = this.configurePayload(config.payload);
      config.payload.uuid = uuid;
      config.payload.is_grouping = is_grouping
     
      newTeachers.push(teacher)
    }
    const refinedConfigs = this.processInvitationData(configs);
    let isDuplicate = false;
    let failImportClassCreation = false;
    let isAdminUsed = false;

    return this.auth
      .apiCreate(this.routes.SCHOOL_ADMIN_TEACHERS, refinedConfigs,this.configureQueryParams())
      .then(res => {
        // check if at least one class was not created successfully via import
        // will send this to validateImport to bring up appropriate error modal import
        const firstTeacherFailedClassCreation = res.find((teacherCreated) => teacherCreated.unsuccessfulImportClassCreation === true);

        if (firstTeacherFailedClassCreation && firstTeacherFailedClassCreation.unsuccessfulImportClassCreation) failImportClassCreation = true;

        res.forEach((re, i) => {
          var teacher = newTeachers.find(teacher => teacher.uuid === re.uuid)
          const invite = res[i].invite;
          teacher = {...teacher, ...invite};
          teacher.is_claimed = res[i].is_claimed;
          teacher.id = re.id
          if( teacher.classCode == '' && teacher.className != undefined){ //teacher.name is class name
            teacher.classCode = teacher.className;
          } 
          if (invite.id)
          {
            if (this.whitelabel.isABED())
            {
              teacher.created_on = this.auth.formatDateForWhitelabel(teacher.created_on);
              teacher.expire_on = this.auth.formatDateForWhitelabel(teacher.expire_on);
            }

            else
            {
              teacher.created_on = moment.utc(teacher.created_on).tz("America/New_York").format(this.lang.tra('datefmt_dashboard_short'));
              teacher.expire_on = moment.utc(teacher.expire_on).tz("America/New_York").format(this.lang.tra('datefmt_dashboard_short'));
            }
          }
          else if (teacher.is_claimed == 1){
            teacher.isConfirmed = true;
          }
          
          console.log('teacher.hasAccess', teacher.isConfirmed)
          

          // handling duplicate teachers
          let isTeacherInList = this.teachers.find(t => (t.email === re.contact_email && t.id === re.id));
          if (isTeacherInList === undefined) {
            isTeacherInList = this.teachers.find(t => t.invit_email === re.invite.invit_email);
          }
          if (configs.length === 1 && isTeacherInList === undefined && !re.isUsedAdmin) {
            if(this.isTeacherRole(configs[0]) ) alert(this.lang.tra('sa_new_teacher_message'));
            else alert(this.lang.tra('sa_new_es_message'));
          }
          if (isTeacherInList === undefined && !re.isUsedAdmin) {
            // handle failed class creation via import, should not display class when teacher created
            if (re.unsuccessfulImportClassCreation) {
              teacher.classCode = '';
              teacher.className = '';
            }
            this.teachers.splice(0, 0, teacher);
          } else if (re.isUsedAdmin) {
            isAdminUsed = true;
          } else {
            isDuplicate = true;
            if (isTeacherInList.classCode !== teacher.classCode) {
              const indexOfTeacherToEdit = this.teachers.indexOf(isTeacherInList);
              this.teachers.splice(indexOfTeacherToEdit, 1, teacher);
            }
          }

          // handle taking grouping from existing teacher and giving it to new teacher
          if (!isDuplicate) {
            let unassignedTeacher = this.teachers.find(t => t.id === re.unassignedTeacherUid);
            if (unassignedTeacher) {
              const indexOfTeacherToEdit = this.teachers.indexOf(unassignedTeacher);
              const indexG9DemoTeacher  = this.g9DemoData.teachers.list.indexOf(unassignedTeacher);
              const newTeacherClassCodes: string[] = teacher.classCode.split(", ");
              for (let classCode of newTeacherClassCodes) {
                unassignedTeacher.classCode = unassignedTeacher.classCode.replace(classCode, "");
              }

              this.teachers.splice(indexOfTeacherToEdit, 1, unassignedTeacher);
              this.g9DemoData.teachers.list.splice(indexG9DemoTeacher, 1, unassignedTeacher);
            }

             if (isSingleTeacher) {
              const config = configs[0];
              // only do this for when creating a new teacher through the UI, when there is always 1 config
              const classrooms: number[] = [];
              for (let key of Object.keys(config.classroomSelected)) {
                if (config.classroomSelected[key]) {
                  classrooms.push(+key);
                }
            }

              for (let classroomId of classrooms) {
                const classroomIdx = this.g9DemoData.classrooms.findIndex(classr => classr.id === classroomId);
                const classroomInfo = this.g9DemoData.teacherClassrooms.map[classroomId];
                if (classroomIdx === -1 || classroomInfo == null) {
                  // should never happen, but fail safely
                  console.log("Class not found to localaly update");
                  continue;
                }

                if (this.isTeacherRole(config)) {
                  const teacherName = re.first_name + " " + re.last_name;
                  // user is added as the primary teacher of the class
                  this.g9DemoData.classrooms[classroomIdx].educator = teacherName;
                  this.g9DemoData.classrooms[classroomIdx].teacher_uid = re.id;

                  this.g9DemoData.teacherClassrooms.map[classroomId].currentTeachers = teacherName;

                  const teacherClassroomidx = this.g9DemoData.teacherClassrooms.list.findIndex(
                  teacherClassroom => teacherClassroom.id === classroomId);
                  if (teacherClassroomidx !== -1) {
                    this.g9DemoData.teacherClassrooms.list[teacherClassroomidx].currentTeachers = teacherName;
                  }
                }

                else {
                  // user is added as a new exam supervisor of the classes
                  const newInvil = {
                    contact_email: re.contact_email,
                    first_name: re.first_name,
                    last_name: re.last_name,
                    uid: re.id,
                    group_id: classroomInfo.group_id
                  }
                
                  this.g9DemoData.invigilators.push(newInvil);
                  const teacherIdx = this.teachers.findIndex(teacher => teacher.id === re.id);
                  if (teacherIdx !== -1) {
                    this.teachers[teacherIdx].classCode = "";
                  }
                }
              }
            }
          }

          //newTeachers.push(teacher);
          this.teachersTable.injestNewData(this.teachers);
          console.log('teacher.hasAccess (post)', teacher.isConfirmed)
          this.g9DemoData.teachers.list.splice(0, 0, teacher); 
        })
        this.wipeSelections();
        this.pageModal.closeModal();
        return {isDuplicate, failImportClassCreation, isAdminUsed};
      })
  }

  isTeacherRole(config: any) {
    return config.payload.role.toLocaleLowerCase() === "teacher";
  }

  processInvitationData(configs) {
    const refinedConfig = configs.map(data => {
      let payload = data.payload;
      let classroomSelected = data.classroomSelected;
      const currentTestWindow =  this.mySchool.getCurrentTestWindow()
      payload = currentTestWindow? { ...payload, ...this.getEmailData(), test_windows_id: currentTestWindow.id }:{ ...payload, ...this.getEmailData()}
      return { payload, classroomSelected }
    })
    return refinedConfig;
  }
  findClassIdByName(classCode) {
    const classId = this.g9DemoData.classrooms.find(classroom => classroom.class_code == classCode)
    return classId ? classId.id : null;
  }


  logInvitation(teacher) {
    const email = prompt(this.lang.tra('sa_connect_email')) // 'Please enter the teachers email address in the box below.'
    if (email !== null) {
      this.createInvite(email, teacher);
    }
  }

  extendInvitation(teacher) {
    const newPayload = { 
      ...this.configurePayload(teacher), 
      endTime: teacher.endTime,
      invit_id: teacher.invit_id,
      offSetDays: 3,
      emailLinkDomain: getFrontendDomain(),
    };
    return this.auth
        .apiUpdate(this.routes.SCHOOL_ADMIN_TEACHERS_INVITE, teacher.invit_id , newPayload, this.configureQueryParams())
        .then(res => {
          const updatedTeacher = this.teachersTable.getCurrentPageData().find(t => t.id == teacher.id);
          
          if (this.whitelabel.isABED())
          {
            updatedTeacher.endTime = this.auth.formatDateForWhitelabel(res.expire_on);
          }

          else
          {
            updatedTeacher.endTime = moment.tz(moment(res.expire_on), moment.tz.guess()).format(this.lang.tra('datefmt_dashboard_short'));
          }
          
          updatedTeacher.expireOn = res.expire_on;
        }).catch(err => {
          console.log(err);
        });
  }

  getEmailData() {
    return {
      langCode: this.lang.getCurrentLanguage(),
      schl_admin_uid: this.userInfo.uid,
      emailLinkDomain: getFrontendDomain()
    }
  }
  createInvite(email: string, teacher: any) {
    let schoolData: any = this.g9DemoData.schoolData
    const invitationLog = {
      email,
      teacher_record: teacher,
      langCode: this.lang.getCurrentLanguage(),
      teacher_UID: teacher.id,
      school_admin_uid: this.userInfo.uid,
      schl_group_id: schoolData.group_id,
      emailLinkDomain: getFrontendDomain(),
    }
    return this.auth
      .apiCreate(this.routes.SCHOOL_ADMIN_TEACHERS_INVITE, { invitationLog },this.configureQueryParams())
      .then(res => {
        //${this.teacher.invit_email}/${this.teacher.invit_id}X${this.teacher.secret_key}/${this.teacher.firstName}/${this.teacher.lastName}`;
        teacher.invit_email = email;
        teacher.invit_id = res.invit_id;
        teacher.secret_key = res.secret_key;
        this.isInvitationSent = true;
        this.teacher = res;
        const now = moment.tz(moment(), moment.tz.guess());

        if (this.whitelabel.isABED())
        {
          teacher.startTime = this.auth.formatDateForWhitelabel(moment().format());
          teacher.endTime = this.auth.formatDateForWhitelabel(moment().add(7, 'days').format());
        }
        
        else
        {
          teacher.startTime = now.format(this.lang.tra('datefmt_dashboard_short'));
          teacher.endTime = now.add(7, 'days').format(this.lang.tra('datefmt_dashboard_short'));
        }
        // alert(this.lang.tra('sa_connect_email_sent'));//"Invitation emails can take up to 24 hours to be received by teachers"
      })
      .catch(err => {
        if (err.message === 'INVALID_EMAIL') {
          alert(this.lang.tra('sa_invalid_email'))
        }
        if (err.message === 'DUPLI_EMAIL') {
          alert(this.lang.tra('sa_dupli_email'))
          const entry = this.teachers.find(theTeacher => theTeacher.id ==  teacher.id )
          const oldAcc = this.teachers.find(theTeacher => theTeacher.email == email)||this.teachers.find(theTeacher => theTeacher.invit_email == email)
          if(oldAcc.classCode && oldAcc.classCode.length >0){
            oldAcc.classCode += (', ');
          }
          oldAcc.classCode += (entry.classCode)
          const i = this.teachers.indexOf(entry);
          this.teachers.splice(i, 1);
          this.teachersTable.injestNewData(this.teachers);
          const indexG9DemoTeacher = this.g9DemoData.teachers.list.indexOf(entry);
          this.g9DemoData.teachers.list.splice(indexG9DemoTeacher,1);
          this.checkSelection();
        }
      })
      ;
  }

  getUserData() {
    this.auth
      .user()
      .subscribe(t => this.userInfo = t)

  }
  setTeacher($event: any) {
    this.selectedTeacher = $event.selected;
    if (!this.selectedTeacher) {
      this.getSelectedClassrooms($event.classCode)
      this.selectedId = $event.id;
    }
  }
  selectedId = null;
  selectedClassrooms = {}
  editTeacherModalStart() {
    this.editCheck = true;
    const teachers: any = this.getSelected(false)
    const config: any = {
      teachers,
      classroomSelected: {}
    };
    this.selectedId = null;
    this.pageModal.newModal({
      type: TeacherAccountsModal.EDIT,
      config,
      finish: config => this.editTeacherModalFinish(config)
    });
  }
  getSelectedClassrooms(classCode) {

    const classId = this.processClassCodes(classCode)
    this.selectedClassrooms = this.buildClassroomSelectedObject(classId)
  }
  processClassCodes(classCode) {
    let classId = []
    if(!classCode) {
      return classId;
    }
    const classCodes = classCode.split(',')
    classCodes.forEach(code => {
      let classCode = code.trim()
      const classes = this.g9DemoData.classrooms.find(classroom => classroom.class_code == classCode)
      if (classes) {
        classId.push(classes.id)
      }
    })
    return classId;
  }
  extractSelectedClassroomsFromImport(classCode) {
    const classId = this.processClassCodes(classCode)
    return this.buildClassroomSelectedObject(classId)
  }
  buildClassroomSelectedObject(classIds: number[]) {
    const classObj = {}
    classIds.forEach(id => {
      classObj[id] = true;
    })
    return classObj;
  }
  getTeacherId() {
    if (this.selectedId) {
      return this.selectedId
    }
    const teacher: any = this.getSelected(false)
    return teacher[0].id
  }
  editTeacherModalFinish(config) {
    delete config.teachers;
    config.classroomSelected = this.selectedClassrooms;
    const validationErrors: IValidationError[] = this.validateTeacher(config);
    // this.editCheck = false;
    if (validationErrors.length === 0) {
      const teacherId = this.getTeacherId()
      const teacher = this.teachers.find(session => session.id == teacherId)
      const indexOfTeacherToEdit = this.teachers.indexOf(teacher)
      const indexG9DemoTeacher  = this.g9DemoData.teachers.list.indexOf(teacher);
      const teacher_meta = this.teachersTable.getCurrentPageData().find(teacher => teacher.id == teacherId)
      config.payload = this.configurePayload(config.payload)
      this.editCheck = false;
      return this.auth
        .apiUpdate(this.routes.SCHOOL_ADMIN_TEACHERS, teacherId, config,this.configureQueryParams())
        .then(res => {
         // console.log(config.payload, teacher_meta);
          var teacherUpdated = {
            ...config.payload,
            invigilator: `${config.payload.first_name} ${config.payload.last_name}`,
            firstName: config.payload.first_name,
            lastName: config.payload.last_name,
            id: teacher_meta.id,
            email: teacher_meta.email,
            invit_email: teacher_meta.invit_email,
            classCode: this.getClassrooms(config.classroomSelected),
            isConfirmed: teacher_meta.isConfirmed,
            startTime: teacher_meta.startTime,
            endTime: teacher_meta.endTime,
            classroom_id: 115,
            description: 'description',
            students: this.getStudents(config.classroomSelected),
            __isSelected: true,
            status: teacher_meta.status,
          };
          teacherUpdated.semester = [];
          teacherUpdated.semester_label = [];
          if(config.classroomSelected){
            teacherUpdated.semester = Object.keys(config.classroomSelected).map(key => this.g9DemoData.classrooms.find(classroom => classroom.id== Number(key)).semester);
            teacherUpdated.semester_label = teacherUpdated.semester.map(semester =>this.g9DemoData.semesters.list.find(semester2 => semester2.id==semester).label);
          }
          if(this.selectedClassrooms){
            const entries = Object.entries(this.selectedClassrooms)
            for (const [id, value] of entries) {
              if (value) {
                const currentClass = this.g9DemoData.classrooms.find(classroom => classroom.id == parseInt(id));
                currentClass.teacher_uid = teacherUpdated.id;
                currentClass.educator = teacherUpdated.invigilator;
                this.teachers.forEach( otherTeacher => {
                  if(otherTeacher.classCode.indexOf(currentClass.class_code) >-1 && otherTeacher !== teacher){
                    const index = this.teachers.indexOf(otherTeacher)
                    const classCodeToremove = currentClass.class_code
                    const studentsToRemove = currentClass.students||0;
                    let otherTeacherClasscode = otherTeacher.classCode.split(', ')
                    otherTeacherClasscode = otherTeacherClasscode.filter(value => value !== classCodeToremove )
                    otherTeacher.classCode = otherTeacherClasscode.join(', ')
                    const semesterToRemove = this.g9DemoData.classrooms.find(classroom => classroom.class_code==classCodeToremove).semester
                    otherTeacher.semester = otherTeacher.semester.filter(semester => semester!=semesterToRemove)
                    const semesterLabelToRemove = this.g9DemoData.semesters.list.find(semester => semester.id == Number(semesterToRemove)).label
                    otherTeacher.semester_label = otherTeacher.semester_label.filter(label => label!=semesterLabelToRemove)
                    //update students
                    otherTeacher.students = otherTeacher.students - studentsToRemove;
                    console.log(otherTeacher)
                    this.teachers.splice(index, 1, otherTeacher)
                  }
                })
              }
            }
          }

          this.teachers.splice(indexOfTeacherToEdit, 1, teacherUpdated)
          this.g9DemoData.teachers.list.splice(indexG9DemoTeacher, 1, teacherUpdated);
          this.g9DemoData.teachers[teacherUpdated.id] = teacherUpdated;
          this.teachersTable.injestNewData(this.teachers);
          this.pageModal.closeModal()
         })
        //.catch(err => alert('could not edit teacher'))
    } else {
      this.editCheck = false;
      setTimeout(() => {
        this.loginGuard.quickPopup(validationErrors[0].message)
        this.editTeacherModalStart();
      }, 0);
    }
  }

  importTeachersModal() {
    const columns = this.getTeacherExportColumns();
    const config: IModalImport = {
      hideTable: false,
      columns
    }

    this.pageModal.newModal({
      type: TeacherAccountsModal.IMPORT,
      config,
      finish: this.importTeachersModalFinish
    })
  }

  importTeachersModalFinish = (config: { importData: any[] }) => {
    this.selectedId = false;
    const mappedImportData = config.importData.map((data) => {
      const classroomSelected = this.extractSelectedClassroomsFromImport(data.classCode)
      return { payload: data, classroomSelected };
    });
    //this.addNewTeachers(mappedImportData);
    this.validateImport(mappedImportData)
  }

  validateImport(importData){
    const errors = []
    const validData =[];
    const isBulkCheck = true;
    importData.forEach(row => {
      const validationErrors:any[] = this.validateTeacher(row, isBulkCheck);
      if (validationErrors.length > 0) {
        errors.push(validationErrors)
        //setTimeout(() => this.loginGuard.quickPopup(validationErrors[0].message), 0);
      }else{
        validData.push(row)
      }
    })
    if(validData.length > 0){
      this.addNewTeachers(validData, false).then(res =>{
        var resultMessage:string = '';
        if (res.failImportClassCreation) {
          const errorMessage = this.lang.tra("msg_teachers_import_class_creation_fail");
          this.loginGuard.quickPopup(errorMessage);
        } else {
          if(validData.length>0 && !res.isDuplicate && !res.isAdminUsed){
            resultMessage += "<b>"+this.lang.tra("msg_teachers_import_success")+"</b>"
            validData.forEach(sc => {resultMessage += ("<br>" + (sc.payload.first_name?sc.payload.first_name:'')+" "+ (sc.payload.last_name?sc.payload.last_name:''))})
            resultMessage += "<br>"
            resultMessage += "<br>"
          } else if (res.isDuplicate) {
              resultMessage += this.lang.tra("sa_dupli_email");
          } else if (res.isAdminUsed) {
            resultMessage += this.lang.tra("sa_teacher_account_duplicate_email");
          }
          if(errors.length>0){
            resultMessage += "<b>"+this.lang.tra("msg_teachers_import_failure")+"</b>"
            errors.forEach(error => resultMessage += "<br>" + error)
          }     
          setTimeout(() => this.loginGuard.quickPopup(resultMessage), 0);
        }
      })
    }

    
  }

  exportTeachersModal() {
    const teachers = this.teachersTable.getFilteredData();
    const columns = this.getTeacherExportColumns();
    const exportData = this.getExportData(teachers, columns);
    downloadFromExportData(exportData, columns, 'teachers-export', this.auth);
  }

  pageChanged() {
    if (!this.isAllSelected) {
      this.teachers.forEach(teacher => teacher.__isSelected = false);
    }
  }

c
  private getImportData(teachers) {
    const columns = this.getTeacherExportColumns();
    return teachers.map((teacher): Partial<ISession> => {
      let entry: Partial<ISession> = {};
      columns.forEach(col => {
        const prop = col.prop;
        entry[prop] = teacher[prop];
      });
      return entry;
    });
  }
  private getExportData(
    teachers: Partial<ISession>[],
    columns: IExportColumn[]): any[] {
    return teachers.map((teacher): Partial<ISession> => {
      let entry: Partial<ISession> = {};
      columns.forEach(col => {
        const prop = col.prop;
        let val = teacher[prop];
        const names = teacher.invigilator.split(' ');
        if (prop === 'firstName' && names && names.length > 0) {
          val = names[0];
        } else if (prop === 'lastName' && names && names.length > 1) {
          val = names[1];
        } else {
          val = teacher[prop];
        }
        entry[prop] = val;
      });
      return entry;
    });
  }

  private getTeacherExportColumns = (): IExportColumn[] => {
    let columns: IExportColumn[] = [];
    let modifiedLabels = {};
      modifiedLabels = {
      firstName: 'FirstName',
      lastName: 'LastName',
      classCode: 'ClassCode/Grouping',
      email: 'Email'
    };
    APITeacherColumnExportList.forEach(source => {
      columns.push({
        prop: source,
        caption: modifiedLabels[source] || source,
        isClickable: false
      });
    });
    return columns;
  }
  revokeSelectedTeachers() {
    const targets: any = this.getSelected(false);
    const confirmationMsg = this.whitelabel.isABED()? this.lang.tra("abed_edit_revoke_teacher_confirm",undefined,{teacher_count: targets.length}) : this.lang.tra('sa_revoke_warning',undefined,{ teacher_count: targets.length }) + " " + this.lang.tra('sa_teacher_revoke'); // You have selected ${targets.length} teachers to be revoked. Are you sure that you would like to remove the teacher(s) ability to administer the assessment?
    // if (targets.length > 1) {
    //  alert(this.lang.tra('sa_revoke_warning',undefined,{ teacher_count: targets.length }))
    //   //alert(`Warning. You have selected ${targets.length} teachers to be revoked.`);
    // };
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      confirm: () => {
        targets.map(entry => {
          this.archiveTeacher(entry.id || entry.uid)
            .then(res => {
              const i = this.teachers.indexOf(entry);
              this.teachers.splice(i, 1);
              this.teachersTable.injestNewData(this.teachers);
              const indexG9DemoTeacher = this.g9DemoData.teachers.list.indexOf(entry);
              this.g9DemoData.teachers.list.splice(indexG9DemoTeacher,1);
              this.checkSelection();
            })
        })
      }
    })
  }

  getCurrentSelections(selections: any[]) {
    return selections.map(selection => selection.invigilator)
  }
  archiveTeacher(classId) {
    return this.auth.apiRemove(this.routes.SCHOOL_ADMIN_TEACHERS, classId,this.configureQueryParams())
  }

  setClassFilter(filterId) {
    this.onSetClassFilter.emit(filterId);

    this.currentClassFilter = filterId;
  }

  getEducatorsHeaderSlug(){
    if (this.whitelabel.isNBED()){
      return 'sa_educators_header_nbed';
    }
    if (this.whitelabel.isABED()){
      return 'sa_educators_header_ABED';
    }
    return 'sa_educators_header';
  }
  
  setTestWindowFilter(tw){
    this.currentTestWindow = tw;

    if (this.currentTestWindow != null) {
      this.availableClassrooms = this.getRelevantClassrooms(this.g9DemoData.classrooms);
    }
  }

  isCurrentTestWindowActive(){
    if(this.currentTestWindow){
      //return new Date(this.currentTestWindow.date_end) > new Date ()
      //Comment out above line of code to allow teacher to be edit in closed windows
      return true;
    }
    return false; 
  }

  //Different from isCurrentTestWindowActive is when this.currentTestWindow is undefined this function will indicate test window is active ()
  isCurrentTestWindowExistAndInactive(){
    if(this.currentTestWindow){
      return new Date(this.currentTestWindow.date_end) < new Date ()
    }
    return false; 
  }

  getSANewTeacherSlug(){
    if (this.whitelabel.isABED()){
      return 'abed_new_teacher_exam_supervisor'
    }
    return 'sa_session_new_teacher'
  }

  getColHeaderClassroomSlug(){
    if (this.whitelabel.isABED()){
      return 'sa_classrooms_col_class_code_ABED'
    }
    return 'sa_classrooms_col_class_code'
  }

  getEditSelectedSlug(){
    if(this.whitelabel.isABED()){
      return "abed_text_eddit_Account_access"
    }
    return "Edit_Account_Access"
  }

  getCancelSelectedSlug(){
    if(this.whitelabel.isABED()){
      return "abed_text_revoke_Account_access"
    }
    return "Revoke_Account_Access"
  }

  getEditTeacherHeader(){
    if(this.whitelabel.isABED()){
      return "abed_edit_teacher_exam_supervisor"
    }
    return "sa_session_edit_teacher"
  }
}
