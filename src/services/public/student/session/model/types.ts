export interface IAttemptTimeInfo {
    ta_started_on: string,
    ts_started_on: string,
    ta_time_ext_m: number,
    ts_time_ext_m: number,
    twtar_duration_m: number,
    twtar_test_date_end?: string,
    ts_duration_m: number,
    twtar_is_sample: number,
    is_sample_time_enforced: number,
    hardstop_offset_h: number,
}

export enum EarlyYearSlugs {
    SUFFIX = "SCORE_ENTRY",
    NUMERACY_SLUG = "EYS_NUM_SCORE_ENTRY",
    LITERACY_SLUG = "EYS_LIT_SCORE_ENTRY",
}