import { Component, Input, OnInit } from '@angular/core';
import {Clipboard} from '@angular/cdk/clipboard';

@Component({
  selector: 'template-json-viewer',
  templateUrl: './template-json-viewer.component.html',
  styleUrls: ['./template-json-viewer.component.scss']
})
export class TemplateJsonViewerComponent implements OnInit {

  constructor(
    private clipboard: Clipboard
  ) { }

  ngOnInit(): void {
  }

  @Input() data: any;
  @Input() currentPath: string = '';

  expandedKeys: { [key: string]: boolean } = {};

  isObject(value: any): boolean {
    return value && typeof value === 'object' && !this.isArray(value);
  }

  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  isExpandable(value: any): boolean {
    return this.isObject(value) || this.isArray(value);
  }

  getKeys(obj: any): string[] {
    return this.isArray(obj) ? obj.map((_, index) => index.toString()) : Object.keys(obj);
  }

  toggleExpand(key: string): void {
    this.expandedKeys[key] = !this.expandedKeys[key];
  }

  formatPrimitive(value: any): string {
    if (typeof value === 'string') {
      return `"${value}"`;
    } else if (value === null) {
      return 'null';
    }
    return value;
  }

  isLastKey(key: string): boolean {
    const keys = this.getKeys(this.data);
    return keys.indexOf(key) === keys.length - 1;
  }
  getPath(elementPath: string): void {
    navigator.clipboard.writeText(elementPath).then(() => {
      alert('Path copied to clipboard!');
    }).catch(err => {
      alert(`Failed to copy: ${err.message}`);
    });
  }

  getCurrentPath(index, key) {
    return this.currentPath + (this.isArray(this.data) ? index : key) + '.';
  }
}
