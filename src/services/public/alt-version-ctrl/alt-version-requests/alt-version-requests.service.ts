// Initializes the `public/alt-version-ctrl/alt-version-requests` service on path `/public/alt-version-ctrl/alt-version-requests`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { AltVersionRequests } from './alt-version-requests.class';
import hooks from './alt-version-requests.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/alt-version-ctrl/alt-version-requests': AltVersionRequests & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/alt-version-ctrl/alt-version-requests', new AltVersionRequests(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/alt-version-ctrl/alt-version-requests');

  service.hooks(hooks);
}
