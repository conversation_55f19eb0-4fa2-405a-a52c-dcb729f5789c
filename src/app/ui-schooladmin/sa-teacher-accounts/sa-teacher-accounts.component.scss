@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/partials/_base.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_cover.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/page-types/classroom.scss';
@import '../../../styles/pseudo-objects/breacrumb-panel.scss';
@import '../../../styles/pseudo-objects/section-icons.scss';
@import '../../../styles/pseudo-objects/entries-list.scss';
.panel-breadcrumb {
    @extend %panel-breadcrumb;
}

.entries-container {
    @extend %entries-container;
}

.is-green {
    color: green;
}
.is-red {
    color: #da1212;
}

@import '../../../styles/partials/_modal.scss';
.custom-modal {
    @extend %custom-modal;
    .modal-contents {
        .simple-content-bounds.student-export {
            max-width: 65vw;
        }
    }
}

.dashboard-overlays {
    @extend %cover;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .dashboard-overlay-screen {
        @extend %cover;
        background-color: rgba(0, 0, 0, 0.6);
        animation: fade-in 500ms;
    }
    .dashboard-overlay-window {
        position: relative;
        background-color: #fff;
        overflow: auto;
        border-radius: 1em;
        box-shadow: 0px 0px 6em rgba(0, 0, 0, 0.8);
        animation: pop-in 900ms;
        .dashboard-overlay-header {
            padding: 1em;
            border-bottom: 1px solid #ccc;
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            .overlay-title {
                font-size: 1.2em;
            }
            .overlay-close {
                color: #ccc;
                cursor: pointer;
                font-size: 1.4em;
                &:hover {
                    color: #f00;
                }
            }
        }
        .dashboard-overlay-content {
            padding: 1em;
        }
    }
}
.classrooms-table-container {
    overflow-x: auto;
    table {
      border: 1px solid #dbdbdb;
      th {
        border: 1px solid white;
        border-bottom: 1px solid #dbdbdb;
          button.btn-filter {
              &:focus:not(:active) {
                  @include focus-box-shadow-light;
              }
          }
      }
    }
}

.extend {
    color:#fff;
    background-color: #5cb85c;
    font-size: 1.25em;
    padding: 0.25em, 0.3em;
    border-radius: 5%;
}

.edit-teacher {
    font-size:1.3rem; 
    font-weight: 700; 
    text-transform: uppercase;
    margin-bottom: 1.5rem !important;
}