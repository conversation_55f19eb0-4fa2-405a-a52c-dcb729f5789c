@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/partials/_modal.scss';
@import "~ag-grid-community/dist/styles/ag-theme-material.css";

.custom-modal {
    @extend %custom-modal;
}

button.template-selector {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 0.5em;
    width: 13em;
    height: 14em;
    background-color: #fff;
    margin: 0.5em;
    cursor: pointer;
    &:hover {
        border: 2px solid #0ac;
    }
}

.invisible {
    opacity: 0%;
}

.deselected {
    pointer-events: none;
    opacity: 60%;
}