export enum SecureLinkSlug {
    SEB_CONFIG = 'SEB_CONFIG',
    SEB_CONFIG_PASS = 'SEB_CONFIG_PASS',
    KIOSK_POLICY = 'KIOSK_POLICY',
    KIOSK_POLICY_CREDS = 'KIOSK_POLICY_CREDS',
  }

export interface ILinkItem {
    linkUrl?: string,
    linkCaption?: string,
    isSecure?: boolean,
    slug?: SecureLinkSlug | string,
    disabled?: boolean,
    modal?: string,
    descSlug?: string,
  }
export interface IIpData{
  ip:string
}

export interface IContactIT{
  primary:IITContact,
  secondary:IITContact
}
export interface ICheckListItem {
    id?: string,
    caption: string,
    links?: ILinkItem[],
    selectionCaption?: any[],
    selectionOptions?: {slug:string, caption:string}[],
  }
  
export interface ITechReadiProfile {
  checklist: {
      prior: ICheckListItem[],
      current: ICheckListItem[], // (main)
      after: ICheckListItem[]
  }
}

export enum TechReadinessModal {
    NEW = 'NEW',
    EGRESS_IP_ADDRESSES = 'EGRESS_IP_ADDRESSES',
    IT_CONTACTS = 'IT_CONTACTS',
    VIEW_PAYMENT_AGREEMENT = 'VIEW_PAYMENT_AGREEMENT',
}

export interface IEgressIPAddress {
    id?: number,
    ip: string;
}

export enum ITContactKey {
    PRIMARY = 'primary',
    SECONDARY = 'secondary'
}

export interface IITContact {
    name: string,
    email: string,
    phone: string
}