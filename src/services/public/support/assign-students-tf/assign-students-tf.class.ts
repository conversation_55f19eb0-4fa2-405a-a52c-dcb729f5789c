import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class AssignStudentsTf implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    const students = <any>data
    const created_by_uid = 21 // await currentUid(this.app, params)
    for (let i=0; i<students.length; i++){
      console.log(i)
      const studentInfo =  students[i];
      const testAttemptRecords = await dbRawRead(this.app, [studentInfo.uid], `select max(id) max_id from test_attempts where twtdar_order =0 and uid = ? order by uid ;`);
      if (testAttemptRecords && testAttemptRecords.length){
        const taId = testAttemptRecords[0].max_id;
        await this.app.service('db/write/test-attempts').patch(taId, {
          test_form_id: studentInfo.tf_id,
        })      
      }
      else{
        console.error('no record for ', studentInfo.uid)
      }
    }
    return []
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const tfs:{tf_id:number, panel_id:number, sourceFormId?:number}[] = <any>data;
    for (let tf of tfs){
      const {tf_id} = tf;
      const testFormRecord = await this.app.service('db/read/test-forms').get(tf_id);
      const testForm = await this.app.service('public/student/session').getTestDesign(testFormRecord.file_path);
      tf.sourceFormId = testForm.sourceFormId
    }
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
