import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
}

export const SQL_04B_REGISTRATIONS_CLASS_FORM_ALLOC:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    queryGen: (config:IQueryConfig) => `
        select sccf.id
            , twtar.type_slug
            , twtar.is_active
            , sccf.school_class_id
            , sccf.test_form_id
            , count(distinct ta.uid) n_students
            , max(ta.started_on) max_ta_started_on
        from test_window_td_alloc_rules twtar 
        join school_class_common_forms sccf 
            on sccf.twtdar_id = twtar.id 
            and sccf.is_revoked = 0
        left join school_class_test_sessions scts 
            on scts.school_class_id  = sccf.school_class_id 
        left join test_attempts ta 
            on ta.test_session_id = scts.test_session_id 
        where twtar.test_window_id IN (:tw_ids)
        group by sccf.id
        order by twtar.type_slug, sccf.school_class_id
    `
}