@import "../../../styles/partials/_modal.scss";
.custom-modal {
    @extend %custom-modal;
    .modal-contents.new-student-modal,
    .modal-contents.reenrol-modal,
    .modal-contents.unenrol-modal {
        width: 66em;
        min-height: 28rem;
        display: flex;
        flex-direction: column;

        .modal-title {
            font-family: Roboto;
            font-style: normal;
            font-weight: 300;
            font-size: 18px;
            line-height: 21px;
            /* identical to box height */

            color: #000000;
        }

        table {
            margin-top: 30px;
            tr {
                th {
                    vertical-align: middle;
                    border: solid 1px black;
                    border-left: none;

                    &:first-child {
                        border-left: solid 1px black;
                    }
                }
            }
            input[type="number"] {
                width: 150px;
                height: 27px;
            }
            button {
                margin-left: 10px;
            }
            .mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-thumb,
            .mat-slide-toggle.mat-primary.mat-checked .mat-ripple-element {
                background-color: black;
            }
            .mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar {
                background-color: rgba(0, 0, 0, 0.38);
            }
            .mat-slide-toggle-content {
                font-weight: 400;
            }
        }

        .lookup-results {
            position: relative;
            min-height: 250px;

            .accounts-loader {
                background-color: transparent;
            }
        }

        .pen-warning,
        .pen-info,
        .pen-error {
            margin-top: 20px;
            display: flex;
            align-items: center;
            color: black;
            border-radius: 10px;
            padding: 15px;

            .fa {
                margin-right: 10px;
                font-size: 24px;
            }
        }

        .pen-warning {
            background-color: rgb(240, 218, 81);
        }

        .pen-info {
            background-color: rgba(41, 144, 240, 0.63);
        }

        .pen-error {
            background-color: rgba(228, 115, 115, 0.5);
        }

        .vertical-grow {
            min-height: 30px;
            flex-grow: 1;
        }

        .student-form {
            > div {
                display: flex;
                align-items: center;
                margin-top: 20px;
                > div {
                    font-family: Roboto;
                    font-style: normal;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 21px;
                    min-width: 110px;
                    /* identical to box height */
                    margin-right: 10px;
                    color: #000000;
                }

                input {
                    // font-size: 10px;
                    height: 27px;
                    width: 200px;
                }
            }

            .required-star {
                color: red;
            }
        }

        .input-error {
            margin-top: 5px;
            color: red;

            // &.pen-error {
            //     margin-left: 158px;
            // }
        }
        .pen-support {
            background-color: rgb(220, 220, 220);
            padding: 10px;
            margin-top: 10px;
            border-radius: 10px;
            > div {
                &:nth-child(2),
                &:nth-child(4),
                &:nth-child(3) {
                    display: flex;
                    > div {
                        &:first-child {
                            width: 100px;
                        }
                    }
                }
                &:nth-child(2),
                &:nth-child(3) {
                    margin-top: 10px;
                }
            }
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            button {
                height: 33px;
                margin-left: 10px;

                &.cancel-button {
                    color: red;
                    border-color: red;
                }

                &.add-button {
                    &:enabled {
                        color: rgb(1, 133, 255);
                        border-color: rgb(1, 133, 255);
                    }
                }
            }
        }
        button {
            border: 1px solid gray;
            box-sizing: border-box;
            border-radius: 3px;
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 16px;
            text-align: center;
            color: #000000;
            margin-right: 10px;
            background-color: white;
            padding: 3px 10px;
            cursor: pointer;

            &:disabled {
                color: gray;
                border-color: gray;
                cursor: default;
            }
        }
    }

    .modal-contents.reenrol-modal,
    .modal-contents.unenrol-modal {
        width: 37rem;
        min-height: 11rem;
    }
}

.accounts-students {
    .page-header {
        .intro {
            margin-top: 10px;
            margin-bottom: 20px;
            font-style: normal;
            font-weight: normal;
            font-size: 14px;
            line-height: 16px;
            color: #000000;
        }
    }
    .page-body {
        position: relative;
        background-color: white;
        padding: 15px;
        // max-width: fit-content /*1055px*/;
        .view-selectors {
            display: flex;
            justify-content: flex-start;
            button {
                border: 1px solid #e4e4e4;
                box-sizing: border-box;
                border-radius: 3px;
                font-style: normal;
                font-weight: 500;
                font-size: 14px;
                line-height: 16px;
                text-align: center;
                color: #000000;
                margin-right: 10px;
                background-color: white;
                padding: 3px 10px;
                cursor: pointer;
                &.selected {
                    background-color: #0085ff;
                    color: white;
                }
                &.final {
                }
                &.pre {
                }
                &.compare {
                }
            }
        }
        .view-container {
        }
        .accounts-students-table {
            thead {
                th {
                    vertical-align: bottom;
                    color: rgb(79, 79, 79);

                    span {
                        font-weight: 600;
                        font-size: 12px;
                    }

                    &:nth-child(9),
                    &:nth-child(10),
                    &:nth-child(11) {
                        color: black;
                    }
                }
            }
        }
        .students-final-registration {
            min-height: 400px;
            position: relative;
            padding: 15px;
            .student-loads {
                margin-bottom: 20px;
                button {
                    height: 45px;
                    border-radius: 5px;
                    background-color: white;
                    cursor: pointer;

                    &:first-child {
                        width: 134px;
                        margin-right: 10px;
                    }

                    &:last-child {
                        width: 134px;
                    }

                    &.selected {
                        color: white;
                        background-color: rgb(1, 133, 255);
                        border: none;
                    }
                }
            }
            .top-section {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                margin-bottom: 15px;
                .add-account {
                    background-color: #99cc87;
                    padding-right: 15px;
                    margin-left: 10px;
                    .fa {
                        margin-left: 5px;
                        margin-right: 5px;
                    }
                }
                .page-title {
                    font-family: Roboto;
                    font-style: normal;
                    font-weight: 300;
                    font-size: 18px;
                    line-height: 21px;
                    /* identical to box height */

                    color: #000000;

                    label {
                        font-family: Roboto;
                        font-style: normal;
                        font-weight: normal;
                        font-size: 14px;
                        line-height: 16px;

                        color: #000000;
                        margin-right: 10px;
                    }

                    select {
                        min-width: 150px;
                        height: 27px;
                        font-family: Roboto;
                        font-style: normal;
                        font-weight: normal;
                        font-size: 14px;
                        line-height: 16px;

                        color: #000000;
                    }
                }

                button {
                    cursor: pointer;
                    background: #f5f5f5;
                    border: 1px solid #c4c4c4;
                    border-radius: 4px;
                    height: 25px;
                    border-radius: 4px;
                    .title {
                        font-style: normal;
                        font-weight: 500;
                        font-size: 13px;
                        line-height: 15px;
                        text-align: center;
                        color: #3e3e3e;
                    }
                    &.export {
                        margin-left: 8px;
                        width: 75px;
                        .icon {
                            width: 12px;
                            height: 13px;
                            position: relative;
                            top: 2px;
                            margin-right: 6px;
                            background-image: url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6388/authoring/fsa_accounts_export_button/*************/fsa_accounts_export_button.svg");
                        }
                    }
                }
            }
            .test-window-tag {
                font-family: Roboto;
                font-style: normal;
                font-weight: normal;
                font-size: 14px;
                line-height: 16px;
                margin-left: 140px;
                margin-top: -7px;
                color: #000000;
                span {
                    color: red;
                }
            }
            .actions {
                margin-top: 33px;
                label {
                    font-family: Roboto;
                    font-style: normal;
                    font-weight: normal;
                    font-size: 14px;
                    line-height: 16px;

                    color: #000000;
                    margin-right: 10px;
                }
                select {
                    min-width: 150px;
                    height: 27px;
                    font-family: Roboto;
                    font-style: normal;
                    font-weight: normal;
                    font-size: 14px;
                    line-height: 16px;

                    color: #000000;
                }
                button {
                    cursor: pointer;
                    background: #f5f5f5;
                    border: 1px solid #c4c4c4;
                    border-radius: 4px;
                    height: 27px;
                    border-radius: 4px;
                    .title {
                        font-style: normal;
                        font-weight: 500;
                        font-size: 13px;
                        line-height: 15px;
                        text-align: center;
                        color: #3e3e3e;
                    }
                    &.export {
                        margin-left: 30px;
                        width: 75px;
                        .icon {
                            width: 12px;
                            height: 13px;
                            position: relative;
                            top: 2px;
                            margin-right: 6px;
                            background-image: url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6388/authoring/fsa_accounts_export_button/*************/fsa_accounts_export_button.svg");
                        }
                    }

                    &.add-account,
                    &.request-special-format {
                        background-color: #99cc87;
                        padding-right: 15px;
                        margin-left: 10px;
                        .fa {
                            margin-left: 5px;
                            margin-right: 5px;
                        }
                    }
                    &.show-validation-code {
                        margin-left: 10px;
                        width: 226px;
                        .fa {
                            margin-right: 5px;
                        }
                    }
                    &.unenrol-button,
                    &.reenrol-button {
                        margin-left: 10px;
                        min-width: 100px;
                    }
                    &.enrolment {
                        height: 50px;
                    }
                }
            }
            .dropdown-selectors {
                margin-bottom: 30px;
                display: flex;
                justify-content: flex-start;
                select {
                    min-width: 200px;
                    font-style: normal;
                    font-weight: 500;
                    font-size: 22px;
                    line-height: 26px;
                    color: #000000;
                    cursor: pointer;
                    padding: 0 0 0 5px;
                    border: none;
                    border-bottom: 1px solid black;
                    option {
                        font-size: 14px;
                    }
                }
                .district-selector {
                    margin-right: 30px;
                }
            }
            .table-paginator-container {
                background-color: white !important;
                margin-bottom: 25px;
            }
            .table-section {
                position: relative;
                margin-top: 10px;
                min-height: 300px;
                table {
                    thead {
                        th {
                            border-bottom: solid 1px black;
                            vertical-align: middle;
                            padding: 0;

                            &:first-child {
                                text-align: center;
                            }

                            > div:first-of-type {
                                display: flex;
                                align-items: center;

                                > div:first-of-type {
                                    height: 100%;
                                    flex-grow: 1;
                                    padding: 10px;
                                    cursor: pointer;
                                    &:hover {
                                        background-color: rgb(230, 230, 230);
                                    }

                                    &:active {
                                        background-color: rgb(205, 205, 205);
                                    }
                                }

                                .fa-caret-up,
                                .fa-caret-down {
                                    margin-left: 10px;
                                }

                                .fa-filter {
                                    padding: 4px 3px;
                                    margin-right: 5px;
                                    width: 20px;
                                    height: 20px;
                                    border: solid 1px gray;
                                    border-radius: 2px;
                                    background-color: #f1f1f1;
                                    cursor: pointer;

                                    &:hover {
                                        background-color: #e5e5e5;
                                    }
                                }
                            }
                            > div:nth-of-type(2) {
                                background-color: #f1f1f1;
                                padding: 10px;
                            }

                            select {
                                width: 100%;
                            }
                        }
                    }
                    tbody {
                        td {
                            height: 37px;

                            .u-tag {
                                background-color: red;
                                color: white;
                                border-radius: 5px;
                                padding: 1px 5px;
                                font-weight: 700;
                                margin-left: 14px;
                            }
                        }
                        tr:last-child {
                            td {
                                border-bottom: solid 1px black;
                            }
                        }
                        td:first-child {
                            border-left: solid 1px black;
                        }
                        td:last-child {
                            border-right: solid 1px black;
                        }
                        td:nth-child(2) {
                            .student-pen {
                                display: flex;
                                flex-wrap: nowrap;

                                &.link {
                                    span {
                                        color: rgb(1, 133, 255);
                                        cursor: pointer;
                                        text-decoration: underline;
                                    }
                                }
                            }
                            .student-new-tag {
                                margin-left: 10px;
                                background-color: rgb(2, 105, 202);
                                width: 35px;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                color: white;
                                border-radius: 6px;
                            }
                        }
                    }
                }
            }
        }

        button.minimal {
            background-color: transparent;
            border: 1px solid #dcdcdc;
            border-radius: 4px;
            height: 32px;
            font-size: 12px;
            font-weight: 500;
            line-height: 14px;
            text-align: center;
            cursor: pointer;

            &:focus {
                outline: none;
            }

            &:active:enabled {
                background-color: rgba(0, 0, 0, 0.1);
            }

            &:disabled {
                cursor: default;
            }
        }

        .no-selected-assessment {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 300px;
            font-size: 18px;
        }
        .total-students {
            margin-left: 20px;
        }

        .accounts-pagination {
            display: flex;
            margin-top: 26px;

            button.minimal {
                height: 20px;
                min-width: 22px;
            }

            > button,
            > div {
                margin-right: 5px;
            }
        }
    }
}

.accounts-loader {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    left: 0;
    top: 0;
    position: absolute;

    div {
        border: 10px solid lightgray;
        border-radius: 50%;
        border-top: 10px solid gray;
        width: 50px;
        height: 50px;
        -webkit-animation: spin 2s linear infinite; /* Safari */
        animation: spin 2s linear infinite;
    }
}

/* Safari */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.green-validation-tag {
    margin-left: 10px;
    background-color: rgb(108, 185, 86);
    width: 65px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    border-radius: 6px;
}
.red-validation-tag {
    margin-left: 10px;
    background-color: red;
    width: 65px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    border-radius: 6px;
}
.walk-in-tag {
    background-color: rgb(1, 133, 255);
    width: 65px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    border-radius: 6px;
}
