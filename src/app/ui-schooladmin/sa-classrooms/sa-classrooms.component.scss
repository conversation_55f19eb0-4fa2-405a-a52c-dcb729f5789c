@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/partials/_modal.scss';
@import '../../../styles/partials/_base.scss';

.custom-modal { @extend %custom-modal; }

.classrooms-table-container {
  overflow-x: auto;
  table {
    border: 1px solid #dbdbdb;
    th {
      border: 1px solid white;
      border-bottom: 1px solid #dbdbdb;
      
      button.btn-filter {
        &:focus:not(:active) {
          @include focus-box-shadow-light;
        }
      }
    }
  }
}

a.modal-link {
  text-decoration: underline;
  font-weight:400;
}
.fr-imm {
  background-color: rgb(183, 223, 160);
}

.edit-grouping {
  font-size:1.3rem; 
  font-weight: 700; 
  text-transform: uppercase;
  margin-bottom: 1.5rem !important;
}