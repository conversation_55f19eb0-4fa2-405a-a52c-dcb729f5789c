export const arrRemoveEl = <T>(arr:T[], value:T) => {
    for (let i = arr.length - 1; i >= 0; i--) {
      if (arr[i] === value) {
        arr.splice(i, 1);
      }
    }
    return arr;
}

export const objectToNumMap = (obj: { [key: number]: any }) => {
  const map = new Map();
  Object.keys(obj).forEach(key => {
    map.set(+key, obj[key]);
  })
  return map;
}

export const objToArr = (obj:any, propName='key') => {
  return Object.entries(obj).map((pair:any) => {
    const [key, val] = pair
    return {
      ... val,
      [propName]: key,
    }
  })
}