import { Component, EventEmitter, OnInit, Output, Input } from '@angular/core';
import { AuthService} from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { G9DemoDataService } from '../g9-demo-data.service';
import { ClassFilterId, MySchoolService } from '../my-school.service';

@Component({
  selector: 'sa-modal-session-payment-alternative-overview',
  templateUrl: './sa-modal-session-payment-alternative-overview.component.html',
  styleUrls: ['./sa-modal-session-payment-alternative-overview.component.scss']
})
export class SaModalSessionPaymentAlternativeOverviewComponent implements OnInit {
  @Input() sessionList: any[];
  @Input() schoolName: string = '';
  @Input() administrationWindow: string = '';
  @Input() assessmentName: string = '';
  @Input() currentClassFilter: string = '';
  @Input() totalCost: number;

  @Output() onBack = new EventEmitter();
  @Output() onCompletePaymentAlt = new EventEmitter();

  studentList: any[] = [];

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private g9DemoData: G9DemoDataService,
  ) { }

  isChecked: boolean = false;

  ngOnInit(): void {
    this.getStudents();
  }

  onChange(e) {
    if (e) {
      this.isChecked = e.checked;
    }
  }

  getStudents() {
    const classroomIds = this.sessionList.map(session => session.classroom_id);
    const students = [];

    this.g9DemoData.schoolAdminStudents.list.map(student => {
      if (this.currentClassFilter == ClassFilterId.G9 && student.eqao_g9_class_code){
        if (classroomIds.includes(student.eqao_g9_class_code)) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.OSSLT && student._g10_eqao_g9_class_code){
        if (classroomIds.includes(student._g10_eqao_g9_class_code)) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.Primary && student._g3_eqao_g9_class_code){
        if (classroomIds.includes(student._g3_eqao_g9_class_code)) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.Junior && student._g6_eqao_g9_class_code){
        if (classroomIds.includes(student._g6_eqao_g9_class_code)) {students.push(student)}
      }
    });

    this.studentList = students.filter(student => !student.isPaid);
  }

  async confirm() {
    const studentsInfo = [];
    for (let student of this.studentList) {
      studentsInfo.push({
        uid: student.uid,
        class_group_id: student.class_group_id
      })
    }
    const altCheckout = await this.auth
      .apiCreate(this.routes.SCHOOL_ADMIN_SESSION_PURCHASE, {
        schl_group_id: this.g9DemoData.schoolData.group_id,
        schl_mident: this.g9DemoData.schoolData.foreign_id,
        test_session_ids: this.sessionList.map(session => session.id),
        studentsInfo,
        purchase_by_uid: this.auth.getUid(),
        schoolName: this.schoolName,
        assessmentName: this.assessmentName,
        administrationWindow: this.administrationWindow,
        sessionNames: this.sessionList.map(session => session.classCode),
        numOfStudents: this.studentList.length,
        purchase_method_id: 2,
        totalCost: this.totalCost
      })
      .then((res) => {
        console.log('altCheckout', res)
        this.onCompletePaymentAlt.emit(res);
      });
  }

  configureQueryParams(){
    return {
      query: {
      }
    }
  }

  back() {
    this.onBack.emit(true);
  }
}
