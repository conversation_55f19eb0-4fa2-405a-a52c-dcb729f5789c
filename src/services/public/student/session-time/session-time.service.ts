// Initializes the `public/student/session-time` service on path `/public/student/session-time`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SessionTime } from './session-time.class';
import hooks from './session-time.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/session-time': SessionTime & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/session-time', new SessionTime(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/session-time');

  service.hooks(hooks);
}
