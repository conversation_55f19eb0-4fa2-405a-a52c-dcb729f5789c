.bcg-loader {
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: -5px 0;

    div {
        border: 7px solid lightgray;
        border-radius: 50%;
        border-top: 7px solid darkgray;
        width: 30px;
        height: 30px;
        -webkit-animation: spin 1s linear infinite; /* Safari */
        animation: spin 1s linear infinite;
    }
}

/* Safari */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.no-school {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    font-family: Roboto;
    font-style: normal;
    font-weight: normal;
    font-size: 17px;
    line-height: 20px;

    color: #000000;
}

.report-section {
    position: relative;
}
.cover-with-loading {
    min-height: 400px;
}
.select-sheet {
    margin-bottom: 20px;
    .sheet-button {
        width: 180px;
        height: 24px;
        border-radius: 3px;
        border: solid 1px rgb(148, 148, 148);
        font-weight: 500;
        font-size: 14px;
        line-height: 16px;
        text-align: center;
        color: black;
        background-color: white;
        margin-top: 9px;
        cursor: pointer;

        &.selected {
            background-color: rgb(0, 133, 255);
            color: white;
            border: none;

            &:active {
                background-color: rgb(0, 118, 228);
            }
        }

        &:disabled {
            color: darkgray;
            cursor: default;
        }

        &:active {
            background-color: rgb(231, 231, 231);
        }

        &:focus {
            outline: none;
        }

        margin-right: 8px;
    }

    .export-button {
        cursor: default;
        background: #f5f5f5;
        border: 1px solid #c4c4c4;
        border-radius: 4px;
        height: 25px;
        radius: 4px;
        margin-left: 30px;
        .title {
            font-style: normal;
            font-weight: 500;
            font-size: 13px;
            line-height: 15px;
            text-align: center;
            color: #3e3e3e;
        }
        //width: 75px;
        .icon {
            width: 12px;
            height: 13px;
            position: relative;
            top: 2px;
            margin-right: 6px;
            background-image: url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6388/authoring/fsa_accounts_export_button/*************/fsa_accounts_export_button.svg");
        }

        &:enabled {
            cursor: pointer;
            color: #3e3e3e;
        }
    }
}

.proficiency-level-description {
    margin-top: 20px;
    > div:first-child {
        font-weight: 700;
    }
}

.proficiency-level-note {
    margin-top: 30px;
}
