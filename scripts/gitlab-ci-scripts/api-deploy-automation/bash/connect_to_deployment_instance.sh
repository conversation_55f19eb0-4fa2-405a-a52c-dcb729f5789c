#!/bin/bash

# Retrieve the instance ID from the stored file
instance_id=$(cat "$CI_PROJECT_DIR/$CI_COMMIT_SHA-test_api_instance_id")
echo "Instance ID: $instance_id"

# Function to execute a command via AWS SSM and retrieve the output
run_command_via_ssm() {
    local instance_id=$1
    local command=$2
    local s3_log_bucket="vea-deploy-pipeline"
    local s3_logfile_key="build-logs-asg-${CI_API_ASG_NM}_${CI_COMMIT_SHA}.txt"

    # remove any existing S3 logs for this commit hash if applicable
    echo "removing existing S3 logs for commit hash"
    aws s3 rm "s3://${s3_bucket}/${s3_log_file_key}"
    # Send the command
    local command_id=$(aws ssm send-command \
        --instance-ids "$instance_id" \
        --document-name "AWS-RunShellScript" \
        --parameters "commands=$command" \
        --output-s3-bucket-name "${s3_log_bucket}" \
        --output-s3-key-prefix "${s3_logfile_key}" \
        --query "Command.CommandId" \
        --output text)
    echo "Command ID: $command_id"

    # Initialize command status
    local status="Pending"

    while [ "$status" != "Success" ] && [ "$status" != "Failed" ];
    do
        echo "Waiting for command to complete..."
        sleep 5  # Adjust the sleep duration as needed

        # Check the command status
        status=$(aws ssm get-command-invocation \
            --command-id "$command_id" \
            --instance-id "$instance_id" \
            --output text \
            --query "Status")

        # Check for timeout or other failure
        if [ "$status" = "Failed" ]; then
            echo "Command failed to execute. Please review SSM logs for command $command_id"
            exit -1
        fi
    done

    if [ "$status" = "Success" ]; then
        # Retrieve and display the command output
        local std_output=$(aws ssm get-command-invocation \
            --command-id "$command_id" \
            --instance-id "$instance_id" \
            --output text \
            --query "StandardOutputContent")
        echo "Output: $std_output"

        local std_err=$(aws ssm get-command-invocation \
            --command-id "$command_id" \
            --instance-id "$instance_id" \
            --output text \
            --query "StandardErrorContent")
        if [ "$std_err" != "" ];then
            echo "Error: $std_err"
        fi
    fi
}

s3_bucket="vea-deploy-pipeline"
build_file="api_build-$CI_COMMIT_SHA.tgz"
api_build_dest="/opt/mpt-api"

# download files from S3 on deployment instance
echo "Sending SSM command to download S3 build files and validate new API deployment"
deploy_cmd="aws s3 cp s3://${s3_bucket}/${build_file} ${api_build_dest} && tar -xzf ${api_build_dest}/${build_file} -C ${api_build_dest} && ${api_build_dest}/scripts/gitlab-ci-scripts/api-deploy-automation/bash/update_api.sh"
echo "DEBUG: command is ${deploy_cmd}"

# message="# :fuelpump: Deploying VEA API \`${CI_COMMIT_BRANCH}\`.\n\n Commit ${CI_COMMIT_SHA} is being
#     deployed to test environment\n\n\nYou can watch the pipeline progress here: ${CI_PIPELINE_URL}"
# curl -X POST -H 'Content-Type: application/json' -d "{\"text\": \"$message\"}" "$MATTERMOST_URL"

# extract files and complete deployment
run_command_via_ssm "$instance_id" "$deploy_cmd"

# get command output
# files should be uploaded with the following naming convention:
# build-logs-{COMMIT_HASH}.txt
#
# for example:
# build-logs-5357f09a4be3fd896b88fe16453705da61bad3d9.txt
#build_log_path="${CI_COMMIT_SHA}.txt/8d19bcb4-cc03-41c5-82a1-fe213ee56f32
#log_id=""
#build_log_path="build-logs-${CI_COMMIT_SHA}.txt/${log_id}/${instance_id}/awsrunShellScript/0.awsrunShellScript/stdout"
build_log_folder="build-logs-asg-${CI_API_ASG_NM}_${CI_COMMIT_SHA}.txt"
# stderr file
#aws --output json s3api list-objects --bucket ${s3_bucket} --prefix "${build_folder}" | jq '.Contents[0].Key'
# stdout file
echo "Looking for SSM output logs in S3 bucket ${build_log_folder}"
s3_commit_files=$(aws --output json s3api list-objects --bucket ${s3_bucket} --prefix "${build_log_folder}")
echo "debug: S3 commit files output $s3_commit_files"
s3_log_file_key=$(echo "$s3_commit_files" | jq -r '.Contents[] | 
    select(.Key | endswith("stdout")) | .Key' | head -n 1)
s3_err_log_file_key=$(echo "$s3_commit_files" | jq -r '.Contents[] | 
    select(.Key | endswith("stderr")) | .Key' | head -n 1)

echo "Logfile is located at \"s3://${s3_bucket}/${s3_log_file_key}\""
echo "Downloading logs"
aws s3 cp "s3://${s3_bucket}/${s3_log_file_key}" "./build_logs.txt"
aws s3 cp "s3://${s3_bucket}/${s3_err_log_file_key}" "./err_logs.txt"
echo "Build logs:"
cat ./build_logs.txt
if grep -q 'The API returned the expected response' ./build_logs.txt; then
    echo "Deployed successfully."
    # Define message to send to Mattermost, call function which posts to Mattermost
    message="# :rocket: VEA API  \`${CI_COMMIT_BRANCH}\` deployed to ${CI_API_ASG_NM}!\n\n"

    curl -X POST -H 'Content-Type: application/json' -d "{\"text\": \"$message\"}" "$MATTERMOST_URL"

    exit 0
else
    echo "Errors occured."
    cat ./err_logs.txt
    exit 1
fi
