.sign-checkbox {
  cursor: pointer;
  align-items: center;
  border: 1px solid #dbdbdb;
  border-radius: 4px;
  cursor: pointer;
  padding: 0.3em;
  font-size: 1.2em;
  user-select: none;
  
  &:hover{
    border-color: #bbb;
  }
  
  .symbol {
  }
  
  &:has(> input:disabled) {
    background-color: #f0f0f0;
    border-color: rgba(0,0,0,0);
    cursor: not-allowed;
  }
  
  input[type="checkbox"] {
    display: none;
  }
}

.form-row {
    display:flex;
    flex-direction: row;
    justify-content: space-between;
    padding:0.3em; 
    .form-row-label {
        min-width: 120px;
        font-weight: 600;
        flex-grow: 1;
    }
    .form-row-input {
        flex-grow: 2;
        text-align: right;
    }
    &.bottom-border {
      border-bottom: 1px solid #f1f1f1;
    }
}

.math-symbol {
    font-size: 1.5em;
    font-weight: bold;
    margin: 0 0.3em;
}

hr {
    margin: 0.5em;
}

.answer-row {
  td {
    input {
      max-width: 5em;
    }
  }
}

.trash-cell {
  cursor: pointer;
  background-color:#ccc;
  color: #fff;
  text-align:center;
  vertical-align:middle;
  align-content: center;
  padding: 0.5em;
  &:hover {
      background-color:rgb(170, 170, 170);
  }
}

.error-bg-color {
  background-color: #fca7ad;
}
