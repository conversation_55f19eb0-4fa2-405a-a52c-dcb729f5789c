import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { IDgIntStyleConfig, DND_STYLE_MODE, HOME_CONTENT_SUFFIX, IDgIntHomeConfig } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { IDgColor } from 'src/app/ui-testrunner/element-render-interactive-diagram/color-palettes';
import { FormControl } from '@angular/forms';
import { StyleprofileService } from 'src/app/core/styleprofile.service';

@Component({
  selector: 'dg-int-dnd-color-style',
  templateUrl: './dg-int-dnd-color-style.component.html',
  styleUrls: ['./dg-int-dnd-color-style.component.scss']
})
export class DgIntDndColorStyleComponent implements OnInit, OnChanges {
  @Input() styleConfig: IDgIntStyleConfig;
  @Input() constructionElement: any;
  @Input() dgIntElement: any;
  @Input() idToLabelMap: Map<string, string>;
  @Input() targetIdList: string[];
  @Input() homeIdList: string[];
  @Input() sortedOptionIdList: string[];
  @Input() homeConfig: IDgIntHomeConfig;
  @Input() isSimplified: boolean = false;
  @Output() onChange = new EventEmitter();
  
  colorPalette: IDgColor[] = [];
  styleModeForm = new FormControl(DND_STYLE_MODE.DEFAULT);
  styleModeOptions = [
    {id: DND_STYLE_MODE.DEFAULT, caption:'Default'},
    {id: DND_STYLE_MODE.INDIVIDUAL, caption:'Individual'},
    {id: DND_STYLE_MODE.HOME, caption:'Home Group'},
  ]
  
  DND_STYLE_MODE = DND_STYLE_MODE;
  HOME_CONTENT_SUFFIX = HOME_CONTENT_SUFFIX;
  
  homeOptionContentLabel: string[] = [];
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private profile: StyleprofileService,
  ) { }
  
  ngOnInit() {
    this.initHomeOptionContentLabel();
    this.updateColorPalette();
    this.profile.styleProfileSub.subscribe(() => {
      this.updateColorPalette();
    });
  }
  ngOnChanges() {
    this.initHomeOptionContentLabel();
  }
  
  isColorPaletteExist(): boolean {
    return !this.colorPalette || (Array.isArray(this.colorPalette) && this.colorPalette.length > 0);
  }
  
  updateColorPalette() {
    this.colorPalette = this.profile.getInteractiveDiagramColorPalette();
  }
  
  get isShowColorInput(): boolean {
    return !this.isSimplified || !this.isColorPaletteExist();
  }
  
  emitChange(){
    this.onChange.emit();
    this.initHomeOptionContentLabel();
  }
  
  initHomeOptionContentLabel(){
    if (this.homeConfig == undefined) return;
    this.homeOptionContentLabel = this.homeConfig.element.map((homeElementIds, homeIndex) => {
      return this.getHomeOptionContentSummary(homeIndex);
    });
  }
  getHomeOptionContentSummary(homeIndex: number): string {
    const homeElementIds = this.homeConfig.element[homeIndex];
    const homeElementLabels = homeElementIds.map(id => this.idToLabelMap.get(id));
    return `${homeIndex+1}: ${homeElementLabels.join(', ')}`;
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  isShowConfigPreview(){
    return true;
  }
  
}
