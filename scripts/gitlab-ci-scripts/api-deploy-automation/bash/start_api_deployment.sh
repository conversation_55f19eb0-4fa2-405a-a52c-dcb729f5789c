#!/bin/bash

# Description:  Given an S3 bucket name, a source directory, and S3 paths to upload the latest API build and where to
#               copy the build to for retaining the release history, this script uploads API builds to S3.
#
#               The goal of this is that after testing and validating an API build, it is uploaded to S3 on a live
#               release target. APIs download this content when they first launch within an AWS ASG, and reload their
#               API service using the downloaded content.
#
#               This script will upload to the API release bucket and include the current commit sha and date
#               in the S3 path the build is uploaded to. This allwos for easy identification of API builds on S3.
#
#               Copying API builds to a release history path allows the capability of rolling back API content to a
#               particular date or even commit sha as well.

# Defined named parameters that can optionally be passed from commandline to this script

bucket_nm=${bucket_nm:-null}                  # S3 bucket to upload contents to
source=${source:-null}                        # Source directory containing API build archive to upload to S3
release_target=${release_target:-null}        # Path on S3 where live release is located
versioning_target=${versioning_target:-null}  # Path on S3 where old releases are located
rollback=${versioning_target:-null}           # Rollback option for APIs (specified in GitLab env. variables)
emergency_rollback_playbook=${emergency_rollback_playbook:-null} # Ansible Playbook to execute for emergency rollbacks

while [ $# -gt 0 ]; do

   if [[ $1 == *"--"* ]]; then
        param="${1/--/}"
        declare $param="$2"
        # echo $1 $2 // Optional to see the parameter:value result
   fi

  shift
done

# Exit script if any args are not defined

if [[ -z $bucket_nm || -z $source || -z $release_target || -z $versioning_target || -z $rollback ]]; then

  echo -e "\nbucket_nm: ${bucket_nm}"
  echo -e "source: ${source}"
  echo -e "release_target: ${release_target}"
  echo -e "versioning_target: ${versioning_target}"
  echo -e "rollback: ${rollback}"
  
  echo -e "\nOne or more arguments missing when calling script! Exiting."
  exit 1

fi


# Since this script calls other scripts in the same directory, store the path to this script when running it so we
# don't have to cd into it to have calls to other scripts work. Using this method allows the script to produce the
# same behaviour regardless of whether the script is invoked as <name> or . <name> (source method)

script_fullpath="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/$(basename "${BASH_SOURCE[0]}")"
script_dir="$(dirname $script_fullpath)"

echo -e "Prepping for release of API build..."

echo -e "\nScript dir is $script_dir"

# Determine deployment type (rollback vs. standard deployment)

shopt -s nocasematch # Temporarily disable case sensitive comparisons of strings

case $rollback in
  standard)
    deployment_type="standard_rollback"
    ;;
  emergency)
    deployment_type="emergency_rollback"
    ;;
  *)
    deployment_type="standard_deployment"
esac

shopt -u nocasematch # re-enable case senstive comparisons

# Start the deployment depending on the type

echo -e "\nDeployment type set to $deployment_type"

# Standard deployment of latest API build

if [[ "$deployment_type" == "standard_deployment"  ]]; then

  bash $script_dir/standard_api_deploy.sh \
  --bucket_nm "vea-api-builds" \
  --source "./" \
  --release_target "vea-api-live-releases/$CI_API_ASG_NM" \
  --versioning_target "vea-api-previous-releases/$CI_API_ASG_NM"

# Standard rollback

elif [[ "$deployment_type" == "standard_rollback" ]]; then

  bash $script_dir/rollback_apis_standard.sh \
  --bucket_nm "vea-api-builds" \
  --commit_sha "$CI_COMMIT_SHA" \
  --api_asg_nm "$CI_API_ASG_NM"

# Emergency rollback

else

  bash $script_dir/rollback_apis_emergency.sh \
  --bucket_nm "vea-api-builds" \
  --commit_sha "$CI_COMMIT_SHA" \
  --api_asg_nm "$CI_API_ASG_NM" \
  --playbook "$emergency_rollback_playbook"

fi