import { Component, OnInit } from "@angular/core";
import { BreadcrumbsService } from "../../core/breadcrumbs.service";
import { Router, ActivatedRoute } from "@angular/router";
import { LangService } from "../../core/lang.service";
import { AuthService } from "../../api/auth.service";
import { LoginGuardService } from "../../api/login-guard.service";
import { RoutesService } from "src/app/api/routes.service";
import { WhitelabelService } from "../../domain/whitelabel.service";


interface ILandingPracticeTest {
  twtar_id: number,
  tw_id: number,
  long_name: string,
  test_design_id: number,
  source_item_set_id: number,
  lang: string,
  resource_td_id?: number,
  resource_item_set_id?: number
  resource_caption?: string,
  test_window_title?: {en: string, fr: string},
  tw_type_slug?: string
}

const twTitlePostfix = '_title';
const twSubTitlePostfix = '_subtitle';

@Component({
  selector: 'view-public-practice',
  templateUrl: './view-public-practice.component.html',
  styleUrls: ['./view-public-practice.component.scss']
})
export class ViewPublicPracticeComponent implements OnInit {
  

  constructor(
    private breadcrumbsService: BreadcrumbsService, 
    private route: ActivatedRoute,
    private router: Router, 
    private auth: AuthService, 
    public lang: LangService, 
    public whiteLabelService: WhitelabelService,
  ) {}

  breadcrumb = [];
  practiceTestRecords: ILandingPracticeTest[] = [];
  practiceTestRecordsMap: Map<number, ILandingPracticeTest[]> = new Map();
  selectedPracTestRec: number 
  is_allow_qa: string 

  ngOnInit(): void {
    const practicePageName = this.whiteLabelService.isABED() ? 'lbl_practice_tests_abed_ABED' : 'lbl_practice_tests_abed';
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(this.lang.tra("lbl_login"), `/${this.lang.c()}/login-router-st`), 
      this.breadcrumbsService._CURRENT(practicePageName, this.router.url)
    ];
    this.is_allow_qa = this.route.snapshot.queryParams['is_allow_qa']
    this.initPracticeTestMap();
  }

  getTestRunnerRoute(record: ILandingPracticeTest, fetchResources?: boolean){
    const {
        test_design_id 
      , source_item_set_id 
      , lang 
      , resource_item_set_id
      , resource_td_id
    } = record;

    let item_set_id = fetchResources ? resource_item_set_id : source_item_set_id;
    let td_id = fetchResources ? resource_td_id : test_design_id;

    return `/${lang}/test-auth/shared-test-version/${item_set_id}/${td_id}`
  }

  getDesignAllocCaption(record){
    return record.long_name
  }

  async initPracticeTestMap(){
    // const records = <ILandingPracticeTest[]> await this.auth.unauthenticatedGet('public/landing/practice-tests',{params:{isTrustedCenter: this.whiteLabelService.getSiteFlag('isTestCenter')}})
    const records = <ILandingPracticeTest[]> await this.auth.unauthenticatedGet('public/landing/practice-tests', {
      params: {
        group_type_slug: this.whiteLabelService.getSiteText('TW_CTRL_GROUP_SLUG','_DEFAULT_'),
        is_allow_qa: this.is_allow_qa,
      }
    });
    this.practiceTestRecords = records.map(this.processPracTestRec);
    
    this.practiceTestRecords.forEach(tr => {
      if (this.practiceTestRecordsMap.has(tr.tw_id)) {
        this.practiceTestRecordsMap.get(tr.tw_id).push(tr);
      } else {
        this.practiceTestRecordsMap.set(tr.tw_id, [tr]);
      }
    });
  }

  processPracTestRec = (tr: ILandingPracticeTest): ILandingPracticeTest => {
    return {
      ... tr,
      test_window_title: typeof tr.test_window_title === 'string' ? JSON.parse(tr.test_window_title) : tr.test_window_title
    }
  }

  hasAllocResources = (record: ILandingPracticeTest) => {
    return record.resource_td_id && record.resource_item_set_id;
  }

  getResourceAllocCaption = (record: ILandingPracticeTest) => {
    return record.resource_caption || 'Try the tools'
  }

  getPracTestRecordsTwsIds = () => this.practiceTestRecordsMap.keys();

  getTestWindowTitle = (twId: number) => {
    const tw_type_slug = this.getTestWindowTypeSlug(twId);
    const slug = tw_type_slug + twTitlePostfix;
    return tw_type_slug ? slug : 'Practice Test';
  }

  getTestWindowSubTitle = (twId: number) => {
    const tw_type_slug = this.getTestWindowTypeSlug(twId);
    const slug = tw_type_slug + twSubTitlePostfix;
    return tw_type_slug ? slug : 'Access practice test';
  }

  getTestWindowTypeSlug = (twId: number) => {
    const twRecs = this.practiceTestRecordsMap.get(twId);
    if (!twRecs.length) {
      return undefined;
    }
    const tw_type_slug = twRecs[0].tw_type_slug;
    return tw_type_slug;
  }

  getSelectedPracTestRec = () => {
    if (!this.selectedPracTestRec) { 
      return [];
    }
    return this.practiceTestRecordsMap.get(this.selectedPracTestRec);
  }

  getPracticeTestTitleSlug(){
    return this.whiteLabelService.isABED() ? 'lbl_practice_tests_abed_ABED' : 'lbl_practice_tests_abed';
  }

  getPracticeTestDescriptionSlug(){
    return this.whiteLabelService.isABED() ? 'txt_descr_practice_tests_abed_ABED' : 'txt_descr_practice_tests_abed';
  }
  isABED() {
    return this.whiteLabelService.isABED();
  }
}