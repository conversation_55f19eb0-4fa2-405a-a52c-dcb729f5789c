.is-error {
    color: #ff5656;
    font-size: 0.9em;
}

.flex-column {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.flex-row {
    display: flex;
    gap: 1em;
}

.title.is-6 {
    margin-bottom: 0em;
}

.has-border {
    border: 1px solid #e1e1e1; 
    padding: 1em; 
    border-radius: 1em;
}

.surface-card {
    background-color: #fff;
    box-shadow: 0 .25em 0.5em -.125em rgba(10,10,10,.1),0 0 0 1px rgba(10,10,10,.02);
    color: #4a4a4a;
    max-width: 100%;
    position: relative;
    padding: 1.5em;
    border-radius: .5em;
    border: none;
}

a.tag-info {
    color: #333;
    // text-decoration: none;
    font-weight: 300;
    &.is-bold {
        font-weight: 600;
        margin-right:1em;
    }
    &.is-unset{
        color: red;    
    }
    &:hover {
        // text-decoration: underline;
    }
}
a.close { 
    color: #ccc;
    &:hover {
        color: #333;
    }
}

.indent {
    padding-left: 1em; 
    border-left: 1px solid #ededed;
}