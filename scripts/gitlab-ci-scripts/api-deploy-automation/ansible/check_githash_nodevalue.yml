---
- hosts: localhost
  gather_facts: false
  vars:
    region: "{{ region | default('ca-central-1') }}"
    instance_ids_input: "{{ instance_ids | default('') }}"
    instances_list: "{{ instance_ids_input.split() if instance_ids_input else [] }}"
    search_pattern: "_00_RELEASE-COMMIT*"
    mattermost_url: "https://mattermost.vretta.com/hooks/eu57eetsfby8md8b5gc7opasro"
    errors: []
    success_messages: []

  tasks:
    - name: Check if there are instance IDs
      fail:
        msg: "No instance IDs provided!"
      when: instances_list | length == 0

    - name: Connect and fetch file names from each instance
      command: >
        aws ssm send-command
        --instance-ids "{{ item }}"
        --document-name "AWS-RunShellScript"
        --region "{{ region }}"
        --parameters 'commands=["ls /opt/mpt-api | grep {{ search_pattern }}"]'
        --query "Command.CommandId"
      register: ssm_command_output
      loop: '{{ instances_list }}'
      ignore_errors: true

    - name: Pause for SSM command execution
      wait_for:
        timeout: 60

    - name: Fetch results from the SSM command for each instance
      command: >
        aws ssm list-command-invocations
        --instance-id "{{ item.0 }}"
        --command-id "{{ item.1.stdout | trim }}"
        --query "CommandInvocations[0].CommandPlugins[0].Output"
        --region "{{ region }}"
      register: ssm_output
      loop: '{{ instances_list | zip(ssm_command_output.results) | list }}'
      ignore_errors: true

    - name: Assert file names consistency
      assert:
        that:
          - ssm_output.results | map(attribute='stdout') | unique | length == 1
        fail_msg: "File names are not consistent across instances!"
      register: file_name_check
      ignore_errors: true

    - name: Append error if file names are inconsistent
      set_fact:
        errors: '{{ errors + [file_name_check.msg] }}'
      when: file_name_check.failed

    - name: Append success message if file names are consistent
      set_fact:
        success_messages: >-
          {{ success_messages + ['Success! Git hash files are the same across
          the servers.'] }}
      when: not file_name_check.failed

    - name: Generate input json for AWS SSM
      copy:
        dest: '/tmp/input.json'
        content: |
          {
              "InstanceIds": {{ instances_list | to_json }},
              "DocumentName": "AWS-RunShellScript",
              "Parameters": {
                "commands": ["sudo -u ubuntu bash -c 'source /home/<USER>/.nvm/nvm.sh; NODE_PATH=$(nvm which current | awk \"{ print \\$NF }\"); $NODE_PATH -e \"const ecosystem = require(\\\"/opt/ecosystem.config\\\"); console.log(ecosystem.apps[0].env.NODE_ENV);\"'"],
                "workingDirectory": ["/opt/mpt-api"]
              }
            }
    - name: Check number of instances and split if necessary
      set_fact:
        instance_chunks: "{{ instances_list | list | batch(50) | list if instances_list | length > 50 else [instances_list] }}"
      
    - name: Send command using AWS SSM CLI
      command:
        cmd: >-
          aws ssm send-command --region "{{ region }}" --cli-input-json
          file:///tmp/input.json --instance-ids "{{ item | join(' ') }}"
      register: ssm_result
      loop: "{{ instance_chunks }}"

    - name: Set empty list for node_env_values
      set_fact:
        node_env_values: []

    - name: Get NODE_ENV value from each instance and compare
      command:
        cmd: >
          aws ssm get-command-invocation --instance-id {{ item }} --command-id
          {{ ssm_result.results | map(attribute='stdout') | join(' ') | from_json | json_query('Command.CommandId') }}
          --region "{{ region }}"
      register: ssm_invocation_result
      retries: 5
      delay: 10
      until: >-
        ssm_invocation_result is success and ('Success' in
        ssm_invocation_result.stdout)
      loop: "{{ instances_list }}"

    - name: Append NODE_ENV values to list
      set_fact:
        node_env_values: >-
          {{ node_env_values + [item.stdout | from_json |
          json_query('StandardOutputContent') | regex_replace('

          ', '')] }}
      loop: '{{ ssm_invocation_result.results }}'

    - name: Assert NODE_ENV values consistency
      assert:
        that:
          - node_env_values | unique | length == 1
        fail_msg: NODE_ENV values are not the same across all instances.
      register: node_env_check
      ignore_errors: true

    - name: Append error if NODE_ENV values are different
      set_fact:
        errors: '{{ errors + [node_env_check.msg] }}'
      when: node_env_check.failed

    - name: Append success message if NODE_ENV values are consistent
      set_fact:
        success_messages: >-
          {{ success_messages + ['All instances have the same NODE_ENV value:
          **' + (node_env_values[0] if node_env_values | length > 0 else
          'UNKNOWN') + '**.'] }}
      when: not node_env_check.failed

    - name: Compare Config Files Content
      block:
        - name: Generate input json for AWS SSM to get config file content
          copy:
            dest: '/tmp/config_input.json'
            content: |
              {
                "InstanceIds": {{ instances_list | to_json }},
                "DocumentName": "AWS-RunShellScript",
                "Parameters": {
                  "commands": ["cat /opt/mpt-api/config/{{ node_env_values[0] }}.json"],
                  "workingDirectory": ["/opt/mpt-api"]
                }
              }

        - name: Send command using AWS SSM CLI to get config file content
          command:
            cmd: >-
              aws ssm send-command --region "{{ region }}" --cli-input-json
              file:///tmp/config_input.json
          register: ssm_config_result

        - name: Get config file content from each instance and compare
          command:
            cmd: >
              aws ssm get-command-invocation --instance-id {{ item }}
              --command-id {{ ssm_config_result.stdout | from_json |
              json_query('Command.CommandId') }} --region "{{ region }}"
          register: ssm_config_invocation_result
          retries: 5
          delay: 10
          until: >-
            ssm_config_invocation_result is success and ('Success' in
            ssm_config_invocation_result.stdout)
          loop: '{{ instances_list }}'

        - name: Assert Config Files Consistency
          assert:
            that:
              - >-
                ssm_config_invocation_result.results | map(attribute='stdout') |
                map('from_json') | map('json_query', 'StandardOutputContent') |
                unique | length == 1
            fail_msg: JSON Config files are not consistent across instances!
          register: config_file_check
          ignore_errors: true

        - name: Append error if Config files are inconsistent
          set_fact:
            errors: '{{ errors + [config_file_check.msg] }}'
          when: config_file_check.failed

        - name: Append success message if Config files are consistent
          set_fact:
            success_messages: >-
              {{ success_messages + ['Success! JSON Configuration files are
              consistent across instances.'] }}
          when: not config_file_check.failed

    - name: Notify Mattermost
      uri:
        url: '{{ mattermost_url }}'
        method: POST
        body_format: json
        body:
          text: >-
            Date: {{ lookup('pipe', 'TZ="America/Toronto" date') }} - {% if
            errors | length == 0 %}Success! {{ success_messages | join(' ') }}{%
            else %}Failure! Errors: {{ errors | join(', ') }}{% endif %}

