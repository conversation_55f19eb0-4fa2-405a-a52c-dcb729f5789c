import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { EditingDisabledService } from '../editing-disabled.service';

@Component({
  selector: 'dropdown-select',
  templateUrl: './dropdown-select.component.html',
  styleUrls: ['./dropdown-select.component.scss']
})
export class DropdownSelectComponent implements OnInit {

  @Input() list: Array<{id:string, caption:string}>;
  @Input() placeholder: string = "Select...";
  @Input() confirmationMessage?: string;
  @Output() onSelect = new EventEmitter<string>();
  
  @ViewChild('dropdownSelect') dropdownSelect: ElementRef;
  
  constructor(private editingDisabled: EditingDisabledService) { }

  ngOnInit() {
  }
  
  selectItem(id:string){
    this.dropdownSelect.nativeElement.value = "";
    if (this.confirmationMessage){
      const confirm = window.confirm(this.confirmationMessage);
      if (!confirm) return;
    }
    this.onSelect.emit(id);
  }

  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

}
