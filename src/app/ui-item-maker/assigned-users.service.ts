import { Injectable } from '@angular/core';
import { AuthService } from '../api/auth.service';
import { IItemAuthNote } from './element-notes/element-notes.component';
import { RoutesService } from '../api/routes.service';
import { UserRoles } from '../api/models/roles';
import { AuthRolesService } from './auth-roles.service';

@Injectable({
  providedIn: 'root'
})
export class AssignedUsersService {

  private assignedUserByNoteId: Map<number, {uid: number; name: string, shortName: string}> = new Map();
  private assignedAccessLevelByNoteId: Map<number, UserRoles> = new Map();
  public groupMembers;

  private accessLevelCaptions;
  private accessLevelCaptionsShort = {};
  private MAX_NAME_LEN: number = 13;

  constructor(private auth: AuthService, private routes: RoutesService, private authRoles: AuthRolesService) {
    this.accessLevelCaptions = authRoles.getCaptionMap();
    for(const accessLevel of Object.keys(this.accessLevelCaptions)) {
      const caption = this.accessLevelCaptions[accessLevel];
      this.accessLevelCaptionsShort[accessLevel] = this.processShortName(caption);
    }
  }

  processShortName(name: string) {
    if(name.length <= this.MAX_NAME_LEN) {
      return name;
    } else {
      return name.slice(0,this.MAX_NAME_LEN) + "...";
    }
  }

  getAssignedUser(noteId: number) : {uid: number; name: string, shortName: string} {
    return this.assignedUserByNoteId.get(noteId);
  }

  getAssignedAccessLevel(noteId: number) {
    return this.assignedAccessLevelByNoteId.get(noteId);
  }

  setAssignedUser(noteId: number, uid: number, name: string, shortName: string) {
    this.assignedUserByNoteId.set(noteId, {uid, name, shortName});
  }

  setAssignedAccessLevel(noteId: number, accessLevel: UserRoles) {
    this.assignedAccessLevelByNoteId.set(noteId, accessLevel);
  }

  unassignUser(noteId: number) {
    const data = {
      assigned_uid: null,
      assigned_access_level: null
    };
    return this.auth.apiPatch(this.routes.TEST_AUTH_NOTES, noteId, data)
    .then(_ => {
      this.assignedUserByNoteId.set(noteId, null);
      this.assignedAccessLevelByNoteId.set(noteId, null);
    })
    .catch(e => {
      console.error(e);
    });
  }

  resetSelectedUsers(id: number) {
    this.groupMembers.forEach(user => {
      if (+user.id !== id) {
        user.is_selected = false;
      }
    });
  }

  //Finds user name corresponding to given uid
  findUserName(uid : number) : {name: string, shortName: string} {
    const member = this.groupMembers.find(m => m.id === uid);
    let name = "";
    if (member) {
      if(member.first_name) {
        name += member.first_name;
        if(member.last_name) {
          name += " " + member.last_name;
        }
      } else {
        name = "Unknown";
      }
    } else {
      name = "Unknown"
    }

    return {
      name,
      shortName: this.processShortName(name)
    }
  }

  getAccessLevelCaption(accessLevel, short: boolean) {
    const map = short ? this.accessLevelCaptionsShort : this.accessLevelCaptions;
    return map[accessLevel];
  }

  //Sets up the map with info from this note
  setupNote(n: IItemAuthNote) {

    const {name, shortName} = this.findUserName(n.assigned_uid);
    if(n.assigned_uid) {
      this.setAssignedUser(n.id, n.assigned_uid,
        name,
        shortName);
    }

    if(n.assigned_access_level) {
      this.setAssignedAccessLevel(n.id, n.assigned_access_level);
    }
  }

  isAssignedToUser(id: number) {
    return this.getAssignedUser(id);
  }

  isAssignedToAccessLevel(id: number) {
    return this.getAssignedAccessLevel(id);
  }
}
