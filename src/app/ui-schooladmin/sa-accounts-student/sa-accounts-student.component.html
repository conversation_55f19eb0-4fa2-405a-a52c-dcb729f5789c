<div class="sa-accounts-student">
    <!-- <div class="view-header">
        <div class="admin-accounts">
            <span>Students</span>
            <img
                src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/504237/authoring/students/*************/students.png">
        </div>
        <div class="school-selector">
            <label>School:</label>
            <select [(ngModel)]="selectedSchool" (change)="onSelectedSchoolChange($event)" [disabled]="isLoading">
                <option *ngFor="let school of schools" [ngValue]="school">
                    {{getSchoolDisplay(school)}}
                </option>
            </select>
        </div>
        <div class="assessment-session-selector">
            <label>Assessment Session:</label>
            <select [(ngModel)]="selectedTestWindow" (change)="onSelectedTestWindowChange($event)"
                [disabled]="isLoading">
                <option *ngFor="let testWindow of testWindows" [ngValue]="testWindow">
                    {{getTestWindowDisplay(testWindow)}}
                </option>
            </select>
        </div>
    </div>
    <hr> -->
    <div class="accounts-students">
        <div class="page-header">
            <!-- <div class="intro">
            <tra-md slug="
              Use this page to review, edit, create, and assign/unassign student accounts. Assessment progress for each Student canbe seen here as well. Students can be reassigned to<br/>
              different classrooms and assessment sessions if needed. If a student is missing from the list, select Create New Account and add all their information into the input form.<br/>
              Accommodations for students can be viewed in the Accommodation column. Student data can also be imported to or exported from the platform using the Import and Export<br/>
              functions.
            "></tra-md>
          </div> -->
        </div>
        <button hidden id="unsubmit-viggo" (click)="unsubmit()">This should be hidden.</button>
        <div class="page-body">
            <!-- <div class="view-selectors">
            <button *ngFor="let view of studentViews" [ngClass]="{selected: view===selectedView}" (click)="selectView($event, view)">
              {{view.name}}
            </button>
          </div> -->
            <!-- <div *ngIf="selectedView" class="view-container">
            <div [ngSwitch]="selectedView.id">
              <div *ngSwitchCase="StudentView.FINAL_REGISTRATIONS">
                <ma-accounts-students-final></ma-accounts-students-final>
              </div>
              <div *ngSwitchCase="StudentView.PRE_REGISTRATIONS">
                <ma-accounts-students-pre></ma-accounts-students-pre>
              </div>
              <div *ngSwitchCase="StudentView.COMPARE_REGISTRATIONS">
                <ma-accounts-students-compare></ma-accounts-students-compare>
              </div>
            </div>
          </div> -->
            <button id="make-sept-load" hidden (click)="makeSeptLoad()">This should be hidden</button>
            <button id="reset-sept-load" hidden (click)="resetSeptLoad()">This should be hidden</button>
            <button id="start-attempts" hidden (click)="startAttempts()">This should be hidden</button>
            <button id="make-submissions" hidden (click)="makeSubmissions()">This should be hidden</button>
            <button id="reset-attempts" hidden (click)="resetAttempts()">This should be hidden</button>
            <button id="reset-submissions" hidden (click)="resetSubmissions()">This should be hidden</button>
    
            <div class="students-final-registration">
                <div class="top-section">
    
                    <div class="page-title">
                        <label><b>Assessment Session:</b></label>
                        <select [(ngModel)]="selectedTestWindowWithoutTestSession" (change)="onSelectedTestWindowChange()"
                            [disabled]="isLoading">
                            <option *ngFor="let testWindow of testWindows" [ngValue]="testWindow">{{
                                getTestWindowTitle(testWindow) }}
                            </option>
                        </select>
                    </div>
                    <button class="export" (click)="export()" [disabled]="isLoading">
                        <span class="icon"></span>
                        <span class="title">Export</span>
                    </button>
                    <button class="add-account" (click)="openNewStudentModal()">
                        <i class="fa fa-plus-square"></i>
                        <span class="button-title">Add New Student</span>
                    </button>
                    <button class="add-account" (click)="openRemoveStudentModal()">
                        <i class="fa fa-minus-square"></i>
                        <span class="button-title">Remove Student</span>
                    </button>
                </div>
                <div class="test-window-tag" *ngIf="selectedTestWindow"><span>*</span> {{getSelectedTestWindowTag()}}
                </div>
                <div class="actions">
                    <label>View Assessment:</label>
                    <select [(ngModel)]="selectedAssessment" (change)="onSelectedAssessmentChange($event)"
                        [disabled]="isLoading">
                        <option hidden selected value> -- Select an Assessment -- </option>
                        <option *ngFor="let assessment of assessments" [ngValue]="assessment">{{
                            getAssessmentDisplayInSelect(assessment) }}
                        </option>
                    </select>
                </div>
                <div *ngIf="selectedAssessment" class="actions">
                    <!-- <button class="request-special-format">
                        <i class="fa fa-file-alt"></i>
                        <span class="button-title">Request Special Format Assessment</span>
                    </button> -->
                    <button *ngIf="false" class="show-validation-code" style="margin-left: 10px;">
                        <i class="fa fa-list"></i>
                        <span class="button-title">Show Validation Code Legend</span>
                    </button>
                    <button class="minimal unenrol-button" (click)="shouldOpenUnenrolModal = true"
                        [disabled]="shouldDisableUnenrol()">Unregister</button>
                    <button class="minimal reenrol-button" (click)="shouldOpenReenrolModal = true"
                        [disabled]="shouldDisableReenrol()">Re-register</button>
    
                </div>
                <!-- <paginator [model]="accountsTable.getPaginatorCtrl()" [page]="accountsTable.getPage()"
              [numEntries]="accountsTable.numEntries()" (pageChange)="pageChanged()">
            </paginator> -->
                <div class="no-selected-assessment" *ngIf="!selectedAssessment">
                    <ng-container *ngIf="!isLoading">
                        Please select an assessment to view student registrations.
    
                    </ng-container>
                    <div *ngIf="isLoading" class="accounts-loader">
                        <div></div>
                    </div>
                </div>
                <ng-container *ngIf="selectedAssessment">
    
                    <div class="accounts-pagination">
                        <div>Page</div>
                        <button class="minimal" (click)="promptPageNumber()">{{ getCurrentPageToDisplay() }}</button>
                        <div>of {{ getTotalPagesToDisplay() }}</div>
                        <button class="minimal" (click)="goToPreviousPage()"
                            [ngClass]="{'disabled': shouldDisablePreviousPage()}" [disabled]="shouldDisablePreviousPage()">
                            <i class="fa fa-caret-left" aria-hidden="true"></i>
                        </button>
                        <button class="minimal" (click)="goToNextPage()" [ngClass]="{'disabled': shouldDisableNextPage()}"
                            [disabled]="shouldDisableNextPage()">
                            <i class="fa fa-caret-right" aria-hidden="true"></i>
                        </button>
                        <div class="total-students"><b>Total: {{pagination.count}} students</b></div>
                    </div>
                    <div class="table-section">
                        <div *ngIf="isLoading" class="accounts-loader">
                            <div></div>
                        </div>
                        <table *ngIf="!isLoading" class="accounts-students-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" [checked]="selectAll" (change)="onSelectAllChange($event)">
                                    </th>
                                    <th *ngFor="let headingToSortBy of getHeadingToSortBy()">
                                        <div>
                                            <div (click)="changeOrderBy(headingToSortBy.sortBy)">
                                                <span>{{ headingToSortBy.heading }}</span>
                                                <i *ngIf="isSortedBy(headingToSortBy.sortBy, 'asc')"
                                                    class="fa fa-caret-up"></i>
                                                <i *ngIf="isSortedBy(headingToSortBy.sortBy, 'desc')"
                                                    class="fa fa-caret-down"></i>
                                            </div>
                                            <i class="fa fa-filter" aria-hidden="true"
                                                (click)="toggleShowFilter(headingToSortBy.sortBy)"></i>
                                        </div>
                                        <div *ngIf="isFilterVisible(headingToSortBy.sortBy)">
                                            <input *ngIf="!headingToSortBy.isSelect" type="text"
                                                (input)="updateFilter($event, headingToSortBy.sortBy)"
                                                [value]="filterInitValue(headingToSortBy.sortBy)">
                                            <select *ngIf="headingToSortBy.isSelect"
                                                (change)="updateFilter($event, headingToSortBy.sortBy)">
                                                <option *ngFor="let option of headingToSortBy.options"
                                                    [selected]="filterInitValue(headingToSortBy.sortBy) == option.label">
                                                    {{option.label}}
                                                </option>
                                            </select>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let row of accountsTable, index as i">
                                    <td><input type="checkbox" [(ngModel)]="row.checked"></td>
                                    <td>
                                        <div class="student-pen" [class.link]="row.unenrolled != 1">
                                            <!--<span *ngIf="row.unenrolled != 1" (click)="openStudentInfoModal(row)">{{row.pen}}</span>-->
                                            <a *ngIf="row.unenrolled != 1" [routerLink]="getStudentDetailsRoute(row.uid)">{{ row.pen }}</a>
                                            <span *ngIf="row.unenrolled == 1">{{ row.pen }}</span>
                                            <div *ngIf="row.isNew" class="student-new-tag">NEW</div>
                                        </div>
                                    </td>
                                    <td>{{ row.first_name }}</td>
                                    <!--                                <td>{{ row.preferred_name }}</td>-->
                                    <td>{{ row.last_name }}</td>
                                    <td><b>{{ row.unenrolled == 1 ? 'Yes' : 'No' }}</b></td>
                                    <td>
                                        <div class="student-walk-in">
                                            <div *ngIf="row.is_walk_in == '1'" class="walk-in-tag">New Registration</div>
                                        </div>
                                    </td>
                                    <td>{{ row.historic_attempts }}</td>
                                    <td *ngIf="false">
                                        <div *ngFor="let code of row.validationCodes, index as i">
                                            <div *ngIf="false && (code  === 'WK1' || code === 'WK2'); else valid">
                                                <div class="red-validation-tag">Awaiting PEN Confirmation</div>
                                            </div>
                                            <ng-template #valid>
                                                <div class="green-validation-tag">Registered</div>
                                            </ng-template>
                                        </div>
                                    </td>
                                    <td>{{row.confirmation_code}}
                                        <span class="u-tag" *ngIf="row.has_unsubmissions == 1">U</span>
                                    </td>
                                    <!-- <td>{{ getRequestStatusText(row.specialFormatStatus) }}</td> -->
                                    <!--                                <td>{{ getRequestStatusText(row.aegrotatStatus) }}</td>-->
                                    <!--                                <td>{{ getRequestStatusText(row.disqualificationStatus) }}</td>-->
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </ng-container>
            </div>
        </div>
    </div>
    
    <div class="custom-modal" *ngIf="shouldOpenUnenrolModal">
        <div class="modal-contents unenrol-modal">
            <!-- <sa-modal-student-personal [savePayload]="cmc()" [isCreatingNew]="true" saveProp="payload">
            </sa-modal-student-personal>
            <modal-footer [pageModal]="pageModal" [isConfirmAlert]="isConfirmAlert()"></modal-footer> -->
    
            <div class="modal-title">Are you sure you want to unregister the selected students?</div>
            <div class="vertical-grow"></div>
            <div class="actions">
                <button class="cancel-button" (click)="shouldOpenUnenrolModal = false">Cancel</button>
                <button class="add-button" (click)="unregisterFromTable()">Unregister</button>
            </div>
        </div>
    </div>
    
    <div class="custom-modal" *ngIf="shouldOpenReenrolModal">
        <div class="modal-contents reenrol-modal">
            <!-- <sa-modal-student-personal [savePayload]="cmc()" [isCreatingNew]="true" saveProp="payload">
            </sa-modal-student-personal>
            <modal-footer [pageModal]="pageModal" [isConfirmAlert]="isConfirmAlert()"></modal-footer> -->
    
            <div class="modal-title">Are you sure you want to re-register the selected students?</div>
            <div class="vertical-grow"></div>
            <div class="actions">
                <button class="cancel-button" (click)="shouldOpenReenrolModal = false">Cancel</button>
                <button class="add-button" (click)="registerFromTable()">Re-register</button>
            </div>
        </div>
    </div>
    
    <div class="custom-modal" *ngIf="shouldOpenNewStudentModal">
        <div class="modal-contents new-student-modal">
            <!-- <sa-modal-student-personal [savePayload]="cmc()" [isCreatingNew]="true" saveProp="payload">
            </sa-modal-student-personal>
            <modal-footer [pageModal]="pageModal" [isConfirmAlert]="isConfirmAlert()"></modal-footer> -->
    
            <div class="modal-title">Add a New Student</div>
            <table>
                <tr>
                    <th>PEN:</th>
                    <th>
                        <input oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^([\d]{0,9}).*$/g, '$1');"
                            [(ngModel)]="newStudentPen">
                        <button (click)="lookupPen2()">Lookup</button>
                    </th>
                    <!-- <th>
                        <mat-slide-toggle [(ngModel)]="strictPenValidation" color="primary">Use strict PEN validation
                        </mat-slide-toggle>
                    </th> -->
                </tr>
            </table>
            <div class="pen-error" *ngIf="newStudentErrors.pen">
                <i class="fa fa-exclamation-circle"></i>
                <div>
                    This is not a valid Personal Education Number (PEN). Visit the Ministry's <a
                        href="https://www2.gov.bc.ca/gov/content/education-training/k-12/administration/program-management/pen"
                        target="_blank">PEN Services website</a> to obtain the student's personal education number.
                </div>
            </div>
            <!-- <div class="input-error pen-error" *ngIf="newStudentErrors.pen">{{newStudentErrors.pen}}</div> -->
    
            <ng-container *ngIf="newStudentLookupResult == LookupResult.EXISTS_IN_THIS_SCHOOL && newStudentComponents">
                <div class="pen-info">
                    <div>This PEN is associated with a student registered by this school.
                        If the student associated with this PEN is <b>not</b> the student you are looking for,
                        please
                        contact Vretta Technical Support.
                    </div>
                </div>
                <div class="pen-support">
                    <div><b>Vretta Technical Support Information</b></div>
                    <div>
                        <div>Email: </div>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div>Email support is available between 8pm to 6pm PT.</div>
                </div>
            </ng-container>
            <ng-container
                *ngIf="!newStudentErrors.pen && newStudentLookupResult && newStudentLookupResult != LookupResult.EXISTS_IN_THIS_SCHOOL && newStudentLookupResult != LookupResult.EXISTS_IN_ANOTHER_SCHOOL">
                <div class="pen-info">
                    <div>This PEN is not associated with any students registered by this school. You will be registering the
                        student to this school for this assessment session.</div>
                </div>
                <div class="student-form">
                    <div>
                        <div>First Name: <span class="required-star">*</span></div>
                        <input type="text" oninput="this.value = this.value.replace(/[^A-Za-z'-]/g, '');"
                            [(ngModel)]="newStudentInfo.firstName" (keyup)="onNewStudentInputChange(null)">
                    </div>
                    <div class="input-error" *ngIf="newStudentErrors.firstName">{{newStudentErrors.firstName}}</div>
                    <div>
                        <div>Last Name: <span class="required-star">*</span></div>
                        <input type="text" oninput="this.value = this.value.replace(/[^A-Za-z'-]/g, '');"
                            [(ngModel)]="newStudentInfo.lastName" (keyup)="onNewStudentInputChange(null)">
                    </div>
                    <div class="input-error" *ngIf="newStudentErrors.lastName">{{newStudentErrors.lastName}}</div>
                    <br>
                    <!-- <mat-slide-toggle [(ngModel)]="newStudentInfo.specialFormRequest" color="primary"><b>I am requesting a Special Format Assessement for this student</b></mat-slide-toggle> -->
                </div>
            </ng-container>
    
            <ng-container *ngIf="newStudentLookupResult == LookupResult.EXISTS_IN_ANOTHER_SCHOOL">
                <div class="pen-warning">
                    <i class="fa fa-exclamation-triangle"></i>
                    <div>
                        This PEN is associated with a student registered by <b>another</b> school. You will be
                        registering this student for this school and for this assessment session.
                    </div>
                </div>
            </ng-container>
    
            <table>
                <thead>
                    <tr>
                        <th>Select</th>
                        <th>Code</th>
                        <th>Assessment</th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let component of newStudentComponents">
                        <td>
                            <input type="checkbox" [(ngModel)]="component.checked"
                                [disabled]="!(!newStudentErrors.pen && newStudentLookupResult && (newStudentLookupResult == LookupResult.NOT_EXISTS || newStudentLookupResult == LookupResult.EXISTS_IN_ANOTHER_SCHOOL)) && component.final"
                                (change)="onNewStudentInputChange(component)">
                        </td>
                        <td>{{component.code}}</td>
                        <td>{{component.name}}</td>
                    </tr>
                </tbody>
            </table>
    
            <div class="vertical-grow"></div>
            <div class="actions">
                <button class="cancel-button" (click)="closeNewStudentModal()">Cancel</button>
                <button class="add-button" *ngIf="newStudentUid" [disabled]="!newStudentPenOk"
                    (click)="register()">Register</button>
                <button class="add-button" *ngIf="!newStudentUid" [disabled]="!newStudentPenOk" (click)="enroll()">Add
                    Student</button>
            </div>
        </div>
    </div>
    
    <div class="custom-modal" *ngIf="shouldOpenRemoveStudentModal">
        <div class="modal-contents new-student-modal">
            <!-- <sa-modal-student-personal [savePayload]="cmc()" [isCreatingNew]="true" saveProp="payload">
            </sa-modal-student-personal>
            <modal-footer [pageModal]="pageModal" [isConfirmAlert]="isConfirmAlert()"></modal-footer> -->
    
            <div class="modal-title">Remove a Student from an Assessment</div>
            <table>
                <tr>
                    <th>PEN:</th>
                    <th>
                        <input oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^([\d]{0,9}).*$/g, '$1');"
                            [(ngModel)]="newStudentPen">
                        <button (click)="lookupPen2()">Lookup</button>
                    </th>
                    <!-- <th>
                        <mat-slide-toggle [(ngModel)]="strictPenValidation" color="primary">Use strict PEN validation
                        </mat-slide-toggle>
                    </th> -->
                </tr>
            </table>
            <div class="pen-error" *ngIf="newStudentErrors.pen">
                <i class="fa fa-exclamation-circle"></i>
                <div>
                    This is not a valid Personal Education Number (PEN). Visit the Ministry's <a
                        href="https://www2.gov.bc.ca/gov/content/education-training/k-12/administration/program-management/pen"
                        target="_blank">PEN Services website</a> to obtain the student's personal education number.
                </div>
            </div>
            <!-- <div class="input-error pen-error" *ngIf="newStudentErrors.pen">{{newStudentErrors.pen}}</div> -->
    
            <ng-container *ngIf="newStudentLookupResult == LookupResult.EXISTS_IN_THIS_SCHOOL && newStudentComponents">
                <div class="pen-info">
                    <div>This PEN is associated with a student registered by this school.
                        If the student associated with this PEN is <b>not</b> the student you are looking for,
                        please
                        contact Vretta Technical Support.
                    </div>
                </div>
                <div class="pen-support">
                    <div><b>Vretta Technical Support Information</b></div>
                    <div>
                        <div>Email: </div>
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                    <div>Email support is available between 8pm to 6pm PT.</div>
                </div>
    
            </ng-container>
    
            <ng-container *ngIf="newStudentLookupResult && newStudentLookupResult != LookupResult.EXISTS_IN_THIS_SCHOOL">
                <div class="pen-info">
                    <div>This PEN is not associated with any students registered by this school.</div>
                </div>
            </ng-container>
    
            <table>
                <thead>
                    <tr>
                        <th>Select</th>
                        <th>Code</th>
                        <th>Assessment</th>
                    </tr>
                </thead>
                <tbody>
                    <ng-container *ngFor="let component of newStudentComponents">
                        <ng-container
                            *ngIf="newStudentLookupResult != LookupResult.EXISTS_IN_THIS_SCHOOL || !component.final">
                            <tr>
                                <td>
                                    <input type="checkbox" [(ngModel)]="component.checked" [disabled]="component.final"
                                        (change)="onRemoveStudentComponentChecked()">
                                </td>
                                <td>{{component.code}}</td>
                                <td>{{component.name}}</td>
                            </tr>
                        </ng-container>
                    </ng-container>
                </tbody>
            </table>
    
            <div class="vertical-grow"></div>
            <div class="actions">
                <button class="cancel-button" (click)="closeRemoveStudentModal()">Cancel</button>
                <button class="add-button"
                    [disabled]="!newStudentPenOk || newStudentLookupResult != LookupResult.EXISTS_IN_THIS_SCHOOL"
                    (click)="unregister()">Remove</button>
            </div>
        </div>
    </div>
    
    <!-- UNUSED -->
    <div class="custom-modal" *ngIf="false && shouldOpenNewStudentModal">
        <div class="modal-contents new-student-modal">
            <!-- <sa-modal-student-personal [savePayload]="cmc()" [isCreatingNew]="true" saveProp="payload">
            </sa-modal-student-personal>
            <modal-footer [pageModal]="pageModal" [isConfirmAlert]="isConfirmAlert()"></modal-footer> -->
    
            <div class="modal-title">Add a New Student</div>
            <table>
                <tr>
                    <th>PEN:</th>
                    <th>
                        <input oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/^([\d]{0,9}).*$/g, '$1');"
                            [(ngModel)]="newStudentPen">
                        <button (click)="lookupPen()">Lookup</button>
                    </th>
                    <!-- <th>
                        <mat-slide-toggle [(ngModel)]="strictPenValidation" color="primary">Use strict PEN validation
                        </mat-slide-toggle>
                    </th> -->
                </tr>
            </table>
            <div class="pen-error" *ngIf="newStudentErrors.pen">
                <i class="fa fa-exclamation-circle"></i>
                <div>
                    This is not a valid Personal Education Number (PEN). Visit the Ministry's <a
                        href="https://www2.gov.bc.ca/gov/content/education-training/k-12/administration/program-management/pen"
                        target="_blank">PEN Services website</a> to obtain the student's personal education number.
                </div>
            </div>
            <!-- <div class="input-error pen-error" *ngIf="newStudentErrors.pen">{{newStudentErrors.pen}}</div> -->
    
            <div class="lookup-results">
                <div *ngIf="isNewStudentLoading" class="accounts-loader">
                    <div></div>
                </div>
                <ng-container *ngIf="!isNewStudentLoading">
                    <ng-container *ngIf="newStudentInfo1">
                        <div class="pen-info">
                            <div>This PEN is associated with a student registered by this school. You will be
                                registering
                                the
                                student for the assessment <b>{{selectedAssessment.code}}</b>.
                                <br>
                                If the student associated with this PEN is <b>not</b> the student you are looking for,
                                please
                                contact Vretta Technical Support.
                            </div>
                        </div>
                        <div class="pen-support">
                            <div><b>Vretta Technical Support Information</b></div>
                            <div>
                                <div>Email: </div>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div>Email support is available between 8pm to 6pm PT.</div>
                        </div>
                    </ng-container>
    
                    <div class="pen-info" *ngIf="newStudentInfo2">
                        <div>This PEN is not associated with any students registered by this school. You will be
                            registering this
                            student for this school and for the assessment <b>
                                {{selectedAssessment.code}}</b>.</div>
                    </div>
    
                    <div class="pen-warning" *ngIf="newStudentWarning">
                        <i class="fa fa-exclamation-triangle"></i>
                        <div>
                            This PEN is associated with a student registered by <b>another</b> school. You will be
                            registering this student for this school and for the assessment
                            <b>{{selectedAssessment.code}}</b>.
                        </div>
                    </div>
                    <div class="pen-info" *ngIf="newStudentInfo3">
                        This PEN is not associated with any student registered for
                        {{selectedAssessment.code}}. This walk-in registration will need to be validated by the ministry
                        administrator before the student may write the assessment.
                    </div>
    
                    <ng-container *ngIf="newStudentError1 || newStudentError2">
                        <div class="pen-error">
                            <i class="fa fa-exclamation-circle"></i>
                            <div *ngIf="newStudentError1">
                                This PEN is already associated with a student registered by this school for the
                                assessment
                                <b>{{selectedAssessment.code}}</b>. The walk-in student will not be able to write
                                the
                                assessment until this matter is resolved. Please contact Vretta Technical Support.
                            </div>
                            <div *ngIf="newStudentError2">
                                This PEN is already associated with a student registered by <b>another</b> school for the
                                assessment <b>{{selectedAssessment.code}}</b>.
                            </div>
                            <div *ngIf="newStudentError3">
                            </div>
                        </div>
                        <div class="pen-support">
                            <div><b>Vretta Technical Support Information</b></div>
                            <div>
                                <div>Email: </div>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                            <div>Email support is available between 8pm to 6pm PT.</div>
                        </div>
                    </ng-container>
    
                    <div class="student-form" *ngIf="newStudentPenOk && !newStudentError1 && !newStudentError2">
                        <div>
                            <div>First Name: <span class="required-star">*</span></div>
                            <input type="text" [(ngModel)]="newStudentInfo.firstName">
                        </div>
                        <div class="input-error" *ngIf="newStudentErrors.firstName">{{newStudentErrors.firstName}}</div>
                        <!-- <div>
                            <div>Preferred Name:</div>
                            <input type="text" [(ngModel)]="newStudentInfo.preferredName">
                        </div> -->
                        <div>
                            <div>Last Name: <span class="required-star">*</span></div>
                            <input type="text" [(ngModel)]="newStudentInfo.lastName">
                        </div>
                        <div class="input-error" *ngIf="newStudentErrors.lastName">{{newStudentErrors.lastName}}</div>
                        <br>
                        <!-- <mat-slide-toggle [(ngModel)]="newStudentInfo.specialFormRequest" color="primary"><b>I am requesting a Special Format Assessement for this student</b></mat-slide-toggle> -->
                    </div>
                </ng-container>
            </div>
    
            <div class="vertical-grow"></div>
            <div class="actions">
                <button class="cancel-button" (click)="closeNewStudentModal()">Cancel</button>
                <button class="add-button" [disabled]="!newStudentPenOk" (click)="addNewStudent()">Add Student</button>
            </div>
        </div>
    </div>
    
    
    <!--<bcg-student-info-modal *ngIf="shouldOpenStudentInfoModal" (close)="closeStudentInfoModal()"
        [testWindow]="selectedTestWindow" [uid]="studentInfoModalRow.uid" [accountType]="AccountType.SCHOOL_ADMIN"
        [disableInteractions]="studentInfoModalRow.unenrolled == 1">
    </bcg-student-info-modal>-->
</div>