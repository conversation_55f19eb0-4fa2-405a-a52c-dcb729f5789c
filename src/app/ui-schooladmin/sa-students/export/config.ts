export const APIStudentColumnOSSLTList = () => [
  'SchMident',
  'TermFormat',
  'FirstName', //'FirstName',
  'LastName', //'LastName',
  'DateofBirth',
  'StudentOEN',
  'SASN',
  'StudentType',
  'EnrolledOntario',
  'OutOfProvinceResidence',
  'StatusInCanada',
  'IndigenousType',
  'MathTeacherFirstName',
  'MathTeacherLastName',
  'ClassCode',
  'FrenchImmersionOrExtended',
  'Gender',
  'EligibilityStatus',
  'LevelofStudyLanguage',
  'DateOfFTE',
  'Grouping',
  'Homeroom',
  'NonParticipationStatus',
  'Graduating',
  'LearningFormat',
  'DateEnteredSchool',
  'DateEnteredBoard',
  'FirstLanguage',
  'BornOutsideCanada',
  'TimeInCanada',
  'Refugee',
  'LanguageLearner',
  'SpecProvBreaks',
  'IEP',
  'IPRCExceptionalities',
  'AccBreaks',
  'AccSign',
  'AccBraille',
  'AccAssistiveTech',
  'AccScribing',
  'AccOther',
  'SpecPermTemp',
  'SpecPermMoved',
  'SpecPermIEP',
]

export const APIStudentColumnG9List = () => [
  'SchMident',
  'FirstName', //'FirstName',
  'LastName', //'LastName',
  'DateofBirth',
  'StudentOEN',
  'SASN',
  'StudentType',
  'EnrolledOntario',
  'OutOfProvinceResidence',
  'StatusInCanada',
  'IndigenousType',
  'LearningFormat',
  'MathTeacherFirstName',
  'MathTeacherLastName',
  'ClassCode',
  'TermFormat',
  'FrenchImmersionOrExtended',
  'Gender',
  'EligibilityStatus',
  'LevelofStudyLanguage',
  'DateOfFTE',
  'Grouping',
  'Homeroom',
  'NonParticipationStatus',
  'Graduating',
  'DateEnteredSchool',
  'DateEnteredBoard',
  'FirstLanguage',
  'BornOutsideCanada',
  'TimeInCanada',
  'Refugee',
  'LanguageLearner',
  'IEP',
  'IPRCExceptionalities',
  'SpecProvBreaks',
  'AccBreaks',
  'AccSign',
  'AccBraille',
  'AccAssistiveTech',
  'AccScribing',
  'AccOther',
  'SpecPermTemp',
  'SpecPermMoved',
  'SpecPermIEP',
]

export const APIStudentColumnPJList = () => [
  'SchMident',
  'FirstName',
  'LastName',
  'DateofBirth',
  'StudentOEN',
  'SASN',
  'StudentType',
  'EnrolledOntario',
  'OutOfProvinceResidence',
  'StatusInCanada',
  'IndigenousType',
  'Grade',
  'LearningFormat',
  'FrenchImmersion',
  'ClassTeacherFirstName',
  'ClassTeacherLastName',
  'ClassCode',
  'Gender',
  'DateEnteredSchool',
  'DateEnteredBoard',
  'FirstLanguage',
  'BornOutsideCanada',
  'TimeInCanada',
  'Refugee',
  'JrKindergarten',
  'SrKindergarten',
  'LanguageLearner',
  'IEP',
  'SpecEdNoExpectationReadWrite',
  'SpecEdNoExpectationMath',
  'IPRCExceptionalities',
  'ExemptionRead',
  'ExemptionWrite',
  'ExemptionMath',
  'AccSignRead',
  'AccSignWrite',
  'AccSignMath',
  'AccBrailleRead',
  'AccBrailleWrite',
  'AccBrailleMath',
  'AccAudioVersionRead',
  'AccAudioVersionWrite',
  'AccAudioVersionMath',
  'AccAssistiveTechRead',
  'AccAssistiveTechWrite',
  'AccAssistiveTechMath',
  'AccScribingRead',
  'AccScribingWrite',
  'AccScribingMath',
  'AccAudioResponseRead',
  'AccAudioResponseWrite',
  'AccAudioResponseMath',
  'SpecPermTemp',
  'SpecPermMoved',
]