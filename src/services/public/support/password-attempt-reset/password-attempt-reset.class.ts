import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import logger from '../../../../logger';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class PasswordAttemptReset implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const {uid} = params.query;
      if(uid){
        return await dbRawRead(this.app, uid, `
          select au.uid
               , au.id as auth_id
               , au.email
               , au.failed_login_attempts
               , u.first_name
               , u.last_name
          from auths au
          join users u on u.id = au.uid
          where au.uid = ?
          ;
        `)
      } 
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!id || !data || !params) {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    if (id && params && data){
      const { uid } = <any>data;
      const created_by_id = await currentUid(this.app, params);
      await dbRawWrite(this.app, [id], `
        UPDATE auths a
        SET failed_login_attempts  = 0
        WHERE id = ?
        ;
      `);

      let log_data = {
        UID: uid,
        AuthId: id,
        FailedLoginAttempts: 0
      }

      let log_entry = {
        created_by_id,
        slug: 'SUPPORT_RESET_PASSWORD_ATTEMPT',
        data: JSON.stringify(log_data)
      }

      logger.info(log_entry);
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed()
  }
}
