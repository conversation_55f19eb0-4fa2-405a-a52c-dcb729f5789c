.invoice-table {
  th {
      border: 1px solid #000000 !important;
      background-color: #d3d3d3 !important;
      color: black !important;
      text-align: center;
      &.limit-width {
          text-align: left;
          max-width: 7rem;
      }
  }
  td {
      border: 1px solid black !important;
  }
  .no-padding {
      padding: 0;
  }
  .students-container {
      .students-container-flex {
          display: flex;

          .student-header {
              font-weight: bold;
              text-align: left;
          }
          .dynamic-col {
              padding: 0.5rem;
              flex: 1;
              text-align: left;
              border-bottom: 1px solid black;
              &:first-child {
                  border-right: 1px solid black;
              }
          }
          &:last-child {
              .dynamic-col {
                  border-bottom: none;
              }
          }
      }
  }
}


@media print {
  body {
    padding: 0.5rem;
  }
  div {
    width: 100%;
  }
  table {
      border-collapse: collapse;
      border-spacing: 0;
      width: 100%;
    }
    th {
      border: 1px solid #000000 !important;
      background-color: #d3d3d3 !important;
      color: #000000  !important;
      text-align: center;
      -webkit-print-color-adjust: exact; 
      padding: 0.5em 0.75em;
      vertical-align: top;
    }
    th.limit-width {
      text-align: left;
      max-width: 7rem;
    }
    td {
        border: 1px solid #000000 !important;
        -webkit-print-color-adjust: exact; 
        padding: 0.5em 0.75em;
        vertical-align: top;
    }
    .no-padding {
        padding: 0;
    }
    .students-container-flex {
      display: flex;
    }
    .students-container-flex:last-child .dynamic-col {
      border-bottom: none;
    }
    .student-header {
        font-weight: bold;
        text-align: left;
    }
    .dynamic-col {
        padding: 0.5rem;
        flex: 1;
        text-align: left;
        border-bottom: 1px solid black;
        -webkit-print-color-adjust: exact; 
    }
  
    .dynamic-col:first-child {
      border-right: 1px solid #000000;
      -webkit-print-color-adjust: exact; 
    }
    .dynamic-col:first-child {
      border-right: 1px solid #000000;
      -webkit-print-color-adjust: exact; 
    }
    .pagebreak {
      clear: both;
      page-break-after: always;
    }
}

.d-none {
  display: none;
}