import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class Exports implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const includeBackgroundJobs = params?.query?.includeBackgroundJobs === 'true';
    
    let limit = 100;
    if (includeBackgroundJobs){
      limit = 500; // load fewer if including background jobs
    }
    
    return dbRawRead(this.app, [], `
      select dej.id
           , dej.status
           , dej.dag_job_name
           , dej.test_window_id
           , dej.description
           , dej.started_on
           , dej.completed_on
           , dej.created_by_uid
           , dej.created_on
           , dej.updated_on
           , dej.last_stage_completed
           , dej.failure_reason
           , dej.is_bg
      from data_export_jobs  dej
      where  1=1
        ${includeBackgroundJobs ? '' : 'and dej.is_bg = 0'}
      order by dej.id desc 
      limit ${limit}
    `);
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
