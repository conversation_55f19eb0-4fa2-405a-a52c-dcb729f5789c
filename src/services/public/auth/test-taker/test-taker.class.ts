import { Id, NullableId, Pa<PERSON>ated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { IUser } from '../../../db/schemas/users.schema';
import { IOCTApplicantId } from '../../../db/schemas/oct_applicant_ids.schema';
import { ICredits } from '../../../db/schemas/credits.schema';
import { IAuth } from '../../../db/schemas/auths.schema';
import { dbDateNow } from '../../../../util/db-dates';
import { AccountType } from '../../../../types/account-types';
import { DBD_U_ROLE_TYPES, DBD_U_GROUP_TYPES } from '../../../../constants/db-extracts';
import { DB_MAX_ACCTS_P_PAGE, DB_MAX_EMAIL_LEN } from '../../../../constants/db-limits';
import { IOCTApplicantIdArchive } from '../../../db/schemas/oct_applicant_ids_archive.schema';
import { ICapObject } from '../../../auth/oct-cap/oct-cap.class';
import logger from '../../../../logger';

interface Data extends Partial<IAuth>{}

export interface IUserMeta {
  id?:number,
  uid: number,
  key_namespace?:string,
  key:string,
  value: string,
}
export interface ICreateFromInfo {
  collegeApplicationNumber: string, 
  firstName: string,
  lastName: string, 
  phoneNumber: string,
  email: string,
  password: string,
  meta: {[key:string]:string},
  //
  langCode? :string
  domain? :string
  school_group_id?: number
}

export interface ICreateCredit {
  uid: number,
  isFree: number,
  freeCreditReason?: string,
  mptStatusCode?: number,
  createdOn?: Date
}

interface IValidatedCapObject {
  capNum: number,
  statusCode: number,
  capString: string
}

interface ServiceOptions {}

export class TestTaker implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async registerUserMeta(uid: number, capObj: IValidatedCapObject) {
    const currentDate = dbDateNow(this.app);
    const userMetaFields = {
      uid: uid,
      key: 'mpt_status_code',
      value: capObj.statusCode,
      created_on: currentDate,
      created_by_uid: uid,
      updated_on: currentDate,
      updated_by_uid: uid
    };

    await this.app
      .service('db/write/user-metas')
      .create(userMetaFields);
  }

  async registerTestTakerWithCredit (uid: number, capObj: IValidatedCapObject): Promise<void> {
    const createdOn = new Date();
    // create new credit
    const newCreditFields: ICreateCredit = {
      uid: uid,
      isFree: 1,
      freeCreditReason: 'free first attempt',
      createdOn: createdOn,
      mptStatusCode: capObj.statusCode
    }
    const newCredit: Partial<ICredits> = await this.app
      .service('credits/credits')
      .create(newCreditFields)

    await this.app
      .service('credits/credit-transactions')
      .createAcquiredTransaction(newCredit.id, createdOn, newCredit.uid, newCredit.free_credit_reason);
  }

  async create (data: ICreateFromInfo, params?: Params): Promise<Data> {
    const {
      collegeApplicationNumber,
      firstName,
      lastName,
      email,
      password,
      langCode,
      domain,
      meta,
      school_group_id
    } = data;

    const creditsEnabled: any = await this.app
      .service('public/credits/credit-system')
      .find();

    const isBypassOctValidator = await this.app.service('public/test-taker/test-sessions/oct-validation').isBypassOctValidator();

    // check if OCT ID is valid
    let validatedCap;
    let validatedCapObj: IValidatedCapObject = {capNum: -1, statusCode: -1, capString: ''};
    let isOctRecordToReset:boolean = false;
    let octRecordToReset:IOCTApplicantId = {};

    if(!isBypassOctValidator){

      if(!collegeApplicationNumber) throw new Errors.BadRequest("MISSING_COLLEGE_APPLICATION_NUMBER");
      
      if (creditsEnabled.isEnabled) {
        validatedCap = <ICapObject> await this.app
        .service('auth/oct-cap')
        .validateOctCap(collegeApplicationNumber, lastName)
        .catch(e => {
          throw new Errors.NotFound('INVALID_OCT_ID');
        });
  
        validatedCapObj.capNum = validatedCap.capNum;
        validatedCapObj.statusCode = validatedCap.statusCode;
        validatedCapObj.capString = validatedCap.capString
        validatedCap = validatedCapObj.capString;
      } else {
        validatedCap = <string> await this.app
        .service('auth/oct-cap')
        .validateOctCap(collegeApplicationNumber, lastName)
        .catch(e => {
          throw new Errors.NotFound('INVALID_OCT_ID');
        });
      }

      // identify previously used OCT ID (tied to a claimed user)
      const prevOctIdRecords = <IOCTApplicantId[]> await this.app
        .service('db/read/oct-applicant-ids')
        .db()
        .where('oct_id', validatedCap)
        .whereNotNull('uid')
        .limit(1)
      
      
      if (prevOctIdRecords.length > 0){
        // identify if unconfirmed user 
        const prevOctIdRecord = prevOctIdRecords[0];
        const octLinkedUid = <number> prevOctIdRecord.uid;
        // is the user confirmed?
        const userAuths = <IAuth[]> await this.app
          .service('db/read/auths').db()
          .where('uid', octLinkedUid)
        if (userAuths[0] && userAuths[0].enabled == 1){
          throw new Errors.BadGateway('OCT_ID_IN_USE');  
        }
        else{
          isOctRecordToReset = true;
          octRecordToReset = prevOctIdRecord;
        }
      }
    }

    // block emails that are unreasonably long
    if (email.length > DB_MAX_EMAIL_LEN){
      throw new Errors.BadGateway('EMAIL_IN_USE');
    }


    // validate the password
    const passwordValidated = await this.app
      .service('auth/password-validation')
      .sanitizeAndValidateNewPassword(data.password)

    // create user entry
    const userCreateFields:Partial<IUser> = {
      account_type : AccountType.TEST_TAKER,
      first_name   : firstName,
      last_name    : lastName,
      contact_email: email,
      is_claimed   : 1 // this is only 0 for invitations
    }
    const userRecord = <IUser> await this.app
      .service('db/write/users')
      .create(userCreateFields);
    const uid = userRecord.id;

    // revoke unclaimed OCT (if required)
    if (isOctRecordToReset){
      // revoke roles
      await this.app
        .service('auth/user-role-actions')
        .revokeUser(<number> octRecordToReset.uid, uid);
      // archive oct id
      const archiveCreateFields:IOCTApplicantIdArchive = {
        oct_applicant_ids_id: <number> octRecordToReset.id,
        oct_id: <string> octRecordToReset.oct_id,
        uid: <number> octRecordToReset.uid,
        created_on: <any> octRecordToReset.created_on,
      }
      await this.app 
        .service('db/write/oct-applicant-ids-archive')
        .create(archiveCreateFields)
      // ... and delete
      await this.app 
        .service('db/write/oct-applicant-ids')
        .remove(<number> octRecordToReset.id)
    }

    if(!isBypassOctValidator){

      // mark the oct ID as used up
      const octaiCreateFields: IOCTApplicantId = {
        oct_id: validatedCap,
        uid,
      }
      await this.app
        .service('db/write/oct-applicant-ids')
        .create(octaiCreateFields)
    }


    // assign generic role
    const sys_group_id = await this.app
      .service('auth/user-role-actions')
      .getSingularGroupId(DBD_U_GROUP_TYPES.mpt_sys);

    await this.app
      .service('auth/user-role-actions')
      .assignUserRoleToGroup({
        uid,
        group_id: <number> sys_group_id,
        role_type: DBD_U_ROLE_TYPES.mpt_applicant,
        created_by_uid: uid,
      })

      if(school_group_id) {
        // assign role to school
        await this.app
        .service('auth/user-role-actions')
        .assignUserRoleToGroup({
          uid,
          group_id: school_group_id,
          role_type: DBD_U_ROLE_TYPES.schl_student,
          created_by_uid: uid,
        });
      }

    // create auth
    const authInsertFields:IAuth = {
      email,
      password: passwordValidated,
      uid,
      enabled:0
    }
    const auth = await this.app
      .service('db/write/auths')
      .create(authInsertFields, {isRecordLookup:true, langCode, domain});

 

    if (meta){
      await Promise.all(
        Object.keys(meta).map(key => {
          const metaProp:IUserMeta = { uid, key, value: meta[key] }
          return this.app
            .service('db/write/user-metas')
            .create(metaProp);
        })
      )
    }

    if (creditsEnabled.isEnabled) {
      await this.registerTestTakerWithCredit(uid, validatedCapObj);
      await this.registerUserMeta(uid, validatedCapObj);

      if(!isBypassOctValidator){

        await this.app
          .service('auth/oct-cap')
          .reviewAndAddTeachables(uid, lastName, validatedCapObj.capString)
          .catch((e) => {
            throw new Errors.BadRequest('FAILED_STORING_OCT_TEACHABLE');
        });
        const isEQAOTesting = this.app.service('auth/oct-cap').checkIfEQAOTesting(validatedCapObj.capString);
        if (!isEQAOTesting) {
          const accessToken = await this.app
            .service('third-party/oct/oct-authentication')
            .getOCTAccessToken();
          try {
            await this.app
              .service('auth/oct-cap')
              .reviewAndAddTeachables(uid, lastName, validatedCapObj.capString, accessToken)
              .catch((e) => {
                throw new Errors.BadRequest('FAILED_STORING_OCT_TEACHABLE');
              });
          }
          catch (e){
            logger.info({created_by_uid: uid, data: JSON.stringify({lastName, cap: validatedCapObj.capString}), slug: 'FAILED_OCT_TEACHABLE_CALL'})
          }
        }
      }
    }

    // return no payload
    return {};
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
