<div>
  <div style="display:flex; justify-content:center; align-items:center;">
      <button class="button dont-print" (click)="printRequest()">
          <i style="margin-right:0.5em" class="fas fa-print"></i>
          <tra slug="btn_print"></tra>
      </button>
  </div>
  <hr/>
  <div [id]="'print-container-1'">
      <table class="invoice-table">
          <thead>
              <th><tra slug="Request No."></tra></th>            <!-- Invoice No. -->     
              <th><tra slug="assessment_name_alt_version"></tra></th>        <!-- Transaction No. -->  
              <th><tra slug="req_formats_alt_version"></tra></th>  
              <th><tra slug="request_status_alt_version"></tra></th>   <!-- Name of Assessment --> 
          </thead>
          <tbody>
              <tr>
                  <td style="text-align: center;">{{rowData.id}}</td>
                  <td style="text-align: center;">{{getAssessmentName()}}</td>
                  <td style="text-align: center;">{{renderRequestedFormats(rowData.requested_formats, rowData.lang)}}</td>
                  <td style="text-align: center;">{{getRequestSlug(rowData.status)}}</td>
              </tr>
          </tbody>
      </table>
      <div>
          <br/>
      </div>
      <table class="invoice-table">
          <thead>
              <th><tra [slug]="getStudentIdSlug()"></tra></th>
              <th *ngIf="isSASNSchool"><tra slug="sasn_alt_version"></tra></th>
              <th><tra slug="sch_admin_name_alt_version"></tra></th>
              <th><tra slug="sch_admin_email_col_alt_version"></tra></th>  
              <th><tra slug="teacher_alt_version"></tra></th>
          </thead>
          <tbody>
              <tr>
                  <td style="text-align: center;">{{rowData.student_id_number}}</td>
                  <td *ngIf="isSASNSchool" style="text-align: center;">{{rowData.student_sasn}}</td>
                  <td style="text-align: center;">{{rowData.schl_admin_name}}</td>
                  <td style="text-align: center;">{{rowData.schl_admin_email}}</td>
                  <td style="text-align: center;">{{rowData.teacher_name}}</td>
              </tr>
          </tbody>
      </table>
      <div>
          <br/>
      </div>
      <table class="invoice-table">
          <tbody>
              <tr>
                  <th class="limit-width"><tra slug="reason_alt_version"></tra></th>   
                  <td>{{rowData.reason}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="op_admin_date_alt_version"></tra></th>     
                  <td>{{parseDate(rowData.op_administration_date)}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="date_approved_alt_version"></tra></th>     
                  <td>{{parseDate(rowData.approved_on)}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="date_rejected_alt_version"></tra></th>  
                  <td>{{parseDate(rowData.rejected_on)}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="canceled_on_alt_version"></tra></th>         
                  <td>{{parseDate(rowData.canceled_on)}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="test_window_alt_version"></tra></th>         
                  <td>{{rowData.test_window_id}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra [slug]="getSchlForeignIdSlug()"></tra></th>         
                  <td>{{rowData.schl_foreign_id}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="sch_name_alt_version"></tra></th>         
                  <td>{{rowData.schl_name}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="sch_phone_alt_version"></tra></th>         
                  <td>{{rowData.schl_phone_braille}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="sch_address_braille_alt_version"></tra></th>         
                  <td>{{rowData.schl_address_braille}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="sch_contact_braille_alt_version"></tra></th>         
                  <td>{{rowData.schl_contact_braille}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="sch_email_braille_alt_version"></tra></th>         
                  <td>{{rowData.schl_email_braille}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="ship_required_alt_version"></tra></th>         
                  <td>{{renderYesNo(rowData.shipping_required)}}</td>
              </tr>
              <!-- <tr>
                  <th class="limit-width"><tra slug="ship_company_sample_alt_version"></tra></th>         
                  <td>{{rowData.shipping_company_sample}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="ship_track_sample_alt_version"></tra></th>         
                  <td><a href="{{rowData.tracking_url_sample}}" target="_blank">{{rowData.tracking_number_sample}}</a></td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="ship_deliver_date_sample_alt_version"></tra></th>         
                  <td>{{rowData.shipping_delivered_date_sample}}</td>
              </tr> -->
              <tr>
                  <th class="limit-width"><tra slug="ship_company_op_alt_version"></tra></th>         
                  <td>{{rowData.shipping_company_operational}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="ship_track_op_alt_version"></tra></th>         
                  <td><a href="{{rowData.tracking_url_operational}}" target="_blank">{{rowData.tracking_number_operational}}</a></td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="ship_deliver_date_op_alt_version"></tra></th>         
                  <td>{{rowData.shipping_delivered_date_operational}}</td>
              </tr>
              <tr>
                  <th class="limit-width"><tra slug="ship_track_return_alt_version"></tra></th>         
                  <td><a href="{{rowData.tracking_url_return}}" target="_blank">{{rowData.tracking_number_return}}</a></td>
              </tr>
          </tbody>
      </table>
      <div>
          <br/>
      </div>
      <div>
          <span style="font-weight: bold;"><tra slug="request_date_alt_version"></tra></span>
          <span>: {{parseDate(rowData.created_on)}}</span>
      </div>  
  </div>
</div>