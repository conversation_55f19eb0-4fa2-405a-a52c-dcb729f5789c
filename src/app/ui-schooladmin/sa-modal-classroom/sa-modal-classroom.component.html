<div class="modal-classroom" [ngClass]="lang.getCurrentLanguage()">
  <div *ngIf="isSelectingClassroom">
    <div><tra slug="select_classroom_edit"></tra></div>
    <ul>
      <li *ngFor="let classroom of classrooms">
        <button class="button" (click)="selectClassroom(classroom)">
          {{classroom.class_code}}
        </button>
      </li>
    </ul>
  </div>
  <div *ngIf="!isSelectingClassroom && formEntries">
    <div 
      class="required-field-container-classroom" 
      style="margin:0.5rem 0 1rem 0">
        <b><tra [slug]="getRequiredFieldSlug()"></tra></b>
        <i class="fa fa-asterisk" aria-hidden="true" style="margin-left: 0.5em;"></i>
    </div>
    <sa-widget-form-entry
        *ngFor="let entry of formEntries"
        [formEntries]="formEntries"
        [formEntry]="entry">
    </sa-widget-form-entry>
    <div *ngIf="isEditing" style="margin-right: 0.5em; margin-top: 1em;">
      <a (click)="addInvigilatorsModalStart()">
        <span><tra slug="pj_add_remove_class_invigilators"></tra></span>
      </a>
      <span style = "font-weight: bold; margin-left: 0.5em; margin-bottom: 1em;"><tra [slug]="getAdditionalInvigilatorSlug()"></tra></span>
    </div> 
    <table *ngIf ='invigilators.length > 0'>
      <tr>
        <th>
          <tra-md *ngIf="!whiteLabelService.isABED()" slug="sa_label_invigilator"></tra-md>
          <tra-md *ngIf="whiteLabelService.isABED()" slug="lbl_exam_supervisors"></tra-md>
        </th>
        <th><tra-md slug="sa_label_invigilator_remove"></tra-md></th>
      </tr>
      <tr *ngFor="let invigilator of invigilators">
        <td>{{invigilator.first_name+" "+invigilator.last_name}}</td>
        <td><button class="icon fa fa-window-close" (click)="removeInvigilator(invigilator)"></button></td>
      </tr>    
    </table>
  </div>
</div>
<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
      <div [ngSwitch]="cModal().type">
        <div *ngSwitchCase="ClassroomModal.ADD_INVIGILATOR" style="width: 30em;">
          <sa-modal-add-invigilators 
            [availableTeachers]="availableTeachers" 
            (teacherChange)="toggleAddRemoveInvigilator($event)"
            (closeInvigilatorModal)="addInvigilatorsModalFinish()"></sa-modal-add-invigilators>
        </div>
      </div>
  </div>
</div>