import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ITemplateDef, StyleprofileService } from 'src/app/core/styleprofile.service';
import { IAuthoringGroup, ItemMakerService } from '../item-maker.service';
import { IAuthRestrictions } from '../item-set-editor/item-set-editor.component';
import { CsvExportParams, GridOptions } from 'ag-grid-community';
import { identifyQuestionResponseEntries } from '../item-set-editor/models/expected-answer';
import { IContentElement } from 'src/app/ui-testrunner/models';
import { ElementTypeDefs } from 'src/app/ui-testrunner/models/ElementTypeDefs';
import { LangService } from 'src/app/core/lang.service';
import { TemplateMeta } from 'src/app/ui-testrunner/element-render-template/model';
import { ElementType } from '../../ui-testrunner/models';
import { VALIDATOR_MODES } from '../element-config-validator/element-config-validator.component';
import { IContentElementValidator } from 'src/app/ui-testrunner/element-render-validator/model';

const coldef = (field:string, headerName:string, options?) => {
  return {
    field,
    headerName,
    sortable:true,
    filter:true,
    ... (options || {})
  }
}

@Component({
  selector: 'template-library',
  templateUrl: './template-library.component.html',
  styleUrls: ['./template-library.component.scss']
})
export class TemplateLibraryComponent implements OnInit, OnDestroy {

  constructor(
    public profile: StyleprofileService,
    public itemMakerService: ItemMakerService,
    public lang: LangService
  ) { }

  subscription = new Subscription();
  isTemplateSelectorModalActive:boolean;
  templateNameFilter = '';
  currentGroupId: number | null = null;
  templateAuthGroups: IAuthoringGroup[] = [];
  hasSimplified  = false;
  hasArchived = false;
  isListView = false;  

  // AG-GRID
  defaultColDef = {
    resizable: true,
  }
  gridOptions: GridOptions = {
    
    rowSelection: 'single'
  }

  gridApi;
  gridColumnApi;

  columnDefs = [
    coldef('id', 'ID', { 
      width:130
    }),
    coldef('template_name', 'Template Name', { 
      width:400
    }),
    coldef('current_version_id', 'Current Version', { 
      width:200
    }),
    coldef('entryElementTypes', 'Response Types', { 
      width:400
    }),
    coldef('description', 'Description', { 
      width:200
    }),
    coldef('is_simplified', 'Simplified?', { 
      width:150
    }),
    coldef('is_math_compatible', 'Math Compatible?', { 
      width:175
    }),
  ]

  async onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    params.columnApi.autoSizeColumns();
    // params.columnApi.autoSizeAllColumns();
  }

  @Input() hideArchived: boolean = true;
  @Input() authRestrictions: IAuthRestrictions = {
    isCurrentUserRestricted: true,
    isCurrentUserTemplateManager: false
  };

  ngOnInit(): void {
    this.initSubscriptions();
    this.initializeGroups();

    this.initializeAuthorizedColumns();
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  /**
   * Initializes the subscriptions to the style profile template observables.
   */
  initSubscriptions(){
    console.log('initing subscriptions')
    this.subscription.add(
      this.profile.templateSelectionReq.subscribe(isActivate => {
        this.isTemplateSelectorModalActive = isActivate
        this.ensureTemplateListIsLoaded();
        this.getGroups();
        if(this.isListView) {
          this.setListView(true);
        }
      })
    )
  }

  initializeAuthorizedColumns() {
    if(!this.hideArchived) {
      this.columnDefs.push(
        coldef('is_archived', 'Is Archived?', { 
          width:150
        })
      )
    }
  }

  /**
   * Emits templateSelectionReq to close the template modal.
   */
  closeTemplateModal(){
    this.profile.templateSelectionReq.next(false);
  }

  /**
   * Selects a template based on the template definition.
   * @param templateDef the template to select.
   */
  selectTemplate(templateDef:ITemplateDef){
    this.profile.templateSelection.next(templateDef);
    this.closeTemplateModal()
  }

  /**
   * Initializes the groups used by bank-specific templates.
   */
  async initializeGroups() {
    const groups = await this.getGroups();
    this.templateAuthGroups = groups;
  }

  /**
   * Get group information of group-specific templates
   * @returns group information of template groups associated with the user
   */
  async getGroups() {
    if(!this.itemMakerService.getAuthoringGroups() || !this.itemMakerService.getAuthoringGroups().length) {
      await this.itemMakerService.loadMyAuthoringGroups(true);
    }

    const list = this.profile.templates;
    const groupIds = list.filter((template) => template.bank_group_id != null).map((template) => template.bank_group_id)
    const groups = this.itemMakerService.getAuthoringGroups();
    const filteredGroups = groups.filter((group) => {
      return groupIds.includes(group.group_id);
    })
    
    return filteredGroups;
  }

  /**
   * Parses the template list from the style profile, and filtering it by the settings the user has specified.
   * @returns the list of templates according to filters specified by the user.
   */
  getTemplateList(isListView: boolean){
    let filterStr = (this.templateNameFilter || '').toLowerCase().trim()

    if(isListView) { // Filter str should be ignored for list view
      filterStr = '';
    }

    const templateList = this.profile.templates.filter(templateDef => 
      this.hideArchived ? templateDef.is_archived == 0 || templateDef.is_archived == undefined : true);
    const searchTemplateList = templateList.filter(templateDef => {
      if (filterStr){
        return ((templateDef.template_name.toLowerCase()).includes(filterStr) ||
                 templateDef.id.toString().startsWith(filterStr));
      }
      return true;
    });
    // check this separately, make sure it will display last (duplicates will be removed at the end of this function)
    const fuzzyTemplateList = filterStr == '' ? [] :  
      templateList.filter(templateDef => this.fuzzyMatch(filterStr, templateDef.template_name));

    const templateConditions = [...searchTemplateList, ...fuzzyTemplateList].filter(item => {
      // Check each filter condition only if it's active
      let conditions = true;
      if(!isListView) { // Only run these conditions if using grid view
        if (this.hasSimplified || this.authRestrictions.isCurrentUserRestricted) {
          conditions = conditions && (item.is_simplified === 1);
        }
        if (this.hasArchived) {
          conditions = conditions && (item.is_archived === 1); // archived templates
        } else {
          conditions = conditions && (item.is_archived === 0); // unarchived templates
        }
      }
      if(this.currentGroupId) {
        conditions = conditions && (item.bank_group_id == this.currentGroupId);
      }
      return conditions;
    });

    return templateConditions.filter((item, i) => templateConditions.findIndex((temp) => temp.id == item.id) == i); // Remove duplicates (for multi-group assigned templates)
  }

  /**
   * Sets the current group ID.
   * @param groupId the group ID to toggle.
   */
  toggleGroupFilter(groupId: number) {
    if(this.currentGroupId == groupId) {
      this.currentGroupId = null;
      return;
    }
    this.currentGroupId = groupId;
  }

  /**
   * Toggles the filter for viewing simplified templates
   */
  toggleSimplifiedFilter() {
    this.hasSimplified = !this.hasSimplified;
  }

  /**
  * Toggles the filter for viewing archived templates
  */
  toggleArchivedFilter() {
    this.hasArchived = !this.hasArchived;
  }
  
  ensureTemplateListIsLoaded() {
    if (!this.profile.isTemplateSuccessfullyLoaded) {
      this.profile.initializeTemplates();
    }
  }
  
  fuzzyMatch(filterText: string, target: string): boolean {
    const lowercaseTarget = target.toLowerCase();
    const filterBits = filterText.split(' ').map(str => str.trim().toLowerCase()).filter(str => str !== '');
    if (filterBits.length == 0) return true;
    return filterBits.every(bit => lowercaseTarget.includes(bit));
  }

  /*
    The is_simplified and is_archived fields need to be converted from
    numbers to boolean.
  */
  getTemplateInfoField(template: ITemplateDef, field: string) {
    if (field === 'is_simplified' || field === 'is_archived') {
      return template[field] === 1;
    }
    return template[field];
  }

  /*
    Return the current date in DD-MM-YYYY format
  */
  getCurrentDateFormatted(): string {
    const date = new Date();
    
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}-${month}-${year}`;
  }

  /*
    For converting data into CSV format. objArray is the data, 
    and headerList is the keys of the data object.
  */
  ConvertToCSV(objArray: ITemplateDef[], headerList: string[]) {
    // Ensure objArray is an object
    const array = typeof objArray !== 'object' ? JSON.parse(objArray) : objArray;

    // Initialize the CSV string
    let str = '';
    
    // Create the header row
    const row = headerList.join(', ') + '\r\n';
    str += row;

    // Populate the CSV rows
    for (let i = 0; i < array.length; i++) {
      let line = '';
      for (let index of headerList) {
        const value = array[i][index] !== undefined ? this.getTemplateInfoField(array[i], index) : '';
        if (index === 'id') {
          line += value;
        } else {
          line += ',' + value;
        }
      }
      str += line + '\r\n';
    }
    return str;
  }

  /*
    Download the csv file containing data with filename
  */
  downloadCsvFile(data: ITemplateDef[], filename: string = 'template_list') {
    const csvData = this.ConvertToCSV(data, [
        'id',
        'template_name',
        'current_version_id', 
        'is_simplified', 
        'is_archived']);

    const blob = new Blob(['\ufeff' + csvData], {
        type: 'text/csv;charset=utf-8;'});
    const dwldLink = document.createElement("a");
    const url = URL.createObjectURL(blob);
    const isSafariBrowser = navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');

    // In some versions of Safari, when a Blob URL is used to download files, 
    // the browser may not correctly handle the download process if the link
    // is opened in the same window. This opens the link in a new tab.
    if (isSafariBrowser) {
        dwldLink.setAttribute("target", "_blank");
    }

    dwldLink.setAttribute("href", url);
    dwldLink.setAttribute("download", `${filename}.csv`);
    dwldLink.style.visibility = "hidden";
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  /* 
    Export the template list based on current filters as a csv
  */
  exportTemplates() {
    const templateData: ITemplateDef[] = this.getTemplateList(false);
    this.downloadCsvFile(templateData, `template_list_${this.getCurrentDateFormatted()}`);
  }

  listData: any[] = [];
  setListView(toggle: boolean) {
    this.isListView = toggle;
    const templateList = this.getTemplateList(true);

    this.listData = this.getAdvancedInformation(templateList);
  }

  /**
   * Selects a template based on the reference of the templateDef from style profile.
   * @param templateDef the template definition to select.
   */
  selectTemplateAdvancedList(templateDef: ITemplateDef) {
    const template = this.getTemplateList(true).find((template) => template.id == templateDef.id);
    this.selectTemplate(template);
  }

  /**
   * Returns a version of the template list with advanced information.
   * @param templateList the list of template definitions.
   */
  getAdvancedInformation(templateList: ITemplateDef[]) {
    return templateList.map((templateDef) => {
      try {
        const templateConfig = this.profile.templateConfigRef.get(templateDef.id);
        
        const content: IContentElement[] = JSON.parse(templateConfig.content_config);

        const entryElements = identifyQuestionResponseEntries(content);

        // Get element type captions
        const entryElementTypes = entryElements.map((element) => {
          const elementType = element.elementType.toUpperCase();
          const elementTypeDef = ElementTypeDefs[elementType];

          if(elementTypeDef) {
            let caption = this.lang.tra(elementTypeDef.caption).replace(/\n/g, '').trim();
            
            if(elementType == ElementType.VALIDATOR.toUpperCase()) { // Use validator type for caption
              const modeId = (<IContentElementValidator>element).mode;
              const mode = VALIDATOR_MODES.find((mode) => mode.id == modeId);
              if(mode) {
                caption = `${caption} (${mode.caption})`;
              }
            }

            return caption
          }

          return element.elementType;
        });

        const entryElementUnique = Array.from(new Set(entryElementTypes)).join(', ');
        
        let is_math_compatible = 'N/A';
        if(templateDef.meta) { // Backwards compatible
          const meta: TemplateMeta = JSON.parse(templateDef.meta);
          is_math_compatible = meta.is_math_compatible ? 'Yes' : 'No'
        }

        return {
          ...templateDef, 
          is_simplified: templateDef.is_simplified == 1 ? 'Yes' : 'No', 
          entryElementTypes: entryElementUnique || 'None',
          is_math_compatible,
          is_archived: templateDef.is_archived == 1 ? 'Yes' : 'No'
        }
      } catch(err) {
        console.error(err);
        return templateDef;
      }
    })
  }

  exportCSV(){
    const params: CsvExportParams = {
      fileName: 'template-library-export'
    };
    this.gridOptions.api?.exportDataAsCsv(params);
  }
}
