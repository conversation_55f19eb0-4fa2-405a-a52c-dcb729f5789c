import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbEscapeString, dbRawReadReporting } from '../../../../util/db-raw';
import { SQL_SELECT_CLASS_LOOKUP } from './model/sql';
import { Errors } from '../../../../errors/general';

interface Data {}

interface ServiceOptions {}

export class ClassLookup implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const {
      schoolCode, 
      classNamePartial, 
      classAccessCodePartial
    } = params?.query || {}
    if (! (schoolCode || classNamePartial || classAccessCodePartial)){
      throw new Errors.BadRequest('NEED_FILTER')
    }
    const dbQueryParams = {
      schoolCode, 
      classNamePartial, 
      classAccessCodePartial
    }
    const queryTemplate = SQL_SELECT_CLASS_LOOKUP(dbQueryParams)
    return dbRawReadReporting(this.app, dbQueryParams, await queryTemplate );
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
