<fieldset [disabled]="isReadOnly()" (change)="emitChange()">
  
  <div style="display: grid; grid-template-columns: max-content max-content; column-gap: 1em;">
    <span>
      Style Mode: 
    </span>
    <select [(ngModel)]="styleConfig.styleMode">
      <option *ngFor="let option of styleModeOptions" [value]="option.id">
        {{option.caption}}
      </option>
    </select>
  </div>
  
  <div *ngIf="!isSimplified">
    <label class="checkbox">
      <input type="checkbox" 
        [(ngModel)]="styleConfig.isUseCustomBorderColors"/>
      Style Border Colour
    </label>
  </div>
  
  
  <br>
    
  <div [ngSwitch]="styleConfig.styleMode">
    <div *ngSwitchCase="DND_STYLE_MODE.DEFAULT">
        <ng-container *ngTemplateOutlet="styleSimple"></ng-container>
    </div>
    <div *ngSwitchCase="DND_STYLE_MODE.INDIVIDUAL">
        <ng-container *ngTemplateOutlet="styleIndividual"></ng-container>
    </div>
    <div *ngSwitchCase="DND_STYLE_MODE.HOME">
        <ng-container *ngTemplateOutlet="styleHomeGroup"></ng-container>
    </div>
  </div>
  
  <ng-template #styleSimple>
    <table class="style-table">
      <tr>
        <td></td>
        <td>Background</td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">Border</td>
      </tr>
      <tr>
        <td>Target</td>
        <td>
          <input type="color" [(ngModel)]="constructionElement.config.targetBgColor" *ngIf="isShowColorInput">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: constructionElement.config, key: 'targetBgColor' }">
          </ng-container>
        </td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">
          <input type="color" [(ngModel)]="styleConfig.commonTargetBorderColor" *ngIf="isShowColorInput">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig, key: 'commonTargetBorderColor' }">
          </ng-container>
        </td>
      </tr>
      <tr>
        <td>Draggable</td>
        <td>
          <input type="color" [(ngModel)]="constructionElement.config.draggableBgColor" *ngIf="isShowColorInput">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: constructionElement.config, key: 'draggableBgColor' }">
          </ng-container>
        </td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">
          <input type="color" [(ngModel)]="styleConfig.commonDraggableBorderColor" *ngIf="isShowColorInput">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig, key: 'commonDraggableBorderColor' }">
          </ng-container>
        </td>
      </tr>
    </table>
    
    
    <!-- <config-preview-container headerText="Home Colours" *ngIf="isShowConfigPreview()">
      Home Background Colour:
      <input type="color" [(ngModel)]="constructionElement.config.homeBgColor" *ngIf="!isSimplified>
      <ng-container 
        *ngTemplateOutlet="colorPalettePicker;  context: { object: constructionElement.config, key: 'homeBgColor' }">
      </ng-container>
    </config-preview-container> -->
    
    <br>
  </ng-template>  
  
  <ng-template #styleIndividual>
    <table class="style-table no-padding">
      <tr>
        <td></td>
        <td>Background</td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">Border</td>
      </tr>
      <tr class="label-row">
        <td>Targets</td>
      </tr>
      <tr *ngFor="let id of targetIdList">
        <td>
          <input 
            type="text" disabled 
            style="width: 10em"
            value="{{idToLabelMap.get(id)}}" 
            class="disabled-input-label">
        </td>
        <td>
          <input type="color" [(ngModel)]="styleConfig.elementColors[id]" *ngIf="isShowColorInput && styleConfig.elementColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementColors, key: id }">
          </ng-container>
        </td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">
          <input type="color" [(ngModel)]="styleConfig.elementBorderColors[id]" *ngIf="isShowColorInput && styleConfig.elementBorderColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementBorderColors, key: id }">
          </ng-container>
        </td>
      </tr>
      <tr class="label-row">
        <td>Options</td>
      </tr>
      <tr *ngFor="let id of sortedOptionIdList">
        <td>
          <input 
            type="text" disabled 
            style="width: 10em"
            value="{{idToLabelMap.get(id)}}" 
            class="disabled-input-label">
        </td>
        <td>
          <input type="color" [(ngModel)]="styleConfig.elementColors[id]" *ngIf="isShowColorInput && styleConfig.elementColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementColors, key: id }">
          </ng-container>
        </td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">
          <input type="color" [(ngModel)]="styleConfig.elementBorderColors[id]" *ngIf="isShowColorInput && styleConfig.elementBorderColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementBorderColors, key: id }">
          </ng-container>
        </td>
      </tr>
    </table>
    
    <!-- <ng-container>
      <config-preview-container headerText="-" *ngIf="isShowConfigPreview()">
        <ng-container *ngTemplateOutlet="homeConfig"></ng-container>
      </config-preview-container>
    </ng-container> -->
    
    <ng-template #homeConfig>
      Homes
      <div *ngFor="let id of homeIdList; let i = index">
        <div class="flex-container">
          <input 
            type="text" disabled 
            style="width: 12em"
            value="{{'home' + i}}" 
            class="disabled-input-label">
              
          <input type="color" [(ngModel)]="styleConfig.elementColors[id]" *ngIf="isShowColorInput && styleConfig.elementColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementColors, key: id }">
          </ng-container>
          
          <ng-container *ngIf="styleConfig.isUseCustomBorderColors">
            <input type="color" [(ngModel)]="styleConfig.elementBorderColors[id]" *ngIf="isShowColorInput && styleConfig.elementBorderColors">
            <ng-container 
              *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementBorderColors, key: id }">
            </ng-container>
          </ng-container>
        </div>
      </div>
    </ng-template>
    
  </ng-template>  
  
  <ng-template #styleHomeGroup>
    
    <table class="style-table no-padding">
      <tr>
        <td></td>
        <td>Background</td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">Border</td>
      </tr>
      <tr class="label-row">
        <td>Targets</td>
      </tr>
      <tr *ngFor="let id of targetIdList">
        <td>
          <input 
            type="text" disabled 
            style="width: 10em"
            value="{{idToLabelMap.get(id)}}" 
            class="disabled-input-label">
        </td>
        <td>
          <input type="color" [(ngModel)]="styleConfig.elementColors[id]" *ngIf="isShowColorInput && styleConfig.elementColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementColors, key: id }">
          </ng-container>
        </td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">
          <input type="color" [(ngModel)]="styleConfig.elementBorderColors[id]" *ngIf="isShowColorInput && styleConfig.elementBorderColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementBorderColors, key: id }">
          </ng-container>
        </td>
      </tr>
      <tr class="label-row">
        <td>Options</td>
      </tr>
      <tr *ngFor="let id of homeIdList; let i = index">
        <td>
          <input 
            type="text" disabled 
            style="width: 10em"
            value="{{homeOptionContentLabel[i]}}" 
            class="disabled-input-label">
        </td>
        <td>
          <input type="color" [(ngModel)]="styleConfig.elementColors[id + HOME_CONTENT_SUFFIX]" *ngIf="isShowColorInput && styleConfig.elementColors">
          <ng-container 
            *ngTemplateOutlet=
            "colorPalettePicker;  context: { object: styleConfig.elementColors, key: (id+HOME_CONTENT_SUFFIX) }">
          </ng-container>
        </td>
        <td *ngIf="styleConfig.isUseCustomBorderColors">
          <input type="color" [(ngModel)]="styleConfig.elementBorderColors[id + HOME_CONTENT_SUFFIX]" *ngIf="isShowColorInput && styleConfig.elementBorderColors">
          <ng-container 
            *ngTemplateOutlet=
            "colorPalettePicker;  context: { object: styleConfig.elementBorderColors, key: (id+HOME_CONTENT_SUFFIX) }">
          </ng-container>
        </td>
      </tr>
    </table>
    
    <!-- <ng-container>
      <config-preview-container headerText="-" *ngIf="isShowConfigPreview()">
        <ng-container *ngTemplateOutlet="homeConfig"></ng-container>
      </config-preview-container>
    </ng-container> -->
    
    <ng-template #homeConfig>
      Homes
      <div *ngFor="let id of homeIdList; let i = index">
        <div class="flex-container">
          <input 
            type="text" disabled 
            style="width: 12em"
            value="{{'home' + i}}" 
            class="disabled-input-label">
              
          <input type="color" [(ngModel)]="styleConfig.elementColors[id]" *ngIf="isShowColorInput && styleConfig.elementColors">
          <ng-container 
            *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementColors, key: id }">
          </ng-container>
          
          <ng-container *ngIf="styleConfig.isUseCustomBorderColors">
            <input type="color" [(ngModel)]="styleConfig.elementBorderColors[id]" *ngIf="isShowColorInput && styleConfig.elementBorderColors">
            <ng-container 
              *ngTemplateOutlet="colorPalettePicker;  context: { object: styleConfig.elementBorderColors, key: id }">
            </ng-container>
          </ng-container>
        </div>
      </div>
    </ng-template>
    
  </ng-template>  
  
  <ng-template #colorPalettePicker let-object="object" let-key="key">
    <div *ngIf="isColorPaletteExist() && object" style="display: inline-block;">
      <select 
        [(ngModel)]="object[key]" 
        [style.width]="isSimplified ? null : '1.5em'"
        [style.backgroundColor]="isSimplified ? object[key] : null"
      >
          <option value="" disabled selected>palette</option>
          <option *ngFor="let color of colorPalette" 
            [style.backgroundColor]="color.color"
            [value]="color.color">
            {{color.name}}
          </option>
      </select>
    </div>
  </ng-template>
  
</fieldset>
