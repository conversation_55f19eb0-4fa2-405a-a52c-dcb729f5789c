// Initializes the `score-profile-groups` service on path `/score-profile-groups`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ScoreProfileGroups } from './score-profile-groups.class';
import hooks from './score-profile-groups.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/scor-lead/score-profile-groups': ScoreProfileGroups & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/scor-lead/score-profile-groups', new ScoreProfileGroups(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/scor-lead/score-profile-groups');

  service.hooks(hooks);
}
