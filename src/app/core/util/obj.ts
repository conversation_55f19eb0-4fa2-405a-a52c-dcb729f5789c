// export const objAssign

export const objDeepClone = (obj:any) => {
  return JSON.parse(JSON.stringify(obj));
}

//Helper function: find sub-object inside input with some key-value pair
export const deepFind = (obj, targetKey, targetVal, isNotTypeStrict: boolean = false) => {
  if (!obj || typeof obj !== 'object') return null;
  if (!isNotTypeStrict && obj[targetKey] === targetVal) return obj;
  if (isNotTypeStrict && obj[targetKey] == targetVal) return obj;
  return Object.values(obj).reduce((acc, val) => acc || deepFind(val, targetKey, targetVal), null);
}