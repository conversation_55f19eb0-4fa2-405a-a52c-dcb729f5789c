#!/bin/bash

# Description:  Given an S3 bucket name, a source directory, and S3 paths to upload the latest API build and where to
#               copy the build to for retaining the release history, this script uploads API builds to S3.
#
#               The goal of this is that after testing and validating an API build, it is uploaded to S3 on a live
#               release target. APIs download this content when they first launch within an AWS ASG, and reload their
#               API service using the downloaded content.
#
#               This script will upload to the API release bucket and include the current commit sha and date
#               in the S3 path the build is uploaded to. This allwos for easy identification of API builds on S3.
#
#               Copying API builds to a release history path allows the capability of rolling back API content to a
#               particular date or even commit sha as well.

# Defined named parameters that can optionally be passed from commandline to this script

bucket_nm=${bucket_nm:-""}                  # S3 bucket to upload contents to
source=${source:-""}                        # Source directory containing the API build archive to upload to S3
release_target=${release_target:-""}        # Path on S3 where live release is located
versioning_target=${versioning_target:-""}  # Path on S3 where old releases are located

while [ $# -gt 0 ]; do

   if [[ $1 == *"--"* ]]; then
        param="${1/--/}"
        declare $param="$2"
        # echo $1 $2 // Optional to see the parameter:value result
   fi

  shift
done

# Exit script if any args are not defined
# Note 2023/07/18: none of these values should be empty or null, as the aws s3 commands
#                  rely on these values to sync/copy to the correct S3 bucket folders
if [[ -z $bucket_nm || -z $source || -z $release_target || -z $versioning_target ]]; then

  echo -e "\nbucket_nm: ${bucket_nm}"
  echo -e "source: ${source}"
  echo -e "release_target: ${release_target}"
  echo -e "versioning_target: ${versioning_target}"

  echo -e "\nOne or more arguments missing when calling script! Exiting."
  exit 1

fi

if [ "$CI_API_ASG_NM" == "" ]; then
  echo "CI_API_ASG_NM missing, exiting"
  exit 1
fi

echo -e "\nExecuting standard API deployment of latest build..."

# Since this script calls other scripts in the same directory, store the path to this script when running it so we
# don't have to cd into it to have calls to other scripts work. Using this method allows the script to produce the
# same behaviour regardless of whether the script is invoked as <name> or . <name> (source method)

script_fullpath="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)/$(basename "${BASH_SOURCE[0]}")"
script_dir="$(dirname $script_fullpath)"

# Validate archive containing API build exists. API build archive is output to a directory created by build_api.sh

api_build_archive=$(ls /project/api_build)
api_archive_location="/project/api_build"    # This directory is created by build_api.sh

if [ ! -d "$api_archive_location" ]; then
  echo "$api_archive_location does not exist."
  api_build_find=$(find / -iname '*api_build*.tgz' 2>/dev/null)
  api_build_archive=$(echo $api_build_find | grep "api_build-$CI_COMMIT_SHA.tgz")
  api_archive_location=$(dirname "$api_build_archive")    # This directory is created by build_api.sh
fi

if [[ $api_build_archive == "" ]]; then
  echo -e "\nUnable to find archive containing API build. Exiting."
  exit 1
fi

echo -e "\nContents of API archive location:"
ls $api_archive_location

# Upload and replace S3 contents at live release target. Exclude files here as well.

echo -e "\nAttempting release of API build in $api_build_archive to release target at s3://$bucket_nm/$release_target"

set +e    # We want to temporarily allow failures to perform some error handling and provide helpful responses
          # back to console if the S3 sync fails

# Check that the contents of the target S3 location are as expected.
# Note 2023/07/18: there should be only 1 item in the target location, which is
#                  the previous live build.

upload_location_count=$(aws s3 ls \
  "s3://$bucket_nm/$release_target" \
  --human-readable | grep -v "0 Bytes" | wc -l
)

echo "Items in release target location: $upload_location_count"

if [[ upload_location_count -ne 1 ]]; then

  echo -e "\nThere are an unexpected number of items in the release target location; exiting."
  exit 1

fi

# Attempt upload of build to S3, exit if failed and return error to console

upload_results=$(aws s3 sync \
$api_archive_location \
"s3://$bucket_nm/$release_target" \
--delete)

upload_exit_code=$?

echo "$upload_results" > upload_results # Log results of aws s3 sync
echo "$upload_results"

if grep -q "Access Denied" "upload_results"; then

  echo -e "\nAPI release failed. Access Denied when attempting to upload API build."
  echo -e "\nThis can occur on protected API releases."
  echo -e "\nPlease ensure to enter the correct credentials manually when running the pipeline."
  echo -e "\nIf entering the correct credentials this may be a permission settings error on AWS for the deploy user."
  exit 1

fi

if [[ upload_exit_code -ne 0 ]]; then

  echo -e "\nS3 sync failed with exit code: $upload_exit_code"
  exit 1

fi

echo -e "\nRelease to live release target complete."

# Attempt to upload build to release history location on S3 to allow for versioning and rolling back if needed

timestamp=$(date +'%Y-%m-%d-H%H-M%M-S%S')
versioning_path="$versioning_target/$timestamp-$CI_COMMIT_SHA"

echo -e "\nAttempting to copy API build to release history target at s3://$versioning_path"

copy_results=$(aws s3 cp \
$api_archive_location \
"s3://$bucket_nm/$versioning_path" \
--recursive)

if grep -q "Access Denied" "copy_results"; then

  echo -e "\nAccess Denied encountered when attempting to copy API build to release history target."
  echo -e "\nPlease double check permissions to deployment user for access to s3://$bucket_nm/$versioning_path"
  echo -e "\nExiting."
  exit 1

fi

set -e # Re-enable default error handling

# Output success message to console

echo -e "\nCopy to release history target complete."

# Start API refresh for the ASG
# Note 2023/06/12: the refresh API step has been moved into its own pipeline step:
# - https://bubo.vretta.com/vea/platform/vea-infrastructure/-/issues/865
# This will allow the system team to clearly see if the build upload completed
#   and the API refresh failed, or if the build upload step failed, and to
#   easily restart the API refresh process if that is the only failure point.

# bash $script_dir/refresh_apis.sh --api_asg_nm "$CI_API_ASG_NM"
