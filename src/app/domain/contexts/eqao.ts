import { API_ADDRESS_LOCAL, API_ADDRESS_LOCALB } from "../../api/api-addr/l";
import { IContextData } from "./common";
import { context } from '../../../context.json';

const footer = [
  [
    {"route":"/contact", "caption": "title_contactus"},
    // {"route":"/news", "caption": "title_news"},
  ],
  [
    {"route":"/privacy", "caption": "title_privacy"},
  ],
  [
    {"route":"/legal", "caption": "title_legal"}
  ],
]

const apiAddress = (hostname:string) => {
// <<<<<<< HEAD
//   switch(hostname){
//     case 'localhost':
//       // return 'https://eqao2-stress-06032022-api.vretta.com';
//       return API_ADDRESS_LOCALB;
//       // return 'https://eqao2-api.vretta.com';
//       // return 'https://qc7-api.vretta.com';
//         // return API_ADDRESS_LOCAL;
//       //return 'https://apiqc.mathproficiencytest.ca'
//       //return 'https://api-eassessment.vretta.com';
//     case 'eqao-xqc.vretta.com':
//       return 'https://api-eqao-xqc.vretta.com';
//     case 'eqao-main-qc.vretta.com':
//       return 'https://apiqc.mathproficiencytest.ca';
//     case 'qc7.vretta.com':
//     case 'd3k4cvve5iw7mr.cloudfront.net':
//       return 'https://qc7-api.vretta.com';
//     case 'eqao.vretta.com':
//     case 'd3h4m0g2lmrmq6.cloudfront.net':
//     case 'private.eqao.vretta.com':
//     case 'eqao-lit.vretta.com':
//     case 'd12o445rx6gi3b.cloudfront.net':
//     case 'dwod99k06nyqh.cloudfront.net':
//       return 'https://eqao2-api.vretta.com';
//       // return 'https://eqao-api.vretta.com';
//     case 'drkpaddt3nk24.cloudfront.net':
//       return 'https://eqao-api.vretta.com';
//     case 'd24a8vriqswfok.cloudfront.net':
//       return 'https://apiqc.mathproficiencytest.ca';
//     case 'd1w433s63hot3w.cloudfront.net':
//     case 'd2r9nuffi6xllr.cloudfront.net':
//       return 'https://eqao-qc-api.vretta.com'
//     case 'qc1.mathproficiencytest.ca':
//     case 'qc4.mathproficiencytest.ca':
//       return 'https://apiqc.mathproficiencytest.ca';
//     default:
//       return 'https://api.mathproficiencytest.ca';
// =======
  if (hostname) {
    return context[hostname];
  } else {
    return context['*'];
  }
}
  
export const EQAO:IContextData = {
    id: 'EQAO',
    homepageRoute: '/en/login-router-st',
    brandName: 'EQAO E-Assessment',
    defaultTimezone: 'America/Toronto',
    defaultTimezoneAbbr: 'EST',
    logo: 'eqao_context_logo',
    footer: null,
    apiAddress,
    siteFlags: {
      BREADCRUMB_MENU: false,
      BOOKING_POLICY_STATS: true,
      IS_EQAO: true,
      EQAO_LOGIN: true,
      'IS_SA_STUDENT_ID': true,
      'INST_MNG_TT': true,
      'TEST_RUNNER_WIDTH_CONSTRAINT': true,
      'PREVENT_AUTO_EDIT': true,
      'IS_SUPPORT_SD_LOOKUP': true,
      'IS_TIMER_ENABLED': false,
      'IS_TTS_ALWAYS': true,
      'IS_OFFLINE_INDIC_INVIG': true,
      'IS_TC_SUMM': true,
      'ENFORCE_ALLOWED_SECTIONS': true,
      'ALLOW_TEACHER_UNSUBMIT': true,
      'IS_TC_DATA': false,
      'IS_TC_SCORING_SCATTER': true,
      'IS_TC_DAILY_REPORT': true,
      'IS_TC_SCHOOL_ADMIN_BULK': true,
      'IS_TC_ASMT_PRICE': true,
      'IS_GUEST_STU_AVAIL': true,
      'IS_SCORER_REPORT': true,
      'IS_SCORER_STATUS_DETAIL': true,
      'IS_SCORE_MSG_CENTRE': true,
      'IS_SA_SC_CREATE': true,
      'IS_SA_IMPORT_TEACHER': true,
      'IS_SA_IMPORT_STUDENT': true,
      'IS_STU_VERIFY_IDENTITY': true,
      'IS_SCORE_RESP_BOOKEND': true,
      'IS_SCORE_SCORING_NUM': true,
      'IS_SCORE_LEAD_DIRECT_ADD_ACCT': true,
      'SD_VIEW_ENABLE__tech_readiness': true,
      'SD_VIEW_ENABLE__security': true,
      'SD_VIEW_ENABLE__monitoring': true,
      'SD_VIEW_ENABLE__sessions': true,
      'SD_VIEW_ENABLE__reports': true,
      'IS_TW_RULES': true, // managing this at the twtar level, but okay to leave this as an option
      'IS_TW_RULES_DUR': true, // managing this at the twtar level, but okay to leave this as an option
      'IS_TW_RULES_DELIV': true, // should be moved to profiles based on test type
      'IS_TW_RULES_MULTI_ATTEMPT': true, // currently controlled at the twtar level
      'IS_TW_RULES_INVIG_UNSUBMIT': false, // this should be configured based on a list of options, this is deprecated now
      'IS_TW_RULES_INVIG_ONBEHALF': false, // would like to activate this more globally for certain accommodation scenarios
      'IS_TW_CONFIG_STU_RESULTS':true, 
      'IS_TW_CONFIG_IRT':true, 
      'IS_TWTAR_USER_METAS': true,
      'IS_DE_EXPORT_NEW':true, 
      'IS_DE_CODEBOOK':true, 
      'IS_DE_PSYCH':true, 
      'RAFI_INSERT_HISTORICAL':true, 
    },
    siteText: {
        copyright: 'title_copyright',
        supportEmail: 'support_email',
        supportPhone: 'eqao_support_phone_number',
        login_admins: 'teacher_administrator',
        my_inst: 'My School',
        my_inst_admin: 'You are the **administrator** of this school.',
        my_inst_manage_acct: 'Manage Teachers+',
        student_ident: 'oen',
        student_ident_2: 'lbl_sasn',
        lbl_sd: 'School Board',
        ttaker_ident: 'Test Taker ID',
        asmt_logo_en: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6276/authoring/EQAO_Logo_BW_sml_trnctd_clear_space-cropped/1631817152201/EQAO_Logo_BW_sml_trnctd_clear_space-cropped.svg',
        asmt_logo_fr: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6276/authoring/OQRE_Logo_BW_sml_trnctd_clear_space-cropped/1631817437150/OQRE_Logo_BW_sml_trnctd_clear_space-cropped.svg',
        header_logo_en: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/g18/1602729003686/g18.png',
        header_logo_fr: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/OQRE_Logo_White_sml%201/1602729029336/OQRE_Logo_White_sml%201.png',
        marking_chat:'Scoring Chat',
        marking:'scoring',
        mark:'score',
        marker:'scorer',
        markers:'scorers',
        marks:'scores',
        marked:'scored',
        reliability:'validity',
        exemplar:'validity response',
        exemplars:'validity responses',
        TnR:'T&V',
    },
    sdRoleTypes: [
      'schl_dist_it_admin',
      'school_district_curr',
      'schl_disct_curr_ele',
      'schl_disct_curr_sec'
    ],
    siteData: {
      'SCORING_SUPPORT': {
        text: 'lbl_technical_support_scor',
        email: 'support_email',
        phone:'eqao_support_phone_number',
      }
    },
    hosts: [
        // 'localhost:4200',
        // 'localhost:4401',
        'd2r9nuffi6xllr.cloudfront.net',
        'd12o445rx6gi3b.cloudfront.net',
        'd1c1qqn86e6v14.cloudfront.net',
        'd1w433s63hot3w.cloudfront.net',
        'd24a8vriqswfok.cloudfront.net',
        'dwod99k06nyqh.cloudfront.net',
        'drkpaddt3nk24.cloudfront.net',
        'eqao.vretta.com',
        'eqao-lit.vretta.com',
        'eqao-lit-qc1.vretta.com',
        'eqao-xqc.vretta.com',
        'qc4.mathproficiencytest.ca',
    ]
}
