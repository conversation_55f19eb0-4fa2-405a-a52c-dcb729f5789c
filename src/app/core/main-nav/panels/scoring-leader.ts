import { IPanelLink } from "./type";
import { AccountType } from "../../../constants/account-types";
import {SCORING_LEADER_VIEWS, RANGE_FINDER_VIEWS, LEADER_ITEMS, LEADER_TRAINING} from '../../../ui-scoring-leader/view-sl-dashboard/data/views'


/** Get a list of non-archived windows asssigned to this scoring leader, create nested panel links for each  */
export const getScoringLeaderPanels = async (auth, routes, isRafi: boolean = false) => {
  const query = {
    isRafi: isRafi ? 1 : undefined
  }
  const scoringLeaderPanels: {title: string, shortTitle: string, isExpanded: boolean, subpanels: IPanelLink[]}[] = []
  const windowRecords = await auth.apiFind(routes.SCOR_LEAD_WINDOWS, {query})
  const nonArchivedWindowRecords = windowRecords.filter(w => !w.is_archived)
  const views = isRafi ? RANGE_FINDER_VIEWS : SCORING_LEADER_VIEWS
  const accountType = isRafi ? AccountType.SCOR_RAFI : AccountType.SCOR_LEAD
  for (const windowRecord of nonArchivedWindowRecords){
    const {window_id, window_name} = windowRecord;
    const newPanelGroup = {
      title: `#${window_id} - ${window_name}`,
      shortTitle: `#${window_id}`,
      isExpanded: false,
      subpanels: views.map(view => {
        return {
            caption: view.caption,
            routerLink: `/en/${accountType}/${view.id}/${window_id}`,
            iconUrl: view.imgUrl
        }
      })
    }
    scoringLeaderPanels.push(newPanelGroup)
  }
  return scoringLeaderPanels;
}








export const scoringRangeFinderPanels:IPanelLink[] = []
//  [LEADER_ITEMS].map(view => {
//     return {
//         caption: view.caption,
//         routerLink: `/en/${AccountType.SCOR_RAFI}/${view.id}/1`, // to complete
//         iconUrl: view.imgUrl
//     }
// })