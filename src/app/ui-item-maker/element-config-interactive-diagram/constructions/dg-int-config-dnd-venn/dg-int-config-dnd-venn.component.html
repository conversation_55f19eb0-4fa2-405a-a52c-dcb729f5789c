<fieldset [disabled]="isReadOnly()">
  <!-- <label class="label">Left Label</label>
  <textarea
    [formControl]="leftLabelForm"
    style="min-width: 10em"
    class="textarea is-small target-option-textarea"
    cdkTextareaAutosize
    [cdkTextareaAutosize]="true"
    [cdkAutosizeMinRows]="2"
  ></textarea>
  
  <label class="label">Right Label</label>
  <textarea
    [formControl]="rightLabelForm"
    style="min-width: 10em"
    class="textarea is-small target-option-textarea"
    cdkTextareaAutosize
    [cdkTextareaAutosize]="true"
    [cdkAutosizeMinRows]="2"
  ></textarea> -->
  
  <div class="layout-config-container">
    <span>Left Label:</span>
    <textarea
      textInputTransform
      #leftLabel
      [formControl]="leftLabelForm"
      style="min-width: 10em; max-width: 20em;"
      class="textarea is-small target-option-textarea"
      cdkTextareaAutosize
      (input)="rerender()"
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(leftLabelForm, 'leftLabel')"
    ></textarea>
    
    <span>Right Label:</span>
    <textarea
      textInputTransform
      #rightLabel
      [formControl]="rightLabelForm"
      style="min-width: 10em; max-width: 20em;"
      class="textarea is-small target-option-textarea"
      cdkTextareaAutosize
      (input)="rerender()"
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(rightLabelForm, 'rightLabel')"
    ></textarea>
    
    <span *ngIf="element.config.capacity.isLimitMaxCapacity">Target Capacity:</span>
    <div *ngIf="element.config.capacity.isLimitMaxCapacity">
      <input type="number" [(ngModel)]="element.config.capacity.maxCapacity" (change)="rerender()"/>
    </div >
    
  </div>
  
  <label class="checkbox">
    <input type="checkbox" [(ngModel)]="element.config.capacity.isLimitMaxCapacity" (change)="rerender()"/>
    Limit capacity per target
  </label>
  
</fieldset>

<hr />

<div>
  <fieldset [disabled]="isReadOnly()">
    
    <button (click)="setUseImage(true)" class="button is-small has-icon" [class.is-info]="isUseImage()">
      <span class="icon"><i class="fa fa-image" aria-hidden="true"></i></span>
      <span>Image</span>
    </button>
    <button (click)="setUseImage(false)" class="button is-small has-icon" [class.is-info]="!isUseImage()">
      <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
      <span>Text</span>
    </button>
    
    <div>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
        Show Advanced Options
      </label>
    </div>
    
    <br/>
    
    <div>
      <div *ngFor="let target of dndTargetOptionsForm; let i = index; trackBy: trackByFn" class="target-container">
        
        <div *ngIf="!isUnusedTarget(target); else unusedTargetLabelHeader">
          <label style="display: inline-block;" class="label">{{target.label}}</label >
          <span *ngIf="target.label == 'Left'"> ({{element.label.left}})</span>
          <span *ngIf="target.label == 'Right'"> ({{element.label.right}})</span>
          <div>
            <span>options:</span>
          </div>
        </div>
        <ng-template #unusedTargetLabelHeader>
          <!-- <span>extra options:</span> -->
          <label style="display: inline-block;" class="label">Outside</label >
        </ng-template>
        
        <div class="sub-property-div">
          <div *ngFor="let optionForm of target.content; let j = index; trackBy: trackByFn" class="option-container">
            <div style="width: 100%;">
              <div [class.option-image-container]="isUseImage()">
                <div style="display: flex;"> 
                  <textarea
                    textInputTransform
                    #targets
                    [formControl]="optionForm"
                    style="min-width: 10em; max-width: 20em;"
                    class="textarea is-small target-option-textarea"
                    [class.non-unique-option]="isNonUniqueOption(optionForm.value)"
                    cdkTextareaAutosize
                    (input)="rerender()"
                    [cdkTextareaAutosize]="true"
                    [cdkAutosizeMinRows]="2"
                    (focus)="setTextFocus(optionForm, 'target_' + i + '_' + j)"
                  ></textarea>
                  <div *ngIf="isAllowDeleteOption(i,j)" (click)="elementEditDeleteOption(i,j)" class="target-option-button"
                    [class.no-pointer-events]="isReadOnly()"
                  >
                    <i class="fas fa-trash" aria-hidden="true"></i>
                  </div>
                </div>
                
                <div *ngIf="isUseImage()">
                  <div *ngIf="!element.isShowAdvancedOptions && getOptionImageElement(i,j).url">
                    <div class="simple-image-container">
                      <img [src]="getOptionImageElement(i,j).url" style="max-width: 10em; max-height: 10em;">
                    </div >
                    <div>
                      scale:
                      <input type="number" [(ngModel)]="getOptionImageElement(i,j).scale" class="small-input" (change)="rerender()">
                    </div>
                    <button (click)="elementEditRemoveOptionImage(i,j)">Clear Image</button>
                  </div>
                  <div *ngIf="element.isShowAdvancedOptions || !getOptionImageElement(i,j).url">
                    <capture-image [element]="getOptionImageElement(i,j)" (change)="rerender()"></capture-image>
                    <asset-library-link [element]="getOptionImageElement(i,j)"></asset-library-link>     
                  </div>
                </div>
              </div>
            </div>
            
          </div>
          
          <button 
            *ngIf="isAllowAddOption(i)"
            (click)="elementEditAddOption(i)" 
            class="button is-small has-icon"
          >
            <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
            <span>Add Option</span>
          </button>
        </div>
        
      </div>
    </div>
  </fieldset>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div class="sub-property-div control">
      <br>Left Target Colour:
      <input type="color" [(ngModel)]="element.config.bgColors.left">
      <br>Intersection Target Colour:
      <input type="color" [(ngModel)]="element.config.bgColors.intersection">
      <br>Right Target Colour:
      <input type="color" [(ngModel)]="element.config.bgColors.right">
      <br>
      <br>Home Background Colour:
      <input type="color" [(ngModel)]="element.config.bgColors.home">
      <br>Draggable Background Colour:
      <input type="color" [(ngModel)]="element.config.bgColors.draggable">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isNoDraggableBackground"/>
        No Draggable Background
      </label>
      <br>
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      <br>
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
    </div>
  </fieldset>
</div>
