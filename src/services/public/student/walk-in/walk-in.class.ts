import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { STUDENT_ROLE_TYPES } from '../../educator/walk-in-students/walk-in-students.class';

interface Data {}

interface ServiceOptions {}

export class WalkIn implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query) throw new Errors.BadRequest("Missing Params");
    const {sch_class_group_id} = params.query;
    if(!sch_class_group_id) throw new Errors.BadRequest("Missing sch_class_group_id");
    //We use the find of this class to pull the teacher and assessment info related to the walk-in student's waiting room
    const teacherAndAssessmentInfo = await dbRawRead(this.app, {sch_class_group_id}, `
      SELECT us.first_name
        , us.last_name
        , scts.slug
        , ts.date_time_start
        , twtdt.caption_short
      FROM users us
      JOIN user_roles ur ON ur.uid = us.id
        AND ur.is_revoked = 0 
        AND ur.role_type  = 'schl_teacher'
      JOIN school_classes sc ON sc.group_id = ur.group_id
        AND sc.is_active = 1
        AND sc.group_id = :sch_class_group_id
      JOIN school_class_test_sessions scts 
        ON scts.school_class_id = sc.id
      JOIN test_window_td_types twtdt 
        on twtdt.type_slug = scts.slug
        and twtdt.test_window_id is null
        and twtdt.is_revoked = 0
      JOIN test_sessions ts 
        ON ts.id = scts.test_session_id
        AND ts.is_cancelled = 0
        AND is_closed = 0
      GROUP BY us.id;
    `)
    if(teacherAndAssessmentInfo.length) return teacherAndAssessmentInfo[0];
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params || !params.query) throw new Errors.BadRequest("Missing Params");
    const {isABED, isStudentWalkIn, sch_class_group_id} = params.query;
    if(!sch_class_group_id) throw new Errors.BadRequest("Missing sch_class_group_id");
    if(isStudentWalkIn && +isABED && id){
      const userRolesCheck = await dbRawRead(this.app, {id, sch_class_group_id}, `
        SELECT id
        FROM
          mpt_dev.user_roles ur
        WHERE 
          ur.uid = :id
          and ur.role_type  = '${STUDENT_ROLE_TYPES.walk_in_student_role}'
          and ur.is_removed = 0
          and ur.group_id = :sch_class_group_id
      ;`);
      if(userRolesCheck.length) return {isStudentWalkIn: true};
      else return {isStudentWalkIn: false};
    }
    else if(id){
      //updated to check the current student's walk-in user_role status: 
      // - if the walk-in role is revoked then that means the teacher has rejected them
      // - if the walk-in role is removed and the student has a test attempt, then that means the teacher has accepted the student
      const userRolesStatus = await dbRawRead(this.app, {id, sch_class_group_id}, `
      SELECT 
        (CASE
          WHEN (ta.id IS not null and ur.is_removed = 1)
            THEN 1
          ELSE 0
        END
        ) AS AcceptedByTeacher
        ,ur.is_revoked AS RejectedByTeacher
        ,ta.*
      FROM
        mpt_dev.user_roles ur
      JOIN 
        mpt_dev.school_classes sc ON 
          sc.group_id = ur.group_id
      JOIN 
        mpt_dev.school_class_test_sessions scts ON 
          scts.school_class_id = sc.id
      JOIN
      mpt_dev.test_sessions ts on 
        ts.id = scts.test_session_id
        AND ts.is_closed = 0
      LEFT JOIN
        mpt_dev.test_attempts ta ON 
          ta.uid = ur.uid 
          AND ta.test_session_id = scts.test_session_id 
          AND ta.is_invalid = 0
        WHERE 
          ur.role_type = '${STUDENT_ROLE_TYPES.walk_in_student_role}'
          AND ur.uid = :id
          AND ur.group_id = :sch_class_group_id
        ORDER BY ur.created_on DESC
        limit 1;
      ;`); //we only care to look at the most recent walkin user_role created for this student in this class's session
      let res: any = {};
      for(let ur of userRolesStatus){
        res.AcceptedByTeacher = +ur.AcceptedByTeacher;
        res.RejectedByTeacher = +ur.RejectedByTeacher;
      }
      return res;
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
