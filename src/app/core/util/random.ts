export const randId = () => randInt(1, 10000000);
export const randInt = (from, to) => Math.floor( from + (to-from)*Math.random() );
export const randArrEntry = <T>(arr:T[]) : T =>  arr[Math.floor(arr.length * Math.random())];
export const randArrEntryProp = <T>(arr:any[], prop:string)  : T => randArrEntry(arr)[prop]
export const randDate = ():string => (new Date().toString());
export const coinFlip = (weight=0.5) => (Math.random() > 1-weight);
export const randHash =(size=8) => {
    let str = '';
    const len = Math.max(1, Math.ceil(size /4));
    for (let i=0; i<len; i++){
        str += (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
    }
    return str;
}
