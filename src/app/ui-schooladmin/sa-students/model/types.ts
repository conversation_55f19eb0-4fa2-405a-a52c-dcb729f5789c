import {FormControl} from "@angular/forms";
import { IStudentAccount } from '../../data/types';
import {EditSaveFormlet} from "../../../ui-partial/edit-save/edit-save.component";
import { IExportColumn } from '../../../ui-testctrl/tc-table-common/tc-table-common.component';
import { IStudentDefaults } from "../../sa-modal-student-personal/sa-modal-student-personal.component";
import { ClassFilterId } from "../../my-school.service";


export enum AccountModal {
  ACCOUNT_DETAILS = 'account-details',
  NEW_ACCOUNT     = 'new-account',
  ASSIGN_SESSION   = 'assign-session',
  UNASSIGN_SESSION   = 'unassign-session',
  ASSIGN_CLASSROOM   = 'assign-classromm',
  UNASSIGN_CLASSROOM   = 'unassign-classroom',
  EXPORT = 'export_students',
  IMPORT = 'import_students',
  IMPORT_PROGRESS = 'import_progress',
  IMPORT_VALIDATION = 'import_validation'
}

export interface IModalNewAccount {
  confirmationCaption?: string;
  studentDefaults?: IStudentDefaults,
  accounts?: Partial<IStudentAccount>[],
  courseFilter?: ClassFilterId,
  payload?: any,
  /*form: {
    email: FormControl,
    password: FormControl,
    first_name: FormControl,
    last_name: FormControl,
    student_pen: FormControl,
  };
  testSessionsSelected: TestSessionSelection;*/
}

export interface IModalExport {
  name: string;
  data: any[];
  hideTable?: boolean;
  columns: IExportColumn[];
  cellSelect?: (event) => void 
}

export interface IModalImport {
  hideTable?: boolean;
  useConfirmAlert?: boolean;
  columns: IExportColumn[];

  cellSelect?: (event) => void;
}

export interface IModalAccountDetails {
  account: IStudentAccount;
  isWalkIn: boolean;
  password: EditSaveFormlet;
  first_name: EditSaveFormlet;
  last_name: EditSaveFormlet;
  student_pen: EditSaveFormlet;
}

export interface IModalAssignSession {
  accounts: IStudentAccount[];
  testSessionsSelected: TestSessionSelection;
}

interface IModalUnassignSession {
  accounts: IStudentAccount[];
  availableTestSessions: number[];
  testSessionsSelected: TestSessionSelection;
}

export interface IModalAssignClassroom {
  accounts: IStudentAccount[];
  classroomSelected: ClassroomSelection;
  isCheckForExisting?: boolean
}

export enum UserAttr {
  email = 'email',
  password = 'password',
  first_name = 'first_name',
  last_name = 'last_name',
  student_pen = 'student_pen'
}

export interface TestSessionSelection {
  [key: number]: boolean;
}

export interface ClassroomSelection {
  [key: number]: boolean;
}