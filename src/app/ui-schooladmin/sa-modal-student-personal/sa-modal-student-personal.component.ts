import { Component, Input, OnDestroy, OnInit, Output, ViewEncapsulation, EventEmitter } from "@angular/core";
import { LangService } from "../../core/lang.service";
import { AuthService } from "../../api/auth.service";
import { RoutesService } from "../../api/routes.service";
import {
  BC_GRAD_ANGLOPHONE,
  BC_GRAD_FRANCOPHONE,
  BC_GRAD_GRADE,
  BC_GRAD_REGISTERED_DATES,
  BC_GRAD_STUDENT_ACCOUNT_OPTIONS,
  EQAO_DB_NULL,
  FSA_ACCESSIBILITY,
  FSA_ACCOMMODATIONS,
  FSA_ANGLOPHONE_COURSE_CODE,
  FSA_FRANCOPHONE_COURSE_CODE,
  FSA_GRADE_LEVEL,
  FSA_REQUEST_STATUS,
  GRAD_ACCESSIBILITY,
  GRAD_PRIMARY_IEDC,
  GRAD_REQUEST_STATUS,
  G<PERSON><PERSON>_SESSION_CODES,
  IG9Courses,
  STUDENT_ASSIST_TECH_1,
  STUDENT_ASSIST_TECH_2,
  STUDENT_ASSIST_TECH_3,
  STUDENT_BRAILLE,
  //STUDENT_BRAILLE_FR,
  STUDENT_BREAKS,
  STUDENT_COLOURED_PRINT,
  STUDENT_ELL,
  STUDENT_G9_COURSES,
  STUDENT_GENDERS,
  STUDENT_GRADES,
  STUDENT_IEP,
  STUDENT_INDIG_TYPE,
  STUDENT_IPRC,
  STUDENT_OSSLT_DATE_OF_FTE,
  STUDENT_OSSLT_ELIG_STATUS,
  STUDENT_OSSLT_LEV_STUD_LANG,
  STUDENT_OSSLT_NON_PART,
  STUDENT_PRIVATE_SCHOOL_ENROL_TYPES,
  STUDENT_SCHOOL_ENROL_TYPES,
  STUDENT_SINGLE_CHECK,
  STUDENT_PJ_AUDIO_SINGLE_CHECK,
  STUDENT_STATUS_IN_CANADA_ONTARIO,
  STUDENT_TIME_IN_CANADA,
  STUDENT_YES_NO_RADIO,
  STUDENT_YES_NO,
  STUDENT_SEMESTET_INDICATOR,
  STUDENT_YES_NO_UNKNOWN_RADIO,
  STUDENT_YES_NO_UNKNOWN_2_RADIO,
  STUDENT_NonParticipationStatus,
  STUDENT_ALFPANA,
  Math_Class_When,
  Learning_Format_Primary,
  Learning_Format_Junior,
  Learning_Format_OSSLT,
  Learning_Format_G9,
  STUDENT_MATH_AUDIO,
  STUDENT_SCHOOL_ENROL_TYPES_PJ,
  STUDENT_MATH_AUDIO_ALTERNATE_FORM,
  STUDENT_G9_ALTERNATIVE_VERSION,
  EQAO_ASSESSMENTS,
  NBED_ASSESSMENTS,
  classCodeABEDKeys,
  SIMPLE_PHONE_PATTERN,
  SIMPLE_EMAIL_PATTERN,
  
} from "../data/constants";
import { applyDefaultRules, initFormEntries, ISaFormEntry, ISaFormEntryConfig, SaFormType, setEntryValue, getSubValProp} from "../sa-widget-form-entry/sa-widget-form-entry.component";
import { IMenuTabConfig } from "../../ui-partial/menu-bar/menu-bar.component";
import { IStudentAccount } from "../data/types";
import { G9DemoDataService, overlapVariables} from "../g9-demo-data.service";
import { DataGuardService } from "../../core/data-guard.service";
import { WhitelabelService } from "../../domain/whitelabel.service";
import { ActivatedRoute } from "@angular/router";
import { IMappedList, isInArr } from "../data/util";
// import { IModalNewAccount } from '../sa-students/model/types';
import { IModalNewAccount } from "../sa-students/model/types";
import { ClassFilterId, MySchoolService } from "../my-school.service";
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { CheckContentShareConnectivityFeedback } from "amazon-chime-sdk-js";
import { DATA_MAPPING_EQAO_G9_STUDENT } from "../data/mappings/eqao-g9";
import { formatDate } from "@angular/common";
import { FormControl } from "@angular/forms";
import { hasUncaughtExceptionCaptureCallback } from "process";
import { LoginGuardService } from "../../api/login-guard.service";
import { SaStudentsComponent } from '../sa-students/sa-students.component'
import { applyMask } from 'src/app/core/util/mask'
import { Subscription } from 'rxjs';
import moment from "moment";
import {isAdminAltReqAllowed} from './../../ui-alt-version-ctrl/feature-flag-config'

enum View {
  PERSONAL_INFO = "PERSONAL_INFO",
  ACCOMMODATION = "ACCOMMODATION",
  PROVISIONS = "PROVISIONS",
  NON_PARTICIPANT_STATUS = "NON_PARTICIPANT_STATUS",
  ABED_ACCOMM = "ABED_ACCOMM",
  SECONDARY_INACTIVE_ASN = "SECONDARY_INACTIVE_ASN",
  ALT_VERSION_REQUEST = 'ALT_VERSION_REQUEST',
}

enum Accom_Sub_View {
  Reading = "Reading",
  Writing = "Writing",
  Mathematics = "Mathematics"
}

enum BCFSAView {
  STUDENT_INFO = "FSA_STUDENT_INFO",
  SPECIAL_FORMATS = "FSA_SPECIAL_FORMATS",
  RESULTS = "FSA_RESULTS"
}

enum BCGRADView {
  STUDENT_INFO = "GRAD_STUDENT_INFO",
  SPECIAL_FORMATS = "GRAD_SPECIAL_FORMATS",
  RESULTS = "GRAD_RESULTS"
}

enum Key_Categories {
  PERSONAL_INFO = "personal_info",
  PROVISION_INFO = "provision_info",
  ACCOMMODATION_INFO = "accommodation_info",
  NON_PARTICIPATION_INFO = "participation_info",
}

enum StudentPersonalModal {
  SUBMIT_ALT_REQUEST = 'SUBMIT_ALT_REQUEST',
}

export interface IStudentDefaults {
  eqao_is_g9?: string;
  eqao_is_g10?: string;
}

export interface IConflictData {
  studentRecord: Partial<IStudentAccount>;
  choosedConflicts: any[];
}

export interface AltSlugs {
  sdc_alt_version_require: string;
  sdc_alt_version_online_require_sub: string;
  sdc_alt_version_audio_delivery_format_sub: string;
}

enum AltVersionRequestStatus{
  Pending = 'Pending',
  Canceled = 'Canceled',
  Approved = 'Approved',
  Rejected = 'Rejected',
  Shipment_Send = 'Shipment Send',
  Operational_Link_Send = 'Operational Link Send'
}

@Component({
  selector: "sa-modal-student-personal",
  templateUrl: "./sa-modal-student-personal.component.html",
  styleUrls: ["./sa-modal-student-personal.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class SaStudentPersonalComponent implements OnInit, OnDestroy {
  @Input() studentRecords: IStudentAccount[];
  @Input() savePayload: IModalNewAccount;
  @Input() saveProp: string;
  @Input() disabled: boolean = false;
  @Input() isCreatingNew: boolean = false;
  @Input() classFilter: string;
  @Input() apiErrMsgToSlug: (args: any) => string;
  @Input() totalRecords: number;
  @Input() selectedStudentIndex: number;
  @Input() isEditDisable = false;
  @Input() isPrivateSchool = false;
  @Input() allowPASIUpdates = true;
  @Input() isSecreteUser:string;
  @Input() accommodationList

  @Output() confirmStudentInformation = new EventEmitter<void>();
  @Output() preRecord = new EventEmitter<void>();
  @Output() nextRecord = new EventEmitter<void>();
  @Output() applyBtnClicked = new EventEmitter<IConflictData>();
  @Output() selectedRecord = new EventEmitter<Partial<IStudentAccount>>();


  /*
  overlapVariables = [
    'first_name',         'last_name',                'eqao_student_school_enrol_type', 'eqao_student_gov_id',     'SASN',
    'date_of_birth',      'eqao_gender',              'eqao_date_entered_school',       'eqao_date_entered_board', 'eqao_indigenous_type',
    'eqao_first_language','eqao_enrolled_ontario',    'eqao_out_province_residence',    'eqao_status_in_canada',   'eqao_time_in_canada',
    'eqao_refugee',       'eqao_born_outside_canada', 'IS_G9',                          'IS_G10',            
   ]
   */
  constructor(
    public lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,
    private route: ActivatedRoute,
    private g9DemoService: G9DemoDataService,
    private dataGuard: DataGuardService,
    public whitelabelService: WhitelabelService,
    public mySchool: MySchoolService,
    private loginGuard: LoginGuardService,
    private saStudentsComponent:SaStudentsComponent,
    private pageModalService: PageModalService,
  ) {}

  selectedView: View = View.PERSONAL_INFO; // = SchoolAdminView.CLASSROOMS;
  views: IMenuTabConfig<View>[] = [
    { id: View.PERSONAL_INFO, caption: this.lang.tra("sdc_1_personal_info") }, // 'Personal Information'
    { id: View.ACCOMMODATION, caption: this.lang.tra("sdc_1_accom_spec_ver") }, // 'Accommodations and Special Versions'
    { id: View.PROVISIONS, caption: this.lang.tra("sdc_1_rec_spec_prov") }, // 'Record Special Provisions'
    { id: View.NON_PARTICIPANT_STATUS, caption: this.lang.tra("sdc_1_non_participant_status") } // 'Record Special Provisions'
  ];

  selected_Accom_Sub_View: Accom_Sub_View = Accom_Sub_View.Reading; 

  Accom_Sub_Views: IMenuTabConfig<Accom_Sub_View>[] = [
    { id: Accom_Sub_View.Reading, caption: this.lang.tra("sdc_1_acc_view_reading") }, 
    { id: Accom_Sub_View.Writing, caption: this.lang.tra("sdc_1_acc_view_writing") }, 
    { id: Accom_Sub_View.Mathematics, caption: this.lang.tra("sdc_1_acc_view_mathematics") }
  ];
  //schoolType = this.route.snapshot.data['schoolType']
  //selectedView: View|BCFSAView|BCGRADView ;
  //views:IMenuTabConfig<View|BCFSAView|BCGRADView>[];
  View = View;
  Accom_Sub_View = Accom_Sub_View;
  //BCFSAView = BCFSAView;
  //BCGRADView = BCGRADView;

  formEntries: { [key: string]: ISaFormEntry };
  formEntriesList: ISaFormEntry[];
  personalInfoColumns: ISaFormEntry[][];
  provisionInfoColumns: ISaFormEntry[];
  participationInfoColumns: ISaFormEntry[];
  isSelectingRecord: boolean;
  studentRecord: Partial<IStudentAccount>;
  errMsgArray: any[];
  SDC_conflict: any[];
  isImportUpdate: boolean = false;
  choosedConflicts: any[] = [];
  currClassCode: number = null;

  altVersionFormEntries: { [key: string]: ISaFormEntry };
  altVersionFormEntriesList: ISaFormEntry[];
  requestAltVersionAcknowledge: boolean = false;
  subscription = new Subscription();

  pageModal: PageModalController;
  StudentPersonalModal = StudentPersonalModal;


  ///////////////////////////////////////////////////////////////////////////////////////////////

  ngOnInit(): void {
    //if (this.isBCSite()) {
    //  if (this.schoolType === 'BC_FSA') {
    //    this.selectedView = BCFSAView.STUDENT_INFO;
    //    this.views = [
    //      {id: BCFSAView.STUDENT_INFO, caption:this.lang.tra('Student Info')},
    //      {id: BCFSAView.SPECIAL_FORMATS, caption:this.lang.tra('Special Formats')},
    //      {id: BCFSAView.RESULTS, caption:this.lang.tra('Results')},
    //    ];
    //  } else if (this.schoolType === 'BC_GRAD') {
    //    this.selectedView = BCGRADView.STUDENT_INFO;
    //    this.views = [
    //      {id: BCGRADView.STUDENT_INFO, caption:this.lang.tra('Student Info')},
    //      {id: BCGRADView.SPECIAL_FORMATS, caption:this.lang.tra('Special Formats')},
    //      {id: BCGRADView.RESULTS, caption:this.lang.tra('Results')},
    //    ];
    //  }

    //} else {
    //  this.selectedView = View.PERSONAL_INFO;
    //  this.views = [
    //    {id: View.PERSONAL_INFO, caption:this.lang.tra('sdc_1_personal_info')}, // 'Personal Information'
    //    {id: View.ACCOMMODATION, caption:this.lang.tra('sdc_1_accom_spec_ver')}, // 'Accommodations and Special Versions'
    //    {id: View.PROVISIONS,    caption:this.lang.tra('sdc_1_rec_spec_prov')}, // 'Record Special Provisions'
    //  ];
    //}
    if (this.whitelabelService.isABED()){
      this.views = 
      [
        { 
          id: View.PERSONAL_INFO, caption: this.lang.tra("abed_personal_information"),
        }, // 'Personal Information'
        { 
          id: View.ABED_ACCOMM, caption: this.lang.tra("accomodation_abed")
          // , disabled: true
        }, // 'Personal Information'
      ];
      if (isAdminAltReqAllowed()){
        this.views.push({
          id: View.ALT_VERSION_REQUEST, caption: this.lang.tra("sdc_1_alt_version_req")
        })
      }
      if(this.allowPASIUpdates){
        // this.views.push(
        // {
        //   id: View.SECONDARY_INACTIVE_ASN, caption: this.lang.tra("extra_asn_header")
        // });
      }
    }
    if (NBED_ASSESSMENTS.includes(this.classFilter)){
      this.views = [
        { id: View.PERSONAL_INFO, caption: this.lang.tra("sdc_1_personal_info") } // 'Personal Information'
      ];
    }
    if (this.classFilter === ClassFilterId.Primary ||this.classFilter === ClassFilterId.Junior) {
      this.views = this.views.filter(view => (view.id !== View.PROVISIONS && view.id !== View.NON_PARTICIPANT_STATUS));
    }
    if (this.classFilter === ClassFilterId.G9) {
      this.views = this.views.filter(view => view.id !== View.NON_PARTICIPANT_STATUS);
    }
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.choosedConflicts =[];
    this.initSelectedStudent();

  }

  ngOnChanges() {
    if (this.classFilter === ClassFilterId.Primary ||this.classFilter === ClassFilterId.Junior) {
      this.views = this.views.filter(view => (view.id !== View.PROVISIONS && view.id !== View.NON_PARTICIPANT_STATUS));
    }
    if (this.classFilter === ClassFilterId.G9) {
      this.views = this.views.filter(view => view.id !== View.NON_PARTICIPANT_STATUS);
    }
    this.choosedConflicts =[];
    this.initSelectedStudent();
    this.filterClassOption();
  }

  ngOnDestroy() {
    this.dataGuard.deactivate();
    this.subscription.unsubscribe();
  }

  initSelectedStudent() {
    if (this.studentRecords) {
      if (this.studentRecords.length === 1) {
        return this.selectRecord(this.studentRecords[0]);
      }
      if (this.studentRecords.length > 1) {
        return (this.isSelectingRecord = true);
      }
    }
    return this.selectRecord({});
  }
  selectRecord(studentRecord: Partial<IStudentAccount>) {
    this.isSelectingRecord = false;
    this.studentRecord = studentRecord;
    this.initFormEntries(this.studentRecord);
    this.identifyMode();

    if(isAdminAltReqAllowed()){
      this.filteredBrailleFormats = this.filterBrailleFormats()
      this.initAltVersionFormEntriesList(this.studentRecord)
    }

    if(this.classFilter !== ClassFilterId.G9){
      this.applyG3G6G10UniqueValue();
    }  
    this.initPayloadSubscription(this.studentRecord);
    if(isAdminAltReqAllowed()){
      this.initHasValidAltRreq();
    }
    this.definePersonalColumns();
    this.applyBusinessRules();
    this.applyDefaultUnknown(studentRecord);
    this.applyDefaultValue(studentRecord);
    this.initErrMsgAndConflict();

    if (this.whitelabelService.isABED())
    {
      this.initClassCode(studentRecord);
    }
    
  }

  initClassCode(studentRecord: Partial<IStudentAccount>)
  {
    const classCodeLabel = this.findStudentClassCode(studentRecord);
    this.currClassCode = studentRecord[classCodeLabel];
  }

  selectSingleRcord(studentRecord: Partial<IStudentAccount>){
    this.selectedRecord.emit(studentRecord);
  }
  
  initErrMsgAndConflict(){
    if(this.classFilter === ClassFilterId.Primary && this.studentRecord._g3_errMsg){
      this.errMsgArray = JSON.parse(this.studentRecord._g3_errMsg);
    }else if(this.classFilter === ClassFilterId.Junior && this.studentRecord._g6_errMsg){
      this.errMsgArray = JSON.parse(this.studentRecord._g6_errMsg);
    }else if (this.classFilter === ClassFilterId.G9 && this.studentRecord.errMsg) {
      this.errMsgArray = JSON.parse(this.studentRecord.errMsg);
    }else if(this.classFilter === ClassFilterId.OSSLT && this.studentRecord._g10_errMsg){
      this.errMsgArray = JSON.parse(this.studentRecord._g10_errMsg);
    } else {
      this.errMsgArray = [];
    }
    if (this.classFilter === ClassFilterId.Primary && this.studentRecord.SDC_conflict_g3) {
      if(this.studentRecord.SDC_conflict_g3.IsImportUpdate == 1) {
        this.isImportUpdate = true;
      }
      this.SDC_conflict = JSON.parse(this.studentRecord.SDC_conflict_g3.compare_result);
    }else if (this.classFilter === ClassFilterId.Junior && this.studentRecord.SDC_conflict_g6) {
      if(this.studentRecord.SDC_conflict_g6.IsImportUpdate == 1) {
        this.isImportUpdate = true;
      }
      this.SDC_conflict = JSON.parse(this.studentRecord.SDC_conflict_g6.compare_result);
    }else if (this.classFilter === ClassFilterId.G9 && this.studentRecord.SDC_conflict_g9) {
      // SDC Load: IsImportUpdate = 0; School Import: if IsImportUpdate = 1
      if(this.studentRecord.SDC_conflict_g9.IsImportUpdate == 1) {
        this.isImportUpdate = true;
      }
      this.SDC_conflict = JSON.parse(this.studentRecord.SDC_conflict_g9.compare_result);
    }else if (this.classFilter === ClassFilterId.OSSLT && this.studentRecord.SDC_conflict_g10) {
      if(this.studentRecord.SDC_conflict_g10.IsImportUpdate == 1) {
        this.isImportUpdate = true;
      }
      this.SDC_conflict = JSON.parse(this.studentRecord.SDC_conflict_g10.compare_result);
    }else {
      this.SDC_conflict = [];
    }
    this.SDC_conflict = this.SDC_conflict.filter(conflict => conflict.fieldname  != 'FrenchImmersion')
  }

  applyDefaultUnknown(studentRecord: Partial<IStudentAccount>) {
    if (!EQAO_ASSESSMENTS.includes(this.classFilter)) return;

    const unknownProps = ["eqao_student_school_enrol_type", "eqao_indigenous_type", "eqao_first_language", "eqao_enrolled_ontario", 
                          "eqao_out_province_residence", "eqao_status_in_canada", "eqao_refugee", "eqao_born_outside_canada", 
                          "eqao_time_in_canada","eqao_full_day_junior","eqao_full_day_senior"];

    if (studentRecord) {
      unknownProps.forEach((key: string) => {
        if (studentRecord[key] === undefined || studentRecord[key] === null) {
          //const formEntry = this.formEntriesList.find(formEntry => formEntry.valProp == key).formControlRef.setValue("#");
          const formEntry = this.formEntriesList.find(formEntry => formEntry.valProp == key)
          if(formEntry && !formEntry.formControlRef.value){
            formEntry.formControlRef.setValue("#");
          }
        }
      });
    }
  }

  applyDefaultValue(studentRecord: Partial<IStudentAccount>) {
    if (!EQAO_ASSESSMENTS.includes(this.classFilter)) return;

    //g3 g9 grade
    let defaultGrade
    if(this.isPrimary){defaultGrade = '3'}
    if(this.isJunior){defaultGrade = '6'}
    if (!studentRecord.eqao_grade) {
      this.formEntriesList.find(formEntry => formEntry.valProp == "eqao_grade").formControlRef.setValue(defaultGrade);
    }  
  }

  isPrimary: boolean = false;
  isJunior: boolean = false;
  isG9: boolean = false;
  isOSSLT: boolean = false;
  isSciences8: boolean = false;
  isTCLE: boolean = false;
  isTCN: boolean = false;
  
  identifyMode() {
    //const g10Val = this.formEntries.eqao_is_g10.formControlRef.value;
    //this.isOSSLT = (g10Val == '1')
    this.isPrimary = this.classFilter === ClassFilterId.Primary;
    this.isJunior = this.classFilter === ClassFilterId.Junior;
    this.isG9 = this.classFilter === ClassFilterId.G9;
    this.isOSSLT = this.classFilter === ClassFilterId.OSSLT;
    this.isSciences8 = this.classFilter === ClassFilterId.SCIENCES8;
    this.isTCLE = this.classFilter === ClassFilterId.TCLE;
    this.isTCN = this.classFilter === ClassFilterId.TCN;
    // console.log('identifyMode', this.isOSSLT, g10Val, this.savePayload)
  }

  applyBusinessRules() {
    this.formEntriesList.forEach(entry => this.applyRules(entry));
  }

  findKeyForVal(checkmarkMap: { [key: string]: boolean }, v: boolean) {
    let matchedKey = "";
    Object.keys(checkmarkMap).forEach(key => {
      if (v === checkmarkMap[key]) {
        matchedKey = key;
      }
    });
    return matchedKey;
  }

  initFormEntries(studentRecord: Partial<IStudentAccount>, tableConfigs?: ISaFormEntryConfig[]) {
    let configs: ISaFormEntryConfig[] = [];
    if (tableConfigs) {
      configs = tableConfigs;
    } else {
      // TODO: Once we move the accomodation info into student_form_config table, we can remove the configs below
      configs = [
        { valProp: "first_name", type: SaFormType.TEXT, label: "sdc_1_first_name", required: true }, // 'First Name'
        { valProp: "last_name", type: SaFormType.TEXT, label: "sdc_1_last_name", required: true }, // 'Last Name'
        { valProp: "eqao_student_school_enrol_type", type: SaFormType.SELECT, label: "sdc_1_student_type", options: this.studentTypeOption() }, // 'Student Type'
        { valProp: "nbed_student_identification_number", type: SaFormType.TEXT, label: "sdc_1_student_identification_number", required: true}, // 'Student Type'
        { valProp: "nbed_user_id", type: SaFormType.TEXT, label: "sdc_1_username", required: true}, // 'Student Type'
        { valProp: "mbed_user_id", type: SaFormType.TEXT, label: "sdc_1_username", required: true}, // 'Student Type'
  
        { valProp: "eqao_student_gov_id", type: SaFormType.TEXT, label: this.isNBED() || this.isMBED()? "sdc_1_username" : "sdc_1_oen", required: true }, // 'OEN'
        { valProp: "SASN", type: SaFormType.TEXT, label: "sdc_1_sasn", isVertical: true, isCustomWidth: true, required: this.isSasnLogin }, // 'SASN'
        { valProp: "date_of_birth", type: SaFormType.DATE, label: "sdc_1_date_birth", required: true, nullDbPlaceholder: EQAO_DB_NULL }, // 'Date of Birth'
        { valProp: "eqao_gender", type: SaFormType.RADIO, label: "sdc_1_gender_1", required: true, options: STUDENT_GENDERS.list }, // 'Gender'
        { valProp: "eqao_g9_course", type: SaFormType.SELECT, label: "sdc_1_course", required: true, options: STUDENT_G9_COURSES.list }, // 'Course'
        { valProp: "eqao_grade", type: SaFormType.SELECT, label: "sdc_1_grade", required: true, options: STUDENT_GRADES.list }, // 'Student Grade G3/G6'
        //{ valProp: "eqao_english_learner", type: SaFormType.RADIOC, label: "sdc_1_english_learner", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Individual Education Plan (IEP) Status'
        { valProp: "eqao_g9_french", type: SaFormType.CHECK, label: "sdc_1_is_french", options: STUDENT_YES_NO.list }, // 'French?'
        { valProp: "eqao_pj_french", type: SaFormType.CHECK, label: "sa_lbl_french_immi", options: STUDENT_YES_NO.list }, // 'French?'
        { valProp: "eqao_is_g9", type: SaFormType.RADIO, label: "sdc_1_is_g9", options: STUDENT_YES_NO.list, isHidden: this.isCreatingNew },
        { valProp: "eqao_is_g10", type: SaFormType.RADIO, label: "sdc_1_is_g10", options: STUDENT_YES_NO.list, isHidden: this.isCreatingNew },
        { valProp: "eqao_g9_class_code", type: SaFormType.SELECT, label: this.isNBED() ? "sdc_1_group" : "sdc_1_class", options: this.filterClassOption(), required: true }, // 'Class'
        { valProp: "eqao_out_province_residence", type: SaFormType.RADIO, label: "sdc_1_perm_res_outside", isVertical: true, options: STUDENT_YES_NO_UNKNOWN_2_RADIO.list }, // 'Student\'s permanent residence is outside Ontario'
        //{ valProp: "eqao_math_class_when", type: SaFormType.SELECT, label: "sdc_1_math_class_when", required: true, options: Math_Class_When.list }, // combine to termformat
        { valProp: "eqao_learning_format", type: SaFormType.SELECT, label: "sdc_1_learning_format", required: true, options: this.learningFormatOption()}, // 'Student\'s permanent residence is outside Ontario'
        { valProp: "eqao_full_day_junior", type: SaFormType.RADIO, label: "sdc_1_full_day_junior", isVertical: true, options: STUDENT_YES_NO_UNKNOWN_RADIO.list }, // 'This student has eqao_full_day_junior'
        { valProp: "eqao_full_day_senior", type: SaFormType.RADIO, label: "sdc_1_full_day_senior", isVertical: true, options: STUDENT_YES_NO_UNKNOWN_RADIO.list }, // 'This student has eqao_full_day_senior'
        { valProp: "EligibilityStatus", type: SaFormType.SELECT, label: "lbl_sdc_eligibility_status", isVertical: true, options: STUDENT_OSSLT_ELIG_STATUS.list, required: true },
        { valProp: "LevelofStudyLanguage", type: SaFormType.SELECT, label: "lbl_sdc_level_of_study_language", isVertical: true, options: STUDENT_OSSLT_LEV_STUD_LANG.list },
        { valProp: "DateOfFTE", type: SaFormType.SELECT, label: "lbl_sdc_date_of_fte", isVertical: true, options: STUDENT_OSSLT_DATE_OF_FTE.list },
        { valProp: "Grouping", type: SaFormType.TEXT, label: "lbl_sdc_grouping", isHidden: true },
        { valProp: "Homeroom", type: SaFormType.TEXT, label: "lbl_sdc_homeroom"},
        //{ valProp: "NonParticipationStatus", type: SaFormType.SELECT, label: "lbl_sdc_non_participation_status", isVertical: true, options: STUDENT_OSSLT_NON_PART.list },
        { valProp: "Graduating", type: SaFormType.RADIO, label: "lbl_sdc_graduating", isVertical: true, options: STUDENT_YES_NO.list },
        { valProp: "eqao_acc_assistive_tech", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true},
        { valProp: "eqao_status_in_canada", type: SaFormType.RADIO, label: "sdc_1_legal_stat", isVertical: true, options: STUDENT_STATUS_IN_CANADA_ONTARIO.list }, // 'Legal status of the student in Canada'
        { valProp: "TermFormat", type: SaFormType.SELECT, label: this.classFilter === ClassFilterId.OSSLT? "sdc_1_semester_indicator":"sdc_1_math_class_when", required: true, isVertical: this.classFilter === ClassFilterId.OSSLT?true:false, options: this.classFilter === ClassFilterId.OSSLT? STUDENT_SEMESTET_INDICATOR.list: Math_Class_When.list}, //
        { valProp: "eqao_enrolled_ontario", type: SaFormType.RADIO, label: "sdc_1_pub_funded", isVertical: true, options: STUDENT_YES_NO_UNKNOWN_2_RADIO.list }, // 'This student has been enrolled in publicly-funded education in Ontario for the past two years'
        { valProp: "eqao_first_language", type: SaFormType.RADIO, label: "sdc_1_eng_home", isVertical: true, options: STUDENT_YES_NO_UNKNOWN_RADIO.list }, // 'This student learned English as his or her first language at home'
        { valProp: "eqao_indigenous_type", type: SaFormType.RADIO, label: "sdc_1_indig_self_id", isVertical: true, options: STUDENT_INDIG_TYPE.list }, // 'This indigenous student has voluntarily self-identified as'
        { valProp: "_school_background", type: SaFormType.LABEL, label: "sdc_1_school_bckg", sublabel: "sc_1_school_bckg_date_entry" }, // 'School Background' sub: '(Record the most recent date of entry)' //This and one below are the same?
        { valProp: "eqao_date_entered_school", type: SaFormType.DATE, label: "sdc_1_date_entered_sch", isVertical: true, nullDbPlaceholder: EQAO_DB_NULL }, // 'Date student entered current school:'
        { valProp: "eqao_date_entered_board", type: SaFormType.DATE, label: "sdc_1_date_entered_board", isVertical: true, nullDbPlaceholder: EQAO_DB_NULL }, // 'Date student entered current board:'
        { valProp: "_born_outside_canada", type: SaFormType.LABEL, label: "sdc_1_born_outside_label" }, // 'Born Outside Canada'
        { valProp: "eqao_born_outside_canada", type: SaFormType.RADIO, label: "sdc_1_born_outside_yes", isVertical: true, options: STUDENT_YES_NO_UNKNOWN_RADIO.list }, // 'a) This student was born outside Canada'
        { valProp: "eqao_time_in_canada", type: SaFormType.RADIO, label: "sdc_1_born_outside_how_long", isVertical: true, options: STUDENT_TIME_IN_CANADA.list }, // 'b) If "Yes," how long has this student been in Canada?'
        { valProp: "eqao_refugee", type: SaFormType.RADIO, label: "sdc_1_refugee", isVertical: true, options: STUDENT_YES_NO_UNKNOWN_2_RADIO.list }, // 'This student\'s family came to Canada as refugees'
        { valProp: "eqao_iep", type: SaFormType.RADIOC, label: "sdc_1_iep_status", isVertical: true, options: STUDENT_IEP.list }, // 'Individual Education Plan (IEP) Status'
        { valProp: "_identification_placement", type: SaFormType.RADIO, label: "sdc_1_iprc_status", sublabel: "sc_1_spec_ed_needs", isVertical: true, options: STUDENT_IPRC.list }, // 'Identification, Placement and Review Committee (IPRC) Status' sub: 'This sudent has special education needs and has been identified as having the following excepionality (check one only)'
        { valProp: "eqao_acc_assistive_tech", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true},
        { valProp: "eqao_acc_assistive_tech_pj_reading", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true},
        { valProp: "eqao_acc_assistive_tech_pj_writing", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true},
        { valProp: "eqao_acc_assistive_tech_pj_mathematics", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true},
        { valProp: "eqao_acc_assistive_tech_1", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true}, // 'Use of a assistive technology select from list or describe in a textbox if required technology is not listed.'
        { valProp: "eqao_acc_assistive_tech_1_pj_reading", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true}, // 'Use of a assistive technology select from list or describe in a textbox if required technology is not listed.'
        { valProp: "eqao_acc_assistive_tech_1_pj_writing", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true}, // 'Use of a assistive technology select from list or describe in a textbox if required technology is not listed.'
        { valProp: "eqao_acc_assistive_tech_1_pj_mathematics", type: SaFormType.CHECK, label: "sdc_1_assist_tech", sublabel: "sdc_1_assist_tech_for_the", isVertical: true, options: STUDENT_ASSIST_TECH_1.list, singleSelect: true}, // 'Use of a assistive technology select from list or describe in a textbox if required technology is not listed.'
        { valProp: "eqao_acc_assistive_tech_2", type: SaFormType.CHECK, label: "", sublabel: "", isVertical: true, options: STUDENT_ASSIST_TECH_2.list, indentation: true, singleSelect: true },
        { valProp: "eqao_acc_assistive_tech_2_pj_reading", type: SaFormType.CHECK, label: "", sublabel: "", isVertical: true, options: STUDENT_ASSIST_TECH_2.list, indentation: true, singleSelect: true },
        { valProp: "eqao_acc_assistive_tech_2_pj_writing", type: SaFormType.CHECK, label: "", sublabel: "", isVertical: true, options: STUDENT_ASSIST_TECH_2.list, indentation: true, singleSelect: true },
        { valProp: "eqao_acc_assistive_tech_2_pj_mathematics", type: SaFormType.CHECK, label: "", sublabel: "", isVertical: true, options: STUDENT_ASSIST_TECH_2.list, indentation: true, singleSelect: true },
        { valProp: "eqao_acc_assistive_tech_custom", type: SaFormType.TEXT, label: "", isVertical: true, indentation: true },
        { valProp: "eqao_acc_assistive_tech_custom_pj_reading", type: SaFormType.TEXT, label: "", isVertical: true, indentation: true },
        { valProp: "eqao_acc_assistive_tech_custom_pj_writing", type: SaFormType.TEXT, label: "", isVertical: true, indentation: true },
        { valProp: "eqao_acc_assistive_tech_custom_pj_mathematics", type: SaFormType.TEXT, label: "", isVertical: true, indentation: true },
        // { valProp: "eqao_acc_assistive_tech_3", type: SaFormType.CHECK, label: "", sublabel: "", isVertical: true, options: STUDENT_ASSIST_TECH_3.list, indentation: true },
        { valProp: "eqao_acc_braille", type: SaFormType.RADIO, label: "sdc_1_ueb", isVertical: true, options: STUDENT_BRAILLE.list }, // 'Unified English Braille (UEB)'
        { valProp: "eqao_acc_braille_pj_reading", type: SaFormType.RADIO, label: "sdc_1_ueb", isVertical: true, options: STUDENT_BRAILLE.list }, // 'Unified English Braille (UEB)'
        { valProp: "eqao_acc_braille_pj_writing", type: SaFormType.RADIO, label: "sdc_1_ueb", isVertical: true, options: STUDENT_BRAILLE.list }, // 'Unified English Braille (UEB)'
        { valProp: "eqao_acc_braille_pj_mathematics", type: SaFormType.RADIO, label: "sdc_1_ueb", isVertical: true, options: STUDENT_BRAILLE.list }, // 'Unified English Braille (UEB)'
        //{ valProp: "eqao_acc_braille_fr", type: SaFormType.RADIO, label: "sdc_1_ueb", isVertical: true, options: STUDENT_BRAILLE_FR.list }, // 'Unified French Braille (UEB)'
        { valProp: "eqao_acc_large_print", type: SaFormType.RADIOC, label: "sdc_1_large_print", isVertical: false, options: STUDENT_SINGLE_CHECK.list }, // 'Large-print version'
        { valProp: "eqao_acc_coloured", type: SaFormType.RADIOC, label: "sdc_1_coloured_paper", isVertical: false, options: STUDENT_COLOURED_PRINT.list }, // 'Coloured-paper version'
        { valProp: "eqao_acc_lp_coloured", type: SaFormType.RADIOC, label: "sdc_1_lp_cp", isVertical: false, options: STUDENT_COLOURED_PRINT.list }, // 'Large-print, coloured-paper version'
        { valProp: "eqao_acc_audio_version", type: SaFormType.RADIOC, label: "sdc_1_mp3", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Audio version (MP3)'
        { valProp: "AccReadGraphic", type: SaFormType.RADIOC, label: "AccReadGraphic", isHidden: true, isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // osslt
        { valProp: "AccReading", type: SaFormType.RADIOC, label: "AccReading", isHidden: true, isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // osslt
        { valProp: "AccVideotapeResponse", type: SaFormType.RADIOC, label: "lbl_sdc_acc_videotape_response", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // osslt
        { valProp: "AccOther", type: SaFormType.RADIOC, label: "lbl_sdc_acc_other", isVertical: true, options: STUDENT_SINGLE_CHECK.list, greyOut: true}, // osslt
        // {valProp:'SpecPermIEP',                  type:SaFormType.RADIOC,   label: 'lbl_sdc_spec_perm_iep', isVertical:true, options:STUDENT_SINGLE_CHECK.list },  // osslt (duplicate of line 169)
        { valProp: "eqao_pres_format", type: SaFormType.RADIOC, header: "sdc_1_diff_pres_form", label: "sdc_1_sign_lang_oral_int", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Different Presentation Format' sub: 'Sign language or an oral interpreter'
        { valProp: "eqao_pres_format_pj_reading", type: SaFormType.RADIOC, header: "sdc_1_diff_pres_form", label: "sdc_1_sign_lang_oral_int", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Different Presentation Format' sub: 'Sign language or an oral interpreter'
        { valProp: "eqao_pres_format_pj_writing", type: SaFormType.RADIOC, header: "sdc_1_diff_pres_form", label: "sdc_1_sign_lang_oral_int", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Different Presentation Format' sub: 'Sign language or an oral interpreter'
        { valProp: "eqao_pres_format_pj_mathematics", type: SaFormType.RADIOC, header: "sdc_1_diff_pres_form", label: "sdc_1_sign_lang_oral_int", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Different Presentation Format' sub: 'Sign language or an oral interpreter'
        { valProp: "_changes_in_setting", type: SaFormType.RADIOC, header: "sdc_1_change_sett", label: "sdc_1_assist_dev", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Changes in Setting' sub: 'Assistive devices or adaptive equipment (special lighting, special pens or pencil grips or devices for supporting the student\'s arm for printing, writing or keyboarding)'
        { valProp: "_extended_time", type: SaFormType.RADIOC, header: "sdc_1_time", label: "sdc_1_ext_per_sup_breaks", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Time' sub: 'Extended periodic supervised breaks'
        // { valProp: "AccAudioVersion", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: this.classFilter === ClassFilterId.G9 ?"sdc_gr9_audio_title":"sdc_1_aud_audio_version", isVertical: true, options: STUDENT_SINGLE_CHECK.list },
        { valProp: "AccAudioVersion", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: this.classFilter === ClassFilterId.G9 ?"sdc_gr9_audio_title":"", isVertical: true, options: STUDENT_SINGLE_CHECK.list },
        { valProp: "osslt_alternative_version_test", type: SaFormType.CHECK, label: "lbl_approved_by_eqao",  isVertical: true, options: STUDENT_SINGLE_CHECK.list, greyOut: !this.isSecreteUser},
        { valProp: "AccAudioVersion_pj_reading", type: SaFormType.CHECK,  header: "sdc_1_diff_resp_form", label: "sdc_gr36_audio_title", isVertical: true, options: STUDENT_PJ_AUDIO_SINGLE_CHECK.list},
        { valProp: "AccAudioVersion_pj_writing", type: SaFormType.CHECK,  header: "sdc_1_diff_resp_form", label: "sdc_gr36_audio_title", isVertical: true, options: STUDENT_PJ_AUDIO_SINGLE_CHECK.list},
        { valProp: "AccAudioVersion_pj_mathematics", type: SaFormType.RADIO, label: "sdc_gr36_audio_title", isVertical: true, options: STUDENT_MATH_AUDIO.list, space:true },
        { valProp: "eqao_alternate_form_pj_reading", type: SaFormType.CHECK,  header: "sdc_1_diff_resp_form", label: "sdc_gr36_alternate_version_title",  isVertical: true, options: STUDENT_SINGLE_CHECK.list, greyOut: !this.isSecreteUser},
        { valProp: "eqao_alternate_form_pj_writing", type: SaFormType.CHECK,  header: "sdc_1_diff_resp_form", label: "sdc_gr36_alternate_version_title",  isVertical: true, options: STUDENT_SINGLE_CHECK.list, greyOut: !this.isSecreteUser},
        { valProp: "eqao_alternate_form_pj_mathematics", type: SaFormType.RADIO,   isVertical: true, options: STUDENT_MATH_AUDIO_ALTERNATE_FORM.list, greyOut: !this.isSecreteUser},
        { valProp: "eqao_exemption_pj_reading", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: "sdc_gr36_exemption", isVertical: true, options: STUDENT_SINGLE_CHECK.list, singleSelect: true,},
        { valProp: "eqao_exemption_pj_writing", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: "sdc_gr36_exemption", isVertical: true, options: STUDENT_SINGLE_CHECK.list, singleSelect: true,},
        { valProp: "eqao_exemption_pj_mathematics", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: "sdc_gr36_exemption", isVertical: true, options: STUDENT_SINGLE_CHECK.list, singleSelect: true },
        { valProp: "eqao_spec_no_exception_pj_readingwriting", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: "sdc_gr36_exemption", isVertical: true, options: STUDENT_SINGLE_CHECK.list, singleSelect: true },
        { valProp: "eqao_spec_no_exception_pj_mathematics", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: "sdc_gr36_exemption", isVertical: true, options: STUDENT_SINGLE_CHECK.list, singleSelect: true },
        { valProp: "_audio_recording_of_resp", type: SaFormType.CHECK, header: "sdc_1_diff_resp_form", label: "sdc_1_aud_rec_st_responses", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Different Response Format' sub: 'Audio recording of student responses'
        { valProp: "eqao_acc_scribing", type: SaFormType.RADIOC, label: "sdc_1_verb_scrb", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Verbatim scribing of responses'
        { valProp: "eqao_acc_scribing_pj_reading", type: SaFormType.RADIOC, label: "sdc_1_verb_scrb", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Verbatim scribing of responses'
        { valProp: "eqao_acc_scribing_pj_writing", type: SaFormType.RADIOC, label: "sdc_1_verb_scrb", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Verbatim scribing of responses'
        { valProp: "eqao_acc_scribing_pj_mathematics", type: SaFormType.RADIOC, label: "sdc_1_verb_scrb", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Verbatim scribing of responses'
        { valProp: "_assist_tech_for_recording", type: SaFormType.RADIOC, label: "sdc_1_use_of_comp_assist", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'Use of a computer or word processor or assistive technology (e.g., speech synthesizer, Brailler, speech-to-text software or augmentative or alternative communication systems) for recording responses only.'
        { valProp: "SpecPermIEP", type: SaFormType.RADIOC, label: "sdc_1_spec_perm_iep", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'This student **has no IEP** but has received the principal\'s permission for one or more appropriate accommodations because of a temporary injury or condition. The accommodation are marked above.'
        { valProp: "_no_iep_temp_inj", type: SaFormType.RADIOC, label: "sdc_1_iep_perm", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'This student **has no IEP** but has received the principal\'s permission for one or more appropriate accommodations because of a temporary injury or condition. The accommodation are marked above.'
        { valProp: "_no_iep_recent_arriv", type: SaFormType.RADIOC, label: "sdc_1_rec_arr", isVertical: true, options: STUDENT_SINGLE_CHECK.list }, // 'This student has recently arrived from another school and **has no IEP** but has received the principal\'s permission for one or more appropriate accommodations. The accommodations are marked above.'
        { valProp: "_ell", type: this.lang.c() === 'en'?SaFormType.RADIOC:SaFormType.RADIO, label:"sdc_1_eng_learn_stat", isVertical: true, options: this.lang.c() === 'en'?STUDENT_ELL.list: STUDENT_ALFPANA.list, space: this.lang.c() === 'en'?false:true},// 'English Language Learner Status'
        { valProp: "_periodic_breaks", type: SaFormType.RADIOC, label: "sdc_1_spec_prov", sublabel: this.lang.tra("sdc_1_spec_prov_will_receive"), isVertical: true, options: STUDENT_BREAKS.list }, // 'Special Provisions' sub: 'This English language learner will receive the following special provisions'
        { valProp: "NonParticipationStatus", type: SaFormType.RADIOC, label: "sdc_1_nonparticipation_status", sublabel: this.lang.tra("sdc_1_nonparticipation_status_if_this"), isVertical: true, options: STUDENT_NonParticipationStatus.list, singleSelect: true },
        { valProp: 'nbed_student_grade', type: SaFormType.TEXT, label: "sdc_1_grade", required: true}, // NBED Student Grade
        { valProp: "errMsg", type: SaFormType.SELECT, label: "" }
      ];
    }
    if (this.classFilter == "OSSLT") {
      let sasn = configs.find(entry => entry.valProp === "SASN");
      let no_IEP = configs.find(entry => entry.valProp === "_no_iep_temp_inj");
      let periodic_breaks = configs.find(entry => entry.valProp === "_periodic_breaks");

      sasn.label = this.lang.tra("sdc_1_sasn_osslt");
      no_IEP.label = "lbl_sdc_spec_perm_iep";
      periodic_breaks.sublabel = this.lang.tra("sdc_1_spec_prov_will_receive_osslt");
    }

    this.formEntriesList = initFormEntries(studentRecord, configs, this.g9DemoService.checkMarkMapping, this.lang.c());
    this.extractFormEntryRef();
    if (this.classFilter === ClassFilterId.Primary || this.classFilter === ClassFilterId.Junior) {
      this.formEntries.eqao_grade.formControlRef.disable({ emitEvent: false });
    }

    if (this.whitelabelService.isABED()) {
      let classLabel = 'classCode';
      if (this.formEntries != null && this.formEntries[classLabel] != null) {
        const matchingOptionIdx = this.formEntries[classLabel].options.findIndex((opt) => {
          return studentRecord[classLabel] === opt.label;
        });

        if (matchingOptionIdx != -1) {
          this.formEntries[classLabel].formControlRef.setValue(this.formEntries[classLabel].options[matchingOptionIdx].id);
        }   
      }  
    }

    if(this.isEditDisable && this.formEntries.eqao_g9_class_code){ // todo:DB_DATA_MODEL
      this.formEntries.eqao_g9_class_code.formControlRef.disable({ emitEvent: false });
    }
    //return configs;
  }

  filteredBrailleFormats = []

  filterBrailleFormats = () => {
    // const isOSSLTView = this.isOSSLT
    // const isG9View = this.isG9
    // const isPrimaryView = this.isPrimary
    // const isJuniorView = this.isJunior
    // const schoolLang = this.g9DemoService.schoolData["lang"]

    const allBrailleFormats = this.g9DemoService.altVersionFormats["braille_format"].value_list.list
    const filteredBrailleFormats = allBrailleFormats
    .filter(format => {
      //TODO: Refactor filtering options
      // if (isG9View) return format.is_g9 == 1
      // else if (isPrimaryView) return format.is_primary == 1
      // else if (isJuniorView) return format.is_junior == 1
      // else if (isOSSLTView) {
      //   if (schoolLang === "en") return format.is_osslt_en == 1
      //   else if (schoolLang === "fr") return format.is_osslt_fr == 1
      // }
      return true;
    })
    return filteredBrailleFormats
  }


  initAltVersionFormEntriesList(studentRecord: Partial<IStudentAccount>) {
    const configs: ISaFormEntryConfig[] = [
      { valProp: 'tent_session_date', label: 'sdc_alt_version_tentative_date',  type: SaFormType.DATE, isVertical: true, required: true},
      { valProp: "alt_version_require", type: SaFormType.RADIO, label: "sdc_alt_version_require_osslt", isVertical: true, options: STUDENT_YES_NO.list },
      { valProp: "alt_version_braille_require", type: SaFormType.RADIO, label: "sdc_alt_version_braille_require", sublabel: "sdc_alt_version_braille_require_sub_abed", isVertical: true, options: STUDENT_YES_NO.list },
      { valProp: "braille_format", type: SaFormType.RADIO, label: "sdc_alt_version_braille_format", sublabel: "sdc_alt_version_braille_format_sub", isVertical: true, options: this.filteredBrailleFormats },
      { valProp: "schl_contact", type: SaFormType.TEXT, label: "sdc_alt_version_schl_contact", required: true },
      { valProp: "schl_email", type: SaFormType.TEXT, label: "sdc_alt_version_schl_email", required: true, pattern: SIMPLE_EMAIL_PATTERN },
      { valProp: "schl_phone", type: SaFormType.TEXT, label: "sdc_alt_version_schl_phone", required: true, pattern: SIMPLE_PHONE_PATTERN },
      { valProp: "schl_admin_phone", type: SaFormType.TEXT, label: "sdc_alt_version_schl_admin_phone", required: true, pattern: SIMPLE_PHONE_PATTERN},
      { valProp: "schl_street_number_name", type: SaFormType.TEXT, label: "sdc_alt_version_schl_street_number_name", required: true },
      { valProp: "schl_city", type: SaFormType.TEXT, label: "sdc_alt_version_schl_city", required: true },
      { valProp: "schl_province", type: SaFormType.TEXT, label: "sdc_alt_version_schl_province", required: true },
      { valProp: "schl_country", type: SaFormType.TEXT, label: "sdc_alt_version_schl_country", required: true },
      { valProp: "schl_postal_code", type: SaFormType.TEXT, label: "sdc_alt_version_schl_postal_code", required: true },
      { valProp: "alt_version_asl_require", type: SaFormType.RADIO, label: "sdc_alt_version_asl_require_osslt", sublabel: "sdc_alt_version_asl_require_osslt_sub", isVertical: true, options: STUDENT_YES_NO.list },
      { valProp: "asl_format", type: SaFormType.RADIO, isVertical: true, options: this.g9DemoService.altVersionFormats["asl_format"].value_list.list},
      { valProp: "alt_version_pdf_require", type: SaFormType.RADIO, label: "sdc_alt_version_pdf_require", sublabel: "sdc_alt_version_pdf_require_sub", isVertical: true, options: STUDENT_YES_NO.list },
      { valProp: "pdf_format", type: SaFormType.RADIO, label: "sdc_alt_version_pdf_format", isVertical: true, options: this.g9DemoService.altVersionFormats["pdf_format"].value_list.list }, 
      { valProp: "audio_delivery_format", type: SaFormType.RADIO, label: "sdc_alt_version_audio_delivery_format", sublabel: "sdc_alt_version_audio_delivery_format_sub_abed", isVertical: true, options: this.g9DemoService.altVersionFormats["audio_delivery_format"].value_list.list }, 
      { valProp: "alt_version_online_require", type: SaFormType.RADIO, label: "sdc_alt_version_online_require", sublabel:  "sdc_alt_version_online_require_sub", isVertical: true, options: STUDENT_YES_NO.list },
      { valProp: "audio_format", type: SaFormType.RADIO, label: "sdc_alt_version_audio_format", sublabel: "sdc_alt_version_audio_format_sub", isVertical: true, options: this.g9DemoService.altVersionFormats["audio_format"].value_list.list },
      { valProp: "reason", type: SaFormType.TEXTAREA, label: "sdc_alt_version_reason", isVertical: true, required: true },
    ];


    const currentTestWindow = this.mySchool.getCurrentTestWindow()

    const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)
    let studentAltVersionRequest = studentRecord[altVersionRequestName]||{}

    //Bad patch solution to inconsistency
    if(Object.keys(studentAltVersionRequest).length == 0){
      const altVersionRequestNameWithPrefix = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow, this.classFilter)
      studentAltVersionRequest = studentRecord[altVersionRequestNameWithPrefix]||{}
    }

    //If existing tent date, reformat it to 8 characters for display
    if (studentAltVersionRequest.tent_session_date && studentAltVersionRequest.tent_session_date.length > 8) {
      studentAltVersionRequest.tent_session_date = moment.utc(studentAltVersionRequest.tent_session_date).format("YYYYMMDD")
    }

    this.altVersionFormEntriesList = initFormEntries(studentAltVersionRequest, configs, undefined, this.lang.c()); //fetch data into form
    this.extractAltVersionFormEntryRef();
  }  

  private getBCFSAConfigs = (): ISaFormEntryConfig[] => {
    return [
      { valProp: "first_name", type: SaFormType.TEXT, label: "Given Name" },
      { valProp: "score_entry_status", type: SaFormType.TEXT, label: "Score Entry Status" },
      { valProp: "last_name", type: SaFormType.TEXT, label: "Surname" },
      { valProp: "eqao_student_gov_id", type: SaFormType.TEXT, label: "PEN" },
      { valProp: "fsa_walk_in_flag", type: SaFormType.RADIO, label: "Walk-in Flag", options: STUDENT_YES_NO.list },
      { valProp: "fsa_grade_level", type: SaFormType.RADIO, label: "Grade Level", options: FSA_GRADE_LEVEL.list },
      { valProp: "fsa_school_name", type: SaFormType.TEXT, label: "School Name" },
      { valProp: "fsa_school_code", type: SaFormType.TEXT, label: "School Code" },
      { valProp: "fsa_anglophone_course_code", type: SaFormType.RADIO, label: "Anglophone", options: FSA_ANGLOPHONE_COURSE_CODE.list, isVertical: true },
      { valProp: "fsa_francophone_course_code", type: SaFormType.RADIO, label: "Francophone", options: FSA_FRANCOPHONE_COURSE_CODE.list, isVertical: true },
      { valProp: "fsa_session_date", type: SaFormType.DATE, label: "Session date", nullDbPlaceholder: EQAO_DB_NULL },
      { valProp: "fsa_exemption", type: SaFormType.RADIO, label: "Exemption", sublabel: "Has this student been exempted from writing the FSA?", options: STUDENT_YES_NO.list },
      { valProp: "fsa_accommodations", type: SaFormType.SELECT, label: "Accommodations", options: FSA_ACCOMMODATIONS.list },
      { valProp: "fsa_student_info_notes", type: SaFormType.TEXT, label: "Other Notes" },
      { valProp: "fsa_school_name", type: SaFormType.TEXT, label: "School Name" },
      { valProp: "fsa_school_ministry_code", type: SaFormType.TEXT, label: "School Ministry Code" },
      { valProp: "fsa_legal_last_name", type: SaFormType.TEXT, label: "Student's Legal Surname" },
      { valProp: "fsa_legal_first_name", type: SaFormType.TEXT, label: "Student's Legal Given Name" },
      { valProp: "fsa_legal_middle_name", type: SaFormType.TEXT, label: "Student's Legal Middle Name" },
      { valProp: "fsa_grad_grade", type: SaFormType.TEXT, label: "Grade" },
      { valProp: "fsa_programme_francophone", type: SaFormType.RADIO, label: "Is the student enrolled in the Programme Francophone?", options: STUDENT_YES_NO.list },
      { valProp: "fsa_accessibility", type: SaFormType.RADIO, label: "Please check only ONE choice", options: FSA_ACCESSIBILITY.list, isVertical: true },
      { valProp: "fsa_request_status", type: SaFormType.RADIO, label: "Request Status", options: FSA_REQUEST_STATUS.list, isVertical: true }
    ];
  };

  private getBCGRADConfigs = (): ISaFormEntryConfig[] => {
    return [
      { valProp: "first_name", type: SaFormType.TEXT, label: "Given Name", required: true },
      { valProp: "given_middle_name", type: SaFormType.TEXT, label: "Given Middle Name" },
      // {valProp:'preferred_name',              type:SaFormType.TEXT,     label: 'Preferred Name', },
      { valProp: "last_name", type: SaFormType.TEXT, label: "Surname", required: true },
      { valProp: "pen", type: SaFormType.TEXT, label: "PEN", required: true },
      { valProp: "assess_code", type: SaFormType.TEXT, label: "Code" },
      { valProp: "assess_name", type: SaFormType.TEXT, label: "Name" },
      { valProp: "assess_local", type: SaFormType.TEXT, label: "Local Assessment ID" },
      { valProp: "assess_mincode", type: SaFormType.TEXT, label: "Mincode Assessment" },
      { valProp: "student_local_id", type: SaFormType.TEXT, label: "Student Local ID" },
      { valProp: "walk_in_flag", type: SaFormType.RADIO, label: "New Student Flag", options: STUDENT_YES_NO.list, required: true },
      { valProp: "grade_level", type: SaFormType.RADIO, label: "Grade Level", options: BC_GRAD_GRADE.list, required: true },
      { valProp: "grad_anglophone_course_code", type: SaFormType.RADIO, label: "Anglophone", options: BC_GRAD_ANGLOPHONE.list, isVertical: true },
      { valProp: "grad_francophone_course_code", type: SaFormType.RADIO, label: "Francophone", options: BC_GRAD_FRANCOPHONE.list, isVertical: true },
      { valProp: "fsa_exemption", type: SaFormType.RADIO, label: "Exemption", options: STUDENT_YES_NO.list },
      { valProp: "aegrostat_standing", type: SaFormType.RADIO, label: "Aegrostat Standing", sublabel: "Has this student been granted Aegrostat Standing?", options: STUDENT_YES_NO.list },
      { valProp: "fsa_accommodations", type: SaFormType.SELECT, label: "Accommodations", options: FSA_ACCOMMODATIONS.list },
      { valProp: "fsa_student_info_notes", type: SaFormType.TEXTAREA, label: "Other Notes" },
      { valProp: "bc_registered_session_dates", type: SaFormType.RADIO, label: "Registered Session Dates", options: BC_GRAD_REGISTERED_DATES.list },
      { valProp: "student_account", type: SaFormType.RADIO, label: "Student Account", options: BC_GRAD_STUDENT_ACCOUNT_OPTIONS.list, isVertical: true },
      { valProp: "grad_request_status", type: SaFormType.RADIO, label: "Request Status", options: GRAD_REQUEST_STATUS.list, isVertical: true },
      { valProp: "legal_first_name", type: SaFormType.TEXT, label: "Student's Legal First Name" },
      { valProp: "legal_last_name", type: SaFormType.TEXT, label: "Student's Legal Last Name" },
      { valProp: "grad_school_ministry_code", type: SaFormType.TEXT, label: "School Ministry Code" },
      { valProp: "grad_school_contact_name", type: SaFormType.TEXT, label: "School Contact Name" },
      { valProp: "grad_school_contact_email", type: SaFormType.TEXT, label: "School Contact Email" },
      { valProp: "grad_school_name", type: SaFormType.TEXT, label: "School Name" },
      { valProp: "grad_primary_IEDC", type: SaFormType.RADIO, label: "Student's primary Inclusive Education Category designation is:", options: GRAD_PRIMARY_IEDC.list, isVertical: true },
      { valProp: "grad_tsvi_name", type: SaFormType.TEXT, label: "TSVI Name" },
      { valProp: "grad_tsvi_email", type: SaFormType.TEXT, label: "TSVI Email" },
      { valProp: "grad_session_assessment_codes", type: SaFormType.SELECT, label: "Select Session(s) & Assessment Code(s)", options: GRAD_SESSION_CODES.list },
      { valProp: "grad_accessibility", type: SaFormType.RADIO, label: "Please check only ONE", options: GRAD_ACCESSIBILITY.list, isVertical: true },
      { valProp: "grad_font_size", type: SaFormType.TEXT, label: "Font Size" },
      { valProp: "grad_font", type: SaFormType.TEXT, label: "Font" },
      { valProp: "grad_formatting", type: SaFormType.TEXT, label: "Formatting" },
      { valProp: "grad_other", type: SaFormType.TEXT, label: "Other" },
      { valProp: "grad_tsvi_signature", type: SaFormType.TEXT, label: "Signature of TSVI" },
      { valProp: "grad_designated_school_contact", type: SaFormType.TEXT, label: "Designated School Contact" },
      { valProp: "grad_date", type: SaFormType.DATE, label: "Date" }
    ];
  };

  /*
  initFormEntries(studentRecord:Partial<IStudentAccount>){
    let configs:ISaFormEntryConfig[];
    
    if (this.isBCSite()) {
      if (this.schoolType === 'BC_FSA') {
        configs = this.getBCFSAConfigs();                
      } else if (this.schoolType === 'BC_GRAD'){
        configs = this.getBCGRADConfigs();
      }
    } else {
      configs = this.getEQAOConfigs();
    }
    

    this.formEntriesList = initFormEntries(studentRecord, configs, this.g9DemoService.checkMarkMapping);
    this.extractFormEntryRef();
    
    // display translations of labels that go with original data
    // const labels = []
    // this.formEntriesList.forEach(entry => {
    //   DATA_MAPPING_EQAO_G9_STUDENT.forEach(mapping => {
    //     if (mapping.target === entry.valProp){
    //       labels.push(mapping.source + '\t' + this.lang.tra(entry.label))
    //     }
    //   })
    // })
    // console.log(labels.join('\n'))

  }
  */

  applyG3G6G10UniqueValue(){
    //const uniqueG10field = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g10')
    let uniqueFields
    let prefix
    switch(this.classFilter){
      case ClassFilterId.ABED_SAMPLE:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g10')
        prefix = '_abed_sample_';
      break;
      case ClassFilterId.TCLE:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g10')
        prefix = '_tcle_';
      break;
      case ClassFilterId.TCN:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g10')
        prefix = '_tcn_';
      break;
      case ClassFilterId.SCIENCES8:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g10')
        prefix = '_sciences8_';
      break
      case ClassFilterId.Primary:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g3')
        prefix = '_g3_'
      break
      case ClassFilterId.Junior:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g6')
        prefix = '_g6_'
      break
      case ClassFilterId.OSSLT:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g10')
        prefix = '_g10_'
      break
      default:
        uniqueFields = DATA_MAPPING_EQAO_G9_STUDENT.filter(record => record.key_namespace ==='eqao_sdc_g3')
        prefix = '_g3_'
      break
    } 
    uniqueFields.forEach(uniqueField => {
      if(this.formEntries[uniqueField.target] !== undefined){   
        const entry = this.formEntries[uniqueField.target];
        const checkMarkMap = this.g9DemoService.checkMarkMapping[entry.valProp];
        const fc = (<ISaFormEntry>entry).formControlRef
        setEntryValue(entry, this.studentRecord, prefix+uniqueField.target, {fc,checkMarkMap})
        if (entry.options){
          entry.options.forEach(option => {
            const tag = option.tag;
            const subValProp = getSubValProp(entry.valProp, tag); 
            // console.log('subValProp', subValProp)
            if (tag && isInArr([SaFormType.RADIOC, SaFormType.CHECK], entry.type)){
              const checkMarkMap = this.g9DemoService.checkMarkMapping[subValProp];
              const subfc = new FormControl();
              if (this.studentRecord){
                setEntryValue(<ISaFormEntry> entry, this.studentRecord, subValProp, {fc: subfc, checkMarkMap})
              }
            }
          })
        }
      }  
    })
  }


  initPayloadSubscription(studentRecord: Partial<IStudentAccount>) {
    const payload = {
      record: studentRecord,
      data: {}
    };
    this.savePayload[this.saveProp] = payload;
    let studentDefaults = this.savePayload.studentDefaults || {};
    payload.data['class_code'] = this.currClassCode;
    // console.log('studentDefaults', studentDefaults, payload.data)
    // console.log("valprops: ", this.formEntriesList.map(a => a.valProp))
    // console.log('student: ', studentRecord)
    this.formEntriesList.forEach(entry => {
      const prop = entry.valProp;
      const defaultInitValue = studentDefaults[prop];
      if (defaultInitValue !== undefined) {
        entry.formControlRef.setValue(defaultInitValue);
        payload.data[prop] = defaultInitValue;
      }
      if (studentRecord) {
        var val = studentRecord[prop];
        if(this.classFilter === ClassFilterId.Primary && overlapVariables.indexOf(prop) == -1){
          val = studentRecord['_g3_'+prop]
        }
        if(this.classFilter === ClassFilterId.Junior && overlapVariables.indexOf(prop) == -1){
          val = studentRecord['_g6_'+prop]
        }
        if(this.classFilter === ClassFilterId.OSSLT && overlapVariables.indexOf(prop) == -1){
          val = studentRecord['_g10_'+prop]
        }
        // if(this.classFilter === ClassFilterId.TCLE && overlapVariables.indexOf(prop) == -1){
        //   val = studentRecord['_g10_'+prop]
        // }
        if (typeof val !== "undefined") {
          // console.log("setting prop ", prop, ' to ', val)
          payload.data[prop] = val;
        }
        // if(prop == 'eqao_g9_class_code'){
        //   console.log('*****************')
        //   console.log("setting prop ", prop, ' to ', studentRecord['_tcle_eqao_g9_class_code_label'])
        //   payload.data[prop] = '_tcle_eqao_g9_class_code_label'
        // }
      }

      entry.formControlRef.valueChanges.subscribe(val => 
      {
        // console.log('entry.valProp', payload.data[entry.valProp], this.getEntryVal(entry))
        payload.data[entry.valProp] = this.getEntryVal(entry);
        this.applyRules(entry);
        this.dataGuard.activate();

        if (entry.valProp === "eqao_is_g10") {
          this.identifyMode();
          this.definePersonalColumns();
        }
        if (entry.valProp === "eqao_is_g9") {
          this.definePersonalColumns();
        }

        if (entry.valProp === 'classCode')
        {
            if (entry.formControlRef.value != null && entry.formControlRef.value != "")
            {
              payload.data['class_code'] = entry.formControlRef.value;
              this.currClassCode = entry.formControlRef.value;
              let foundOptIdx = entry.options.findIndex(opt => opt.id === +entry.formControlRef.value);
              payload.data['class_code_label'] = entry.options[foundOptIdx].label;
            } 
            
            else 
            {
              payload.data['class_code'] = undefined;
              payload.data['class_code_label'] = undefined;
            }
        }

        else 
        {
          // implement if needed
        }
      });
    });

    if (isAdminAltReqAllowed()){
      this.initAltVersionRequesPayloadSubscription(studentRecord, payload)
    }
  }

  findStudentClassCode(studentRecord: any): null | string
  {
    return "classCode";
  }

  getEntryVal(entry: ISaFormEntry) {
    let v = entry.formControlRef.value;
    if (entry.checkMarkMap) {
      v = this.findKeyForVal(entry.checkMarkMap, v);
    }
    return v;
  }

  applyRules(entry: ISaFormEntry) {
    applyDefaultRules(entry);
    this.applyCustomRules(entry);
  }

  applyCustomRules(entry: ISaFormEntry) {
    if (!this.isBCSite()) {      
      // EQAO Custom Rules
      
      //If StatusInCanada is 4,5,6 than OutOfProvinceResidence need to be 1
      if (entry.valProp === "eqao_status_in_canada") {
        const statusInCanadaFC = entry.formControlRef;
        const outOfProvinceResidenceFC = this.formEntries.eqao_out_province_residence.formControlRef;
        const value = statusInCanadaFC.value;
        if (value === "4" || value === "5" || value === "6") {
          outOfProvinceResidenceFC.setValue("1", { emitEvent: false });
        }
      }
      

      let prefix1 ="eqao_acc_assistive_tech_1";
      let prefix2 ="eqao_acc_assistive_tech_2";
      if((this.isPrimary|| this.isJunior) && this.selected_Accom_Sub_View === Accom_Sub_View.Reading){
        prefix1 ="eqao_acc_assistive_tech_1_pj_reading";
        prefix2 ="eqao_acc_assistive_tech_2_pj_reading";
      }else if((this.isPrimary|| this.isJunior) && this.selected_Accom_Sub_View === Accom_Sub_View.Writing){
        prefix1 ="eqao_acc_assistive_tech_1_pj_writing";
        prefix2 ="eqao_acc_assistive_tech_2_pj_writing";
      }else if((this.isPrimary|| this.isJunior) && this.selected_Accom_Sub_View === Accom_Sub_View.Mathematics){
        prefix1 ="eqao_acc_assistive_tech_1_pj_mathematics";
        prefix2 ="eqao_acc_assistive_tech_2_pj_mathematics";
      }

      const OtherAT = () => [
        prefix2+"_kurz_dl",prefix2+"_kurz_ext",prefix2+"_nvda", prefix2+"_voiceover",
        prefix2+"_readaloud", prefix2+"_jaws",prefix2+"_chromevox",prefix2+"_read",prefix2+"_other",
      ]; 

      //is Read/Write Google Chrome Extesion Assistive Tech
      if (entry.valProp === prefix1+"_chrome" && entry.formControlRef.value){
        OtherAT().forEach( oat => {
          if(this.formEntries[oat].formControlRef.value){ 
            this.formEntries[oat].formControlRef.setValue(false)
          }  
        })
      }
      
      //Other Assistive Tech
      if (entry.valProp === prefix1+"_other" && !entry.formControlRef.value){
        OtherAT().forEach( oat => {
          if(this.formEntries[oat].formControlRef.value){ 
            this.formEntries[oat].formControlRef.setValue(false)
          }  
        })
      }

      //Other Assistive Tech
      const isOtherAt = OtherAT().find( oat => entry.valProp === oat && entry.formControlRef.value )

      if(isOtherAt != undefined){
        this.formEntries[prefix1+"_other"].formControlRef.setValue(true);
      }

      // Born outside Canada
      if (entry.valProp === "eqao_born_outside_canada") {
        const bornOutsideFC = entry.formControlRef;
        const timeInCanadaFC = this.formEntries.eqao_time_in_canada.formControlRef;
        const value = bornOutsideFC.value;
        if (value === "2" || value === "#") {
          timeInCanadaFC.setValue("#", { emitEvent: false });
          timeInCanadaFC.disable({ emitEvent: false });
        } else if (value === "1") {
          timeInCanadaFC.enable({ emitEvent: false });
        }
      }
      //const namespace = this.classFilter === ClassFilterId.G9? '':'_g10_'
      let namespace
      switch(this.classFilter){
        case ClassFilterId.Primary:
          namespace = '_g3_'
          break
        case ClassFilterId.Junior:
          namespace = '_g6_'
          break
        case ClassFilterId.G9:
          namespace = ''
          break
        case ClassFilterId.OSSLT:
          namespace = '_g10_'
          break
        default:
          namespace = '_g3_'
          break
      }
      if (entry.valProp === "eqao_iep") {
        const IEP = entry.formControlRef.value;
        const hasIPRC = this.formEntries._identification_placement.formControlRef.value;
        const specPermIEP = this.formEntries.SpecPermIEP.formControlRef.value;
        if (IEP === false && hasIPRC && hasIPRC!= '#') {
          this.formEntries._identification_placement.formControlRef.setValue('#');
          
          this.studentRecord[namespace+'_identification_placement'] = "#";
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra("sa_std_dsl_IEP_1")), 0);
        }
        if (IEP === false && specPermIEP) {
          this.formEntries.SpecPermIEP.formControlRef.setValue(false);
          this.studentRecord.SpecPermIEP = "#";
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra("sa_std_dsl_IEP_2")), 0);
        }
      }
      if (entry.valProp === "_ell" ) {
        const ellValue = this.formEntries._ell.formControlRef.value
        const periodicBreaks = this.formEntries._periodic_breaks.formControlRef.value;
        if ( (ellValue !== true && ellValue !== '2' && ellValue !== '3') && periodicBreaks) {
          this.formEntries._periodic_breaks.formControlRef.setValue(false);
          this.studentRecord[namespace+'_periodic_breaks'] = "#";
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra("sa_std_dsl_ESLELD")), 0);
        }
      }
    }

    // If the student's class changes
    if (entry.valProp == "eqao_g9_class_code" && isAdminAltReqAllowed()){
      // may need to change default tenative date in alt request
      this.handleTentativeDate();
    }

    if(entry.valProp === "eqao_alternate_form_pj_reading"){
      const value = this.formEntries.eqao_alternate_form_pj_reading.formControlRef.value
      if(value === '1'){
        this.formEntries["AccAudioVersion_pj_reading"].formControlRef.setValue(this.formEntries.eqao_alternate_form_pj_reading.formControlRef.value)
      }  
    }
    if(entry.valProp === "eqao_alternate_form_pj_writing"){
      const value = this.formEntries.eqao_alternate_form_pj_writing.formControlRef.value
      if(value === '1'){
        this.formEntries["AccAudioVersion_pj_writing"].formControlRef.setValue(this.formEntries.eqao_alternate_form_pj_writing.formControlRef.value)
      }  
    }
    if(entry.valProp === "eqao_alternate_form_pj_mathematics"){
      const value = this.formEntries.eqao_alternate_form_pj_mathematics.formControlRef.value
      if(value === '1'||value === '2'){
        this.formEntries["AccAudioVersion_pj_mathematics"].formControlRef.setValue(this.formEntries.eqao_alternate_form_pj_mathematics.formControlRef.value)
      }  
    }
  }

  extractFormEntryRef() {
    this.formEntries = {};
    this.formEntriesList.forEach(entry => {
      this.formEntries[entry.valProp] = entry;
    });
  }

  retrieveDynamicConfigs(item: any) {
    if (item.valProp === "eqao_g9_class_code" || item.valProp == "classCode") 
    {
      // pointless as configs are now from the DB and dynamic
      // item.label = this.isNBED() ? "sdc_1_group" : "sdc_1_class";
      item.options = this.filterClassOption();
    }

    if (item.valProp === "_ell") {
      item.type = this.lang.c() === 'en' ? SaFormType.RADIOC : SaFormType.RADIO;
      item.options = this.lang.c() === 'en' ? STUDENT_ELL.list : STUDENT_ALFPANA.list;
      item.space = this.lang.c() === 'en' ? false : true;
    }

    if (item.valProp === '_periodic_breaks') {
      item.sublabel = this.lang.tra(item.sublabel);
    }

    if (item.valProp === 'NonParticipationStatus') {
      item.sublabel = this.lang.tra(item.sublabel);
    }

    if (item.valProp === 'eqao_student_school_enrol_type') {
      item.options = this.studentTypeOption();
    }

    if (item.valProp === 'eqao_learning_format') {
      item.options = this.learningFormatOption();
    }

    if (item.valProp === 'eqao_student_gov_id') {
      item.label = this.isNBED() || this.isMBED()? "sdc_1_username" : "sdc_1_oen";
    }

    if (item.valProp === 'TermFormat') {
      item.label = this.classFilter === ClassFilterId.OSSLT ? "sdc_1_semester_indicator" : "sdc_1_math_class_when";
      item.isVertical = this.classFilter === ClassFilterId.OSSLT ? true : false;
      item.options = this.classFilter === ClassFilterId.OSSLT ? STUDENT_SEMESTET_INDICATOR.list : Math_Class_When.list;
    }

    return item;
  }

  getStudentModalForm(form_type: string, class_type: string) {
    this.g9DemoService
      .fetchStudentMetaProfiles({form_type, class_type})
      .then(form_items => {        
        const personalInfoColumns = [];
        const provisionInfoColumns = [];
        const participationInfoColumns = [];
        const itemConfigs = form_items.map(({config, key_category}) => {
          const item = {
            config: JSON.parse(config),            
          };          
          item.config.key_category = key_category;          
          return this.retrieveDynamicConfigs(item.config);
        });

        if (NBED_ASSESSMENTS.includes(this.classFilter) || this.whitelabelService.isABED()) {
          this.initFormEntries(this.studentRecord, itemConfigs);
          this.initPayloadSubscription(this.studentRecord);
          this.applyG3G6G10UniqueValue();
        }

        itemConfigs.map((item) => { 
          const formEntryItem = this.formEntries[item.valProp];
          //since we get the labels from the DB then we can use a site text check, if there is a match then we use it, if there is no match then no change happens
          if(this.whitelabelService.getSiteText(formEntryItem.label)){
            formEntryItem.label = this.whitelabelService.getSiteText(formEntryItem.label);
          }
          formEntryItem.order = item.order;
          formEntryItem.column = item.column;
          if (item.key_category === Key_Categories.PERSONAL_INFO) {
            if(!this.isTestCenter() && item.ForTestCenter) return;
            if( this.isTestCenter() && item.HideFromTestCenter) return;
            if(!this.studentRecord.is_PASI_student && item.pasiColumn) return;
            if(formEntryItem.valProp != 'classCode' && this.allowPASIUpdates){
              formEntryItem.isLocked = true;
            }
            if (item.column > personalInfoColumns.length - 1) {
              const newColumn = [formEntryItem];
              personalInfoColumns.push(newColumn);
            } else {
              personalInfoColumns[item.column].push(formEntryItem);
            }
          }

          if (item.key_category === Key_Categories.PROVISION_INFO) {
            provisionInfoColumns.push(formEntryItem);
          }

          if (item.key_category === Key_Categories.NON_PARTICIPATION_INFO) {
            participationInfoColumns.push(formEntryItem);
          }
        });

        this.personalInfoColumns = personalInfoColumns.map((column) => {
          return column.sort((a, b) => {
            return a.order - b.order;
          });
        });

        // add another column or eles the form will have very big input boxes
        if (NBED_ASSESSMENTS.includes(this.classFilter)) {
          this.personalInfoColumns.push([]);
        }

        console.log(this.formEntriesList);

        this.provisionInfoColumns = provisionInfoColumns.sort((a, b) => {
          return a.order - b.order;
        });

        this.participationInfoColumns = participationInfoColumns.sort((a, b) => {
          return a.order - b.order;
        });
      });
  }

  lastColMatch: string;
  definePersonalColumns() {
    console.log("definePersonalColumns", this.lastColMatch);
    if (this.isPrimary){
      this.definePersonalColumns_Primary_Junior();
    } else if(this.isJunior){
      this.definePersonalColumns_Primary_Junior();
    } else if (this.isOSSLT) {
      this.definePersonalColumns_OSSLT();
    } else if (this.isG9) {
      this.definePersonalColumns_G9();
    } else if (this.isNBED()){
      this.definePersonalColumns_NBED();
    } else if (this.isABED()){
      this.definePersonalColumns_ABED();
    }
  }
  definePersonalColumns_Primary_Junior() {
    this.lastColMatch = "primary_junior";
    this.getStudentModalForm("modal", "eqao_pj");
    return;
    // info below moved to student_form_configs table
    this.personalInfoColumns = [
      [
        this.formEntries.first_name,
        this.formEntries.last_name,
        this.formEntries.eqao_student_school_enrol_type,
        this.formEntries.eqao_student_gov_id,
        this.formEntries.SASN,
        this.formEntries.date_of_birth,
        this.formEntries.eqao_gender,
        this.formEntries.eqao_g9_class_code,
        this.formEntries.eqao_grade,
        this.formEntries._ell,
        this.formEntries.eqao_learning_format,
        this.formEntries.eqao_date_entered_school,
        this.formEntries.eqao_date_entered_board,
        this.formEntries.eqao_full_day_junior,
        this.formEntries.eqao_full_day_senior,
        this.formEntries.eqao_indigenous_type
      ],
      [this.formEntries.eqao_first_language, this.formEntries.eqao_enrolled_ontario, this.formEntries.eqao_out_province_residence, this.formEntries.eqao_status_in_canada, this.formEntries.eqao_refugee, this.formEntries._born_outside_canada, this.formEntries.eqao_born_outside_canada, this.formEntries.eqao_time_in_canada]
    ];
  }

  definePersonalColumns_OSSLT() {
    //if (this.lastColMatch === 'osslt'){ return }
    this.lastColMatch = "osslt";
    this.getStudentModalForm("modal", "eqao_osslt");
    return;
    // info below moved to student_form_configs table
    let firstColumn = [
      this.formEntries.first_name,
      this.formEntries.last_name,
      this.formEntries.eqao_student_school_enrol_type,
      this.formEntries.SASN,
      this.formEntries.eqao_student_gov_id,
      this.formEntries.date_of_birth,
      this.formEntries.eqao_gender
      //this.formEntries.eqao_is_g9,
      //this.formEntries.eqao_is_g10
    ];
    /*
    if (this.formEntries.eqao_is_g9.formControlRef.value === "1") {
      firstColumn.push(this.formEntries.eqao_g9_course);
    }
    */
    firstColumn = firstColumn.concat([
      // this.formEntries.Grouping,
      this.formEntries.eqao_g9_class_code,
      this.formEntries.Homeroom,
      this.formEntries.eqao_learning_format,
      this.formEntries.eqao_date_entered_school,
      this.formEntries.eqao_date_entered_board,
      this.formEntries.EligibilityStatus,
      this.formEntries.TermFormat,
      this.formEntries.LevelofStudyLanguage,
      this.formEntries.DateOfFTE,
      //this.formEntries.NonParticipationStatus,
      this.formEntries.Graduating
      // this.formEntries.AccReadGraphic,
      // this.formEntries.AccReading,
      //this.formEntries.eqao_indigenous_type,
    ]);
    this.personalInfoColumns = [firstColumn, [this.formEntries.eqao_indigenous_type, this.formEntries.eqao_first_language, this.formEntries.eqao_enrolled_ontario, this.formEntries.eqao_out_province_residence, this.formEntries.eqao_status_in_canada, this.formEntries.eqao_refugee, this.formEntries._born_outside_canada, this.formEntries.eqao_born_outside_canada, this.formEntries.eqao_time_in_canada]];
  }
  definePersonalColumns_G9() {
    //if (this.lastColMatch === 'g9'){ return }
    this.lastColMatch = "g9";
    this.getStudentModalForm("modal", "eqao_g9");
    return;
    // info below moved to student_form_configs table
    this.personalInfoColumns = [
      [
        this.formEntries.first_name,
        this.formEntries.last_name,
        this.formEntries.eqao_student_school_enrol_type,
        this.formEntries.eqao_student_gov_id,
        this.formEntries.SASN,
        this.formEntries.date_of_birth,
        this.formEntries.eqao_gender,
        //this.formEntries.eqao_is_g9,
        //this.formEntries.eqao_is_g10,
        //this.formEntries.eqao_g9_course,
        this.formEntries.eqao_g9_class_code,
        this.formEntries.TermFormat,
        this.formEntries.eqao_learning_format,
        this.formEntries.eqao_date_entered_school,
        this.formEntries.eqao_date_entered_board,
        this.formEntries.eqao_indigenous_type
      ],
      [this.formEntries.eqao_first_language, this.formEntries.eqao_enrolled_ontario, this.formEntries.eqao_out_province_residence, this.formEntries.eqao_status_in_canada, this.formEntries.eqao_refugee, this.formEntries._born_outside_canada, this.formEntries.eqao_born_outside_canada, this.formEntries.eqao_time_in_canada]
    ];
  }

  columnsToDisplay_NBED = [];
  columnsDisabled = [];
  definePersonalColumns_ABED() {
    this.getStudentModalForm("modal", "abed");
    return;
  }

  definePersonalColumns_NBED() {
    this.getStudentModalForm("modal", "nbed");
    return;
    // info below moved to student_form_configs table
    this.lastColMatch = "g9";
    this.personalInfoColumns = [
      [
        this.formEntries.first_name,
        this.formEntries.last_name,
        // this.formEntries.eqao_student_gov_id,
        this.formEntries.nbed_user_id,
        this.formEntries.nbed_student_identification_number,
        this.formEntries.eqao_g9_class_code
      ],
      []
    ];
    this.columnsToDisplay_NBED = [
      [
        this.personalInfoColumns[0][0],
        this.personalInfoColumns[0][1],
        this.personalInfoColumns[0][2],
        this.personalInfoColumns[0][3],
        this.personalInfoColumns[0][4],
      ]
    ];
    this.columnsDisabled = [true, true, true, true, false];
  }

  ////////////////////////////////////////////////////////////////////////////////////////////////

  setFormEntryValue(prop: string, value: number | string) {
    const entry = this.formEntries[prop];
    if (entry) {
      return entry.formControlRef.setValue(value);
    }
  }

  selectView(id: View) {
    this.selectedView = id;
  }

  selectAccomSubView(id: Accom_Sub_View) {
    this.selected_Accom_Sub_View = id;
  }

  // getStudentVal(prop: string) {
  //   return this.studentRecord[prop];
  // }

  getStudentTeacherName() {
    // console.log(this.g9DemoService);
    let teacherName = '';
    this.g9DemoService.teacherClassrooms.list.forEach(cls => {
      for(let i=0; i < cls.currentStudents.list.length; i++) {
        if(cls.currentStudents.list[i].id === this.studentRecord.id) {
          let student = {...cls.currentStudents.list[i]};
          if (student) {
            let className = cls.name; 

            if (className) {
              this.g9DemoService.teachers.list.forEach(t => {
                if(t.classCode && t.classCode.length > 0) {
                  let classes = t.classCode.split(",");
                  for(let i=0; i < classes.length; i++) {
                    classes[i] = classes[i].trim();
                  }
                  if (classes.indexOf(className) > -1) {
                    teacherName = t.firstName + " " + t.lastName;
                  }
                }
              })
            }
          }
        }
      }
    })
    return teacherName;
  }

  getFormEntryVal(prop: string) {
    const entry = this.formEntries[prop];
    if (entry) {
      return entry.formControlRef.value;
    }
  }

  getFormEntryValYesNo(prop: string) {
    const entry = this.formEntries[prop];
    if (entry && entry.formControlRef) {
      return entry.formControlRef.value;
      // return (''+entry.formControlRef.value) === '1';
    }
  }

  getELL(prop1) {
    const entry1 = this.formEntries[prop1];
    if (entry1 && entry1.formControlRef && entry1.formControlRef.value!=='#'){
      return entry1.formControlRef.value
    }
  }

  getCourseLabel() {
    //const coursePropName = this.isBCSite() ? '' : 'eqao_g9_course'; // TODO: add BC course prop name
    const coursePropName = this.isBCSite() ? "" : "eqao_learning_format"; // TODO: add BC course prop name
    //const EQAOCourseList = STUDENT_G9_COURSES;
    const EQAOCourseList = this.classFilter === ClassFilterId.G9 ? Learning_Format_G9 : Learning_Format_OSSLT;
    const BCCourseList: IMappedList<IG9Courses> = { map: {}, list: [] }; // TODO: add BC course list
    const courseList = this.isBCSite() ? BCCourseList : EQAOCourseList;
    let label = "";
    const val = this.getFormEntryVal(coursePropName);
    if (val) {
      label = courseList.map[val].label;
    }
    return label;
  }
  getCourseLabelFromId(id) {
    let label = "";
    if (id) {
      label = STUDENT_G9_COURSES.map[id].label;
    }
    return this.lang.tra(label);
  }

  getClassLabelFromId(id) {
    let label = "";
    if (id) label = this.g9DemoService.classOptions.map[id].label;
    return label;
  }

  getMultiSelectStudentSlug(type: string) {
    // using if-else blocks instead of ternary to account for more whiteLabels in the future
    if (type == "studentIdentifier") {
      if (this.whitelabelService.isABED()) {
        return "lbl_ASN_ABED";
      }

      return "sa_students_col_oen";
    }

    if (type === "firstName") {
      if (this.whitelabelService.isABED()) {
        return "abed_first_name";
      }

      return "sa_students_col_fname";
    }

    if (type === "lastName") {
      if (this.whitelabelService.isABED()) {
        return "abed_last_name";
      }

      return "sa_students_col_lname";
    }

    if (type === "className") {
      if (this.whitelabelService.isABED()) {
        return "abed_class_code";
      }

      return "sa_students_col_class";
    }
    
  }

  getMultiSelectStudentKey(type: string) {
    if (type == "studentIdentifier") {
      if (this.whitelabelService.isABED()) {
        return "StudentIdentificationNumber";
      }

      return "eqao_student_gov_id";
    }

    if (type === "className") {
      if (this.whitelabelService.isABED()) {
        return "classCode";
      }

      return "eqao_g9_class_code";
    }
  }

  // getCourseLabel(){
  //   return this.getCourseLabelFromId(this.getFormEntryVal('eqao_g9_course'));
  // }

  getClassLabel() 
  {
    if (this.whitelabelService.isABED())
    {
      return this.getClassLabelFromId(this.getFormEntryVal("classCode"));
    }

    return this.getClassLabelFromId(this.getFormEntryVal("eqao_g9_class_code"));
  }

  renderAccommodations(acct){
    // for ABED
    return acct.accommodations?.join(', ');
  }

  haveNonParticipationStatus() {
    if (this.formEntries["NonParticipationStatus_exempted"].formControlRef.value || this.formEntries["NonParticipationStatus_deferred"].formControlRef.value || this.formEntries["NonParticipationStatus_osslc"].formControlRef.value) {
      return true;
    } else {
      return false;
    }
  }

  getEligibilityStatus() {
    switch (this.getFormEntryVal("EligibilityStatus")) {
      case "1": {
        return "sdc_osslt_eligstat_1";
      }
      case "2": {
        return "sdc_osslt_eligstat_2";
      }
      default: {
        return;
      }
    }
  }
  isBCSite(): boolean {
    return this.whitelabelService.getSiteFlag("BC_STUDENT");
  }

  isOtherTechSelected() {
    let prefix ="eqao_acc_assistive_tech_2_other";
    if((this.isPrimary|| this.isJunior) && this.selected_Accom_Sub_View === Accom_Sub_View.Reading){
      prefix ="eqao_acc_assistive_tech_2_pj_reading_other";
    }else if((this.isPrimary|| this.isJunior) && this.selected_Accom_Sub_View === Accom_Sub_View.Writing){
      prefix ="eqao_acc_assistive_tech_2_pj_writing_other";
    }else if((this.isPrimary|| this.isJunior) && this.selected_Accom_Sub_View === Accom_Sub_View.Mathematics){
      prefix ="eqao_acc_assistive_tech_2_pj_mathematics_other";
    }
    return this.formEntries[prefix].formControlRef.value;
  }

  fetchErrMsg(err: string): string {
    return this.lang.tra(this.apiErrMsgToSlug(err));
  }

  clickPreRecord() {
    this.choosedConflicts = [];
    this.preRecord.emit();
  }

  clickNextRecord() {
    this.choosedConflicts = [];
    this.nextRecord.emit();
  }

  clickConfirmStudentInformation() {
    this.confirmStudentInformation.emit();
  }

  convertimportDateTime(studentRecord:any) {
    const conflictKey = this.classFilter == ClassFilterId.Primary?"SDC_conflict_g3":this.classFilter == ClassFilterId.Junior?"SDC_conflict_g6":this.classFilter == ClassFilterId.G9?"SDC_conflict_g9":"SDC_conflict_g10"
    const importDate = studentRecord[conflictKey].user_metas_import_created_date
    let theDate = new Date(importDate);
    return formatDate(theDate, "MMM dd yyyy", "en-US");
  }

  get_DATA_MAPPING_EQAO_G9_STUDENT(source: string) {
    if (source == "FirstName") {
      return "first_name";
    }
    if (source == "LastName") {
      return "last_name";
    }
    if (source == "ClassCode") {
      return this.classFilter === ClassFilterId.Primary?'_g3_eqao_g9_class_code_label':this.classFilter === ClassFilterId.Junior?'_g6_eqao_g9_class_code_label': 'eqao_g9_class_code_label';
    }
    if(source == 'Grouping'){
      return "_g10_eqao_g9_class_code_label";
    }
    const map_result = DATA_MAPPING_EQAO_G9_STUDENT.find(r => r.source == source);
    const namespace = (this.classFilter === ClassFilterId.G9 || overlapVariables.indexOf(map_result.target) > -1)?'':this.classFilter === ClassFilterId.Primary?'_g3_':this.classFilter === ClassFilterId.Junior?'_g6_':'_g10_'
    return namespace+map_result.target;
  }

  selectedConflicts(conflict: any, event: any) {
    if (event.value == "board") {
      this.choosedConflicts.push(conflict);
    }
    if (event.value == "school") {
      this.choosedConflicts = this.choosedConflicts.filter((conf: any) => conf.fieldname != conflict.fieldname);
    }
  }

  clickApplyBtn() {
    this.applyBtnClicked.emit({ studentRecord: this.studentRecord, choosedConflicts: this.choosedConflicts });
  }

  filterClassOption() {
    const currentTestWindow = this.mySchool.getCurrentTestWindow();
    const allClasses = this.g9DemoService.classOptions ? this.g9DemoService.classOptions.list : [];
    const filterClassesByGroupType = (group_type) => {
      return allClasses.filter(theClass => {
        const classroom = this.g9DemoService.classrooms.find(cr => cr.id === theClass.id)
        const semester = this.g9DemoService.semesters.map[+classroom.semester]
        if(semester && currentTestWindow.id === semester.testWindowId && theClass.group_type == group_type){
          return theClass;
        }
      });
    }

    switch (this.classFilter){
      case ClassFilterId.Primary: return filterClassesByGroupType('EQAO_G3');
      case ClassFilterId.Junior: return filterClassesByGroupType('EQAO_G6');
      case ClassFilterId.G9: return filterClassesByGroupType('EQAO_G9');
      case ClassFilterId.OSSLT: return filterClassesByGroupType('EQAO_G10');
      case ClassFilterId.TCLE: return filterClassesByGroupType('NBED_TCLE');
      case ClassFilterId.TCN: return filterClassesByGroupType('NBED_TCN');
      case ClassFilterId.SCIENCES8: return filterClassesByGroupType('NBED_SCIENCES8');
      case ClassFilterId.ABED_SAMPLE: return filterClassesByGroupType('ABED_SAMPLE');
      case ClassFilterId.ABED_GRADE_6: return filterClassesByGroupType('ABED_GRADE_6');
      case ClassFilterId.ABED_GRADE_9: return filterClassesByGroupType('ABED_GRADE_9');
      case ClassFilterId.ABED_GRADE_12: return filterClassesByGroupType('ABED_GRADE_12');
    }
  }
  

  studentTypeOption(){
    switch(this.classFilter){
      case ClassFilterId.Primary:
      case ClassFilterId.Junior:
        return STUDENT_SCHOOL_ENROL_TYPES_PJ.list
      case ClassFilterId.G9:
      case ClassFilterId.OSSLT:
      default:
        return this.isPrivateSchool?STUDENT_PRIVATE_SCHOOL_ENROL_TYPES.list:STUDENT_SCHOOL_ENROL_TYPES.list
    }
  }

  learningFormatOption(){
    switch(this.classFilter){
      case ClassFilterId.Primary:
        return Learning_Format_Primary.list
      case ClassFilterId.Junior:
        return Learning_Format_Junior.list
      case ClassFilterId.G9:
        return Learning_Format_G9.list
      case ClassFilterId.OSSLT:
        return Learning_Format_OSSLT.list
      default:
        return Learning_Format_Primary.list
    }
  }

  FieldValue(fieldName:string, fieldValue:string){
    console.log(this.studentRecord)
    // const entriesSelectionList = () =>[
    //   '_identification_placement'
    // ]
    //const target = DATA_MAPPING_EQAO_G9_STUDENT.find(record => record.source == fieldName).target
    if (fieldName == "FirstName") {
      fieldName = "first_name";
    }
    if (fieldName == "LastName") {
      fieldName = "last_name";
    }
    if (fieldName == "ClassCode") {
      fieldName = "class_code";
    }
    const map_result = DATA_MAPPING_EQAO_G9_STUDENT.find(r => r.source == fieldName);
    const target = map_result.target;


    //const target = this.get_DATA_MAPPING_EQAO_G9_STUDENT(fieldName);
    
    //const selectedEntry = entriesSelectionList().find(entry => target.includes(entry))
    var entry = this.formEntries[target];
    //if(selectedEntry!==undefined){
      //entry = this.formEntries[selectedEntry];
    //}
    if(entry != undefined){
      // if(selectedEntry!==undefined){
      //   if(entry.checkMarkMap[fieldValue]){
      //     return this.lang.tra('lbl_yes')
      //   }else{
      //     return this.lang.tra('lbl_no')
      //   }
      // }
      const options = entry.options
      if(options!= undefined){
        var option = options.find(record => record.id == fieldValue)
        if(option){
          const label = option.label
          if(label!==undefined && label!==null && label!== ''){
            return this.lang.tra(label)
          }
          else if(label=== ''){
            if(option.id === '1'){
              return this.lang.tra('lbl_yes')
            }else{
              return this.lang.tra('lbl_no')
            }
          } 
        }
        else if (fieldValue === '#'){
          return this.lang.tra('lbl_no')
        }
      }
    }
    if (fieldValue === '#'){
      return this.lang.tra('sdc_generaal_unknown_no')
    }
    return fieldValue;    
  }

  conflictFieldText(fieldName:string){
    //const record = DATA_MAPPING_EQAO_G9_STUDENT.find(record => record.source == fieldName)
    //const target = record.target;
    // var target = this.get_DATA_MAPPING_EQAO_G9_STUDENT(fieldName);
    // if(fieldName === 'Grouping'){
    //   target = 'Grouping'
    // }
    // if(fieldName === 'ClassCode'){
    //   target = 'eqao_g9_class_code' 
    // }
    if (fieldName == "FirstName") {
      fieldName = "first_name";
    }
    if (fieldName == "LastName") {
      fieldName = "last_name";
    }
    if (fieldName == "ClassCode") {
      fieldName = "class_code";
    }
    const map_result = DATA_MAPPING_EQAO_G9_STUDENT.find(r => r.source == fieldName);
    const target = map_result.target;
    const entry = this.formEntries[target];
    if(entry !== undefined && entry.label !=undefined){
      return this.lang.tra(entry.label)
    }
    return fieldName;
  }

  isNBED = () =>  this.whitelabelService.getSiteFlag('IS_NBED');
  isABED = () =>  this.whitelabelService.getSiteFlag('IS_ABED');
  isMBED = () =>  this.whitelabelService.getSiteFlag('IS_MBED');

  get isSasnLogin():boolean {
    return this.mySchool.isSASNLogin();
  }

  renderASN(prop){
    const val = this.studentRecord[this.propName(prop)];
    if (val) return applyMask(val, this.whitelabelService.getSiteText('STU_ID_MASK'));
  }

  propName(prop) {
    return this.g9DemoService.getPropName(prop, this.classFilter);
  }

  isTestCenter(){
    return this.whitelabelService.getSiteFlag("isTestCenter");
  }

  getBuiltInAccommodationList(){
    return this.accommodationList.filter(accommodation => accommodation.is_external == 0)
  }

  getOtherAccommodationList(){
    return this.accommodationList.filter(accommodation => accommodation.is_external == 1)
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  startSubmitAltRequestModal(){
    const config  = {};
    this.pageModal.newModal({
      type: StudentPersonalModal.SUBMIT_ALT_REQUEST,
      config,
      finish: this.submitAltRequest
    })
  }

  submitAltRequest(){
    const currentTestWindow = this.mySchool.getCurrentTestWindow()
    const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)

    const data = {
      test_window_id: currentTestWindow.id,
      student_uid: this.savePayload[this.saveProp].record.uid || this.savePayload[this.saveProp].record.id,
      request_info: {
        ...this.savePayload[this.saveProp].data[altVersionRequestName],
      },
      request_prereq: [],
    }  

    // Set address to null on submit if braille format not supported
    if(!this.showaltRequestSchoolAddress()) {
      data.request_info.schl_contact = null; 
      data.request_info.schl_email = null;
      data.request_info.schl_street_number_name = null;
      data.request_info.schl_city = null;
      data.request_info.schl_phone = null;
      data.request_info.schl_province = null;
      data.request_info.schl_country = null;
      data.request_info.schl_postal_code = null;
    }

    // Set admin phone to null on submit if braille format is chosen
    if (this.getAltVersionInfoValue('alt_version_braille_require') === '1') {
      data.request_info.schl_admin_phone = null;
    }

    this.auth.apiCreate(
      this.routes.SCHOOL_ADMIN_ALTERNATIVE_VERSION_REQUEST, 
      data, 
      { query: { schl_group_id: this.g9DemoService.schoolData.group_id } }
    ).then(res =>{
      this.g9DemoService.setAltVersionRequires( res[0])
      this.savePayload[this.saveProp].data[altVersionRequestName] = res[0]
      this.savePayload[this.saveProp].record[altVersionRequestName] = res[0]
      //If request succeeds, clear any previous Linear user meta value from client, so that it does not overwrite the one just set in the API on the next Save of the modal
      // this.savePayload[this.saveProp].data['eqao_dyn_linear'] = undefined
      this.hasValidAltReq = true;
      //// this.updateAccomRestrictionsByAltReq() 
      this.loginGuard.quickPopup("alt_version_request_send_gen")
    }).catch(error =>{
      switch(error.message){
        case 'SCHOOL_INFORMATION_MISSING':
          this.loginGuard.quickPopup(this.lang.tra("sa_alt_request_schl_info_missing"))
          break
        case 'SCHOOL_ADMIN_PHONE_MISSING':
          this.loginGuard.quickPopup(this.lang.tra("sa_alt_request_schl_admin_phone_missing"))
          break
        case 'REQUEST_REASON_MISSING':
          this.loginGuard.quickPopup(this.lang.tra("sa_alt_request_reason_missing"))
          break   
        case 'TENT_SESSION_DATE_MISSING':
          this.loginGuard.quickPopup(this.lang.tra("sa_alt_tent_assessment_date_missing"))
          break   
        // case 'ALT_VERSION_NO_PREREQS':
        //   this.loginGuard.quickPopup(this.lang.tra("lbl_alt_version_require_iep_spec"))
        //   break
        case 'ALT_VERSION_REQUEST_EXIST': //this error are not suppose to trigger, so not translating it. (the request should be disable if there already exist one)
        default: 
          alert(error.message)
          break
      }
    })
    this.requestAltVersionAcknowledge = false;
    this.pageModal.closeModal();
  }

  getRequestFormatList(){
    const braille_format_value = this.getAltVersionInfoValue('braille_format')
    const asl_format_value = this.getAltVersionInfoValue('asl_format')
    const pdf_format_value = this.getAltVersionInfoValue('pdf_format')
    const audio_delivery_format_value = this.getAltVersionInfoValue('audio_delivery_format')
    const audio_format_value = this.getAltVersionInfoValue('audio_format')
    const alt_version_online_require = this.getAltVersionInfoValue('alt_version_online_require')
    return this.g9DemoService.getAltRequestFormatList({
      braille_format_value, asl_format_value, pdf_format_value, audio_delivery_format_value, audio_format_value, alt_version_online_require
    })
  }

  getAltVersionInfoValue(valProp){
    return this.altVersionFormEntries[valProp].formControlRef.value
  }

  //If the form is submitted and the question was answered in it, should display despite changes in IEP/blindness/classroom
  submittedAndAnswered(valProp){
    return this.altVersionSubmit() && this.getAltVersionInfoValue(valProp)
  }

  /**
   * Check if the entered Tent Session Date is valid( within test window time)
   * @returns boolean
   */
  validTentSessionDate(){
    let validateResult = false
    const tentDate_fc = this.altVersionFormEntries['tent_session_date'].formControlRef
    if(tentDate_fc && tentDate_fc.value && tentDate_fc.value.length === 8){
      const tentAssessmentDate = moment(tentDate_fc.value, "YYYYMMDD");
      const testWindow = this.mySchool.getCurrentTestWindow()
      const testWindowDateEnd = moment(testWindow.date_end);
      const testWindowStart = moment(testWindow.date_start);
      const isValidTentDate = tentAssessmentDate.isSameOrAfter(testWindowStart.startOf('day')) && tentAssessmentDate.isSameOrBefore(testWindowDateEnd.endOf('day'));
      if (isValidTentDate) {
        validateResult = true;
      }
    }
    return validateResult
  }


  showAltQuestions(){
    return true;
    // No error conditions defined so show questions
  }

  haveAltReqPrereq(){
    // No prerequisites defined
    return true;
  }


  altVersionSubmit(){
    const currentTestWindow = this.mySchool.getCurrentTestWindow()
    const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)
    const altVersionRequest = this.savePayload[this.saveProp].record[altVersionRequestName]
    if(altVersionRequest && altVersionRequest.alt_version_requests_id){
      return true
    }
    return false
  }

    /**
   * Determine if the entire form is completed by checking possible question combinations
   * @returns True/False if all questions were completed
   */
   //TODO: Refactoring to take from central config
    altQuestionsCompleted():boolean{

      /** Return if the value for  acertain in the alt version form matches the pattern defined for it */
      const isPatternMatched = (valProp:string) => {
        const pattern = this.altVersionFormEntries[valProp].pattern
        const regexPattern = new RegExp(pattern);
        const value = this.altVersionFormEntries[valProp].formControlRef.value
        if (!value || !regexPattern.test(value)){
          return false;
        }
        return true;
      }
  
      if (!this.validTentSessionDate()){
        return false;
      }
  
      const alt_version_braille_require = this.getAltVersionInfoValue('alt_version_braille_require')
      const alt_version_pdf_require = this.getAltVersionInfoValue('alt_version_pdf_require')
      // const alt_version_asl_require = this.getAltVersionInfoValue('alt_version_asl_require')
      const alt_version_online_require = this.getAltVersionInfoValue('alt_version_online_require')
      const reason = this.getAltVersionInfoValue('reason')
      const tent_session_date = this.getAltVersionInfoValue('tent_session_date')
  
      const schl_admin_phone = this.getAltVersionInfoValue('schl_admin_phone')
  
      const schl_contact = this.getAltVersionInfoValue('schl_contact')
      const schl_email = this.getAltVersionInfoValue('schl_email')
      const schl_phone = this.getAltVersionInfoValue('schl_phone')
      const schl_street_number_name = this.getAltVersionInfoValue('schl_street_number_name')
      const schl_city = this.getAltVersionInfoValue('schl_city')
      const schl_province = this.getAltVersionInfoValue('schl_province')
      const schl_country = this.getAltVersionInfoValue('schl_country')
      const schl_postal_code = this.getAltVersionInfoValue('schl_postal_code')
  
      const braille_format_value = this.getAltVersionInfoValue('braille_format')
      const pdf_format_value = this.getAltVersionInfoValue('pdf_format')
      // const asl_format_value = this.getAltVersionInfoValue('asl_format')
      const audio_delivery_format_value = this.getAltVersionInfoValue('audio_delivery_format')
      const audio_format_value = this.getAltVersionInfoValue('audio_format')
  
      let answerDisplayedFields = true;
      if(!(+braille_format_value || +audio_delivery_format_value || +audio_format_value || +pdf_format_value || alt_version_online_require == "1")) answerDisplayedFields = false
      else if (!alt_version_braille_require) answerDisplayedFields = false
      else if(alt_version_braille_require == 1 && !(+braille_format_value && +audio_delivery_format_value && +pdf_format_value)) answerDisplayedFields = false
      // else if((this.isG9 || this.isPrimary || this.isJunior) && alt_version_braille_require == 1 && !(+audio_format_value)) answerDisplayedFields = false
      else if(alt_version_braille_require == "#" && !(alt_version_pdf_require)) answerDisplayedFields = false
      else if(alt_version_braille_require == "#" && !(schl_admin_phone && isPatternMatched("schl_admin_phone"))) answerDisplayedFields = false
      // ASL format no longer used
      // else if(this.isOSSLT && alt_version_braille_require == "#" && !(alt_version_asl_require)) answerDisplayedFields = false
      else if (alt_version_pdf_require == "1" && !(+pdf_format_value && +audio_delivery_format_value)) answerDisplayedFields = false
      // else if ((this.isG9 || this.isPrimary || this.isJunior) && alt_version_pdf_require == "1" && !(+audio_format_value)) answerDisplayedFields = false
      else if ((alt_version_pdf_require == "#" && !pdf_format_value) && !alt_version_online_require) answerDisplayedFields = false
      else if (( alt_version_pdf_require == "1" || alt_version_online_require == "1" ) && !reason) answerDisplayedFields = false
      // else if ((this.isG9 || this.isPrimary || this.isJunior) && ( alt_version_pdf_require == "1" || alt_version_online_require == "1" ) && !(+audio_format_value)) answerDisplayedFields = false
      else if (this.showaltRequestSchoolAddress() && !(schl_contact && schl_email && isPatternMatched("schl_email") && schl_phone && isPatternMatched("schl_phone") && schl_street_number_name && schl_city && schl_province && schl_country && schl_postal_code)) answerDisplayedFields = false
      else if (!tent_session_date) answerDisplayedFields = false
  
      return answerDisplayedFields
  
    }
    
    hasValidAltReq:boolean;
    initHasValidAltRreq(){
      const currentTestWindow = this.mySchool.getCurrentTestWindow()
      const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)
      const request_info_record = this.savePayload[this.saveProp].record[altVersionRequestName]
      const alt_version_requests_status = request_info_record?.alt_version_requests_status
      if(request_info_record && alt_version_requests_status && alt_version_requests_status  !== AltVersionRequestStatus.Canceled){
        this.hasValidAltReq = true
      }
      else this.hasValidAltReq = false;
    }
  
    showSubmitAltVersionBtn(){
      const alt_version_require = this.getAltVersionInfoValue('alt_version_require')
      const currentTestWindow = this.mySchool.getCurrentTestWindow()
      const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)
      const request_info_record = this.savePayload[this.saveProp].record[altVersionRequestName] 
      return (alt_version_require && (!request_info_record || !request_info_record.alt_version_requests_id))
    }
  
    showCancelAltVersionBtn(){
      const currentTestWindow = this.mySchool.getCurrentTestWindow()
      const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)
      const request_info_record = this.savePayload[this.saveProp].record[altVersionRequestName]
      if(request_info_record && request_info_record.alt_version_requests_status  === AltVersionRequestStatus.Pending){
        return true
      }
      return false
    }
  
    cancelAltRequest(){
      const currentTestWindow = this.mySchool.getCurrentTestWindow()
      const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)
      const altVersionRequestNameWithPrefix = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow, this.classFilter)
      const request_info = this.savePayload[this.saveProp].record[altVersionRequestName]
      const request_id = request_info.alt_version_requests_id
      this.loginGuard.confirmationReqActivate({
        caption: this.lang.tra('sa_cancel_alt_request_warning'),
        confirm: () => {
          this.auth.apiRemove(
            this.routes.SCHOOL_ADMIN_ALTERNATIVE_VERSION_REQUEST, 
            request_id, 
            { query: { schl_group_id: this.g9DemoService.schoolData.group_id } }
          ).then(res =>{
            this.savePayload[this.saveProp].data[altVersionRequestName] = undefined
            this.savePayload[this.saveProp].record[altVersionRequestName] = undefined
            //Bad patch solution to inconsistency
            this.savePayload[this.saveProp].data[altVersionRequestNameWithPrefix] = undefined
            this.savePayload[this.saveProp].record[altVersionRequestNameWithPrefix] = undefined

            this.filteredBrailleFormats = this.filterBrailleFormats()
  
            this.initAltVersionFormEntriesList(this.studentRecord)
            this.initAltVersionRequesPayloadSubscription(this.studentRecord, this.savePayload[this.saveProp] )
  
            this.hasValidAltReq = false;
  
            this.handleTentativeDate();
  
            this.loginGuard.quickPopup("sa_alt_version_request_cancelled")
          }).catch(error =>{
            switch(error.message){  //these error are not suppose to trigger, so not translating it. (The cancel btn should not show up it request does not exist or its status is not pending)
              case 'ALT_VERSION_REQUEST_NOT_EXIST':
              case 'ALT_VERSION_REQUEST_NOT_PENDING':
              default: 
                alert(error.message)
                break;
            }
          })
        }
      })
    }
  
    /** If a student has an operational session, put its start as the tentative date in the alt request, otherwise clear value  */
    handleTentativeDate(){
      // If this is an already submitted form, do nothing
      if (this.submittedAndAnswered('tent_session_date')) return;
  
      const opSessionDate = this.getOpSessionDate()
      const tentDate_fc = this.altVersionFormEntries['tent_session_date'].formControlRef
      if (opSessionDate) {
        tentDate_fc.setValue(opSessionDate)
        this.altVersionFormEntries['tent_session_date'].greyOut = true;
      } else {
        //tentDate_fc.setValue(null) //no need to set null here so it won't erase the saved date
        this.altVersionFormEntries['tent_session_date'].greyOut = false;
      }
    }
  
    /**
     * @returns the start of the operational session of the class that the student is in, in YYMMDD format, or undefined if DNE
     * Used to autopopulate the "tentative date" question, if it's autopopulated no other input is possible
     */
    getOpSessionDate(){
      return undefined;
      //TODO: Fix function to find op date of current class if any
      // const currentClassId = this.formEntries['eqao_g9_class_code'].formControlRef.value 
      // if (!currentClassId) return false
      // const schoolSessions = this.g9DemoService.assessments.list
  
      // let targetSessionSlug;
      // if (this.isG9) targetSessionSlug = "G9_OPERATIONAL"
      // else if (this.isOSSLT) targetSessionSlug = "OSSLT_OPERATIONAL"
      // else if (this.isPrimary) targetSessionSlug = "PRIMARY_OPERATIONAL"
      // else if (this.isJunior) targetSessionSlug = "JUNIOR_OPERATIONAL"
  
      // const validSession = schoolSessions.find(session => {
      //     return (session.classroom_id == currentClassId && !session.isclosed && session.slug === targetSessionSlug)
      // })
      // // startTimeUTC -> startTime
      // if (!validSession || !validSession.startTime) return
      // const startFormmated = moment(validSession.startTime).format('YYYYMMDD');
  
      // return startFormmated
    }

    extractAltVersionFormEntryRef() {
      this.altVersionFormEntries = {};
      this.altVersionFormEntriesList.forEach(entry => {
        this.altVersionFormEntries[entry.valProp] = entry;
      });
    }

    showaltRequestSchoolAddress(){
      const chosenBrailleFormat = this.getAltVersionInfoValue('braille_format')
      //TODO: Refactor
      const regularBrailleFormats = ['1', '2', '3', '4', '16', '17', '18', '19']
      return regularBrailleFormats.includes(chosenBrailleFormat) && ((this.showAltQuestions()) || this.altVersionSubmit())
    }

    initAltVersionRequesPayloadSubscription(studentRecord, payload){
      //save alt version request result to payload.data
      const currentTestWindow = this.mySchool.getCurrentTestWindow()
      const altVersionRequestName = this.g9DemoService.getStudentAltVersionRequestName(currentTestWindow)
      const studentAltVersionRequest = studentRecord[altVersionRequestName]  
      payload.data[altVersionRequestName] = {}
      if (studentAltVersionRequest) {
        payload.data[altVersionRequestName] = studentAltVersionRequest
      }
      payload.data[altVersionRequestName] = studentAltVersionRequest
      this.altVersionFormEntriesList.forEach(entry => {
        const prop = entry.valProp;
        // if (studentAltVersionRequest) {
        //   var val = studentAltVersionRequest[prop];
        //   if (typeof val !== "undefined") {
        //     if(payload.data[altVersionRequestName] === undefined){
        //       payload.data[altVersionRequestName] = {}
        //     }
        //     payload.data[altVersionRequestName][prop] = val;
        //   }
        // }
        entry.formControlRef.valueChanges.subscribe(val => {
          if(payload.data[altVersionRequestName] === undefined){
            payload.data[altVersionRequestName] = {}
          }
          this.applyAltVersionRules(entry);
          //if a single value of altversionFormEntries change, all other value might change relatively so need to apply
          this.altVersionFormEntriesList.forEach( entry2 => {
            payload.data[altVersionRequestName][entry2.valProp] = this.getEntryVal(entry2);
          })
        });
      }) 
    }

    //TODO: Refactoring to take from central config
    applyAltVersionRules(entry){
      // const brailleFormatDefaultValue = this.filteredBrailleFormats[0].id
      // const pdfFormatDefaultValue = this.g9DemoService.altVersionFormats["pdf_format"].values[0].id
      const aslFormatDefaultValue = this.g9DemoService.altVersionFormats["asl_format"].values[0].id
      // const audioDeliveryFormatDefaultValue = this.g9DemoService.altVersionFormats["audio_delivery_format"].values[0].id
      // const audioFormatDefaultValue = this.g9DemoService.altVersionFormats["audio_format"].values[0].id
  
      // if any of the require change from true to false reset all "requires" to # and format value to 0 
      if (  entry.valProp === "alt_version_require" &&  entry.formControlRef.value === '#'){
        this.altVersionFormEntries.alt_version_braille_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.alt_version_pdf_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.alt_version_asl_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.tent_session_date.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.alt_version_online_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.braille_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.pdf_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.asl_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.audio_delivery_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.audio_format.formControlRef.setValue("0", { emitEvent: false })
        //not resetting schl_contact and address now but comment out for future use if needed
        // this.altVersionFormEntries.schl_contact.formControlRef.setValue("", { emitEvent: false })
         // this.altVersionFormEntries.schl_email.formControlRef.setValue("", { emitEvent: false })
         // this.altVersionFormEntries.schl_phone.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_street_number_name.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_city.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_province.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_country.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_postal_code.formControlRef.setValue("", { emitEvent: false })
      }
  
      // If alt_version_require changes to Yes, might need to re-fill the automatic value of the tentative date
      if (entry.valProp === "alt_version_require" &&  entry.formControlRef.value === '1'){
        this.handleTentativeDate();
      }
  
      // if braille require is set to no, set braille_format pdf_format audio_delivery_format audio_format to 0
      if (  entry.valProp === "alt_version_braille_require" && entry.formControlRef.value === '#'){
        this.altVersionFormEntries.alt_version_pdf_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.alt_version_online_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.braille_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.pdf_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.audio_delivery_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.audio_format.formControlRef.setValue("0", { emitEvent: false })
        //not resetting schl_contact and address now but comment out for future use if needed
        // this.altVersionFormEntries.schl_contact.formControlRef.setValue("", { emitEvent: false })
         // this.altVersionFormEntries.schl_email.formControlRef.setValue("", { emitEvent: false })
         // this.altVersionFormEntries.schl_phone.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_street_number_name.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_city.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_province.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_country.formControlRef.setValue("", { emitEvent: false })
        // this.altVersionFormEntries.schl_postal_code.formControlRef.setValue("", { emitEvent: false })
      }
  
      // if braille require is set to 1, set braille_format pdf_format audio_delivery_format audio_format to 1 if it does not have any value
      if (  entry.valProp === "alt_version_braille_require" && entry.formControlRef.value === '1'){
        this.altVersionFormEntries.alt_version_pdf_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.alt_version_online_require.formControlRef.setValue(undefined, { emitEvent: false })
        //const braille_format_fc = this.altVersionFormEntries.braille_format.formControlRef
        //if(!(+braille_format_fc.value) || +braille_format_fc.value == 0){
          //braille_format_fc.setValue(brailleFormatDefaultValue, { emitEvent: false })
        //}
        //const pdf_format_fc = this.altVersionFormEntries.pdf_format.formControlRef
        //if(!(+pdf_format_fc.value) || +pdf_format_fc.value == 0){
          //pdf_format_fc.setValue(pdfFormatDefaultValue, { emitEvent: false })
        //}
        // const audio_delivery_format_fc = this.altVersionFormEntries.audio_delivery_format.formControlRef
        // if(!(+audio_delivery_format_fc.value) || +audio_delivery_format_fc.value == 0){
        //  audio_delivery_format_fc.setValue(audioDeliveryFormatDefaultValue, { emitEvent: false })
        // }
        //const audio_format_fc = this.altVersionFormEntries.audio_format.formControlRef
        //if(!(+audio_format_fc.value) || +audio_format_fc.value == 0){
          //audio_format_fc.setValue(audioFormatDefaultValue, { emitEvent: false })
        //}
        const schl_admin_phone_fc = this.altVersionFormEntries.schl_admin_phone.formControlRef
        if(!schl_admin_phone_fc.value){
          schl_admin_phone_fc.setValue('', { emitEvent: false })
        }
        const schl_contact_fc = this.altVersionFormEntries.schl_contact.formControlRef
        if(!schl_contact_fc.value){
          schl_contact_fc.setValue('', { emitEvent: false })
        }
        const schl_email_fc = this.altVersionFormEntries.schl_email.formControlRef
        if(!schl_email_fc.value){
          schl_email_fc.setValue('', { emitEvent: false })
        }
        const schl_phone_fc = this.altVersionFormEntries.schl_phone.formControlRef
        if(!schl_phone_fc.value){
          schl_phone_fc.setValue(null, { emitEvent: false })
        }
        const schl_street_number_name_fc = this.altVersionFormEntries.schl_street_number_name.formControlRef
        if(!schl_street_number_name_fc.value){
          schl_street_number_name_fc.setValue(null, { emitEvent: false })
        }
        const schl_city_fc = this.altVersionFormEntries.schl_city.formControlRef
        if(!schl_city_fc.value){
          schl_city_fc.setValue(null, { emitEvent: false })
        }
        const schl_province_fc = this.altVersionFormEntries.schl_province.formControlRef
        if(!schl_province_fc.value){
          schl_province_fc.setValue(null, { emitEvent: false })
        }
        const schl_country_fc = this.altVersionFormEntries.schl_country.formControlRef
        if(!schl_country_fc.value){
          schl_country_fc.setValue(null, { emitEvent: false })
        }
        const schl_postal_code_fc = this.altVersionFormEntries.schl_postal_code.formControlRef
        if(!schl_postal_code_fc.value){
          schl_postal_code_fc.setValue(null, { emitEvent: false })
        }
      }
  
      //NEW: IF Braille is saved to 1, set asl_require and asl_format to none/0
      if (  entry.valProp === "alt_version_braille_require" && entry.formControlRef.value === '1'){
        this.altVersionFormEntries.alt_version_asl_require.formControlRef.setValue(undefined, { emitEvent: false })
        const asl_format_fc = this.altVersionFormEntries.asl_format.formControlRef
        asl_format_fc.setValue("0", { emitEvent: false })
      }
  
      // if pdf require is set to no, set pdf_format audio_delivery_format audio_format to 0
      if (  entry.valProp === "alt_version_pdf_require" && entry.formControlRef.value === '#'){
        this.altVersionFormEntries.alt_version_online_require.formControlRef.setValue(undefined, { emitEvent: false })
        this.altVersionFormEntries.pdf_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.audio_delivery_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.audio_format.formControlRef.setValue("0", { emitEvent: false })
      }
  
      //if asl require is set to 1, set asl_format to default (ASL Video)
      if (  entry.valProp === "alt_version_asl_require" && entry.formControlRef.value === '1'){
        const asl_format_fc = this.altVersionFormEntries.asl_format.formControlRef
        asl_format_fc.setValue(aslFormatDefaultValue, { emitEvent: false })
      }
      //if asl require is set to no, set asl_format to 0 / none
      else if (entry.valProp === "alt_version_asl_require" && entry.formControlRef.value === '#'){
        const asl_format_fc = this.altVersionFormEntries.asl_format.formControlRef
        asl_format_fc.setValue("0", { emitEvent: false })
      }
  
      // if  pdf require is set to 1, set pdf_format audio_delivery_format audio_format online_format to 1 if it does not have any value
      if (  entry.valProp === "alt_version_pdf_require" && entry.formControlRef.value === '1'){
        this.altVersionFormEntries.alt_version_online_require.formControlRef.setValue(undefined, { emitEvent: false })
        //const pdf_format_fc = this.altVersionFormEntries.pdf_format.formControlRef
        //if(!(+pdf_format_fc.value) || +pdf_format_fc.value == 0){
          //pdf_format_fc.setValue(pdfFormatDefaultValue, { emitEvent: false })
        //}
        //const audio_delivery_format_fc = this.altVersionFormEntries.audio_delivery_format.formControlRef
        //if(!(+audio_delivery_format_fc.value) || +audio_delivery_format_fc.value == 0){
          //audio_delivery_format_fc.setValue(audioDeliveryFormatDefaultValue, { emitEvent: false })
        //}
        //const audio_format_fc = this.altVersionFormEntries.audio_format.formControlRef
        //if(!(+audio_format_fc.value) || +audio_format_fc.value == 0){
          //audio_format_fc.setValue(audioFormatDefaultValue, { emitEvent: false })
        //}
      }
  
      // if online require is set to no, set audio_format to 0
      if (  entry.valProp === "alt_version_online_require" && entry.formControlRef.value === '#'){
        this.altVersionFormEntries.audio_delivery_format.formControlRef.setValue("0", { emitEvent: false })
        this.altVersionFormEntries.audio_format.formControlRef.setValue("0", { emitEvent: false })
      }
  
      // if online require is set to 1, set audio_format  to 1 if it does not have any value
      if (  entry.valProp === "alt_version_online_require" && entry.formControlRef.value === '1'){
        this.altVersionFormEntries.audio_delivery_format.formControlRef.setValue("9", { emitEvent: false })  //online format = e-assessment foramt
        //const audio_format_fc = this.altVersionFormEntries.audio_format.formControlRef
        //if(!(+audio_format_fc.value) || +audio_format_fc.value == 0){
          //audio_format_fc.setValue(audioFormatDefaultValue, { emitEvent: false })
        //}
      }
  
      //If in OSSLT, set audio format to 0 / none
      if (this.isOSSLT){
        this.altVersionFormEntries.audio_format.formControlRef.setValue("0", { emitEvent: false })
      }
    }

    closeStudentPersonalModal(){
      this.requestAltVersionAcknowledge = false;
      this.pageModal.closeModal();
    }

    confirmAcknowledge(){
      this.requestAltVersionAcknowledge = !this.requestAltVersionAcknowledge
    }




  
}
