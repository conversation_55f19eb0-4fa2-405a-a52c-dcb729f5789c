<div class="page-body" *ngIf="mode != DisplayMode.STUDENT">
    <header [breadcrumbPath]="breadcrumb" [hasSidebar]="true"></header>
    <button hidden id="simulate_irt" (click)="customControl_yes()">This should be hidden</button>
    <button hidden id="unsimulate_irt" (click)="customControl_no()">This should be hidden</button>
    <div class="page-content">
        <div class="sa-accounts-students">
            <bc-header-layout [tra]="'sa_sa_students'"
                [imgSrc]="'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/504237/authoring/students/*************/students.png'"
                [accountType]="accountType" (school)="onSelectedSchoolChange($event)"
                (district)="onSelectedDistrictChange($event)" (testWindow)="onSelectedTestWindowChange($event)">
            </bc-header-layout>

            <ng-container *ngIf="mode">
                <ng-container *ngIf="mode == DisplayMode.OVERVIEW">
                    <div class="grade-selector">
                        <select [(ngModel)]="selectedGrade" (change)="onSelectedGradeChange($event)"
                            [disabled]="isLoading">
                            <option *ngFor="let grade of gradeOptions" [ngValue]="grade">
                                <tra-md [slug]="grade.caption"></tra-md>
                            </option>
                        </select>
                    </div>

                    <div class="table-section">
                        <table class="table is-width-auto">
                            <tr>
                                <th>
                                    <tra slug="sa_sa_actions_list"></tra>
                                </th>
                                <th>
                                    <tra slug="sa_sa_actions_count"></tra>
                                </th>
                                <th>
                                    <tra slug="sa_sa_actions_action"></tra>
                                </th>
                                <th>
                                    <tra slug="sa_sa_actions_export"></tra>
                                </th>
                            </tr>
                            <tr>
                                <td>
                                    <a (click)="goToList(getListLink(StudentList.ENROLLED))">
                                        <tra slug="sa_sa_enrolled_list"></tra>
                                    </a>
                                </td>
                                <td>{{enrolledCount}}</td>
                                <td>
                                    <div>
                                        <button class="enroll-new-student-button" (click)="enrollNewStudentClicked()">
                                            <tra slug="sa_sa_enroll_new"></tra>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <bc-export-button [disabled]="isLoading" (export)="export(StudentList.ENROLLED)">
                                    </bc-export-button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a (click)="goToList(getListLink(StudentList.EXEMPTED))">
                                        <tra slug="sa_sa_exempted_list"></tra>
                                    </a>
                                </td>
                                <td>{{exemptedCount}}</td>
                                <td>
                                    <div >
                                        <button *ngIf="!isDistAdmin()" class="exempt-student-button" (click)="exemptStudentClicked()">
                                            <tra slug="sa_sa_exempt"></tra>
                                        </button>
                                    </div>
                                </td>
                                <td>
                                    <bc-export-button *ngIf="false" [disabled]="isLoading" (export)="export(StudentList.EXEMPTED)">
                                    </bc-export-button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <a (click)="goToList(getListLink(StudentList.NO_LONGER_ENROLLED))">
                                        <tra slug="sa_sa_no_more_enrolled_list"></tra>
                                    </a>
                                </td>
                                <td>{{unenrolledCount}}</td>
                                <td>
                                    <div>
                                        <button *ngIf="!isDistAdmin()" class="unenroll-student-button" (click)="unenrollStudentClicked()">
                                            <tra slug="sa_sa_unenroll"></tra>
                                        </button>
                                    </div>
                                </td>
                                <td  >
                                    <bc-export-button *ngIf="false" [disabled]="isLoading"
                                        (export)="export(StudentList.NO_LONGER_ENROLLED)"></bc-export-button>
                                </td>
                            </tr>
                        </table>
                    </div>
                </ng-container>
                <ng-container *ngIf="mode != DisplayMode.OVERVIEW" [ngSwitch]="mode">
                    <div *ngIf="doesModeNeedStudentListDropDown(mode)" class="list-header">
                        <div class="list-selector">
                            <select [(ngModel)]="selectedList" (change)="onSelectedListChange($event)"
                                [disabled]="isLoading">
                                <option *ngFor="let list of studentLists" [ngValue]="list">
                                    {{getListDisplay(list)}}
                                </option>
                            </select>
                        </div>
                        <a (click)="backToOptions()">
                            <tra slug="sa_sa_back_to_options"></tra>
                        </a>
                    </div>

                    <ng-container *ngSwitchCase="DisplayMode.ENROLLED">
                        <sa-accounts-students-enrolled 
                            *ngIf="!isLoading && districtDetail && selectedTestWindow"
                            [schoolDetail]="schoolDetail" [districtDetail]="districtDetail"
                            [testWindow]="selectedTestWindow" [accountType]="accountType"
                            [isDistAdmin]="isDistAdmin()"
                        ></sa-accounts-students-enrolled>
                    </ng-container>

                    <ng-container *ngSwitchCase="DisplayMode.EXEMPTED">
                        <sa-accounts-students-exempted 
                            *ngIf="!isLoading && districtDetail && selectedTestWindow"
                            [schoolDetail]="schoolDetail" [districtDetail]="districtDetail"
                            [testWindow]="selectedTestWindow"
                            [isDistAdmin]="isDistAdmin()"
                        ></sa-accounts-students-exempted>
                    </ng-container>

                    <ng-container *ngSwitchCase="DisplayMode.UNENROLLED">
                        <sa-accounts-students-unenrolled 
                            *ngIf="!isLoading && districtDetail && selectedTestWindow"
                            [schoolDetail]="schoolDetail" [districtDetail]="districtDetail"
                            [testWindow]="selectedTestWindow"
                            [isDistAdmin]="isDistAdmin()"
                        ></sa-accounts-students-unenrolled>
                    </ng-container>

                    <ng-container *ngIf="mode === DisplayMode.ENROLL">
                        <div *ngIf="!schoolDetail">
                            <tra slug="sa_sa_select_school"></tra>
                        </div>
                        <sa-accounts-students-enroll 
                            *ngIf="schoolDetail && selectedTestWindow"
                            [schoolDetail]="schoolDetail" [testWindow]="selectedTestWindow"
                        ></sa-accounts-students-enroll>
                    </ng-container>

                    <ng-container *ngIf="mode === DisplayMode.EXEMPT">
                        <sa-accounts-students-exempt 
                            *ngIf="selectedTestWindow" [testWindow]="selectedTestWindow"
                            [districtDetail]="districtDetail"
                        >
                        </sa-accounts-students-exempt>
                    </ng-container>

                    <ng-container *ngIf="mode === DisplayMode.UNENROLL">
                        <sa-accounts-students-unenroll 
                            *ngIf="selectedTestWindow" [testWindow]="selectedTestWindow"
                            [districtDetail]="districtDetail"
                        ></sa-accounts-students-unenroll>
                    </ng-container>

                    <!-- <ng-container *ngIf="mode === DisplayMode.STUDENT">
                        <sa-student-details [testWindow]="selectedTestWindow" [schoolDetail]="schoolDetail">
                        </sa-student-details>
                    </ng-container> -->

                </ng-container>
            </ng-container>
        </div>
    </div>

</div>

<div *ngIf="mode == DisplayMode.STUDENT">
    <bc-header-layout [phantom]="true" [tra]="'sa_sa_students'"
        [imgSrc]="'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/504237/authoring/students/*************/students.png'"
        [accountType]="accountType" (school)="onSelectedSchoolChange($event)"
        (district)="onSelectedDistrictChange($event)" (testWindow)="onSelectedTestWindowChange($event)">
    </bc-header-layout>
    <sa-student-details *ngIf="selectedTestWindow" [testWindow]="selectedTestWindow" [accountType]="accountType">
    </sa-student-details>
</div>