#!/bin/bash

# Description:  Given an autoscaling group name, this script will check the AWS scaling group for an existing Standby
#               instance matching a prefix given to API instances that are intended for testing API builds on.
#
#               If an existing Standby instance isn't found in the ASG, this script will deploy one, wait until it is
#               healthy, and then proceed to place the instance in Standby. If the instance already exists, this script
#               will exit gracefully. It handles deployment by adjusting the Desired Count parameter of the ASG.
#
#               The purpose of this is to have a Standby API instance within ASGs that can readily be available for
#               testing API builds on safely in the same environment live APIs are running.

# Define named parameters that can optionally be passed from commandline to this script

api_asg_nm=${CI_API_ASG_NM:-""}     # Name of AWS ASG to query for running instances; default value is CI_API_ASG_NM

while [ $# -gt 0 ]; do

   if [[ $1 == *"--"* ]]; then
        param="${1/--/}"
        declare $param="$2"
        # echo $1 $2 // Optional to see the parameter:value result
   fi

  shift
done

# Exit script if any args are not defined

if [[ -z $api_asg_nm ]]; then

  echo -e "\nOne or more arguments missing when calling script! Terminating pipeline."
  exit 1

fi

# Configure name of the new test instance here

date=$(date '+%Y-%m-%d-H%H:M%m:S%S')
test_instance_prefix="$CI_API_ASG_NM-api-build-test"
tag_key="Name"
tag_value="$test_instance_prefix-$date"

# First query for existing test instance. We don't need to add another to the ASG if one doesn't already exist.
# Adding a new instance and waiting for it to fully deploy can take ~10 minutes.
# We query by "running" instances to omit those that may have recently been terminated

echo -e "\nChecking for existing test API instance with prefix for name matching $test_instance_prefix..."

test_api_instance_id=$(echo $(aws ec2 describe-instances \
--filters "Name=tag:Name,Values=$test_instance_prefix*" "Name=instance-state-name,Values=running" \
--query "Reservations[*].Instances[*].InstanceId" \
--output text) | xargs)

if [ "$test_api_instance_id" != "" ]; then

  echo -e "\nExisting Standby instance found in $api_asg_nm ASG with name matching prefix $test_instance_prefix"
  echo -e "\nInstance ID is $test_api_instance_id and will be used for validating API build."

else

  # If existing Standby instance was not found for the ASG, we need to deploy one before moving on

  echo -e "\nExisting test instance not found. Deploying new test API instance to the $api_asg_nm ASG..."
  echo -e "\nThis will take roughly 10 minutes to complete..."

  # Store current desired capacity of the ASG so we can add 1 onto this and deploy a new instance
  # Echoing to xargs removes whitespace around CLI return output and avoids interpretation errors later (typ.)

  asg_desired_capacity=$(echo $(aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names $api_asg_nm \
  --query "AutoScalingGroups[*].DesiredCapacity" --output text) | xargs)

  new_desired_capacity=$((asg_desired_capacity + 1))

  echo -e "\nCurrent count of API instances within $api_asg_nm is $asg_desired_capacity."
  echo -e "\nIncreasing this to $new_desired_capacity for testing API build"

  # Update ASG count by 1

  aws autoscaling update-auto-scaling-group \
  --auto-scaling-group-name "$api_asg_nm" \
  --desired-capacity $new_desired_capacity

  # Obtain InstanceId of new instance added to the ASG;
  #   allow up to 10 minutes to pull this information

  timeout=240
  desired_output_returned="false"

  echo -e "Retrieving instance ID of new ASG instance. Timing out after $timeout seconds..."
  
  while [ $desired_output_returned == "false" ]; do

    test_api_instance_id=$(echo $(aws autoscaling describe-auto-scaling-groups \
    --auto-scaling-group-names "$api_asg_nm" \
    --query "AutoScalingGroups[*].Instances[?starts_with(LifecycleState,'Pending')].InstanceId" \
    --output text) | xargs)

    if [[ "$test_api_instance_id" != ""  ]]; then

      desired_output_returned="true"

    else

      timeout=$((timeout - 10))
      sleep 10

      if [ $timeout == 0 ]; then

        echo -e "\nTimeout after $timeout seconds for attempting to launch new instance in the ASG. Terminating script"
        exit 1

      fi

    fi

  done

  echo -e "\nNew instance ID is $test_api_instance_id. Allowing system to pass status checks before proceeding..."

  # Allow instance to passs both status checks before doing anything with it

  aws ec2 wait instance-status-ok --instance-ids "$test_api_instance_id"

  # Tag the instance with name to make it obvious it is the one that is being tested on

  echo -e "\nTest instance $test_api_instance_id passed both status checks. Tagging instance..."

  aws ec2 create-tags \
  --resources "$test_api_instance_id" \
  --tags Key="$tag_key",Value="$tag_value"

  echo -e "\nInstance tagged with the following name: $tag_value. You can find this in the $api_asg_nm ASG..."

  echo -e "\nTest instance $test_api_instance_id passed both status checks. Tagging instance..."

  aws ec2 create-tags \
  --resources "$test_api_instance_id" \
  --tags Key="$tag_key",Value="$tag_value"

  echo -e "\nInstance tagged with the following name: $tag_value. You can find this in the $api_asg_nm ASG..."

  # New Section: Ensure instance is in 'InService' state before proceeding
  # Retry logic to check instance state

  echo -e "\nEnsuring instance is in 'InService' state before placing in Standby..."

  in_service_retry_interval=10
  in_service_timeout=120
  while [ $in_service_timeout -gt 0 ]; do
      current_state=$(aws autoscaling describe-auto-scaling-groups \
      --auto-scaling-group-names "$api_asg_nm" \
      --query "AutoScalingGroups[*].Instances[?InstanceId == '$test_api_instance_id'].LifecycleState" \
      --output text | xargs)

      if [[ "$current_state" == "InService" ]]; then
          echo -e "\nInstance $test_api_instance_id is in 'InService'. Proceeding to place in Standby."
          break
      else
          echo -e "\nWaiting for instance to become 'InService'. Retrying in $in_service_retry_interval seconds..."
          sleep $in_service_retry_interval
          in_service_timeout=$((in_service_timeout - in_service_retry_interval))
      fi
  done

  if [ $in_service_timeout -le 0 ]; then
      echo -e "\nTimeout while waiting for the instance to be in 'InService'. Cannot proceed. Exiting."
      exit 1
  fi


  echo -e "\nPlacing instance in Standby..."

  aws autoscaling enter-standby \
  --instance-ids "$test_api_instance_id" \
  --auto-scaling-group-name "$api_asg_nm" \
  --should-decrement-desired-capacity

  # Allow instance to enter Standby before moving on (can likely make a function out of this and use it with the
  # above similar section to reduce some of the length of this script...)

  timeout=600
  desired_output_returned="false"

  echo -e "Waiting for instance to enter Standby. Timing out after $timeout seconds..."

  while [ $desired_output_returned == "false" ]; do

    in_standby=$(echo $(aws autoscaling describe-auto-scaling-groups \
    --auto-scaling-group-names "$api_asg_nm" \
    --query "AutoScalingGroups[*].Instances[?InstanceId == '$test_api_instance_id'].LifecycleState" \
    --output text) | xargs)

    if [[ "$in_standby" == "Standby"  ]]; then

      echo -e "\n$test_api_instance_id with name $tag_value is in Standby and ready for testing API build."
      desired_output_returned="true"

    else

      timeout=$((timeout - 10))
      sleep 10

      if [ $timeout == 0 ]; then

        echo -e "\nTimeout while awaiting the instance to enter Standby. Terminating script"
        exit 1

      fi

    fi

  done

fi

# Once instance is in Standby, output artifact containing InstanceId for the instance to be used for testing API build
# before deploying to InService (production) API instances in the ASG.
# This uses the current commit hash wihtin the filename to ensure a unique artifact is generated

echo -e "\nOutputting artifact with InstanceId for use in next stage of pipeline..."
echo "$test_api_instance_id" > $CI_PROJECT_DIR/$CI_COMMIT_SHA-test_api_instance_id
cat $CI_PROJECT_DIR/$CI_COMMIT_SHA-test_api_instance_id


