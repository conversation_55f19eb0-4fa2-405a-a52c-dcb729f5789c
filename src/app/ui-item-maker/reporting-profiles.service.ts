import { Injectable } from '@angular/core';
import { AuthService } from '../api/auth.service';
import { RoutesService } from '../api/routes.service';
import { IAuthGroupOption } from './view-im-group-access/model/types';
import { IReportingProfileNew, IReportingProfileView } from './view-im-reporting-profiles/model/types';

@Injectable({
  providedIn: 'root'
})
export class ReportingProfilesService {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
  ) { }

  ///////////////////
  ////// UTIL ///////
  ///////////////////

  /**
   * Convert an array of IAuthGroupOption to an array of group IDs
   */
  private mapAuthGroupIds(authGroupOptions: IAuthGroupOption[]) {
    return authGroupOptions.map(r => r.group_id);
  }

  ///////////////////
  ///// MODELS //////
  ///////////////////

  /**
   * REPORTING_PROFILES
   */
  async findReportingProfiles(authGroupOptions: IAuthGroupOption[]):Promise<IReportingProfileView[]> {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return await this.auth.apiFind(this.routes.TEST_AUTH_RP_REPORTING_PROFILES, {
      query: {
        authoring_group_ids
      }
    });
  }

  async createReportingProfile(authGroupId: number, payload: IReportingProfileNew) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_RP_REPORTING_PROFILES, payload);
  }
  
  async patchReportingProfile(authGroupId: number, id: number, payload: any) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_RP_REPORTING_PROFILES, id, payload);
  }

  /**
   * DOMAIN_SCHEMAS
   */
  async findDomainSchemas(rp_profile_id?:number, authGroupOptions?: IAuthGroupOption[]) {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return this.auth.apiFind(this.routes.TEST_AUTH_RP_DOMAIN_SCHEMAS, {
      query: {
        rp_profile_id,
        authoring_group_ids
      }
    });
  }

  async createDomainSchema(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_RP_DOMAIN_SCHEMAS, payload);
  }

  async patchDomainSchema(authGroupId: number, id: number, payload: any) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_RP_DOMAIN_SCHEMAS, id, payload);
  }

  /**
   * SCALING_FACTOR_PROFILES
   */
  async findScalingFactorProfiles(rp_profile_id?:number, authGroupOptions?: IAuthGroupOption[]) {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return this.auth.apiFind(this.routes.TEST_AUTH_RP_SCALING_FACTOR_PROFILES, {
      query: {
        rp_profile_id,
        authoring_group_ids
      }
    });
  }

  async createScalingFactorProfile(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_RP_SCALING_FACTOR_PROFILES, payload);
  }

  async patchScalingFactorProfile(authGroupId: number, id: number, payload: any) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_RP_SCALING_FACTOR_PROFILES, id, payload);
  }

  /**
   * CATEGORY_SCHEMAS
   */
  async findCategorySchemas(rp_profile_id?:number, authGroupOptions?: IAuthGroupOption[]) {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return this.auth.apiFind(this.routes.TEST_AUTH_RP_CATEGORY_SCHEMAS, {
      query: {
        rp_profile_id,
        authoring_group_ids
      }
    });
  }

  async createCategorySchema(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_RP_CATEGORY_SCHEMAS, payload);
  }

  async patchCategorySchema(authGroupId: number, id: number, payload: any) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_RP_CATEGORY_SCHEMAS, id, payload);
  }

  /**
   * CUT_SCORE_SCHEMAS
   */
  async findCutScoreSchemas(rp_profile_id?:number, authGroupOptions?: IAuthGroupOption[]) {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return this.auth.apiFind(this.routes.TEST_AUTH_RP_CUT_SCORE_SCHEMAS, {
      query: {
        rp_profile_id,
        authoring_group_ids
      }
    });
  }

  async createCutScoreSchema(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_RP_CUT_SCORE_SCHEMAS, payload);
  }

  async patchCutScoreSchema(authGroupId: number, id: number, payload: any) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_RP_CUT_SCORE_SCHEMAS, id, payload);
  }

  /**
   * CUT_SCORE_PROFILES
   */
  async findCutScoreProfiles(rp_profile_id?:number, authGroupOptions?: IAuthGroupOption[]) {
    const authoring_group_ids = this.mapAuthGroupIds(authGroupOptions);
    return this.auth.apiFind(this.routes.TEST_AUTH_RP_CUT_SCORE_PROFILES, {
      query: {
        rp_profile_id,
        authoring_group_ids
      }
    });
  }

  async createCutScoreProfile(authGroupId: number, payload: any) {
    return this.auth.apiCreate(this.routes.TEST_AUTH_RP_CUT_SCORE_PROFILES, payload);
  }

  async patchCutScoreProfile(authGroupId: number, id: number, payload: any) {
    return this.auth.apiPatch(this.routes.TEST_AUTH_RP_CUT_SCORE_PROFILES, id, payload);
  }

}
