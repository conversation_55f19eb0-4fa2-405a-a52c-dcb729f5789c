import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';
import logger from '../../../../logger';

interface Data {}

interface ServiceOptions {}

export class VerifyStudent implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: {studentLastNameInput:string, attemptId:number}, params?: Params): Promise<Data> {
    if (!data || !params) throw new Errors.BadRequest();
    const created_by_uid = await currentUid(this.app, params);
    const log_data = {attemptId: data.attemptId, studentLastNameInput: data.studentLastNameInput};
    let log_entry = {
      created_by_uid,
      slug: 'STUDENT_LOGIN_VERIFICATION',
      data: JSON.stringify(log_data)
    }
    logger.info(log_entry);
    return {};
    // return await this.app.service('db/write/log').create({slug, created_by_uid,  data: JSON.stringify(logData)})
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }
}
