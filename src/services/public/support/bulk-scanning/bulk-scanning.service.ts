// Initializes the `public/support/bulk-scanning` service on path `/public/support/bulk-scanning`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { BulkScanning } from './bulk-scanning.class';
import hooks from './bulk-scanning.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/support/bulk-scanning': BulkScanning & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/support/bulk-scanning', new BulkScanning(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/support/bulk-scanning');

  service.hooks(hooks);
}
