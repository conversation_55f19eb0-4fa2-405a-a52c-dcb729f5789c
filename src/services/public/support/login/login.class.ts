import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { generateSecretCode } from '../../../../util/secret-codes';

interface Data {
  target_uid: number
}

interface ServiceOptions {}

export class Login implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Error();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (params){
      const created_by_uid = await currentUid(this.app, params);
      const {target_uid} = data;
      const secret = generateSecretCode(42);
      return this.app.service('db/write/auth-as').create({
        created_by_uid,
        target_uid,
        secret
      })
    }
    throw new Error();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error();
  }
}
