import { Component, Input, OnInit } from '@angular/core';
import { LangService } from 'src/app/core/lang.service';
import { formatDate } from '@angular/common';
import { mtz } from 'src/app/core/util/moment';

@Component({
  selector: 'sa-modal-view-invoice',
  templateUrl: './sa-modal-view-invoice.component.html',
  styleUrls: ['./sa-modal-view-invoice.component.scss']
})
export class SaModalViewInvoiceComponent implements OnInit {
  @Input() set config(cnf: {purchasesByInvoiceNumber: any[], studentsByInvoiceNumber: any[]}) {
    this.processData(cnf.purchasesByInvoiceNumber, cnf.studentsByInvoiceNumber);
  }

  porcessedList: state[] = [];

  constructor(
    public lang: LangService
  ) { }

  ngOnInit(): void {}

  private processData(purchasesByInvoiceNumber: any[], studentsByInvoiceNumber: any[]): void {
    this.porcessedList = [];
    purchasesByInvoiceNumber?.forEach(purchases => {
      const state: state = {
        invoice_number: null,
        purchase_trans_num: null,
        schl_name: null,
        schl_mident: null,
        assessment_name: null,
        admin_window: null,
        purchase_method: null,
        class_name: [],
        total_amount: 0,
        num_student_attempts_purchased: 0,
        student_attempts: [],
        is_refunded: 0,
        recovery_value: null,
        refunded_student_count: 0
      };

      // Each transaction data
      purchases.forEach((purchase: Object) => {
        if (state.invoice_number === null) {
          state.invoice_number = purchase['invoice_number'];
        }
        state.total_amount += Number(purchase['purchase_price']);
        state.class_name.push(purchase['class_name']);
        state.purchase_trans_num = purchase['purchase_trans_num'];
        state.num_student_attempts_purchased = purchase['num_student_attempts_purchased'];
        state.purchase_method = purchase['purchase_method'];
        state.admin_window = this.getTestWindowViewText(purchase['test_window_start_date'], purchase['test_window_end_date']);
        state.assessment_name = this.getOperationalLabel(purchase['assessment_name']);
        state.schl_name = purchase['schl_name'];
        state.schl_mident = purchase['schl_mident'];
        state.is_refunded = purchase['is_refunded'];
        state.recovery_value = purchase['recovery_value'];
        state.refunded_student_count = purchase['refunded_student_count'];
      });

      // Students associated with this invoice
      const invoiceStudentPurchases = studentsByInvoiceNumber[state.invoice_number.toString()];
      if (invoiceStudentPurchases !== null && invoiceStudentPurchases !== undefined) {
        invoiceStudentPurchases.forEach(s => {
          const index = state.student_attempts.findIndex(x => x.student_oen === s.student_oen);
          if (index === -1) {
            state.student_attempts.push({
              student_first_initial: s['student_first_initial'],
              student_last_name: s['student_last_name'],
              student_oen: s['student_oen'],
              payment_status: s['payment_status']
            });
          }
        });
      }
      this.porcessedList.push(state);
    });
  }

  isEnglish(){
    return (this.lang.c() === 'en')
  }

  convertTotalAmount(totalAmount:string){
    return "$" + Number(totalAmount).toFixed(2);
  }

  getOperationalLabel(assessmentName:string) {
    switch(assessmentName) {
      case 'EQAO_G3':
        return this.lang.tra('lbl_primary_asmt');
      case 'EQAO_G6':
        return this.lang.tra('lbl_junior_asmt');
      case 'EQAO_G9':
        return this.lang.tra('g9_assess_math');
      case 'EQAO_G10':
        return this.lang.tra('lbl_osslt_test');
    }
  }


  getTestWindowViewText(startDate:string, endDate:string){
    if(startDate && endDate){
      const start_date = formatDate(new Date(startDate), 'MMM yyyy', 'en_US')
      const end_date = formatDate(new Date(endDate), 'MMM yyyy', 'en_US')
      const isActive = (new Date(endDate) > new Date ())? "lbl_active":"lbl_inactive"
      return `${start_date} ${this.lang.tra("lbl_date_to")} ${end_date} (${this.lang.tra(isActive)})`;
    } 
  }

  getCurrentDate(){
    // return mtz(new Date()).format(this.lang.tra('datefmt_day_month_year'));
    return new Date().toString();
  }

  printInvoice(): void{
    const printBody: string[] = [];
    const css = `
    @media print {
      body {
        padding: 0.5rem;
      }
      div {
        width: 100%;
      }
      table {
          border-collapse: collapse;
          border-spacing: 0;
          width: 100%;
        }
        th {
          border: 1px solid #000000 !important;
          background-color: #d3d3d3 !important;
          color: #000000  !important;
          text-align: center;
          -webkit-print-color-adjust: exact; 
          padding: 0.5em 0.75em;
          vertical-align: top;
        }
        th.limit-width {
          text-align: left;
          max-width: 5rem;
        }
        td {
            border: 1px solid #000000 !important;
            -webkit-print-color-adjust: exact; 
            padding: 0.5em 0.75em;
            vertical-align: top;
        }
        .no-padding {
            padding: 0;
        }
        .students-container-flex {
          display: flex;
        }
        .students-container-flex:last-child .dynamic-col {
          border-bottom: none;
        }
        .student-header {
            font-weight: bold;
            text-align: left;
        }
        .dynamic-col {
            padding: 0.5rem;
            flex: 1;
            text-align: left;
            border-bottom: 1px solid black;
            -webkit-print-color-adjust: exact; 
        }
      
        .dynamic-col:first-child {
          border-right: 1px solid #000000;
          -webkit-print-color-adjust: exact; 
        }
        .dynamic-col:first-child {
          border-right: 1px solid #000000;
          -webkit-print-color-adjust: exact; 
        }
        .pagebreak {
          clear: both;
          page-break-after: always;
        }
    }
      `;
    const invoiceTitle = document.getElementById('invoice-title');
    // const invoiceDate = document.getElementById('invoice-date');
    for(let i=0; i<this.porcessedList.length; i++){
      const id = `print-container-${i}`;
      const printContent = document.getElementById(id);
      if (printContent === null)
        throw `${id} not found!`;
      printBody.push(`<div><br/></div>${invoiceTitle.innerHTML}<div><br/></div>${printContent.innerHTML}`);
    }

    const WindowPrt = window.open('', '', 'left=0,top=0,width=900,height=900,toolbar=0,scrollbars=0,status=0');
      WindowPrt.document.write('<html><head>');
      WindowPrt.document.write(`<style>${css} .style-title{margin-left:2.75rem;} .style-margin{margin-left:1.25rem;} .style-response{margin-left:3.5rem;}</style>`);
      WindowPrt.document.write('</head><body>');
      WindowPrt.document.write(printBody.join('<div class="pagebreak"> </div>'));
      WindowPrt.document.write('</body></html>');
      WindowPrt.document.close();
      WindowPrt.focus();
      WindowPrt.print();
      WindowPrt.close();
  }
}

interface state {
  invoice_number: number;
  purchase_trans_num: string;
  schl_name: string;
  assessment_name: string;
  admin_window: string;
  purchase_method: string;
  class_name: string[];
  total_amount: number;
  num_student_attempts_purchased: number;
  student_attempts: studentAttempt[];
  schl_mident: string;
  is_refunded: number,
  recovery_value: string,
  refunded_student_count: number
};
interface studentAttempt {
  student_first_initial: string;
  student_last_name: string;
  student_oen: string;
  payment_status: string;
}