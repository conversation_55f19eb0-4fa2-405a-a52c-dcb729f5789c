export const SCOR_STATUSES = {
    S_0_0:    '0.0) Access Revoked',
    S_1_0:    '1.0) Invited',
    S_1_1:    '1.1) Ready to Score', // '1.1) Training Materials', //todo:generalize
    S_1_2:    '1.2) Completing PT',
    S_1_3:    '1.3) Completing QT',
    S_1_3_2:  '1.3.2) Completing QT (2nd time)',
    S_1_3_3:  '1.3.3) Failed QT',
    S_1_4:    '1.4) Claiming Batches',
    S_1_5:    '1.5) Scoring',
    S_1_6:    '1.6) No Batches Avail.',
    S_2_1:    '2.1) Rolling Validity Fail (1st time)',
    S_2_2:    '2.2) Completing PT (re-train)',
    S_2_3:    '2.3) Completing QT (re-train)',
    S_2_3_2:  '2.3.2) Failed QT (re-train)',
    S_2_4:    '2.4) Claiming Batches',
    S_2_5:    '2.5) Scoring',
    S_2_6:    '2.6) No Batches Avail.',
    S_3_1:    '3.1) Rolling Validity Fail (2nd time)',
}

// export const SCOR_BLOCKED_STATUSES = {
//     ROL_VAL_FAIL_2: 'Account Activation Link Expired',
//     ROL_VAL_FAIL_2: 'Training not completed on time',
//     ROL_VAL_FAIL_2: 'Re-training not completed on time',
//     ROL_VAL_FAIL_2: 'Not meeting weekly batch expectations',
//     ROL_VAL_FAIL_2: 'Stat Holiday',
//     ROL_VAL_FAIL_2: 'Batch expired too many times',
//     ROL_VAL_FAIL_2: 'Item is fully scored',
//     ROL_VAL_FAIL_2: 'Converstation required with scorer before proceeding',
//     ROL_VAL_FAIL_2: 'Failed Qualification Test (1st time)',
//     ROL_VAL_FAIL_2: 'Failed Qualification Test (2nd time)',
//     ROL_VAL_FAIL_2: 'Failed Qualification Test (during retraining)',
//     ROL_VAL_FAIL_2: 'No batches available (could be because of max batch % or no items to score)',
//     ROL_VAL_FAIL_2: '48 hour weekly time limit reached',
//     ROL_VAL_FAIL_2: 'B1) Rolling Validity Fail - First Time',
//     ROL_VAL_FAIL_2: 'C1) Rolling Validity Fail - Second Time',
// }