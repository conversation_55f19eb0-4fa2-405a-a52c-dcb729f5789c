<div *ngIf="isSimpleView">
  <div style="display: flex; justify-content: flex-end;">
    <mat-slide-toggle (change)="toggleLeftHand($event)">
      Show previous text?
    </mat-slide-toggle>
  </div>
  <div *ngIf="formattedDataText" style="display: flex; flex-direction: column; gap: 1em;" >
    <div *ngIf="formattedDataText.answerKeys.length > 0">
      <twiddle style="width: 100%;" caption="Answer Keys" (change)="setTwiddleToggleInfo('answerKeys', $event)"></twiddle>
      <ul *ngIf="getTwiddleToggleInfo('answerKeys')">
        <li class="diff-listing" *ngFor="let diff of formattedDataText.answerKeys"><div [innerHTML]="diff"></div></li>
      </ul>
    </div>
    <div *ngIf="formattedDataText.layout.length > 0">
      <twiddle style="width: 100%;" caption="Layout" (change)="setTwiddleToggleInfo('layout', $event)"></twiddle>
      <ul *ngIf="getTwiddleToggleInfo('layout')">
        <li class="diff-listing" *ngFor="let diff of formattedDataText.layout"><div [innerHTML]="diff"></div></li>
      </ul>
    </div>
    <div *ngIf="formattedDataText.voiceover.length > 0">
      <twiddle style="width: 100%;" caption="Voiceover" (change)="setTwiddleToggleInfo('voiceover', $event)"></twiddle>
      <ul *ngIf="getTwiddleToggleInfo('voiceover')">
        <li class="diff-listing" *ngFor="let diff of formattedDataText.voiceover"><div [innerHTML]="diff"></div></li>
      </ul>
    </div>
    <div *ngIf="formattedDataText.responseKeys.length > 0">
      <twiddle style="width: 100%;" caption="Response Keys" (change)="setTwiddleToggleInfo('responseKeys', $event)"></twiddle>
      <ul *ngIf="getTwiddleToggleInfo('responseKeys')">
        <li class="diff-listing" *ngFor="let diff of formattedDataText.responseKeys"><div [innerHTML]="diff"></div></li>
      </ul>
    </div>
    <div *ngIf="formattedDataText.other.length > 0">
      <twiddle style="width: 100%;" caption="Other" (change)="setTwiddleToggleInfo('other', $event)"></twiddle>
      <ul *ngIf="getTwiddleToggleInfo('other')">
        <li class="diff-listing" *ngFor="let diff of formattedDataText.other"><div [innerHTML]="diff"></div></li>
      </ul>
    </div>
  </div>
  <div *ngIf="currentContent.last_updated_on" style="display: flex; justify-content: flex-end;">
    <i>Last updated on: {{currentContent.last_updated_on}}</i>
  </div>
</div>

<mat-accordion *ngIf="!isSimpleView && !isOnlyOther()" [class.size-minimize]="isNgContent">
  <mat-expansion-panel [ngStyle]="style || {}" [expanded]="isExpanded" (opened)="panelOpened()" (closed)="panelClosed()">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <!-- <a (click)="selectQuestion(title)" style="font-weight: bold;">{{title}}</a> -->
        <div class="panel-title" style="font-weight: bold;">{{questionLabel}} ({{lang}})</div>
        <!-- <div class="panel-subtitle" *ngIf="subTitle" style="margin-left: 0.5em;">({{subTitle}})</div> -->
      </mat-panel-title>
      <mat-panel-description>
        {{description}}
      </mat-panel-description>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <ng-container *ngIf="!isNgContent">
        <div *ngIf="isItemDeleted"><code>Item deleted</code></div>
        <ng-container *ngIf="!isItemDeleted">
          <section *ngFor="let c of content" class="content-block">
            <ng-container [ngSwitch]="c.type">
              <div *ngSwitchCase="ExpansionPanelContentType.JSON_VIEW">
                <!-- <label class="content-label">{{c.label}} </label> -->
                <div style="display: flex; justify-content: space-between;">
                  <a (click)="selectQuestion(questionId)" style="font-weight: bold;">Go to item</a>
                  <mat-slide-toggle (change)="toggleLeftHand($event)">
                    Show previous text?
                  </mat-slide-toggle>
                </div>
                <div style="display: flex; flex-direction: column; gap: 1em;" *ngIf="formattedDataText">
                  <div *ngIf="formattedDataText.answerKeys.length > 0">
                    <twiddle style="width: 100%;" caption="Answer Keys" (change)="setTwiddleToggleInfo('answerKeys', $event)"></twiddle>
                    <ul *ngIf="getTwiddleToggleInfo('answerKeys')">
                      <li class="diff-listing" *ngFor="let diff of formattedDataText.answerKeys"><div [innerHTML]="diff"></div></li>
                    </ul>
                  </div>
                  <div *ngIf="formattedDataText.layout.length > 0">
                    <twiddle style="width: 100%;" caption="Layout" (change)="setTwiddleToggleInfo('layout', $event)"></twiddle>
                    <ul *ngIf="getTwiddleToggleInfo('layout')">
                      <li class="diff-listing" *ngFor="let diff of formattedDataText.layout"><div [innerHTML]="diff"></div></li>
                    </ul>
                  </div>
                  <div *ngIf="formattedDataText.voiceover.length > 0">
                    <twiddle style="width: 100%;" caption="Voiceover" (change)="setTwiddleToggleInfo('voiceover', $event)"></twiddle>
                    <ul *ngIf="getTwiddleToggleInfo('voiceover')">
                      <li class="diff-listing" *ngFor="let diff of formattedDataText.voiceover"><div [innerHTML]="diff"></div></li>
                    </ul>
                  </div>
                  <div *ngIf="formattedDataText.responseKeys.length > 0">
                    <twiddle style="width: 100%;" caption="Response Keys" (change)="setTwiddleToggleInfo('responseKeys', $event)"></twiddle>
                    <ul *ngIf="getTwiddleToggleInfo('responseKeys')">
                      <li class="diff-listing" *ngFor="let diff of formattedDataText.responseKeys"><div [innerHTML]="diff"></div></li>
                    </ul>
                  </div>
                  <div *ngIf="formattedDataText.other.length > 0">
                    <twiddle style="width: 100%;" caption="Other" (change)="setTwiddleToggleInfo('other', $event)"></twiddle>
                    <ul *ngIf="getTwiddleToggleInfo('other')">
                      <li class="diff-listing" *ngFor="let diff of formattedDataText.other"><div [innerHTML]="diff"></div></li>
                    </ul>
                  </div>
                  <div *ngIf="
                    !formattedDataText.answerKeys.length &&
                    !formattedDataText.layout.length && 
                    !formattedDataText.voiceover.length && 
                    !formattedDataText.responseKeys.length && 
                    !formattedDataText.other.length">
                    No changes.
                  </div>
                </div>
              </div>
              <div *ngSwitchCase="ExpansionPanelContentType.TEXT_AREA">
                <div class="flex-gap">
                  <span class="text-area-middle">
                    <label class="content-label">{{c.label}} </label>
                    <textarea style="margin-left:0.5em" [(ngModel)]="c.data" [readonly]="readOnly"></textarea>
                  </span>
                </div>
              </div>
              <div *ngSwitchCase="ExpansionPanelContentType.SPAN_LIST">
                <label class="content-label">{{c.label}} </label>
                <div>
                  <ng-container *ngFor="let span of c.data">
                    <span [ngStyle]="span.style">{{ span.text }}</span>
                  </ng-container>
                </div>
              </div>
              <div *ngSwitchCase="ExpansionPanelContentType.TEXT_LIST">
                <label class="content-label">{{c.label}} </label>
                <div>
                  <ng-container *ngFor="let text of c.data">
                    <p [ngStyle]="text.style">{{ text.text }}</p>
                  </ng-container>
                </div>
              </div>
            </ng-container>
          </section>
          <div *ngIf="currentContent.last_updated_on" style="display: flex; justify-content: flex-end;">
            <i>Last updated on: {{currentContent.last_updated_on}}</i>
          </div>
        </ng-container>
      </ng-container>

      <ng-container *ngIf="isNgContent">
        <ng-content></ng-content>
      </ng-container>

    </ng-template>
  </mat-expansion-panel>
</mat-accordion>