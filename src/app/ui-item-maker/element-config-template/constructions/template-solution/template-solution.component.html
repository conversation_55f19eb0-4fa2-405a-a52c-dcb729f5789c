<fieldset>
  <ng-template #innerElement let-contentElement="contentElement" let-index="index">
    <div [ngSwitch]="getElementType(contentElement)">
      <div *ngSwitchCase="'text'">
        <textarea 
            textInputTransform
            [(ngModel)]="contentElement['caption']"
            cdkTextareaAutosize 
            [cdkTextareaAutosize]="true" 
            [cdkAutosizeMinRows]="2"
            class="textarea"
        ></textarea>
      </div>
      <div *ngSwitchCase="'image'">
        <capture-image [element]="contentElement" fileType="image" [isNoScale]="false" [displayImageControls]="false"></capture-image>
        <asset-library-link [element]="contentElement"></asset-library-link>
      </div>
      <div *ngSwitchCase="'math'">
        <div class="capture-math-container">
            <capture-math [obj]="contentElement" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
        </div>
      </div>
      <div *ngSwitchCase="'table'">
        <template-table
            [elementRef]="elementRef + '.content.0'"
            [tableElement]="contentElement"
            [twiddleToggleMap]="twiddleToggleMap"
            [isNonInteractive]="true"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
            (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
        ></template-table>
      </div>
      <div *ngSwitchCase="'virtual_tools'">
        <element-config [contentElement]="contentElement"></element-config>
      </div>
      <div *ngSwitchCase="'text:advanced_inline'">
        <template-advanced-inline
            [isStaticElementOnly]="true"
            [allowBookmarkLink]="false"
            [advancedList]="contentElement['advancedList']"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
        ></template-advanced-inline>
      </div>
      <div *ngSwitchCase="'text:bullet'">
        <template-advanced-inline
            [isStaticElementOnly]="true"
            [allowBookmarkLink]="false"
            [advancedList]="contentElement['advancedList']"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
        ></template-advanced-inline>
      </div>
      <div *ngSwitchCase="'text:numbered'">
        <template-advanced-inline
            [isStaticElementOnly]="true"
            [allowBookmarkLink]="false"
            [advancedList]="contentElement['advancedList']"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
        ></template-advanced-inline>
      </div>
      <div *ngSwitchDefault>
        NOT IMPLEMENTED
      </div>
    </div>
  </ng-template>
  
  <div>
    <div cdkDropList (cdkDropListDropped)="dropContent($event)">
      <div *ngFor="let contentElement of element.content; let i = index" cdkDrag>
        <div class="nested-element">
          <div class="nested-element-header" style="font-size: 0.8em;" cdkDragHandle>
            <button (click)="contentElement._isCollapsed = !contentElement._isCollapsed" class="button is-small">
                <i class="fa" [ngClass]="elementIcons[getElementType(contentElement)]" aria-hidden="true"></i>
            </button>
            <a [class.is-disabled]="isReadOnly()" class="button is-small" (click)="removeElementEmit(element.content, contentElement);" >
              <i class="fas fa-trash"></i>
            </a>                  
          </div>
          <div class="nested-element-content" *ngIf="!contentElement._isCollapsed">
            <ng-container *ngTemplateOutlet="innerElement; context: {contentElement: contentElement, index: i}"></ng-container>
          </div>
        </div>
      </div>
    </div>
    
    <div class="field has-addons" style="margin-top:1em;">
        <div class="control is-expanded">
            <div class="select is-fullwidth">
                <select [(ngModel)]="elementTypeForInsertion">
                    <option *ngFor="let elementType of elementOptions" [value]="elementType">
                        {{lang.tra(elementLabels[elementType])}}
                    </option>
                </select>
            </div>
        </div>
        <div class="control">
            <button class="button is-primary" (click)="addElement(elementTypeForInsertion)">Add Element</button>
        </div>
    </div>
  </div>
    
</fieldset>