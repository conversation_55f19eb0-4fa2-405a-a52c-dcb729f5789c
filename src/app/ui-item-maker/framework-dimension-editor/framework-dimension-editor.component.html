<div class="param-block">
  <div class="header-row">
    <button 
      class="button is-small"
      (click)="isAdvancedOptions = !isAdvancedOptions"
      [class.is-dark]="isAdvancedOptions"
    > 
      <i class="fas fa-cog"></i> 
    </button>
    <a [class.is-disabled]="isReadOnly()" class="close" (click)="remove.emit()">
      <i class="fa fa-times" aria-hidden="true"></i>
    </a>
  </div>
  <div *ngIf="!isAdvancedOptions" style="margin-bottom:0.5em">
    <div *ngFor="let flag of specialFlags" >
      <div *ngIf="checkSpecialFlag(flag.key)" style="display: flex; align-items: center;">
        <i class="fa fa-check" aria-hidden="true" style="margin-right: 1em; color: #a7a7a7; font-size: 0.7em; margin-left: 0.5em;"></i>
        <tra [slug]="flag.caption"></tra>
      </div>
    </div>
  </div>
  <fieldset [disabled]="isReadOnly()" [class.is-enhanced-view]="isEnhancedView">
    <div *ngIf="isAdvancedOptions" style="margin-bottom:0.5em;">
      <ng-container *ngFor="let flag of specialFlags;let i = index">
        <div *ngIf="!flag.condition || flag.condition()"  style="display: flex; align-items: center;">
          <button [disabled]="isReadOnly()" class="button is-small is-white" (click)="toggleSpecialFlag(flag)">
            <span class="checkbox-icon" [ngSwitch]="checkSpecialFlag(flag.key)">
              <i *ngSwitchCase="true" class="fa fa-check-square" aria-hidden="true"></i>
              <i *ngSwitchDefault class="far fa-square" aria-hidden="true"></i>
            </span>
          </button>
          <span style="margin-left:0.5em;" (click)="toggleSpecialFlag(flag)">
            <tra [slug]="flag.caption"></tra>
          </span>
        </div>
      </ng-container>
    </div>
    <div class="text-input-row" [class.is-enhanced-view-row]="isEnhancedView">
      <input placeholder="Code"  class="input" style="max-width:6em" type="text"[formControl]="code">
      <input placeholder="Name"  class="input" type="text" [formControl]="name">
      <input type="color" class="color-picker"  [formControl]="color">
      <button [disabled]="isReadOnly()" class="button" (click)="param.isHidden = !param.isHidden">
        <i *ngIf="!param.isHidden" class="fa fa-eye"></i>
        <i *ngIf="param.isHidden" class="fa fa-eye-slash"></i>
      </button>
    </div>
  <!-- </div>
  <div class="text-input-row">
    <input placeholder="Code"  class="input" style="max-width:3em" type="text"[formControl]="code">
    <input placeholder="Name"  class="input" type="text" [formControl]="name">
    <input type="color" class="color-picker"  [formControl]="color">
    <button [disabled]="isReadOnly()" class="button" (click)="param.isHidden = !param.isHidden">
      <i *ngIf="!param.isHidden" class="fa fa-eye"></i>
      <i *ngIf="param.isHidden" class="fa fa-eye-slash"></i>
    </button>
  </div>

  <div>
    
    <div class="select is-small is-fullwidth">
      <select [formControl]="type">
        <option [value]="DimensionType.SELECT">Selection</option>
        <option [value]="DimensionType.BINARY">Checkbox</option>
        <option [value]="DimensionType.NUMERIC">Number</option>
        <option [value]="DimensionType.COORD">Coordinate</option>
        <option [value]="DimensionType.LABEL">Short Text</option>
        <option [value]="DimensionType.TEXT">Long Text</option>
        <option [value]="DimensionType.VOICE">Voice Audio</option>
        <option [value]="DimensionType.IMAGE">Image</option>
      </select>
    </div>
  </div>
  <div *ngIf="type.value === DimensionType.SELECT ">
    <div style="padding:1em;" *ngIf="param.config">
      <div *ngFor="let tag of param.config.tags" class="tag-input-row">
        <div >
          <a class="tag-info is-bold" [class.no-pointer-events]="isReadOnly()" [class.is-unset]="!tag.code" (click)="replaceTagProp(tag, 'code')">{{tag.code || 'Code'}}</a>
          <a class="tag-info" [class.no-pointer-events]="isReadOnly()" [class.is-unset]="!tag.name" (click)="replaceTagProp(tag, 'name')">{{tag.name || 'Name/Desc.'}}</a> -->
    <div>
      <div [class.is-disabled]="isReadOnly()" class="select is-small is-fullwidth">
        <select [formControl]="category">
          <option [value]="DimensionCategory.META">Meta</option>
          <option [value]="DimensionCategory.SCORING">Scoring</option>
        </select>
      </div>
    </div>
    <div>
      <div [class.is-disabled]="isReadOnly() || !enableTypeSelection" class="select is-small is-fullwidth">
        <select [formControl]="type">
          <!-- <option [value]="DimensionType.MULTI_SELECT">Multi Selection</option> -->
          <option [value]="DimensionType.SELECT">Selection</option>
          <option [value]="DimensionType.BINARY">Checkbox</option>
          <option [value]="DimensionType.NUMERIC">Number</option>
          <option [value]="DimensionType.COORD">Coordinate</option>
          <option [value]="DimensionType.LABEL">Short Text</option>
          <option [value]="DimensionType.TEXT">Long Text</option>
          <option [value]="DimensionType.IMAGE">Image</option>
        </select>
      </div>
    </div>
    <div>
      <div [class.is-disabled]="isReadOnly()" class="select is-small is-fullwidth">
        <select [formControl]="storageLocation">
          <!-- <option [value]="DimensionType.MULTI_SELECT">Multi Selection</option> -->
          <option [value]="PARAMETER_LOCATIONS.ITEM">Item</option>
          <option [value]="PARAMETER_LOCATIONS.TEST_DESIGN">Test Design</option>
        </select>
      </div>
    </div>
    <div *ngIf="type && type.value === DimensionType.SELECT ">
      <div style="padding:1em;" *ngIf="param.config">
        <div *ngFor="let tag of param.config.tags" class="tag-input-row">
          <div >
            <a class="tag-info is-bold" [class.is-disabled]="isReadOnly()" [class.is-unset]="!tag.code" (click)="replaceTagProp(tag, 'code')">{{tag.code || 'Code'}}</a>
            <a class="tag-info" [class.is-disabled]="isReadOnly()" [class.is-unset]="!tag.name" (click)="replaceTagProp(tag, 'name')">{{tag.name || 'Name/Desc.'}}</a>
          </div>
          <a class="close" [class.is-disabled]="isReadOnly()" (click)="removeTag(param.config.tags, tag)" style="margin-left:1em;">
            <i class="fa fa-times" aria-hidden="true"></i>
          </a>
        </div>
      </div>
      <div style="display: flex;flex-direction: column; gap: 0.5em;">
        <div style="display: flex; gap: 0.5em;">
          <div>
            <button [disabled]="isImporting" class="button is-fullwidth" (click)="startConfigImport()">Import Configuration</button>
            <input id="fileInput" hidden type="file" (change)="fileChange($event.target.files)" name="file" accept=".csv,.xlsx" (click)="$event.target.value=null">        
          </div>
          <div  style="text-align:center;">
            <button [disabled]="isReadOnly()" (click)="insertTagOption()" class="button">
              <i class="fa fa-plus" aria-hidden="true"></i>
            </button>
          </div>
        </div>
        <div style="display: flex; justify-content: space-between; align-items: flex-end;">
          <button [disabled]="!hasConfigurations()" class="button" (click)="exportConfiguration()">Export Configuration</button>
          <a [href]="getTemplateUrl()">Download excel template</a>
        </div>
      </div>
    </div>
    <!-- <div *ngIf="type && type.value === DimensionType.MULTI_SELECT ">
      <div style="padding:1em;" *ngIf="param.config">
        <div *ngFor="let tag of param.config.tags" class="tag-input-row">
          <div >
            <a class="tag-info is-bold" [class.is-disabled]="isReadOnly()" [class.is-unset]="!tag.code" (click)="replaceTagProp(tag, 'code')">{{tag.code || 'Code'}}</a>
            <a class="tag-info" [class.is-disabled]="isReadOnly()" [class.is-unset]="!tag.name" (click)="replaceTagProp(tag, 'name')">{{tag.name || 'Name/Desc.'}}</a>
          </div>
          <a class="close" [class.is-disabled]="isReadOnly()" (click)="removeTag(param.config.tags, tag)" style="margin-left:1em;">
            <i class="fa fa-times" aria-hidden="true"></i>
          </a>
        </div>
      </div>
      <div  style="text-align:center;">
        <button [disabled]="isReadOnly()" (click)="insertTagOption()" class="button">
          <i class="fa fa-plus" aria-hidden="true"></i>
        </button>
      </div>
    </div> -->
  </fieldset>
</div>
