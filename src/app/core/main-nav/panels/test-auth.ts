import { IPanelLink } from "./type";

export const testAuthPanels:IPanelLink[] = [
    {
        caption: 'Form Allocations & Sign-off',
        routerLink: '/en/test-auth/auth/tw-alloc',
        iconUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/Frame 1/1720436831596/Frame 1.png',
    },
    {
        caption: 'Composite Forms & Designs',
        routerLink: '/en/test-auth/auth/designs',
        iconUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/Frame 2/1720436846289/Frame 2.png',
    },
    {
        caption: 'Item Sets', 
        routerLink: '/en/test-auth/auth/sets',
        iconUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/Frame 3/1720436857625/Frame 3.png',
    },
    {
        caption: 'Commments & Notifications', 
        routerLink: '/en/test-auth/issues',
        iconUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/Frame 4/1720436869383/Frame 4.png',
    },    {
        caption: 'Parameters', 
        routerLink: '/en/test-auth/parameters',
        iconUrl: 'https://firebasestorage.googleapis.com/v0/b/calculating-cats.appspot.com/o/website%2Ficons%2Ficons-students.png?alt=media&token=1bb26aa0-e615-434e-aa7e-a97ebc52976a',
    },
    {
        caption: 'Assets',
        routerLink: '/en/test-auth/asset-library',
        iconUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/Frame 5/1720436882151/Frame 5.png',
    },
    {
        caption: 'Assessment Profiles',
        routerLink: '/en/test-auth/auth/profiles',
        iconUrl: 'https://vea-authoring.vretta.com/user_uploads/21/authoring/template/1735647043583/template.png',
    },
    {
        caption: 'Group Roles',
        routerLink: '/en/test-auth/auth/groups',
        iconUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/Frame 6/1720436894121/Frame 6.png',
    },

]