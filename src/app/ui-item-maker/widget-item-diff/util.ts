import { IContentElement } from 'src/app/ui-testrunner/models';
import {getValueByPath} from '../element-config-template/util'
import { checkElementIsEntry, IQuestionConfig } from '../item-set-editor/models';
export const getElementEntryByPath = (path: string, content: any): IContentElement => {
    const element = getValueByPath(path, content);
    if(!element || !element.elementType) {
        return undefined;
    }

    if(checkElementIsEntry(element)) {
        return element;
    }

    return undefined;
}