#!/bin/bash

# Description:  This script utilizes npm to compile API content.
#
#               It starts by setting the memory limit used for builds to a higher value than the default (512 MB)
#               Setting a higher memory limit helps to avoid build failures, particularly the "JavaScript heap out
#               of memory error."
#
#               During builds, this script will look for a cache of node_modules content within .npm so that it does
#               not have to download node modules from the Internet if they already exist within the .npm directory.
#               This greatly speeds up builds.
#
#               The build is then output to a compressed tar file containing the current commit sha passed by the Gitlab
#               deployment pipeline via the CI_COMMIT_SHA environment variable.

# First check to see if CI_API_ROLLBACK is set to "standard" or "emergency", if so exit.

shopt -s nocasematch # Temporarily disable case sensitive comparisons of strings

if [[ "$CI_API_ROLLBACK" == "standard" || "$CI_API_ROLLBACK" == "emergency" ]]; then

  echo -e "\nCI_API_ROLLBACK for release branch $CI_ENVIRONMENT_NAME is currently set to $CI_API_ROLLBACK"
  echo -e "\nIf you are attempting to release the latest API build please set the CI_API_ROLLBACK variable to "
  echo -e "something other than \"standard\" or \"emergency\"."
  echo -e "\nThis variable can be updated for the $CI_ENVIRONMENT_NAME Environment on GitLab within the Variables "
  echo -e "section here: https://bubo.vretta.com/vea/platform/vea-api/-/settings/ci_cd"
  echo -e "Once the variable is updated, you can retry the pipeline to release the latest API build from the Pipelines"
  echo -e "section in GitLab here: https://bubo.vretta.com/vea/platform/vea-api/pipelines"

  shopt -u nocasematch # Re-enable case sensitive checks

  exit 1

fi

shopt -u nocasematch # Re-enable case sensitive checks

# Proceed with build if CI_API_ROLLBACK is not set to "standard" or "emergency"

# Set maximum memory for npm to use during builds for avoiding the JavaScript heap out of memory error

memory_limit=3072
export NODE_OPTIONS=--max-old-space-size=$memory_limit

# store current date in seconds
current=$(date +%s)

# Compile the API using the configured memory limit

echo -e "\n\nCompiling API with memory limit of $memory_limit...\n"

npm ci --cache .npm --prefer-offline
npm i -g pm2
npm run compile

# Add file with commit hash to directory to make it easy to identify which commit is live on S3

echo -e "\nAdding file to app directory indicate current release commit..."
touch _00_RELEASE-COMMIT-$CI_COMMIT_SHA

# Create compressed file to store API build results

build_file="api_build-$CI_COMMIT_SHA.tgz"
build_destination="api_build"

echo -e "\nCreating tar file to store API build at $(pwd)/$build_file"
touch $build_file

# Compress API and place it within the build_destination with unique name based on current commit hash

echo -e "\nCompressing API build and placing it within $(pwd)/$build_destination/$build_file..."

tar --exclude=$build_file -czf $build_file ./
mkdir $build_destination
mv $build_file ./$build_destination/

#echo "Size of workspace:"
#du -h .

echo "Size of build file:"
du -h "./$build_destination/$build_file"

# output the length of time in mintues the build took
end=$(date +%s)
minutes_per_hour=60

build_length=$(( ($current - $end) / $minutes_per_hour ))
echo "$build_length" > build_length
