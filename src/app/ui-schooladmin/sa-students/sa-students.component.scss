@import '../../../styles/partials/_base.scss';
@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/test-admin.scss';
@import '../../../styles/page-types/accounts.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/partials/_table.scss';
@import '../../../styles/pseudo-objects/pre-table-strip.scss';

.page-body {
  @extend %page-body;
  @extend %page-body-ta;
  @extend %page-body-accounts;
}

@import '../../../styles/pseudo-objects/form-input.scss';
.input-row { @extend %input-row; }

@import '../../../styles/partials/_modal.scss';
.custom-modal { 
  @extend %custom-modal;

  .modal-contents {
    .simple-content-bounds.student-export {
      max-width: 65vw;
    }
  }
  
}

.expiry-label{display:flex; align-items: center;}
.expiry-button{margin-left:20px}

.test-session-header {
  color: #f14668;
}
.test-window-header {
  color: #1c48c2;
}

code.test-window {
  color: #1c48c2;
}
.session-listing {
  display:flex;
  flex-direction: row;
  margin-bottom:0.5em;
  border-top: 1px solid #f1f1f1;
  transition: 300ms;
  justify-content: stretch;
  code.test-session {
    flex-grow: 0;
    margin-right: 0.2em;
  }
  .test-session-info {
    padding: 0.1em;
    flex-grow: 1;
    .session-time {
      font-size:0.8em;
    }
  }
  code.test-window {
    flex-grow: 0;
    margin-left: 0.2em;
    color: #1c48c2;
  }
  &:hover {
    background-color: #f1f1f1;
    code {
      background-color: #ccc;
      // color: #333;
    }
  }
}

.accounts-table-container {
  overflow-x: auto;
  table {
    border: 1px solid #dbdbdb;
    th {
      border: 1px solid white;
      border-bottom: 1px solid #dbdbdb;
      
      button.btn-filter {
        &:focus:not(:active) {
          @include focus-box-shadow-light;
        }
      }
    }
  }
}

td.link span {
  cursor: pointer;
}

a.modal-link {
  text-decoration: underline;
  font-weight:400;
}

.import-validation {
  .error-message {
    padding: 0 0 15px 5px;
  }
}

.is-alert {
  background-color:pink;
  font-weight: bold;
}
.is-select{
  background-color: #297cb3;
  font-weight: bold;
  color: white; 
}
.is-yellow-flag {
  background-color:rgb(248, 226, 100);
  font-weight: bold;
}

.student-info-conf-notif-box {
  background-color:pink; 
  display: table; 
  width: 100%; 
  padding:1em;
  .student-info-notif-icon {
    width:1.6em; 
    height:1.6em;
  }

  &.student-info-no-err {
    background-color:rgb(253, 255, 236); 
    .student-info-notif-icon {
      display:none;
    }
  }

}

.admin-info-conf-notif-box {
  background-color:rgb(248, 226, 100);
  display: table; 
  width: 100%; 
  padding:1em;
}

.show-in-row{
  display: flex;
  flex-direction: row;
}

.export-summary{
  display: flex; 
  margin-bottom: 0.8em;
  .export-summary-description{
    flex: 1; 
    display: flex; 
    flex-direction: column; 
    justify-content: left; 
  }
}

.btn-export{
  text-align: center; 
  margin-bottom: 1.5em;
  margin-top: 1.25em;
  .btn-export-summary{
    background-color: #f1f1f1;
    cursor: pointer;
    &:hover {
      background-color: #e5e5e5;
    }
    &:active {
      background-color: #e5e5e5;
    }
  }
}


.nobreak {
  white-space: nowrap;
}

.is-success{
  color: #067000;
}
.is-danger{
  color: #ce0000;
}
.is-warn{
  color: #F39F21;
}

.certificate-container{
  display: flex; 
  justify-content: center; 
  margin: 1em 0em;
  .certificate-body{
    width: 80%; 
    background-color: white; 
    padding: 2em;
  }
}

.pasi{
  background-color: black;
  color: white;
  width: 1.5em;
  height: 1.5em;
  text-align: center;
  border-radius: 3em;
  margin-right: .5em;
}

.guest-student {
  background-color: rgb(183, 223, 160);
}

