// Initializes the `public/support/class-lookup` service on path `/public/support/class-lookup`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ClassLookup } from './class-lookup.class';
import hooks from './class-lookup.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/support/class-lookup': ClassLookup & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/support/class-lookup', new ClassLookup(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/support/class-lookup');

  service.hooks(hooks);
}
