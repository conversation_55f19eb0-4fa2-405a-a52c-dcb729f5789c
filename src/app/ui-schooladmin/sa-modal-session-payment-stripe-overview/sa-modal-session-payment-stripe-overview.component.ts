import { formatDate } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AuthService, getFrontendDomain } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import { G9DemoDataService } from '../g9-demo-data.service';
import { ClassFilterId, MySchoolService } from '../my-school.service';

@Component({
  selector: 'sa-modal-session-payment-stripe-overview',
  templateUrl: './sa-modal-session-payment-stripe-overview.component.html',
  styleUrls: ['./sa-modal-session-payment-stripe-overview.component.scss']
})
export class SaModalSessionPaymentStripeOverviewComponent implements OnInit {

  @Input() sessionList: any[];
  @Input() schoolName: string = '';
  @Input() administrationWindow: string = '';
  @Input() assessmentName: string = '';
  @Input() currentClassFilter: string = '';
  @Input() totalCost: number;

  @Output() onBack = new EventEmitter();

  constructor(
    private routes: RoutesService,
    private auth: AuthService,
    private lang: LangService,
    private g9DemoData: G9DemoDataService,
    public schoolService: MySchoolService,
  ) { }

  studentList: any[] = [];
  isChecked: boolean = false;

  ngOnInit(): void {
    this.getStudents();
  }

  onChange(e) {
    if (e) {
      this.isChecked = e.checked;
    }
  }

  getStudents() {
    const classroomIds = this.sessionList.map(session => session.classroom_id);
    const students = [];

    this.g9DemoData.schoolAdminStudents.list.map(student => {
      if (this.currentClassFilter == ClassFilterId.G9 && student.eqao_g9_class_code){
        if (classroomIds.includes(student.eqao_g9_class_code)) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.OSSLT && student._g10_eqao_g9_class_code){
        if (classroomIds.includes(student._g10_eqao_g9_class_code)) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.Primary && student._g3_eqao_g9_class_code){
        if (classroomIds.includes(student._g3_eqao_g9_class_code)) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.Junior && student._g6_eqao_g9_class_code){
        if (classroomIds.includes(student._g6_eqao_g9_class_code)) {students.push(student)}
      }
    });

    this.studentList = students.filter(student => !student.isPaid);
  }

  getTestWindowViewText(tw){
    if(tw){
      const startDate = formatDate(new Date(tw.date_start), 'MMM yyyy', 'en_US')
      const endDate = formatDate(new Date(tw.date_end), 'MMM yyyy', 'en_US')
      const isActive = (new Date(tw.date_end) > new Date ())? "lbl_active":"lbl_inactive"
      return startDate+" "+this.lang.tra("lbl_date_to") +" "+endDate+" ("+this.lang.tra(isActive)+")";
    } 
  }

  async confirm() {
    if (this.isChecked) {
      // TO DO - API call
      console.log('isChecked', this.isChecked)

      const studentsInfo = [];
      for (let student of this.studentList) {
        studentsInfo.push({
          uid: student.uid,
          class_group_id: student.class_group_id
        })
      }
      const stripeCheckout = await this.auth
        .apiCreate(this.routes.SCHOOL_ADMIN_SESSION_PURCHASE, {
          domain: getFrontendDomain(),
          schl_group_id: this.g9DemoData.schoolData.group_id,
          schl_mident: this.g9DemoData.schoolData.foreign_id,
          lang: this.lang.c()  ,
          test_session_ids: this.sessionList.map(session => session.id),
          studentsInfo,
          purchase_by_uid: this.auth.getUid(),
          schoolName: this.schoolName,
          assessmentName: this.assessmentName,
          administrationWindow: this.administrationWindow,
          sessionNames: this.sessionList.map(session => session.classCode),
          numOfStudents: this.studentList.length,
          purchase_method_id: 1,
          totalCost: this.totalCost
        })
        .then((res) => {
          location.assign(res.sessionUrl);
        });

    }
  }

  back() {
    this.onBack.emit(true);
  }

}
