import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { randInt, randId, generateEntries, randArrEntry, coinFlip } from '../../ui-testadmin/demo-data.service';
import { IClassroom, IIdentifiedEntry, IStudentAccount } from '../data/types';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import { LangService } from "../../core/lang.service";
import { LoginGuardService } from '../../api/login-guard.service';
import { ListSelectService, ListSelector, IListElement } from '../../ui-partial/list-select.service';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { STUDENT_MATH_CLASS_WHEN, STUDENT_G9_COURSES, G9_COURSES, STUDENT_G9_COURSES_SIMPLE,Learning_Format_G9,Learning_Format_OSSLT, DEMO_SEMESTER, ABED_ASSESSMENTS, classLabelABEDKeys, classCodeABEDKeys } from '../data/constants';
import { IMappedList, renderMappedValue, initMappedList } from '../data/util';
import { APIColumnExportList } from "./export/config";
import { G9DemoDataService } from '../g9-demo-data.service';
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { MySchoolService, ClassFilterId } from '../my-school.service';
import { downloadFromExportData, IExportColumn, saveDataAsTsv } from '../../ui-testctrl/tc-table-common/tc-table-common.component';
import { FilterSettingMode } from '../../ui-partial/capture-filter-range/capture-filter-range.component';
import { v4 as uuidv4 } from 'uuid';
import { formatDate } from '@angular/common';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { cloneDeep } from 'lodash';
import * as _ from 'lodash';

export enum actions 
{
  UNASSIGN = "UNASSIGN",
}

export enum ClassroomModal {
  NEW = 'NEW',
  EDIT = 'EDIT',
  IMPORT = 'IMPORT',
  EXPORT = 'EXPORT',
  ASSIGN_TESTWINDOW = "ASSIGN_TESTWINDOW",
  ADD_INVIGILATOR = "ADD_INVIGILATOR"
}

enum UserMetaKeyMapping {
  NBED = 'NBED_UserId',
  MBED = 'MBED_UserId',
  ABED = 'StudentIdentificationNumber',
  EQAO = 'StudentOEN'
}

export interface IModalExport {
  name: string;
  data: IClassroom[];
  hideTable?: boolean;
  columns: IExportColumn[];
  cellSelect?: (event) => void
}

export interface IModalImport {
  hideTable?: boolean;
  columns: IExportColumn[];
  cellSelect?: (event) => void;
}

@Component({
  selector: 'sa-classrooms',
  templateUrl: './sa-classrooms.component.html',
  styleUrls: ['./sa-classrooms.component.scss']
})
export class SaClassroomsComponent implements OnInit {

  @Output() selectGroup = new EventEmitter();

  constructor(
    public lang: LangService,
    public loginGuard: LoginGuardService,
    private listSelectService: ListSelectService,
    private pageModalService: PageModalService,
    private g9DemoData: G9DemoDataService,
    public mySchool: MySchoolService,
    private auth: AuthService,
    private routes: RoutesService,
    public whiteLabelService: WhitelabelService
  ) { }

  @Output() onSetClassFilter = new EventEmitter();

  public isClassroomSelectedInModal: boolean = false;
  public userInfo: any;
  public groupId: number;
  public isLoaded: boolean;
  public isInited: boolean;
  public baseFilter: string;
  public classrooms: IClassroom[] = [];
  public classroomsTable: MemDataPaginated<IClassroom>;
  public classroomSelections: ListSelector;
  isPrivateSchool = false;
  ClassroomModal = ClassroomModal;
  pageModal: PageModalController;
  IS_ONBOARDING_IMPLEMENTED = false;
  IS_ASMT_COUNT_IMPLEMENTED = false;
  isShowingSemesterFilter: boolean;
  currentClassFilter: ClassFilterId;
  ClassFilterId = ClassFilterId;
  importClassesTempSave :IClassroom[] = [];
  currentTestWindow;
  testWindowsToAssign = [];
  /*
  semesterValues = {
    "First semester": "sdc_student_math_class_when_1", "Second semester": "sdc_student_math_class_when_2",
    "First quadmester": "sdc_student_math_class_when_A", "Second quadmester": "sdc_student_math_class_when_B", "Third quadmester": "sdc_student_math_class_when_C", "Fourth quadmester": "sdc_student_math_class_when_D",
    "First octomester": "sdc_student_math_class_when_E", "Second octomester": "sdc_student_math_class_when_F", "Third octomester": "sdc_student_math_class_when_G", "Fourth octomester": "sdc_student_math_class_when_H",
    "Fifth octomester": "sdc_student_math_class_when_I", "Sixth octomester": "sdc_student_math_class_when_J", "Seventh octomester": "sdc_student_math_class_when_K", "Eighth octomester": "sdc_student_math_class_when_L",
    "First hexamester": "sdc_student_math_class_when_M", "Second hexamester": "sdc_student_math_class_when_N", "Third hexamester": "sdc_student_math_class_when_O", "Fourth hexamester": "sdc_student_math_class_when_P",
    "Fifth hexamester": "sdc_student_math_class_when_Q", "Sixth hexamester": "sdc_student_math_class_when_R", "Full year": "lbl_full_year",
  };
  */
  semesterValues = {
    "Full year": "lbl_full_year", "Semester":"sdc_student_math_class_semester", "Quadmester":"sdc_student_math_class_quadmester", "Octomester":"sdc_student_math_class_octomester"
  };
   

  ngOnInit() {
    this.initRouteView();
    this.isPrivateSchool = this.g9DemoData.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.setTestWindowFilter(this.mySchool.getCurrentTestWindow())
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.classroomSelections.wipeSelections();
    // console.log(this.classroomsTable);
  }

  toggleSemesterFilter() {
    if (this.isShowingSemesterFilter) {
      this.isShowingSemesterFilter = false; // to do: clear filter as well
      this.changeCategory(null)
    }
    else {
      this.isShowingSemesterFilter = true
    }
  }

  initRouteView() {
    if (!this.isInited) {
      this.isInited = true;
      this.loadClassrooms();
    }
  }

  loadClassrooms() {
    this.classrooms = [];
    //this.classrooms = this.g9DemoData.classrooms;
    this.g9DemoData.classrooms.forEach(classroom => this.processNewClassroom(classroom));
    this.loadLearningFormat();
    //console.log('classrooms', this.classrooms)
    this.flagMultipleCourses();
    this.classroomSelections = this.listSelectService.defineNewSelectableList(this.classrooms);
    this.classroomsTable = new MemDataPaginated({
      data: this.classrooms,
      pageSize: 20,
      filterSettings: {
        testWindowFilter:  (theClass: IClassroom) => this.filterClassTestWindow(theClass),
        students: (classroom: IClassroom, val: any) => (classroom.students == null ? 0 : +classroom.students) === +val
      },
      sortSettings: {}
    });
    this.getSemester(this.classrooms);
    this.isLoaded = true;
  }
  // toggleSelectAll() {
  //   this.setSelectAll(this.isAllSelected);
  // }

  // setSelectAll(state: boolean) {
  //   this.isAllSelected = state;
  //   this.classrooms.forEach(session => session.__isSelected = state);
  //   this.isAnySelected = state;
  // }
  processNewClassroom(classroom) {
    classroom.semester_label = this.renderSemester(classroom.semester)
    classroom.invigilators = this.getInvigilators(classroom);
    this.classrooms.splice(0, 0, classroom);
  }

  setClassFilter(filterId) {
    this.onSetClassFilter.emit(filterId);

    this.currentClassFilter = filterId;

    this.classroomsTable.activeFilters['course_type'] = null;
    if (this.currentClassFilter) {
      let value;
      switch (this.currentClassFilter){
        case ClassFilterId.Primary: value = 'EQAO_G3'; break;
        case ClassFilterId.Junior:  value = 'EQAO_G6'; break;
        case ClassFilterId.G9:      value = 'EQAO_G9'; break;
        case ClassFilterId.OSSLT:   value = 'EQAO_G10'; break;
        default: value = this.currentClassFilter;
      }
      this.classroomsTable.activeFilters['course_type'] = { 
        mode: FilterSettingMode.VALUE, 
        config: { value } 
      }
    }
    console.log("classFilter Change",this.currentClassFilter)
    this.classroomsTable.refreshFilters();
  }

  setTestWindowFilter(tw){
    this.currentTestWindow = tw;
    this.classroomsTable.activeFilters['testWindowFilter'] = {mode: FilterSettingMode.VALUE, config: { value: this.currentTestWindow } };
    this.classroomsTable.refreshFilters();
  }

  filterClassTestWindow(theClass:IClassroom){
    //const semester = this.g9DemoData.semesters.list.find(sm => sm.id === +theClass.semester);
    const semester = this.g9DemoData.semesters.map[+theClass.semester]
    if(semester){
      return this.currentTestWindow.id === semester.testWindowId;
    }
    return false;
  }

  public sortNumerically(a: Partial<IClassroom>)
  {
    let data = cloneDeep(a);
    data.students = data.students == null ? 0 : data.students;
    return +data.students;
  }

  private flagMultipleCourses() {
    let studentsMap: any = {};
    this.g9DemoData.schoolAdminStudents.list.forEach(student => {
      const studentClass = student.eqao_g9_class_code;
      if (studentClass) {
        let classStudents = studentsMap[studentClass];
        if (!classStudents) {
          classStudents = [];
          studentsMap[studentClass] = classStudents;
        }
        classStudents.push(student);
      }
    });
    this.classrooms.forEach(classroom => {
      if (typeof classroom.course !== 'undefined' && classroom.course !== null) {
        const students = studentsMap[classroom.id];
        if (students) {
          for (let i = 0; i < students.length; i++) {
            const studentCourse = students[i].eqao_g9_course;
            if (typeof studentCourse !== 'undefined' && studentCourse !== null) {
              if (studentCourse.toString() !== classroom.course.toString()) {
                classroom.__multipleCourses = true;
                break;
              }
            }
          }
        }
      }
    });
  }
  renderSemester(id) {
    let semesterslug = this.semesterValues[renderMappedValue(this.g9DemoData.semesters, id)];
    return this.lang.tra(semesterslug);
  }
  renderCourse(id) {
    const Learning_Format:any  = this.currentClassFilter === ClassFilterId.G9?Learning_Format_G9:Learning_Format_OSSLT;
    return renderMappedValue(Learning_Format, id);
  }

  renderSemesterSlug = (ml: IMappedList<IIdentifiedEntry>, key: number) => {
    const entry = ml.map[key];
    if (entry) {
      return `sdc_student_math_class_when_${entry.foreign_id}`;
    }
  }

  renderTeacherName(id: number) {
    return renderMappedValue(this.g9DemoData.teachers, id);
  }
  semesterFilters = []
  filteredStudents = []
  filteredStudentsList = []
  processFilterConditions(config) {
    this.filteredStudentsList = []
    //this.semesterFilters.push(value)
    if (config.checked) {
      this.semesterFilters.push(config.semester_group)
    }
    else {
      const i = this.semesterFilters.indexOf(config.semester_group)
      this.semesterFilters.splice(i, 1);
    }
    let filterCondition
    let filteredStudents
    if (this.semesterFilters.length > 0) {
      this.semesterFilters.forEach(item => {
        filterCondition = (student) => student.semester_label === item
        filteredStudents = this.filteredStudentsList;
        this.filteredStudentsList = filteredStudents.concat(this.classrooms.filter(filterCondition))
      })
      this.classroomsTable.injestNewData(this.filteredStudentsList)
    }
    else {
      this.classroomsTable.injestNewData(this.classrooms)
    }
  }

  changeCategory($event) {
    //this.processFilterConditions({checked:true,semester_group:this.baseFilter})
    this.semesterFilters = []
    this.classroomsTable.injestNewData(this.classrooms)
  }
  getSemester(students) {
    var numMapping = {};
    for (var i = 0; i < students.length; i++) {
      if (numMapping[students[i].semester_label] === undefined) {
        if (students[i].semester_label !== undefined) {
          numMapping[students[i].semester_label] = 0;
        }
      }
      numMapping[students[i].semester_label] += 1;
    }
    var greatestFreq = 0;
    var mode;
    for (var prop in numMapping) {
      if (numMapping[prop] > greatestFreq) {
        greatestFreq = numMapping[prop];
        mode = prop;
      }
    }
    //console.log(numMapping, mode)
    this.baseFilter = mode;
    // this.processFilterConditions({checked:true,semester_group:mode})
    // this.semesterFilters=[];
  }

  async archiveClassroom(classId): Promise<any>{
    //return await this.auth.apiRemove(this.routes.SCHOOL_ADMIN_CLASSES, classId, this.configureQueryParams())
    return await this.auth.apiRemove(this.routes.SCHOOL_ADMIN_CLASSES, classId, this.configureRemoveQueryParams());
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config; }

  configurePayload(data) {

    const _findSemesterId = () => {
      
      if (this.currentTestWindow && this.currentTestWindow.id){
        for (let semester of this.g9DemoData.semesters.list){
          if (semester.testWindowId == this.currentTestWindow.id){
            return semester.id
          }
        }
      }

      let type_slug = ''
      switch(this.currentClassFilter){
        case ClassFilterId.G9:
          return data.semester;
        case ClassFilterId.TCLE:
          type_slug = 'NBED_TCLE';
          break
        case ClassFilterId.TCN:
          type_slug = 'NBED_TCN'
          break
        case ClassFilterId.SCIENCES8:
          type_slug = 'NBED_SCIENCES8'
          break
        case ClassFilterId.Primary:
          type_slug = 'EQAO_G3P'
          break;
        case ClassFilterId.Junior:
          type_slug = 'EQAO_G6J'
          break;
        case ClassFilterId.MBED_SAMPLE:
          type_slug = 'MBED_SAMPLE'
          break;
        case ClassFilterId.OSSLT:
          type_slug = 'EQAO_G10L'
          break;
        case ClassFilterId.SMCS_G7_EN:
          type_slug = 'SMCS_G7_EN'
          break;
        default:
          type_slug = this.currentClassFilter
          break
      }

      // depecated backup
      if (this.g9DemoData?.semesters?.list){
        const targetTw = this.g9DemoData.testWindows.find(tw =>tw.type_slug == type_slug && new Date(tw.date_end) > new Date());
        const semester = this.g9DemoData?.semesters?.list.find(semester => semester.testWindowId == targetTw?.id)
        return semester?.id;
      }
      
    }

    const _getKey = () => {
      if(this.whiteLabelService.isNBED()) return UserMetaKeyMapping.NBED
      if(this.whiteLabelService.isMBED()) return UserMetaKeyMapping.MBED
      if(this.whiteLabelService.isABED()) return UserMetaKeyMapping.ABED
      return UserMetaKeyMapping.EQAO;
    }
    
    let schoolData: any = this.g9DemoData.schoolData
    const class_id = this.findClassIdByName(data.class_code)
    const semester_id = _findSemesterId();
    const is_fi = data.is_fi?1:0
    const key = _getKey()
    return {
      schl_group_id: schoolData.group_id,
      schl_dist_group_id: schoolData.schl_dist_group_id,
      name: data.class_code,
      semester_id: semester_id,
      is_grouping: data.is_grouping,
      course_type: data.course_type,
      class_id,
      educator_id: data.teacher_uid,
      is_fi,
      key 
      // data.educator
    }
  }
  configureQueryParams() {
    const schoolData: any = this.g9DemoData.schoolData
    if (schoolData) {
      return {
        query: {
          schl_group_id: schoolData.group_id,
        }
      }
    }
    return null;
  }

  configureRemoveQueryParams() {
    const schoolData: any = this.g9DemoData.schoolData
    if (schoolData) {
      return {
        query: {
          schl_group_id: schoolData.group_id,
          lang: this.lang.c(),
        }
      }
    }
    return null;
  }

  newClassroomModalStart(isGroup: boolean = false) {
    const config: any = {
      isGroup,
      classrooms: [
        { semester: DEMO_SEMESTER, }
      ]
    };
    console.log('pre payload', config, this.currentClassFilter)
    this.pageModal.newModal({
      type: ClassroomModal.NEW,
      config,
      finish: config => this.newClassroomModalFinish(config)
    });
  }
  
  private addNewClassroom(classroom: IClassroom) {

    // pull the class from invlaid test window to new test window
    if(this.classInInactiveWindow(classroom.class_code)){
      this.updateSemester([classroom])
      this.pageModal.closeModal()
      return;
    }

    const classroomData = [];
    const payload = this.configurePayload(classroom);    
    return this.auth
      .apiCreate(
          this.routes.SCHOOL_ADMIN_CLASSES,
          [payload],
          this.configureQueryParams()
      )
      .then(res => {

        if (res[0].id == -1){
          this.loginGuard.quickPopup('Could not create new grouping. (there is likely already grouping with the same name)')
          throw new Error();
        }
        // console.log('New Classroom has been created')
        const theSemester= this.g9DemoData.semesters.list.find(semester => semester.id == Number(payload.semester_id));
        const newClassroomRecord = 
        {
          ...classroom,
          semester: theSemester.id.toString(),
          id: res[0].id,
          group_id: res[0].group_id,
          students: 0,
          onboarding: 0,
          assessment: 0,
          currentStudents: initMappedList([]),
          semester_label: theSemester? theSemester.label:'',
        };

        let teacher = this.g9DemoData.teachers.list.find(teacher => teacher.id == classroom.teacher_uid);
        if (teacher) 
        {
          newClassroomRecord.educator = teacher.invigilator;
          //get the teachers current classes and turn them into an array
          let classes = teacher.classCode.split(", ");
          //add the new class as an array item
          classes.push(newClassroomRecord.class_code);
          //convert the classes that now include the new one to the expected format 
          teacher.classCode = classes.join(', ');
          if (teacher.classCode[0] === ',')
          {
            // remove unneccesary ", " if any
            teacher.classCode = teacher.classCode.slice(2);
          }
          
          // update corresponding map
          this.g9DemoData.teachers.map[classroom.teacher_uid] = teacher;
        }

        this.classrooms.splice(0, 0, newClassroomRecord);
        this.classroomsTable.injestNewData(this.classrooms);
        this.g9DemoData.classrooms.push(newClassroomRecord);
        this.g9DemoData.teacherClassrooms.list.push(newClassroomRecord);

        const classOptionList = [];
        const classOptionEl = {id: res[0].id, label: res[0].name, group_type: res[0].group_type};
        classOptionList.push(classOptionEl);

        const mappedClassEl = initMappedList(classOptionList);
        this.g9DemoData.classOptions = {map: {...this.g9DemoData.classOptions.map, ...mappedClassEl.map}, list: this.g9DemoData.classOptions.list.concat(mappedClassEl.list)}
        this.g9DemoData.teacherClassrooms.map[newClassroomRecord.id] = newClassroomRecord;
        classroomData.splice(0, 0, payload);
        // this.g9DemoData.setForceTeacherDataRefresh(true);
        this.pageModal.closeModal();
      })
      .catch(err => 
      {
        if (err.message === "DUPL_CLASS_CODE") {
          this.loginGuard.quickPopup(this.lang.tra('alert_msg_new_class_class_code_exists'))
        }
        // this.pageModal.closeModal()
      });
  }

  updateSemester(classrooms:any[], isImport = false){
    let classesToAPI = [];
    let course_type;
    let type_slug;
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        course_type = "EQAO_G3"
        type_slug = "EQAO_G3P"
      break;
      case ClassFilterId.Junior:
        course_type = "EQAO_G6"
        type_slug = "EQAO_G6J"
      break;
      case ClassFilterId.G9:
        course_type = "EQAO_G9"
        type_slug = "EQAO_G9M"
      break;
      case ClassFilterId.OSSLT:
        course_type = "EQAO_G10"
        type_slug = "EQAO_G10L"
      break;
      case ClassFilterId.ABED_SAMPLE:
        course_type = "ABED_SAMPLE"
        type_slug = "ABED_SAMPLE"
      break;
      default:
        course_type = "EQAO_G3"
        type_slug = "EQAO_G3P"
      break;  
    }
    //const course_type = this.currentClassFilter === ClassFilterId.G9 ? "EQAO_G9":"EQAO_G10"
    //const type_slug = this.currentClassFilter === ClassFilterId.G9 ? "EQAO_G9M":"EQAO_G10L"
    const testWindow = this.g9DemoData.testWindows.find( tw => (new Date(tw.date_end) > new Date ()) && tw.type_slug === type_slug)
    const testWindowId = testWindow.id
    classrooms.forEach( cr => {
      let theClass;
      if(!isImport){
        theClass = this.classrooms.find(classroom => classroom.class_code === cr.class_code && classroom.course_type === course_type)
      }else{
        theClass = this.classrooms.find(classroom => classroom.class_code === cr && classroom.course_type === course_type)
      }  
      
      if(!theClass){
        return
      }
      if(this.currentClassFilter === ClassFilterId.Primary){
        const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => sm.testWindowId === +testWindowId)
        if(newSemester){
          classesToAPI.push({id:theClass.id, new_semester_id: newSemester.id})
        }
      }
      if(this.currentClassFilter === ClassFilterId.Junior){
        const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => sm.testWindowId === +testWindowId)
        if(newSemester){
          classesToAPI.push({id:theClass.id, new_semester_id: newSemester.id})
        }
      }
      if(this.currentClassFilter === ClassFilterId.G9){
        const oldSemester = this.g9DemoData.semesters.list.find(sm => sm.id === +theClass.semester)
        const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => 
          sm.foreign_scope_id === null 
          && sm.label.toUpperCase() === oldSemester.label.toUpperCase() 
          && sm.testWindowId === +testWindowId
        )
        if(newSemester){
          classesToAPI.push({id:theClass.id, new_semester_id: newSemester.id})
        }
      }
      if(this.currentClassFilter === ClassFilterId.OSSLT){
        const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => sm.testWindowId === +testWindowId)
        if(newSemester){
          classesToAPI.push({id:theClass.id, new_semester_id: newSemester.id})
        }
      }
    });
    this.auth
      .apiPatch(
        this.routes.SCHOOL_ADMIN_CLASSES,
        testWindowId,
        classesToAPI,
        this.configureQueryParams()
      ).then((res) => {
        //update G9 class room semester.
        res.forEach(cr => {
          //const theSemester = this.g9DemoData.c.list.find( (sm:any) => sm.id === classroom.id)
          const theClass = this.g9DemoData.classrooms.find( classroom => classroom.id === cr.id)
          theClass.id = cr.newClassId
          theClass.group_id = cr.newgroup_id
          theClass.semester = cr.new_semester_id
          theClass.assessment = 0;
          theClass.recentAssessments = [];
          theClass.openAssessments = [];
          const theTeacherClass = this.g9DemoData.teacherClassrooms.list.find( tcr => tcr.id == cr.id)
          theTeacherClass.classCode = cr.newaccess_code
          theTeacherClass.group_id = cr.newgroup_id
          theTeacherClass.id = cr.newClassId
          theTeacherClass.openAssessments = []
          theTeacherClass.recentAssessments = []
          theTeacherClass.scheduledAssessments = []
          this.g9DemoData.teacherClassrooms.map[cr.newClassId] = theTeacherClass;
          this.g9DemoData.schoolAdminStudents.list.forEach( stu => {
            if(stu._g3_eqao_g9_class_code == cr.id){
               stu._g3_eqao_g9_class_code = cr.newClassId
            }   
            if(stu._g6_eqao_g9_class_code == cr.id) {
              stu._g6_eqao_g9_class_code = cr.newClassId
            }  
            if(stu.eqao_g9_class_code == cr.id){
              stu.eqao_g9_class_code = cr.newClassId
            }
            if(stu._g10_eqao_g9_class_code == cr.id) {
              stu._g10_eqao_g9_class_code = cr.newClassId
            }  
          })
        });
        this.classroomsTable.injestNewData(this.classrooms);
    })
  }
  
  newClassroomModalFinish(config: { payload: { data: IClassroom } }) {
    let data = config.payload.data;
    if (this.validateClassroom(data)) {
      this.addNewClassroom(data);
    }
  }

  renderCourseType(str: string) {
    switch (str) {
      case 'EQAO_G9': return this.lang.tra('txt_g9_math');
      case 'EQAO_G10': return this.lang.tra('lbl_osslt');
    }
    return str;
  }

  findClassIdByName(classCode) {
    const classId = this.g9DemoData.classrooms.find(classroom => classroom.class_code == classCode)
    return classId ? classId.id : null;
  }

  assignClassrooms(){
    let test_window_type
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        test_window_type = "EQAO_G3P"
      break;
      case ClassFilterId.Junior:
        test_window_type = "EQAO_G6J"
      break;
      case ClassFilterId.G9:
        test_window_type = "EQAO_G9M"
      break;
      case ClassFilterId.OSSLT:
        test_window_type = "EQAO_G10L"
      break;
      default:
        test_window_type = this.currentClassFilter;
      break;  
    }

    this.testWindowsToAssign = this.g9DemoData.testWindows.filter( tw => {
      if(tw.type_slug === test_window_type){
        if(this.isCurrentTestWindowActive()){
          if(new Date(tw.date_end) < new Date ()){
            return tw;
          }
        }else{
          if(new Date(tw.date_end) > new Date ()){
            return tw;
          }
        }
      }
    })

    var classrooms = this.classroomSelections.getSelected();
    classrooms =classrooms.filter(classroom =>{
      if(this.currentClassFilter === ClassFilterId.Primary && classroom.course_type == 'EQAO_G3' ){
        return classroom
      }else if(this.currentClassFilter === ClassFilterId.Junior && classroom.course_type == 'EQAO_G6' ){
        return classroom
      }else if(this.currentClassFilter === ClassFilterId.G9 && classroom.course_type == 'EQAO_G9' ){
        return classroom
      }else if(this.currentClassFilter === ClassFilterId.OSSLT && classroom.course_type == 'EQAO_G10' ){
        return classroom
      }else if(ABED_ASSESSMENTS.includes(this.currentClassFilter) && classroom.course_type === this.currentClassFilter){
        return classroom
      }else{
        return;
      }
    })
    const config: any = {
      classrooms,
      testWindowSelected:{}
    };
    this.pageModal.newModal({
      type: ClassroomModal.ASSIGN_TESTWINDOW,
      config,
      finish: this.assignClassroomModalFinish
    });
  }

  assignClassroomModalFinish = (config) => {
    const testWindowId = +Object.keys(config.testWindowSelected)[0]
    let havePlacehodler = false;
    let hasValidTestSessions = false;
    let hasSameClassName = false;

    if(testWindowId){
      let classesToAPI = [];
      config.classrooms.forEach( cr => {

        if(+cr.is_placeholder === 1){
          havePlacehodler = true;
          return;
        }

        this.g9DemoData.classrooms.find( cr2 =>{
          const cr2Semester = this.g9DemoData.semesters.list.find( sm => +sm.id == +cr2.semester)
          const cr2TW =  this.g9DemoData.testWindows.find( tw => tw.id == cr2Semester.testWindowId)
          if(cr2.class_code == cr.class_code && cr2TW.id == testWindowId){
            hasSameClassName = true;
          }
        })

        if(hasSameClassName){
          return
        }

        const test_sessions = this.g9DemoData.schoolSessions.find (ts => 
             ts.school_class_id === cr.id
          && ts.date_time_start > this.currentTestWindow.date_start
          && ts.date_time_start < this.currentTestWindow.date_end  
        )

       /*
        const test_sessions = this.g9DemoData.schoolSessions.find (ts => {
           const a = ts.school_class_id === cr.id 
           const b = ts.date_time_start > this.currentTestWindow.date_start
           const c = ts.date_time_start < this.currentTestWindow.date_end
           if( a ){
             if (b && c)
              return ts;
           }
        })
        */

        if(test_sessions !== undefined && this.isCurrentTestWindowActive()){
          hasValidTestSessions = true;
          return;
        }

        this.g9DemoData.classrooms.find( cr2 =>{
          const cr2Semester = this.g9DemoData.semesters.list.find( sm => +sm.id == +cr2.semester)
          const cr2TW =  this.g9DemoData.testWindows.find( tw => tw.id == cr2Semester.testWindowId)
          if(cr2.class_code == cr.class_code && cr2TW.id == testWindowId){
            hasSameClassName = true;
          }
        })

        if(hasSameClassName){
          return
        }

        if(this.currentClassFilter === ClassFilterId.Primary){
          const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => sm.testWindowId === +testWindowId)
          if(newSemester){
            classesToAPI.push({id:cr.id, new_semester_id: newSemester.id})
          }
        }
        if(this.currentClassFilter === ClassFilterId.Junior){
          const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => sm.testWindowId === +testWindowId)
          if(newSemester){
            classesToAPI.push({id:cr.id, new_semester_id: newSemester.id})
          }
        }
        if(this.currentClassFilter === ClassFilterId.G9){
          const oldSemester = this.g9DemoData.semesters.list.find(sm => sm.id === cr.semester)
          const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => 
            sm.foreign_scope_id === null 
            && sm.label.toUpperCase() === oldSemester.label.toUpperCase() 
            && sm.testWindowId === +testWindowId
          )
          if(newSemester){
            classesToAPI.push({id:cr.id, new_semester_id: newSemester.id})
          }
        }
        if(this.currentClassFilter === ClassFilterId.OSSLT){
          const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => sm.testWindowId === +testWindowId)
          if(newSemester){
            classesToAPI.push({id:cr.id, new_semester_id: newSemester.id})
          }
        }
        if(ABED_ASSESSMENTS.includes(this.currentClassFilter)) {
          const newSemester = this.g9DemoData.semesters.list.find( (sm:any) => sm.testWindowId === +testWindowId)
          if(newSemester){
            classesToAPI.push({id:cr.id, new_semester_id: newSemester.id})
          }
        }
      });

      if(havePlacehodler || hasValidTestSessions||hasSameClassName){
        let message = ""
        if(havePlacehodler){
          message += this.lang.tra('alert_msg_reassign_placeholder')
        }

        if(havePlacehodler && hasValidTestSessions){
          message += "\n"
        }

        if(hasValidTestSessions){
          message += this.lang.tra('alert_msg_reassign_class_with_valid_ts')
        }

        if( (havePlacehodler || hasValidTestSessions) && hasSameClassName){
          message += "\n"
        }
        if(hasSameClassName){
          message += this.lang.tra('alert_msg_reassign_class_with_same_name')
        }

        alert(message)
      }

      this.auth
        .apiPatch(
          this.routes.SCHOOL_ADMIN_CLASSES,
          testWindowId,
          classesToAPI,
          this.configureQueryParams()
        ).then((res) => {
          //update G9 class room semester.
          res.forEach(cr => {
            //const theSemester = this.g9DemoData.c.list.find( (sm:any) => sm.id === classroom.id)
            const theClass = this.g9DemoData.classrooms.find( classroom => classroom.id === cr.id)
            theClass.id = cr.newClassId
            theClass.group_id = cr.newgroup_id
            theClass.semester = cr.new_semester_id
            theClass.assessment = 0;
            theClass.recentAssessments = [];
            theClass.openAssessments = [];
            const theTeacherClass = this.g9DemoData.teacherClassrooms.list.find( tcr => tcr.id == cr.id)
            theTeacherClass.classCode = cr.newaccess_code
            theTeacherClass.group_id = cr.newgroup_id
            theTeacherClass.id = cr.newClassId
            theTeacherClass.openAssessments = []
            theTeacherClass.recentAssessments = []
            theTeacherClass.scheduledAssessments = []
            this.g9DemoData.teacherClassrooms.map[cr.newClassId] = theTeacherClass;
            this.g9DemoData.schoolAdminStudents.list.forEach( stu => {
              if(stu._g3_eqao_g9_class_code == cr.id){
                 stu._g3_eqao_g9_class_code = cr.newClassId
              }   
              if(stu._g6_eqao_g9_class_code == cr.id) {
                stu._g6_eqao_g9_class_code = cr.newClassId
              }  
              if(stu.eqao_g9_class_code == cr.id){
                stu.eqao_g9_class_code = cr.newClassId
              }
              if(stu._g10_eqao_g9_class_code == cr.id) {
                stu._g10_eqao_g9_class_code = cr.newClassId
              }  
              if(stu._abed_sample_eqao_g9_class_code == cr.id) {
                stu._abed_sample_eqao_g9_class_code = cr.newClassId
              }
            })
          });
          this.classroomsTable.injestNewData(this.classrooms);
      })

      this.pageModal.closeModal()
    }else{
      this.pageModal.closeModal()
    }  
  }

  editClassroomModalStart() 
  {
    var classrooms = this.classroomSelections.getSelected();
    console.log(classrooms);
    console.log(this.currentClassFilter);
    this.isClassroomSelectedInModal = classrooms.length > 1 ? false : true;
    classrooms = classrooms.filter(classroom =>
    {
      // console.log(classroom);
      switch(this.currentClassFilter){
        case ClassFilterId.Primary:
          if (classroom.course_type == 'EQAO_G3' || classroom.course_type.id == 'EQAO_G3'){
            return true;
          }
          break;
        case ClassFilterId.Junior:
          if (classroom.course_type == 'EQAO_G6' || classroom.course_type.id == 'EQAO_G6'){
            return true;
          }
          break;
        case ClassFilterId.G9:
          if (classroom.course_type == 'EQAO_G9' || classroom.course_type.id == 'EQAO_G9'){
            return true;
          }
          if (classroom.course_type == 'EQAO_G10' || classroom.course_type.id == 'EQAO_G10'){
            return true;
          }
          break;
        default:
          if (classroom.course_type == this.currentClassFilter || classroom.course_type.id == this.currentClassFilter){
            return true;
          }
          break;
      }
      return false;
    });

    const config: any = {
      classrooms,
    };
    this.pageModal.newModal({
      type: ClassroomModal.EDIT,
      config,
      finish: this.editClassroomModalFinish
    });
  }

  setIsClassroomSelected(flag: boolean)
  {
    this.isClassroomSelectedInModal = flag;
    // console.log(this.isClassroomSelectedInModal);
  }

  editClassroomModalFinish = (config: { payload: { id: any, data: IClassroom } }) => {
    let data = config.payload.data;
    let id = config.payload.id;
    if (this.validateClassroom({id, ...data})) {
      data = { ...data, semester_id: data.semester }
      const classId = config.payload.id;
      let teacher = <any>this.g9DemoData.teachers.list.filter(teacher => teacher.id == data.teacher_uid)[0]
      if (teacher) {
        data.educator = teacher.invigilator;
      }

      return this.auth
        .apiUpdate(
          this.routes.SCHOOL_ADMIN_CLASSES,
          classId,
          this.configurePayload(data),
          this.configureQueryParams()
        )
        .then(() => 
        {
          let oldTeacherUid = null;
          this.classrooms.forEach(classroom => {
            if (classroom.id === classId) 
            {
              oldTeacherUid = classroom.teacher_uid;
              Object.keys(data).forEach(prop => {
                classroom[prop] = data[prop];
              })
              if (classroom.teacher_uid && classroom.teacher_uid.length === 0) {
                classroom.educator = '';
              }
              if(this.currentClassFilter === ClassFilterId.G9){
                classroom.semester = data.semester;
              }  
              const theSemester= this.g9DemoData.semesters.list.find(semester => semester.id == Number(data.semester));
              if(theSemester){
                classroom.semester_label = theSemester.label;
              }
            }
          });

          this.classroomsTable.injestNewData(this.classrooms);

          const classOptIdx = this.g9DemoData.classOptions.list.findIndex(option => option.id === classId);

          if (classOptIdx !== -1)
          {
            // update classOptions
            this.g9DemoData.classOptions.list[classOptIdx].group_type = data.course_type;
            this.g9DemoData.classOptions.list[classOptIdx].label = data.class_code;
            this.g9DemoData.classOptions.map[classId] = this.g9DemoData.classOptions.list[classOptIdx];
          }


          const teacherClassroomIdx = this.g9DemoData.teacherClassrooms.list.findIndex(option => option.id === classId);
          if (teacherClassroomIdx !== -1)
          {
            // update teacherClassrooms
            this.g9DemoData.teacherClassrooms.list[teacherClassroomIdx].name = data.class_code;
            this.g9DemoData.teacherClassrooms.list[teacherClassroomIdx].currentTeachers = data.educator;
            this.g9DemoData.teacherClassrooms.list[teacherClassroomIdx].curricShort = data.course_type;
            this.g9DemoData.teacherClassrooms.map[classId] = this.g9DemoData.teacherClassrooms.list[teacherClassroomIdx];
          }

          let teacher = this.g9DemoData.teachers.list.find(teacher => teacher.id == data.teacher_uid);
          if (teacher)
          {
            //get the teachers current classes and turn them into an array
            let classes = teacher.classCode.split(", ");
            //add the new class as an array item
            classes.push(data.class_code);
            //convert the classes that now include the new one to the expected format 
            teacher.classCode = classes.join(', ');
            if (teacher.classCode[0] === ',')
            {
              // remove unneccesary ", " if any
              teacher.classCode = teacher.classCode.slice(2);
            } 

            // update corresponding map
            this.g9DemoData.teachers.map[data.teacher_uid] = teacher;
          }

          let movedTeacher = this.g9DemoData.teachers.list.find(teacher => teacher.id == oldTeacherUid);
          if (oldTeacherUid != null && movedTeacher != null)
          {
            // delete teacher's old grouping

             //get the teachers current classes and turn them into an array
             let classes = movedTeacher.classCode.split(", ");

             //delete the deleted class as an array item
             const deletedClassIdx = classes.findIndex(classCode => data.class_code === classCode);

             classes.splice(deletedClassIdx, 1);

             //convert the classes that now include the new one to the expected format 
             movedTeacher.classCode = classes.join(', ');

             if (movedTeacher.classCode[0] === ',')
             {
               // remove unneccesary ", " if any
               movedTeacher.classCode = movedTeacher.classCode.slice(2);
             } 

             // update corresponding map
             this.g9DemoData.teachers.map[oldTeacherUid] = movedTeacher;
          }


          // console.log(this.g9DemoData.classrooms);
          // console.log(this.g9DemoData.classOptions);
          // console.log(this.g9DemoData.teacherClassrooms);
          // console.log(this.g9DemoData.teachers);
          // this.g9DemoData.setForceTeacherDataRefresh(true);
 
          //update class student French Immer
          this.g9DemoData.schoolAdminStudents.list.forEach(student => {
            if(+student._g3_eqao_g9_class_code === +classId){
              student._g3_eqao_pj_french = data.is_fi?'1':'0'
            }});  
          this.pageModal.closeModal();

        })
        .catch(err => {
          if (err.message === "DUPL_CLASS_CODE") {
            this.loginGuard.quickPopup(this.lang.tra('alert_msg_new_class_class_code_exists'))
          }else if (err.message === "Invalid_Term_Format") { 
            alert('Term format is not valid!') 
          }else if (err.message === "INVALID_IS_FI") { 
            this.loginGuard.quickPopup(this.lang.tra('alert_msg_new_class_is_fi_invalid'))
          }else if(err.message ==="CLASS_ROOM_NOT_FOUND"){
            this.loginGuard.quickPopup(this.lang.tra('alert_msg_classroom_not_found'))
          }
          else{
            alert('Fail to create classes')
          }
          this.pageModal.closeModal()
        });
    }
  }

  private validateClassroom(classroom: IClassroom, isEdit = false): boolean {
    const requiredFields = [
      { prop: 'class_code', label: 'sa_classrooms_col_class_code' },
    ];
    if(this.currentClassFilter === ClassFilterId.G9){
      requiredFields.push({prop: 'semester', label:'sa_classrooms_m_termformate_error'});
    }
    // if (this.g9DemoData.teachers.list.length > 0){
    //   requiredFields.push(
    //     {prop: 'teacher_uid', label: 'sa_classrooms_col_teachers'}
    //   );
    // }
    const schoolClassId = classroom.id;

    for (let i = 0; i < requiredFields.length; i++) {
      const prop = requiredFields[i].prop;
      const label = requiredFields[i].label;
      const val = classroom[prop];
      if ((typeof val === 'undefined' || val === null || val.toString().trim() === '')&&(prop != 'semester')) {
        setTimeout(() => this.loginGuard.quickPopup(this.whiteLabelService.isABED() ? this.lang.tra("abed_required_field", null, { fieldName: this.lang.tra(label) }) : this.lang.tra("sa_required_field", null, { fieldName: this.lang.tra(label) })), 0);
        return false;
      }
      if(prop == 'class_code'){
        const matchesOtherName = this.classrooms.find(classroom =>{
          if (classroom.class_code.toLowerCase().replace(/\W/g, '') === val.toLowerCase().replace(/\W/g, '')){
            if (+classroom.id !== +schoolClassId){
              console.log('class code', classroom.id, schoolClassId)
              return true;
            }
          }
        })
        // if (matchesOtherName && !this.classInInactiveWindow(val)){
        //   setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('sa_duplicate_classcode')), 0);
        //   return false;
        // }
      }
      // todo: move to API 
      if(prop == 'class_code'){
        if(this.currentClassFilter === ClassFilterId.G9 && val.length > 20){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('brc_classcode_2')), 0);
          return false;
        }
        if(this.currentClassFilter === ClassFilterId.OSSLT && val.length > 25){
          setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('brc_grouping_2')), 0);
          return false;
        }
      }

      if (prop == 'semester' && (val =='A'||val =='null'||val ==undefined||val == '')) {
        setTimeout(() => this.loginGuard.quickPopup(this.lang.tra(this.lang.tra(label))), 0);
        return false;
      }
    }
    return true;
  }
  exportClassroomModalStart() {
    const classrooms = this.classroomsTable.getFilteredData();
    const columns = this.getClassroomExportColumns();
    const exportData = this.getExportData(classrooms, columns);
    downloadFromExportData(exportData, columns, 'classes', this.auth);
  }

  importClassroomModalStart() {
    const columns = this.getClassroomExportColumns();
    const config: IModalImport = {
      hideTable: false,
      columns
    }
    this.pageModal.newModal({
      type: ClassroomModal.IMPORT,
      config,
      finish: this.importClassroomModalFinish
    });
  }

  importClassroomModalFinish = (config: { importData: any[] }) => {
    const importedData = this.extractTeacherIds(config.importData);

    if(importedData.inOldTestWindow.length >0){
      this.updateSemester(importedData.inOldTestWindow, true)
    }

    return this.auth
      .apiCreate(
        this.routes.SCHOOL_ADMIN_CLASSES,
        importedData.refinedData,
        this.configureQueryParams()
      )
      .then(res => {
        var successCreate:any[]=[];
        var failCreate:any[]=importedData.duplicated;

        res.forEach(element => {
          if(element.id === -1){
            failCreate.push(element.name);
          }else{
            const newClassroomRecord= this.importClassesTempSave.find(saveclass => saveclass.uuid == element.uuid)
            newClassroomRecord.id = element.id;
            this.classrooms.splice(0, 0, newClassroomRecord);
            this.classroomsTable.injestNewData(this.classrooms);
            this.g9DemoData.classrooms.push(newClassroomRecord);
            this.g9DemoData.teacherClassrooms.list.push(newClassroomRecord)
            this.g9DemoData.teacherClassrooms.map[newClassroomRecord.id] = newClassroomRecord;
            successCreate.push(element.name)
          }
        });
        this.importClassesTempSave = [];
        this.classroomsTable.injestNewData(this.classrooms);
        var resultMessage:string = '';
        if(successCreate.length>0){
          resultMessage += "<b>"+this.lang.tra("msg_classes_import_success")+"</b>"
          successCreate.forEach(sc => resultMessage += "<br>" + sc)
          resultMessage += "<br>"
          resultMessage += "<br>"
        }

        if(importedData.inOldTestWindow.length >0){
          resultMessage += "<b>"+this.lang.tra("msg_classes_import_transfer")+"</b>"
          importedData.inOldTestWindow.forEach(sc => resultMessage += "<br>" + sc)
          resultMessage += "<br>"
          resultMessage += "<br>"
        }

        if(failCreate.length>0){
          resultMessage += "<b>"+this.lang.tra("msg_classes_import_failure")+"</b>"
          failCreate.forEach(sc => sc?resultMessage += "<br>" + sc:resultMessage += "<br>")
        }
        setTimeout(() => this.loginGuard.quickPopup(resultMessage), 0);
        this.pageModal.closeModal();
      })
      .catch(err => { 
        if (err.message === "DUPL_CLASS_CODE") { 
          alert('One or more classcodes are duplicated!') 
        }
        else if (err.message === "Invalid_Term_Format") { 
          alert('Term format is not valid!') 
        }
        else{
          alert('Fail to import classes')
        }
        this.pageModal.closeModal(); 
      });
  }

  classInInactiveWindow(className:string){
    let group_type;
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        group_type = "EQAO_G3"
      break;
      case ClassFilterId.Junior:
        group_type = "EQAO_G6"
      break;
      case ClassFilterId.G9:
        group_type = "EQAO_G9"
      break;
      case ClassFilterId.OSSLT:
        group_type = "EQAO_G10"
      break;
      case ClassFilterId.ABED_SAMPLE:
        group_type = "ABED_SAMPLE"
      break;
      default:
        group_type = "EQAO_G3"
      break;  
    }
    //const group_type = this.currentClassFilter === ClassFilterId.G9 ? "EQAO_G9":"EQAO_G10"
    const theClass = this.classrooms.find(classroom => classroom.class_code === className && classroom.course_type === group_type)
    if(theClass === undefined) {
      return false
    }
    const classSemester = this.g9DemoData.semesters.list.find(sm => sm.id === +theClass.semester)
    const classTestWindow = this.g9DemoData.testWindows.find( tw => tw.id === +classSemester.testWindowId)
    return (new Date(classTestWindow.date_end) < new Date ()) 
  }

  extractTeacherIds(importData) {
    let schoolData: any = this.g9DemoData.schoolData
    let refinedData = []
    let duplicated = [];
    let inOldTestWindow = []
    let course_type;
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        course_type = "EQAO_G3"
      break;
      case ClassFilterId.Junior:
        course_type = "EQAO_G6"
      break;
      case ClassFilterId.G9:
        course_type = "EQAO_G9"
      break;
      case ClassFilterId.OSSLT:
        course_type = "EQAO_G10"
      break;
      case ClassFilterId.ABED_SAMPLE:
        course_type = "ABED_SAMPLE"
      break;
      default:
        course_type = "EQAO_G3"
      break;  
    }
    //const course_type = this.currentClassFilter === ClassFilterId.OSSLT ? 'EQAO_G10' : 'EQAO_G9';
    const stringEqualCompare = (str1: string, str2: string): boolean => {
      if (str1 === null || str1 === undefined) {
        return false;
      }
      if (str2 === null || str2 === undefined) {
        return false;
      }

      if (str1.trim().toLowerCase() === str2.trim().toLowerCase()) {
        return true;
      }

      return false;
    };
    let test_window_type
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        test_window_type = "EQAO_G3P"
      break;
      case ClassFilterId.Junior:
        test_window_type = "EQAO_G6J"
      break;
      case ClassFilterId.G9:
        test_window_type = "EQAO_G9M"
      break;
      case ClassFilterId.OSSLT:
        test_window_type = "EQAO_G10L"
      break;
      default:
        test_window_type = "EQAO_G3P"
      break;  
    }
    //const test_window_type =  this.currentClassFilter === ClassFilterId.G9?"EQAO_G9M":"EQAO_G10L"
    //const validTestwindows = this.g9DemoData.testWindows.filter( tw => tw.type_slug === test_window_type && new Date(tw.date_end) > new Date ())
    const validTestwindows = this.g9DemoData.testWindows.filter( tw => tw.id == this.currentTestWindow.id)
    importData.forEach(data => {
      var semester 
      if(this.currentClassFilter === ClassFilterId.Primary){
        semester = this.g9DemoData.semesters.list.find(semester => 
          (validTestwindows.find(vtw => vtw.id == semester.testWindowId) !== undefined)
        )
      }
      if(this.currentClassFilter === ClassFilterId.Junior){
        semester = this.g9DemoData.semesters.list.find(semester => 
          (validTestwindows.find(vtw => vtw.id == semester.testWindowId) !== undefined)
        )
      }
      if(this.currentClassFilter === ClassFilterId.G9){
        semester = this.g9DemoData.semesters.list.find(semester => 
          stringEqualCompare(semester.label, data.semester) 
          && (semester.foreign_scope_id == null || semester.foreign_scope_id == undefined)
          && (validTestwindows.find(vtw => vtw.id == semester.testWindowId) !== undefined)
        )
      }
      if(this.currentClassFilter === ClassFilterId.OSSLT || ClassFilterId.ABED_SAMPLE){
        semester = this.g9DemoData.semesters.list.find(semester => 
          (validTestwindows.find(vtw => vtw.id == semester.testWindowId) !== undefined)
        )
      }

      const semester_id = semester ? semester.id: -1;
      let name = '' + data.first_name + ' ' + data.last_name;
      let teacherRecord = this.g9DemoData.teachers.list.find(teacher => teacher.invigilator == name.trim())
      const uuid = uuidv4();
      let payload = {
        uuid:uuid,
        schl_group_id: schoolData.group_id,
        schl_dist_group_id: schoolData.schl_dist_group_id,
        name: data.eqao_g9_class_code,
        semester_id: semester_id,
        foreign_id: '',
        educator_id: teacherRecord ? teacherRecord.id : '',
        course_type
      }
      let classroom: IClassroom = {
        ...data,
        class_code: payload.name,
        uuid:uuid,
        educator: teacherRecord ? name : '',
        semester: semester_id,
        semester_label : data.semester,
        id: randId(),
        students: 0,
        onboarding: 0,
        assessment: 0,
        course_type
      };
      if (this.validateClassroom(classroom)) {
        // pull the class from invlaid test window to new test window
        if(this.classInInactiveWindow(classroom.class_code)){
          inOldTestWindow.push(classroom.class_code)
        }else{
          this.importClassesTempSave.push(classroom);
          refinedData.push({ ...payload })
        } 
      } else {
        duplicated.push(classroom.class_code);
      }
    })
    return {
      inOldTestWindow: inOldTestWindow,
      refinedData: refinedData,
      duplicated: duplicated
    };
  }

  private getClassroomExportColumns = (): IExportColumn[] => {
    let columns: IExportColumn[] = [];
    let modifiedLabels = {}
    const exportList = [...APIColumnExportList];
    if (this.currentClassFilter === ClassFilterId.Primary) {
      modifiedLabels = {
        class_code: 'ClassCode', // 'ClassCode'
        first_name: 'TeacherFirstName', // 'TeacherFirstName'
        last_name: 'TeacherLastName', // 'TeacherLastName'
      };
      exportList.splice(exportList.indexOf('semester'),1)
    }else if (this.currentClassFilter === ClassFilterId.Junior) {
      modifiedLabels = {
        class_code: 'ClassCode', // 'ClassCode'
        first_name: 'TeacherFirstName', // 'TeacherFirstName'
        last_name: 'TeacherLastName', // 'TeacherLastName'
      };
      exportList.splice(exportList.indexOf('semester'),1)
    }
    else if (this.currentClassFilter === ClassFilterId.G9){
      modifiedLabels = {
        class_code: 'ClassCode', // 'ClassCode'
        semester: 'TermFormat',
        first_name: 'TeacherFirstName', // 'TeacherFirstName'
        last_name: 'TeacherLastName', // 'TeacherLastName'
      };
    }else if (this.currentClassFilter === ClassFilterId.OSSLT || ClassFilterId.ABED_SAMPLE) {
      modifiedLabels = {
        class_code: 'Grouping', // 'ClassCode'
        first_name: 'TeacherFirstName', // 'TeacherFirstName'
        last_name: 'TeacherLastName', // 'TeacherLastName'
      };
      exportList.splice(exportList.indexOf('semester'),1)
    }
    
    exportList.forEach(source => {
      const target = this.g9DemoData.getAPITargetMapping(source);
      columns.push({
        prop: target,
        caption: modifiedLabels[source] || source,
        isClickable: false,
        cssStyles: source === 'class_code' ? {whiteSpace: 'pre-wrap'} : null
      });
    });
    return columns;
  }

  private getExportData(
    classrooms: Partial<IClassroom>,
    columns: IExportColumn[]
  ): IClassroom[] {

    return classrooms.map((classroom): Partial<IClassroom> => {
      let entry: Partial<IClassroom> = {};
      const educatorName = (classroom.educator || '').trim()
      const first_name = educatorName.split(' ')[0];
      const last_name = educatorName.substr(educatorName.indexOf(" ") + 1);
      columns.forEach(col => {
        const prop = col.prop;
        let exportVal;
        if (prop === 'eqao_g9_class_code') {
          exportVal = classroom['class_code'];
        }
        if (prop === 'first_name') {
          exportVal = first_name
        }
        if (prop === 'last_name') {
          exportVal = last_name
        }
        if (prop === 'semester') {
          exportVal = this.renderSemester(classroom.semester)
        }
        exportVal
        entry[prop] = exportVal;
      });
      return entry;
    });
  }

  archiveSelectedClassrooms() {
    let confirmationMsg = "";
    let deleteNotAllowedMsg = "";
    if (this.whiteLabelService.isABED()) {
      confirmationMsg = this.lang.tra("sa_delete_classrooms_ABED");
      deleteNotAllowedMsg = this.lang.tra("abed_cannot_be_deleted");
    } else {
      confirmationMsg = this.lang.tra("sa_delete_classrooms");
      deleteNotAllowedMsg = this.lang.tra("sa_cannot_delete_classrooms");
    }

    // will refactor into listSelect service later.doing this way for now so as not to break other pages.
    // this.listSelectService.archiveTableEntry(this.classrooms, this.classroomsTable,confirmationMsg);
    const targets = [].concat(this.classrooms).filter(entry => entry.__isSelected);

    // if one of the selected class/grouping has ongoing session, block the delete class action
    let isDeleteAllowed = true;
    for(let i=0; i<targets.length; i++){
      const onGoingSessions = this.g9DemoData.getOngoingSessionsByClassroomId(targets[i].id);
      if(onGoingSessions && onGoingSessions.length > 0) {
        isDeleteAllowed = false;
        break;
      }
    }
    if(!isDeleteAllowed){
      this.loginGuard.confirmationReqActivate({
        caption: deleteNotAllowedMsg,
        btnCancelConfig: {
          hide: true
        },
      });
      return;
    } 
    
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      confirm: async () => {
        for (let entry of targets){
          await this.archiveClassroom(entry.id)
            .then(res => {
              this.updateArchivedClass(entry);
              this.updatePlaceholderClassAndStudents(entry);
              this.updateTeacherInfo(entry);
            }).catch(err => {
              if (err.message === "PLACEHOLDER_CLASS") {
                this.loginGuard.quickPopup(this.lang.tra('alert_placeholder_class_remove'))
              }
            });
        }
      }
    })  
  }

  updateArchivedClass(entry: any) {
    const i = this.classrooms.indexOf(entry);
    this.classrooms.splice(i, 1);
    this.classroomsTable.injestNewData(this.classrooms);
    const j = this.g9DemoData.classrooms.indexOf(entry);
    this.g9DemoData.classrooms.splice(j,1);
    const k = this.g9DemoData.teacherClassrooms.list.indexOf(entry);
    this.g9DemoData.teacherClassrooms.list.splice(k,1);
    delete this.g9DemoData.teacherClassrooms.map[entry.id];
    const classOptIdx = this.g9DemoData.classOptions.list.findIndex(classOption => +classOption.id === +entry.id);
    if (classOptIdx !== -1) {
      this.g9DemoData.classOptions.list.splice(classOptIdx, 1);
    }
    delete this.g9DemoData.classOptions.map[entry.id];
  }

  updatePlaceholderClassAndStudents(entry: any) {
    // update placeholder class (from this test window and semester)'s student count on the UI
    const placeholderClassIdx = this.g9DemoData.classrooms.findIndex(classroom => 
      classroom.is_placeholder === 1 && 
      this.g9DemoData.filterClassroomTestWindow(classroom.id, +this.currentTestWindow.id));

    if (placeholderClassIdx !== -1) {
      const oldStudentCount = entry.students == null ? 0 : entry.students;
      const placeholderStudentCount = this.g9DemoData.classrooms[placeholderClassIdx].students;
      this.g9DemoData.classrooms[placeholderClassIdx].students = placeholderStudentCount == null 
      ? oldStudentCount : placeholderStudentCount + oldStudentCount;

      // update students in deleted class to be in the placeholder class  on the UI
      this.g9DemoData.schoolAdminStudents.list.forEach(student => {
        if (+student.classCode === +entry.id) {
          student.classCode = this.g9DemoData.classrooms[placeholderClassIdx].id; 
          student.classLabel = this.g9DemoData.classrooms[placeholderClassIdx].class_code;

          // this shouldn't be needed, but I think there is some hardcoded key names still
          // in the codebase still, so to avoid risking breaking it I am doing it, to be safe
          student.class_code = student.classCode;
          student.class_label = student.classLabel;
          const classLabelKey = classLabelABEDKeys.find(key => key.includes(this.currentClassFilter.toLocaleLowerCase()));
          const classCodeKey = classCodeABEDKeys.find(key => key.includes(this.currentClassFilter.toLocaleLowerCase()));
          if (classLabelKey != null) {
            student[classLabelKey] = student.classLabel;
          }

          if (classCodeKey != null) {
            student[classCodeKey] = student.classCode;
          }
          
          
          this.g9DemoData.schoolAdminStudents.map[student.uid] = student;
        }
      });
    }
  }

  updateTeacherInfo(entry: any) {
    let teacher = this.g9DemoData.teachers.list.find(teacher => teacher.id == entry.teacher_uid);
    if (teacher)
    {
      //get the teachers current classes and turn them into an array
      let classes = teacher.classCode.split(", ");

      //delete the deleted class as an array item
      const deletedClassIdx = classes.findIndex(classCode => entry.class_code === classCode);

      classes.splice(deletedClassIdx, 1);
      //convert the classes that now include the new one to the expected format 
      teacher.classCode = classes.join(', ');

      if (teacher.classCode[0] === ',')
      {
        // remove unneccesary ", " if any
        teacher.classCode = teacher.classCode.slice(2);
      } 

      // update corresponding map
      this.g9DemoData.teachers.map[entry.teacher_uid] = teacher;
    }
  }

  loadLearningFormat(){
    this.classrooms.forEach(classroom =>{
      const students = this.g9DemoData.getStudentsByClassroomId(String(classroom.id));
      if(students)
      {
        const learningFormats = students.list.map(student => student.eqao_learning_format);
        classroom.learningFormats = learningFormats.filter( (value, index, self) => {return self.indexOf(value) === index;});
      } 

    })
    // console.log();
  }

  renderLearningFormat(learningFormats){
    if(learningFormats){
      return learningFormats.map(learningFormat => {
        const Learning_Format:any  = this.currentClassFilter === ClassFilterId.G9?Learning_Format_G9:Learning_Format_OSSLT;
        return this.lang.tra(renderMappedValue(Learning_Format, learningFormat))
      });
    }else{
      return '';
    }  
  }

  isCurrentTestWindowActive(){
    if(this.currentTestWindow){
      return new Date(this.currentTestWindow.date_end) > new Date ()
    }
    return false;
  }

  pageChanged() {
    // if (!this.classroomSelections.isAllSelected) {
    //   this.classrooms.forEach(classroom => classroom.__isSelected = false);
    // }
  }
  
  getTestWindowViewText(tw){
    if(tw){
      const startDate = formatDate(new Date(tw.date_start), 'MMM yyyy', 'en_US')
      const endDate = formatDate(new Date(tw.date_end), 'MMM yyyy', 'en_US')
      const isActive = (new Date(tw.date_end) > new Date ())? "lbl_active":"lbl_inactive"
      return startDate+" "+this.lang.tra("lbl_date_to") +" "+endDate+" ("+this.lang.tra(isActive)+")";
    } 
  }

  noOtherTestWindow(){
    let test_window_type

    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        test_window_type = "EQAO_G3P"
      break;
      case ClassFilterId.Junior:
        test_window_type = "EQAO_G6J"
      break;
      case ClassFilterId.G9:
        test_window_type = "EQAO_G9M"
      break;
      case ClassFilterId.OSSLT:
        test_window_type = "EQAO_G10L"
      break;
      case ClassFilterId.ABED_SAMPLE:
        test_window_type = "ABED_SAMPLE"
      break;
      default:
        test_window_type = "EQAO_G3P"
      break;  
    }

    this.testWindowsToAssign = this.g9DemoData.testWindows.filter( tw => {
      if(tw.type_slug === test_window_type){
        if(this.isCurrentTestWindowActive()){
          if(new Date(tw.date_end) < new Date ()){
            return tw;
          }
        }else{
          if(new Date(tw.date_end) > new Date ()){
            return tw;
          }
        }
      }
    })
    if(this.testWindowsToAssign.length === 0){
      return true;
    }
    return false;
  }


  groupings = [
    ClassFilterId.G9, 
    ClassFilterId.OSSLT, 
    ClassFilterId.TCLE, 
    ClassFilterId.TCN, 
    ClassFilterId.SCIENCES8, 
    ClassFilterId.MBED_SAMPLE, 
    ClassFilterId.SMCS_G7_EN,
    ClassFilterId.ABED_SAMPLE,
    ClassFilterId.ABED_GRADE_6,
    ClassFilterId.ABED_GRADE_9,
    ClassFilterId.ABED_GRADE_12
  ];

  isCurrentFilterGroupings = (currentClassFilter: ClassFilterId): boolean => {
    return this.groupings.indexOf(currentClassFilter) !== -1;
  }
  
  //get URL to download template 
  getClassTemplate(){
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        return "sa_g3_class_template_url" 
      case ClassFilterId.Junior:
        return "sa_g6_class_template_url"
      case ClassFilterId.G9:
        return "sa_class_template_url" 
      case ClassFilterId.OSSLT:
        return "sa_osslt_class_template_url"
      case ClassFilterId.ABED_SAMPLE:
        return "sa_osslt_class_template_url"
      default:
        return "sa_g3_class_template_url"
    }
  }

  getInvigilators(classroom:any){
    let invigilators = '';
    const class_group_id = this.g9DemoData.teacherClassrooms.map[classroom.id].group_id
    const classInvigilators = class_group_id?this.g9DemoData.invigilators.filter(invig => invig.group_id == class_group_id):[]
    classInvigilators.forEach(invig => {
      const invigName = invig.first_name + " " + invig.last_name;
      invigilators = invigilators == '' ? invigName : invigilators + ', '+ invigName
    })
    return invigilators == '' ? null : invigilators;
  }

  shouldEditOrArchiveBtnsBeDisabled(): boolean {
    return !this.classroomSelections.isAnySelected || !this.isCurrentTestWindowActive() ||
    this.isPlaceholderClassSelected();
    ;
  }

  isPlaceholderClassSelected(): boolean {
    return (this.classrooms.findIndex(classroom => classroom.__isSelected && classroom.is_placeholder) !== -1);
  }

  getNewClassSectionSlug(){
    if(this.whiteLabelService.isNBED()){
      return 'new_class_section_nbed';
    }
    if(this.whiteLabelService.isABED()){
      return 'new_grouping_section_ABED'
    }
    return 'new_class_section';
  }

  getClassroomDataInfoSlug(){
    if(this.whiteLabelService.isNBED()){
      return 'classroom_data_info_nbed';
    }
    if(this.whiteLabelService.isABED()){
      return 'classroom_data_info_ABED'
    }
    return 'classroom_data_info';
  }
  
  getCreateNewSlug(){
    if (this.whiteLabelService.isABED()){
      return 'sa-create-new-grouping-ABED'
    }
    return 'sa_classrooms_new'
  }

  getTableHeaderClassCode(){
    if (this.whiteLabelService.isABED()){
      return "sa_classrooms_col_class_code_ABED"
    }
    return 'sa_classrooms_col_class_code'
  }

  getClassesReqFilterSlug(){
    if (this.whiteLabelService.isABED()){
      return 'txt_msg_classes_req_filter_ABED'
    }
    return 'txt_msg_classes_req_filter'
  }

  selectClassGroup(classroom){
    console.log(classroom, ' selected')
    this.selectGroup.emit(classroom);
  }

  getEditSlug() {
    if (this.whiteLabelService.isABED()) {
      return "abed_edit";
    }
    return "sa_classrooms_edit";
  }

  getEditGroupingSlug() {
    if (this.whiteLabelService.isABED()) {
      return "abed_edit_grouping";
    }
    return "sa_classrooms_edit";
  }

  getDeleteSlug() {
    if (this.whiteLabelService.isABED()) {
      return "abed_delete";
    }
    return "sa_classrooms_delete";
  }

  getAssignToTestWindow() {
    if (this.whiteLabelService.isABED()) {
      return "abed_assign_test_window";
    }
    return "sa_classrooms_assign_tw";
  }

  getTeacherSlug(){
    return this.whiteLabelService.getSiteText("teacher_title", "sa_class_educator")
  }

  configureRemoveTeachQueryParams() {
    const schoolData: any = this.g9DemoData.schoolData
    if (schoolData) {
      return {
        query: {
          schl_group_id: schoolData.group_id,
          lang: this.lang.c(),
          action: actions.UNASSIGN
        }
      }
    }
    return null;
  }

  handleUnassignTeacherPopup(){
    const classrooms = this.classroomSelections.getSelected();
    const names = classrooms.map(c => c.class_code).join(', ');
    const ids   = classrooms.map(c => c.id);
    this.loginGuard.confirmationReqActivate({
      caption: this.lang.tra('sa_classroom_unassign_popup', null, {CLASSROOM: names}),
          confirm: async () => {
            await this.handleUnassignTeacher(ids);
          }
    })
  }

  async handleUnassignTeacher(ids){
    try{
      await this.auth.apiPatch(this.routes.SCHOOL_ADMIN_CLASSES, null, ids, this.configureRemoveTeachQueryParams());
      //updating local record:
      for(let id of ids){
        const classroom = this.classrooms.find(c => c.id == id);
        classroom.educator = null;
        for(let invig of this.g9DemoData.invigilators){
          if(invig.group_id == classroom.group_id){
            invig.group_id = -1;
          }
        }
      }
    }catch(e){console.log(e)}
  }

  disableUnassignTeachersBtn(){
    if(!this.classroomSelections.isAnySelected){
      return true;
    }
    return false;
  }

}
