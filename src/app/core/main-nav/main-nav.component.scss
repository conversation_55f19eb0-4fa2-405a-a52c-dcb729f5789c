
@import './style/common.scss';

.is-hidden { 
    display:none;
}

.user-profile {
    height: $topDivHeight;
    overflow: hidden;
    border-bottom: solid 1px #ccc;
    cursor: pointer;
    &.dropdown {
        width: 100%;
    }
    .dropdown-trigger { 
        width: 100%;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0.8em 0.8em;
        $avatar-diameter: 3em;
        .avatar-holder {
            background-color: #d3d3d3;
            background-image: url(https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/default-avatar/1633303426783/default-avatar.png);
            background-position: center;
            background-repeat: no-repeat;
            background-size: cover;
            height:$avatar-diameter;
            width:$avatar-diameter;
            border-radius: 100%;
            margin: 0.2em;
            overflow:hidden;
            img {
                height:$avatar-diameter;
                width:$avatar-diameter;
            }
        }    
        a {
            color: #333;
            font-weight: 200;
            line-height: 100%;
            .account-subtitle {
                font-size:70%;
                color: #767676;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: 11em; // any more and it will push the the photo out of view.
            }
            .main {
                max-width: 11em;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
        }
    }
    
}

.account-mgmt-well {
    user-select:none;
    &.is-collapsed {
        display:none;
    }
}

.panel-links {
    display:flex;
    flex-direction: column;
}

.panel-link-header {
  border-left-width: 5px;
  padding: 0.5em 0.8em;
  font-size: larger;
}

.panel-link {
    border-left-width: 5px;
    display:flex;
    align-items:center;
    padding: 0.5em 0.8em;
    .is-subpanel {
      margin-left: 0.5em;
    }
    img.panel-icon {
        // flex-grow: 1;
        margin-right: 0.5em;
        filter: saturate(0);
        width:32px;
        height:32px;
        max-width:32px;
        max-height:32px;    
    }
    .panel-link-caption {
        color: #333;
        flex-grow: 3;
    }
    &:hover {
        border-color: $menuSelectionColor;
    }
    &.is-active, &:hover {
        border-color: $menuSelectionColor;
        .panel-link-caption {
            color: $menuSelectionColor;
        }
        border-left-width: 5px;
        img.panel-icon {
            filter: saturate(1);
            animation: pop 200ms cubic-bezier(0.64, 0.57, 0.67, 1.53);
        }
    }

}



.left-panel {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: absolute;
    top:0px;
    left:0px;
    right:0px;
    bottom:0px;
    overflow-y: auto;
    overflow-x: hidden;
    &.is-collapsed {
        .user-profile {
            a {
                display:none;
            }
            .icon {
                display:none;
            }
            .dropdown-trigger { 
                justify-content: center;
                padding-left: 0em;
                padding-right: 0em;
            }
        }
        .panel-link {
            justify-content: center;
            .panel-icon {
                margin-right: 0em;
            }
        }
        .panel-link-caption {
            display:none;
        }
    }
}

.logo-container {
    text-align:center;
    padding: 5px;
}

.expander-collapser {
    text-align:right; 
    padding-bottom:0.2em;
    button {
        &:focus {
            border-radius: 2px;
            box-shadow: 0 0 0 0.125em #000000 !important;
            outline: none;
        }
    }
}

@keyframes pop {
    0% {
        // opacity: 0;
        transform: scale(0.5) ;
    }
    100% {
        // opacity: 1;
        transform: scale(1);
        
    }
}