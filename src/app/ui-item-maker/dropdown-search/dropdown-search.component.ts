import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { EditingDisabledService } from '../editing-disabled.service';

@Component({
  selector: 'dropdown-search',
  templateUrl: './dropdown-search.component.html',
  styleUrls: ['./dropdown-search.component.scss']
})
export class DropdownSearchComponent implements OnInit {

  @Input() list: Array<{id:string, caption:string}>;
  @Output() onSelect = new EventEmitter<string>();
  
  filteredList: Array<{id:string, caption:string}> = [];
  isShowDropdown = false;
  focusTimer = null;
  isHoverDropdown = false;

  constructor(private editingDisabled: EditingDisabledService) { }

  ngOnInit() {
    this.filterList("");
  }
  
  onFocus(val: string){
    clearTimeout(this.focusTimer);
    this.isShowDropdown = true;
    this.filterList(val);
  }
  onBlur(){
    this.focusTimer = setTimeout(() => {
      if (this.isHoverDropdown){
        this.onBlur();
      } else {
        this.isShowDropdown = false;
      }
    }, 100);
  }
  
  hideDropdown(){
    clearTimeout(this.focusTimer);
    this.isShowDropdown = false;
  }
  
  filterList(val: string){
    const query = val.toLowerCase();
    this.filteredList = this.list.filter(item => item.caption.toLowerCase().includes(query));
  }
  
  selectItem(id:string){
    this.onSelect.emit(id);
    this.hideDropdown();
  }

  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  setMouseHoverState(state: boolean) {
    this.isHoverDropdown = state;
  }

}
