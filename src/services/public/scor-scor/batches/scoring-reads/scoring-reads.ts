import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Errors } from '../../../../../errors/general';
import { currentUid } from '../../../../../util/uid';
import { dbRawRead, dbRawReadSingle } from '../../../../../util/db-raw';
import { AssignedItemComponentType } from '../../summary/types/assigned-tasks';
import { SQL_EXISTING_BATCH_GROUP_FROM_GROUP_ID, SQL_ITEM_COUNTS_BY_READ, SQL_READ_COUNTS_BY_ASSESSMENT, SQL_SCORER_SCORING_TASK } from '../../../../../sql/scoring';
import { dbDateNow } from '../../../../../util/db-dates';
import { snakeify } from '../../../../../util/caseify';
import { IExistingBatchGroup } from '../claim/claim.class';
import { mtcRec } from '../../../scor-lead/force-marker-claim/force-marker-claim.class';

interface Data {
  [key: string]: any
}

interface ServiceOptions {}

export class ScoringReads implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if(!params || !params.query || !params.query.mw_id ) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    const {mw_id} = params.query;

    const markCountsByRead = await dbRawRead(this.app, {mw_id}, SQL_READ_COUNTS_BY_ASSESSMENT)

   return markCountsByRead;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  /**
   * Used to create a new empty read from a batch group ID
   * @param data 
   * @param params 
   * @returns 
   */
  async create (data: {claimedBatchgroupId: number, scorer_uid?: number, mcbrInfo: {supervisorNote?: string | null, is_marked?: number}, is_sent_back?: number}, params?: Params): Promise<Data> {    
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }
    if(!data || !data.claimedBatchgroupId) {
      throw new Errors.BadRequest('MISSING_DATA');
    }
    
    const {claimedBatchgroupId, scorer_uid, mcbrInfo, is_sent_back} = data;

    const mw = await dbRawReadSingle(this.app, {claimedBatchgroupId}, `
    select mw.is_rescore 
    from marking_windows mw
    join marking_window_items mwi 
      on mwi.marking_window_id = mw.id
    join marking_claimed_batch_responses mcbr 
      on mcbr.window_item_id = mwi.id
    where mcbr.claimed_batch_group_id  = :claimedBatchgroupId
    group by mw.id
      `)
    const is_rescore: number = mw.is_rescore;

    let sent_back = 0;
    if(is_sent_back){
      sent_back = is_sent_back
    }
    const uid = scorer_uid ? scorer_uid : await currentUid(this.app, params);

    // Get the marking taqr cache records associated with the batch group responses
    const mtcRec: mtcRec[] = await dbRawRead(this.app, {claimedBatchgroupId}, `
      SELECT mtc.taqr_id, mtc.mwi_id, mwi.group_to_mwi_id, mwi.slug, mtc.schl_group_id, mtc.student_accommodation_cache
      FROM marking_claimed_batch_responses mcbr
      JOIN marking_window_items mwi 
        on mwi.id = mcbr.window_item_id 
      JOIN marking_taqr_cache mtc
          ON mtc.taqr_id = mcbr.taqr_id 
          AND mtc.mwi_id = mcbr.window_item_id 
      where claimed_batch_group_id = :claimedBatchgroupId
      group by mtc.id, mtc.mwi_id 
      ; 
    `)

    if(mtcRec && mtcRec.length <= 0) {
      throw new Errors.GeneralError('NO_MTC_FOUND_FOR_REPOOL');
    }

    // Get the latest read supression information from the batch group
    const existingBatchGroup: IExistingBatchGroup[] = await dbRawRead(this.app, {claimedBatchgroupId}, SQL_EXISTING_BATCH_GROUP_FROM_GROUP_ID);

    // Hand off to force-marker-claim for creation logic
    return await this.app.service('public/scor-lead/force-marker-claim').assignBatch(mtcRec, existingBatchGroup, uid, mcbrInfo, is_rescore, claimedBatchgroupId);
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  //Here, is inspected
  async patch (id: number, data: Data, params: Params): Promise<Data> {
    if(!params || !params.query || !params.query.marker_read_id) {
      throw new Errors.BadRequest('MISSING_PARAMS')
    }
    const marker_read_id = params.query.marker_read_id;
    const batchGroupId = id;

    const responses = await dbRawRead(this.app, [marker_read_id, batchGroupId], `
      SELECT * 
      FROM marking_claimed_batch_responses
      WHERE marker_read_id = ?
      AND claimed_batch_group_id = ?
    `)

    if (data.inspected == 1){
      const uid = await currentUid(this.app, params);
      data.inspected_by_uid = uid
      data.inspected_on = dbDateNow(this.app)
    } else if (data.inspected == 0 && data.inspected != undefined) {
      data.inspected_by_uid = null
      data.inspected_on = null
    }

    responses.forEach(async (resp) => {
      await this.app.service('db/write/marking-claimed-batch-responses').patch(resp.id, data);
    });

    return [{success: true}];
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
