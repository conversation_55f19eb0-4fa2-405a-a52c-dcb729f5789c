import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
const axios = require('axios');

// const API_ROOT = 'http://localhost:3031';
const API_ROOT = 'https://eqao2-api.vretta.com';
const stressTestVersion = '3.0'

const intervalSeconds = 5
let numStudents = 0;
let numTeachers = 0;
const interval = setInterval(() => {
  // console.log({
  //   Student_Concurrent: numStudents/intervalSeconds,
  //   Teachers_Concurrent: numTeachers/intervalSeconds,
  // })
  numStudents = 0;
  numTeachers = 0;
}, intervalSeconds*1000);

const axiosCreate = async (tracker:any[], headers:any, route:string, data:any, params?:any) => {
  const request = {route, method:'create', start: +new Date(), end: 0, isError: false};
  tracker.push(request);
  try {
      const res = (await axios({
          method: 'post',
          url: API_ROOT+route,
          data,
          headers,
      })).data;
      request.end = +new Date();
      return res
  }
  catch (e){
    request.end = +new Date();
    request.isError = true;
    // console.error(route, e)
  }
}

const axiosGet = async (tracker:any[], headers:any, route:string, params?:any) => {
  const request = {route, method:'get', start: +new Date(), end: 0, isError: false};
  tracker.push(request);
  try {
    const res = (await axios({
        method: 'get',
        url: API_ROOT+route,
        headers,
        params: { ... params, stressTestVersion}
    })).data;
    request.end = +new Date();
    return res
  }
  catch (e){
    request.end = +new Date();
    request.isError = true;
    // console.error(route, e)
  }
}

const authenticate = async (tr:any[], payload:any) => {
  const authRes = await axiosCreate(tr, {}, '/authentication',  payload);
  return {  
      'Content-Type': 'application/json', 
      'Authorization': 'Bearer ' + authRes.accessToken
  }
}

const sleep = (ms:number) => {
  return new Promise(resolve => setTimeout(resolve, ms));
}
interface IStudentCred { 
  oen:string, 
  access_code:string,
  sc_gid: number,
  session_id: number,
}
interface ITeacherCred { 
  email:string, 
  password:string,
  s_gid: number,
  sc_id: number, 
  sc_gid: number, 
  ts_id: number, 
}

interface Data {
  secret: string,
  isTeacher:boolean,
  isStudentSimple:boolean,
  iterations:number,
  batchSize:number,
  credList:(IStudentCred | ITeacherCred)[],
}

interface ServiceOptions {}

export class Student implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (data.secret !== 'sdfs0fhs9df8hsdf9sdfsdf') { throw new Error(); }
    /* no await -- intentional, this is a long running request */ 
    this.runStressTest(
      data.iterations,
      data.batchSize, 
      data.credList,
      {isTeacher: data.isTeacher, isStudentSimple: data.isStudentSimple},
    );
    return <any> {};
  }

  async runStressTest(iterations:number, BATCH_SIZE:number, credList:(IStudentCred | ITeacherCred)[], config:{isTeacher:boolean, isStudentSimple:boolean} ){
    
    const stressSessionId = Math.floor(Math.random()*999999);
    const {isTeacher, isStudentSimple} = config;
   

    if (!iterations){
      iterations = 1
    }

    const tr:any[] = [];

    const processStudent = async (studentCred:IStudentCred) => {
      numStudents++;
      let h = await authenticate(tr, {
        "strategy":"loginKey",
        "studentNumber": studentCred.oen,
        "accessCode": studentCred.access_code,
        "isSasnLogin": 0
      });
      await axiosGet(tr, h, '/public/test-auth/style-profiles/1?slug=gr9styleprofile.json&pullFromS3=1')
      await axiosGet(tr, h, '/public/anon/sample-test-design-form/404?lang=en');
      await axiosGet(tr, h, `/public/student/session?schl_class_group_id=${studentCred.sc_gid}&allow_inactive_subsession=true`);
      await sleep(10+(Math.floor(Math.random()*10))*1000)
      await axiosGet(tr, h, '/public/test-auth/style-profiles/1?slug=osslt-styleprofile.json&pullFromS3=1')
      await axiosGet(tr, h, '/public/test-auth/style-profiles/1?slug=osslt-styleprofile.json&pullFromS3=1')
      await axiosCreate(tr, h, '/public/log', {"slug":"_STUDENT_ACCESS_DOC","data":{"uid":-1,"session_id":-2,"state":{"section_index": -1}}})
      await axiosGet(tr, h, `/public/student/map/${studentCred.session_id}`)
      const attempts = await axiosGet(tr, h, `/public/student/session/${studentCred.session_id}?schl_class_group_id=${studentCred.sc_gid}`)
      const attempt = attempts[0]
      const attemptId = attempt.attemptId
      for (let i=0; i<10; i++){
        await sleep(20*1000);
        await axiosCreate(tr, h, `/public/student/session-question?schl_class_group_id=${studentCred.sc_gid}`, {"test_question_id":-100000000+Math.random()*999999,"question_index":Math.floor(Math.random()*6),"question_caption":"Text","section_index":0,"module_id":null,"__CMPR_response_raw":"N4Ig+mC2CmAuCGIBcwC+qg==","__CMPR_response":"Q===","test_attempt_id":attemptId})
      }
      
      // numStudents--;
    }

    const processStudentSimple = async (studentCred:IStudentCred) => {
      let h = await authenticate(tr, {
        "strategy":"loginKey",
        "studentNumber": studentCred.oen,
        "accessCode": studentCred.access_code,
        "isSasnLogin": 0
      });
      await axiosGet(tr, h, '/public/test-auth/style-profiles/1?slug=gr9styleprofile.json')
      await axiosGet(tr, h, '/public/student/session/72716?schl_class_group_id=259755');
      await axiosGet(tr, h, '/public/ping');
    }

    const processTeacher = async (cred:ITeacherCred) => {
      numTeachers++;
      let h = await authenticate(tr, {
        "strategy":"local",
        "email": cred.email,
        "password": cred.password,
      });
      if (Math.random()<0.3){
        await axiosGet(tr, h, '/public/educator/school?schl_group_id='+cred.s_gid)
      }
      await axiosGet(tr, h, `/public/educator/session-questionnaire?account_type=educator&slug=TEACHER_QUESTIONNAIRE`);
      await axiosGet(tr, h, `/public/educator/health-check`);
      await sleep(10*1000);
      // const newSession = await axiosCreate(tr, h, `/public/educator/session?school_class_group_id=${cred.sc_gid}`, {"school_class_id":cred.sc_id,"slug":"PRIMARY_SAMPLE","isScheduled":true,"scheduled_time":["2022-06-10","2022-06-12","2022-06-10","2022-06-12"],"is_fi":false});
      await sleep(10*1000);
      // console.log('ts start', cred.ts_id);
      const session =  await axiosGet(tr, h, `/public/educator/session/${cred.ts_id}?school_class_group_id=${cred.sc_gid}&school_class_id=${cred.sc_id}`);
      await sleep(5*1000);
      
      const scanInfo = await axiosGet(tr, h, `/public/educator/class-scan-info/${cred.sc_id}`);
      const hearbeat = () => axiosGet(tr, h, `/public/educator/session/${cred.ts_id}?school_class_group_id=${cred.sc_gid}&school_class_id=${cred.sc_id}`); // dynamic?

      // await axiosCreate   (tr, h, `/public/educator/session-sub/72716?school_class_group_id=259755`, {"subSessionId":256068,"twtdarOrder":0,"isClosing":false,"studentUids":[1925266],"classId":cred.sc_id,"isPJ":true,"isSubmitting":false}) /* student unlock */
      for (let i=0; i<10; i++){
        await sleep(60*1000);
        await hearbeat()
      }

      // console.log('ts end', cred.ts_id);
      // numTeachers--;
    }

    for (let iter = 0; iter < iterations; iter++){
      for (let i=0; i<(credList.length+BATCH_SIZE-1); i+=BATCH_SIZE){
        const apiPatches = [];
        for (let j=0; j<BATCH_SIZE; j++){
          const cred = credList[i+j];
          if (isTeacher){
            apiPatches.push( processTeacher(<ITeacherCred> cred) )
          }
          else if (isStudentSimple){
            apiPatches.push( processStudentSimple(<IStudentCred> cred) )
          }
          else {
            apiPatches.push( processStudent(<IStudentCred> cred) )
          }
        }
        await Promise.all(apiPatches);
      }
    }
    // clearInterval(interval);
    console.log( 'STRESS_TEST_STATS:'+ JSON.stringify(tr) );

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error();
  }
}


