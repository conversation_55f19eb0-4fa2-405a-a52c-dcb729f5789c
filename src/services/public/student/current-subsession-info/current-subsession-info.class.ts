import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class CurrentSubsessionInfo implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest();
    }
    const uid = await currentUid(this.app, params);
    const test_session_id = id;

    const subSessions = await dbRawRead(this.app, [test_session_id, uid], 
      `SELECT tsss.id, tsss.slug, tsss.order, tsss.twtdar_order, tsss.caption, tass.is_submitted, ta.active_sub_session_id FROM
      test_session_sub_sessions tsss
      JOIN test_sessions ts ON tsss.test_session_id = ts.id
      JOIN test_attempts ta ON ta.test_session_id = ts.id
      JOIN test_attempt_sub_sessions tass ON ta.id = tass.test_attempt_id
      WHERE tass.sub_session_id = tsss.id
      AND ts.id = ?
      AND ta.uid = ? 
      AND tass.is_invalid != 1
      `
    )

    if(!subSessions?.length) {
      throw new Errors.NotFound('NO_ATTEMPT');
    }

    return subSessions;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
