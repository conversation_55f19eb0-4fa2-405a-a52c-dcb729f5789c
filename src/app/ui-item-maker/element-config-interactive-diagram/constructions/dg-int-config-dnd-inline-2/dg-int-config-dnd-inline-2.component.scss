@import '../../element-config-interactive-diagram.component.scss';

.target-container {
  margin-bottom: 1em;
}

.target-name-input-label {
  max-width: 10em;
}
.target-group-unused-label {
  max-width: 4em;
}

.target-option-textarea {
  display: inline-block;
}

.list-editor-bookmark-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5em;
  span {
    width: 10em;
  }
  margin-top: 0.2em;
}
