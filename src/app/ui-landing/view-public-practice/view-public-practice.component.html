<div class="page-container" [class.is-abed]="isABED()">
    <div class="header-container" [class.is-abed]="isABED()">
      <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
    </div>
    <div class="panel-container" [class.is-abed]="isABED()">
      <div class="column is-8">
  
        <div class="public-practice-title">
          <div class="public-practice-title-big">
            <tra [slug]="getPracticeTestTitleSlug()"></tra>
          </div>
          <div>
            <tra [slug]="getPracticeTestDescriptionSlug()"></tra>
          </div>
        </div>

        <ng-container [ngSwitch]="!!selectedPracTestRec">
        <!-- Show links -->
          <div *ngSwitchCase="true" class="public-practice-links">
            <!-- navigator -->
            <button class="back-button" (click)="selectedPracTestRec = null"><i class="fas fa-arrow-left"></i></button>
            <!-- list assessments -->
            <ul>
              <li *ngFor="let record of getSelectedPracTestRec()">
                <div class="public-practice-links-row">
                  <a [routerLink]="getTestRunnerRoute(record)" target="_blank">
                    <span [innerHTML]="getDesignAllocCaption(record)" ></span>
                  </a>
                  <a
                    *ngIf="hasAllocResources(record)"
                    style="width: 30%;"
                    [routerLink]="getTestRunnerRoute(record, true)"
                    target="_blank"
                  >
                    {{getResourceAllocCaption(record)}}
                  </a>
                </div>
              </li>              
            </ul>
          </div>
          <!-- show tiles -->
          <ng-container *ngSwitchCase="false">
            <div class="flex" >
              <button *ngFor="let twId of getPracTestRecordsTwsIds()" class="card" (click)="selectedPracTestRec = twId">
                <tra-md class="title" [slug]="getTestWindowTitle(twId)" [isCondensed]="true"></tra-md>
                <tra-md class="sub-title" [slug]="getTestWindowSubTitle(twId)" [isCondensed]="true"></tra-md>
              </button>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>
  