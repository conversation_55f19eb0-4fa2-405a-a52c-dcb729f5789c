import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

export class AdminWindowTfPanelMapping implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  getOSSLTTestFormRecords(){
    return dbRawRead(this.app, [], `
      select tf.source_tf_id panel_number
          , twtar.slug
          , tf.id tf_id
          , tf.file_path
          , tf.created_on
          , (tf.created_on < '2021-09-10 17:35:32') is_previous_window
          , count(distinct ta.uid) num_students
      from test_attempts ta
      join test_attempt_question_responses taqr on taqr.test_attempt_id = ta.id and taqr.is_invalid = 0
      join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id and scts.slug IN ('OSSLT_OPERATIONAL')
      join test_sessions ts on ts.id = scts.test_session_id
      join test_windows tw on tw.id = ts.test_window_id and tw.is_active=1
      join school_classes sc on sc.id = scts.school_class_id
      join schools s on s.group_id = sc.schl_group_id
      join school_districts sd on sd.group_id = sc.schl_dist_group_id and sd.is_sample = 0
      join test_window_td_alloc_rules twtar on twtar.id = ta.twtdar_id and twtar.is_scheduled = 1
      join test_forms tf on tf.id = ta.test_form_id
      where ta.started_on is not null
      group by tf.id
    ;`);
  }

  getTestDesignTestFormRecords(testDesignId:number[]){
    return dbRawRead(this.app, [testDesignId], `
      select tf.test_design_id
          , tf.source_tf_id panel_number
          , tf.id tf_id
          , tf.file_path
          , tf.created_on
          , td.framework
          , tf.lang
          , if(twtar.type_slug is not null, twtar.type_slug, twtarh.type_slug) type_slug
      from test_forms tf
      join test_designs td on td.id = tf.test_design_id
      left join test_window_td_alloc_rules twtar on twtar.test_design_id = td.id
      left join test_window_td_alloc_rules_history twtarh on twtarh.test_design_id = td.id
      where tf.test_design_id in (?)
      group by tf.id
    ;`);
  }

  getTestFormRecordsByIds(testFormIds:number[]){
    return dbRawRead(this.app, [testFormIds], `
      select tf.source_tf_id panel_number
          , tf.id tf_id
          , tf.file_path
          , tf.created_on
      from test_forms tf
      where tf.id in (?)
    ;`);
  }

  getItemIdsByItemLabels(itemLabels: string[]) {
    return dbRawRead(this.app, [itemLabels], `
      select question_label, id, is_archived
      from test_questions 
      where question_label in (?)
    ;`)
  }

  getCachedItemIds(itemLabels: string[], questionDict: any) {
    const itemIds: string[] = [];
    for (let label of itemLabels) {
      let question = questionDict.find((q: any) => q.question_label === label && q.is_archived != 1);
      if (!question) question = questionDict.find((q: any) => q.question_label === label);
      if (question) itemIds.push(question.id);
    }
    if (itemIds.length === itemLabels.length) {
      return itemIds;
    } else {
      throw new Error('Question(s) not found.');
    }
  }

  async getPanelModulesByQuestionIds(firstSectionQues: number[], testDesign: any) {
    const labels = testDesign.panels.flatMap((p: any) => p.modules.flatMap((m: any) => m.itemLabels));
    const questionDict = await this.getItemIdsByItemLabels(labels);

    const panelToFind = testDesign.panels.find((panel: any) => {
      const missing = firstSectionQues.filter(itemId => {
        const firstModule = panel.modules.find((m:any) => m.moduleId == 1)
        let cachedItemIds = firstModule.__cached_itemIds;
        if (!cachedItemIds) {
          cachedItemIds = this.getCachedItemIds(firstModule.itemLabels, questionDict);
        }
        return !cachedItemIds.includes(itemId)
      });
      if (missing.length < 1) {
        return panel;
      }
    });
    
    for (let module of panelToFind.modules) {
      if (!module.__cached_itemIds) {
        module.__cached_itemIds = this.getCachedItemIds(module.itemLabels, questionDict);
      }
    }
    return panelToFind.modules;
  }

  getSectionNum (sectionIndex: number) {
    switch (sectionIndex) {
      case 0:
        return 1;
      case 1:
        return 2;
      case 2:
        return 2;
      case 3:
        return 3;
      case 4:
        return 4;
      case 5:
        return 4;
      default:
        throw new Error('unknown sectionIndex: ' + sectionIndex);
    }
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    let testFormRecords;

    if (params && params.query && params.query.test_design_id){
      return this.parseTestDesignPanelMapping(params.query.test_design_id)
    } 
    else {
      // sectionToSession = {1:1, 2:1, 3:1, 4:2, 5:2, 6:2,} // this only applies to OSSLT, data should be moved into configuration at some point so that it is not hard-coded
      // testFormRecords = await this.getOSSLTTestFormRecords();
      throw new Errors.BadRequest();
    }
  }

  async parseTestDesignPanelMapping(test_design_ids:number[]){
    let testFormRecords;

    testFormRecords = await this.getTestDesignTestFormRecords(test_design_ids)

    console.log('testFormRecords', testFormRecords.length);
    
    const questions:any[] = [];

    for (let testFormRecord of testFormRecords){
      console.log('gen: ', testFormRecord.panel_number)
      const test_design_id = testFormRecord.test_design_id
      const test_panel_number = testFormRecord.panel_number
      const test_panel_id = testFormRecord.tf_id
      const num_students = testFormRecord.num_students || 0
      const lang = testFormRecord.lang
      const typeSlug = testFormRecord.type_slug || ''
      const testForm =  await this.app.service('public/student/session').getTestDesign(testFormRecord.file_path);
      const testDesign = JSON.parse(testFormRecord.framework);
      if (testForm){
        if (testDesign && testDesign.testFormType === 'MSCAT') {
          const modules = await this.getPanelModulesByQuestionIds(testForm.sections[0].questions, testDesign);
          modules.forEach((module: any, sectionIndex: number) => {
            const sectionNum = this.getSectionNum(sectionIndex);
            module.__cached_itemIds.forEach((ques: any) => {
              questions.push({
                test_design_id,
                test_panel_number,
                test_panel_id,
                question_id: ques, 
                module_id: module.moduleId,
                sub_session: null,
                section_num: sectionNum, 
                lang
              })
            })
          })
        } else {
          const sectionToSession: any = typeSlug.startsWith('OSSLT_') ? {1:1, 2:1, 3:1, 4:2, 5:2, 6:2,} : {}; // this only applies to OSSLT, data should be moved into configuration at some point so that it is not hard-coded
          testForm.sections.map((section:any, sectionIndex:number) => {
            section.questions.forEach((question_id:number) => {
              const section_num = sectionIndex +1
              const sub_session = sectionToSession[section_num] ?? null
              questions.push({
                test_design_id,
                test_panel_number,
                test_panel_id,
                question_id, 
                module_id: null,
                sub_session,
                section_num, 
                lang
              })
            })
          })
        }
      }
      else{
        console.error('Cannot read test form', testFormRecord.file_path)
      }
    }

    return questions;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const testFormIds = <number[]> data;
    const testFormRecords = await this.getTestFormRecordsByIds(testFormIds);
    for (let tfRecord of testFormRecords){
      tfRecord.testForm =  await this.app.service('public/student/session').getTestDesign(tfRecord.file_path);
    }
    return testFormRecords;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
