import { Component, OnInit, Input, Output,EventEmitter} from '@angular/core';
import { FormControl } from '@angular/forms';
import { LangService } from '../../core/lang.service';
import { ISaFormEntry, SaFormType, getSubValProp } from 'src/app/ui-schooladmin/sa-widget-form-entry/sa-widget-form-entry.component';


@Component({
  selector: 'sb-widget-form-entry',
  templateUrl: './sb-widget-form-entry.component.html',
  styleUrls: ['./sb-widget-form-entry.component.scss']
})
export class SbWidgetFormEntryComponent implements OnInit {


  @Input() formEntries:ISaFormEntry[];
  @Input() formEntry:ISaFormEntry;
  @Input() disabled:boolean;
  @Output() selected = new EventEmitter();
  constructor(
    private lang: LangService
  ) { }

  @Input() formEntryLabelContainerStyle: object = null;

  SaFormType = SaFormType;

  ngOnInit(): void {
    if (this.formEntry.defaultSelection){
      this.disabled = true;
      this.formEntry.formControlRef.setValue(this.formEntry.defaultSelection);
    }
  }

  isLabel(){
    return (this.formEntry.type === SaFormType.LABEL);
  }
  isVertical(){
    return (this.formEntry.isVertical || this.isLabel());
  }
  isHorizontal(){
    return !this.isVertical();
  }
  showSelected($event){
   this.selected.emit($event)
  }
  getFormEntryById(id:string):ISaFormEntry{
    let match;
    if (this.formEntries){
      this.formEntries.forEach(entry => {
        if (entry.valProp === id){
          match = entry;
        }
      })
    }
    return match;
  }

  getVal(){
    return this.formEntry.formControlRef.value;
  }
  setVal(val:boolean | number | string){
    this.formEntry.formControlRef.setValue(val);
  }

  setValById(id:string, val:boolean | number | string){
    const entry = this.getFormEntryById(id);
    if (entry){
      entry.formControlRef.setValue(val);
    }
  }
  getValById(id:string){
    const entry = this.getFormEntryById(id);
    if (entry){
      return entry.formControlRef.value;
    }
  }
  
  getSubValProp(valProp: string, tag: string): string{
    return getSubValProp(valProp, tag);
  }
  
  getSubFormControl(valProp: string, tag: string): FormControl {
    if (valProp && tag){
      const subValProp = getSubValProp(valProp, tag)
      // console.log('subValProp', subValProp)
      return this.getFormEntryById(subValProp).formControlRef;    
    }
    else{
      return this.formEntry.formControlRef
    }
  }

}
