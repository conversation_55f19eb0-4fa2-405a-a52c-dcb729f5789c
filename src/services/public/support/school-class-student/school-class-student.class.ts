import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';

interface Data {}

interface ServiceOptions {}

export class SchoolClassStudent implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Error();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Error();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (params){
      const {
        schoolGroupId,
        testWindowIds,
        classGroupName,
        student,
      } = <any> data;
      const created_by_uid = await currentUid(this.app, params);
      const schoolClass = await dbRawReadSingle(this.app, [classGroupName, testWindowIds, schoolGroupId], `
        SELECT sc.id, sc.group_id 
        from schools s 
        join school_classes sc 
          on sc.schl_group_id  = s.group_id 
          and sc.name = ?
        join school_semesters ss 
          on ss.id = sc.semester_id 
          and ss.test_window_id in (?)
        where s.group_id = ?;
      `);
      if (schoolClass){
        const newStudent = await this.app
          .service('public/dist-admin/student')
          .createStudentRecordForClassroom(
            student,
            schoolClass.id,
            created_by_uid,
          )
        return newStudent;
      }
      else {
        return {isFailed: true, student};
      }
    }
    throw new Error('BAD_REQUEST')
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Error();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Error();
  }
}
