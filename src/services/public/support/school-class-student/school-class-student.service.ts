// Initializes the `public/support/school-class-student` service on path `/public/support/school-class-student`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SchoolClassStudent } from './school-class-student.class';
import hooks from './school-class-student.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/support/school-class-student': SchoolClassStudent & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/support/school-class-student', new SchoolClassStudent(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/support/school-class-student');

  service.hooks(hooks);
}
