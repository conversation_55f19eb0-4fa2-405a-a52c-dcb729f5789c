@import '../styles/drag-drop.scss';
@import '../../../styles/partials/_modal.scss';

.custom-modal {
  @extend %custom-modal;
}

$selectedColor: #209cee;
$darkGrey: #555;
$lightGrey: #d0d0d0;;

$menuSelectionColor: #1c90f3;
$topDivHeight: 5em;
$leftPanelWidth: 4em;
$leftPanelWidthExpanded: 16em;

// top: 0.5em;
//     left: 0.5em;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     background-color: #4dc187f2;
//     font-size: 1em;
//     padding: 0.3em 1em;
//     font-weight: 600;
//     color: rgb(255, 255, 255);
// position:fixed;
// top: 0px; left: 0px;
//  right: 0px;

.save-indicator {
  background-color: #f1f1f1;
  color: #ccc;
  display:flex;
  justify-content: center;
  align-items: center;
  font-size: 1em;
  padding:0.3em 0.5em;
  font-weight: 600;
  margin-bottom: 1em;
  &.is-saving {
    background-color: rgba(62, 207, 135, 0.37);
    color: rgba(20, 143, 81, 0.5);
  }
}

.is-hidden {
  display:none;
}

$questionPanelWidth: 14em;
$rightPanelWidth: 34em;
$rightPanelExpandedWidth: 54em;
$roundCornerSmall: 0.2em;


.is-hidden {
  display:none;
}
.panels-container {
  position:fixed;
  top:0px;
  bottom:0px;
  left:0px;
  right:0px;
  transition: left 400ms;

  &.is-left-panel-visible {
    left: $leftPanelWidth;
    &.is-left-panel-expanded {
      left: $leftPanelWidthExpanded;
    }
  }
}
.panel-questions, .panel-preview, .panel-editor {
  position:absolute;
  top:0px;
  bottom:0px;
}
.panel-questions {
  left:0px;
  width:$questionPanelWidth;
  padding: 1em;
}
.panel-preview {
  left:$questionPanelWidth;
  right:$rightPanelWidth;
  &.is-condensed {
    right:$rightPanelExpandedWidth;
  }
  // background-color: #f1f1f1;
  border-left: 2px solid #ccc;
  overflow: auto;
  &>div {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100vh;
  }
  .question-container {
    padding: 2em;
  }
  .question-config {
    margin-top:2em;
    padding: 2em;
    background-color: #f1f1f1;
    border-top: 1px solid #ccc;
  }
}
.panel-editor {
  width:$rightPanelWidth;
  right:0px;
  padding: 3em;
  padding-top: 1em;
  border-left: 1px solid #ccc;
  background-color: #fff;
  &.is-expanded {
    width:$rightPanelExpandedWidth;
    border-left: 2px solid #ccc;
  }
  overflow: auto;
}

.section-header {
  margin-top:2em;
  margin-bottom:0.5em;
  font-size: 120%;
  font-weight: 900;
  color: $darkGrey;
}

.stack-edit-container {
  border-radius: 2*$roundCornerSmall;
  box-shadow: 8px 8px 8px rgba(0,0,0, 0.2);
  background-color: #f1f1f1;
  // margin: 2em;
  padding:0.5em;
}
.stack-edit-element {
  margin: 0.5em;
  border-radius: $roundCornerSmall;
  background-color: #fff;
  overflow: hidden;
  .stack-edit-element-header {
    padding: 0.2em;
    display: flex;
    justify-content: space-between;
    color: $lightGrey;
    margin-bottom: 0.5em;
    transition: 200ms;
    cursor: move;
    &:hover {
      background-color: lighten($selectedColor, 0.2);
      color: #fff;
    }
  }
  .stack-edit-element-config {
    padding: 0.4em 1.2em;
  }
}


.panel-questions {
  display:flex;
  flex-direction: column;
  justify-content: space-between;
}
.question-list-container{
  display:flex;
  flex-direction: column;
  align-items: center;
  user-select:none;
  overflow:auto;

}
.question-block {
  width:5em;
  height:2em;
  min-height:1.5em;
  font-size:2em;
  display:flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ccc;
  margin-bottom: 0.3em;
  cursor: pointer;
  .question-block-indicator {
    position: absolute;
    top: 0px;
    right: 3px;
    font-size: 12px;
    .is-confirmed { color: #83e8bb; }
    .is-unconfirmed { color: #f1f1f1; }
  }
  .question-block-label {
    font-size:0.5em;
  }
  &.is-info-slide {
    height:1em;
    justify-content: flex-start;
    padding: 0.2em;
    background-color: #ececec;
    .question-block-label {
      font-size:0.4em;
      font-weight: 600;
      text-align:left;
    }
  }
  &:hover {
    border: 1px solid $selectedColor;
  }
  &.is-active {
    border: 1px solid $darkGrey;
    color: #fff;
    background-color: $selectedColor;
  }

}


.element-selector-container {
  display:flex;
  flex-wrap: wrap;
  user-select: none;

}
.element-selector {
  width: 8em;
  height: 6em;
  margin: $roundCornerSmall;
  font-size:1em;
  font-weight:900;
  display:flex;
  flex-direction: column;
  border: 1px solid #ccc;
  color: $darkGrey;
  align-items: center;
  justify-content: center;
  border-radius: $roundCornerSmall;
  cursor: pointer;
  .icon {
    font-size: 1.6em;
    margin-bottom:0.2em;
  }
  &:hover {
    border: 1px solid $selectedColor;
  }
  &.is-disabled {
    color: #ccc;
    &:hover {
      border: 1px solid rgb(131, 100, 100);
    }
  }
}

.lang-btn-container{
  display: flex;
  margin-bottom: 1em;
  justify-content: center;
}

.mode-toggle {
  margin-bottom:2em;
  display:flex;
  flex-direction: row;
  justify-content: flex-end;
  .mode-toggle-option {
    margin-left:1em;
    padding: 0.3em;
    &.is-active {
      font-weight: 600;
      border-bottom: 2px solid #333;
    }
  }
}

.zoom-settings {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding-bottom:1em;
  border-bottom: 1px solid #ccc;
  margin-bottom:1em;
}

.advanced-setting-option {
  margin-bottom: 0.2em;
}

.simple-field {
  display:flex;
  flex-direction:row;
  justify-content: flex-start;
  margin-bottom: 0.7em;
  .simple-field-label {
    width: 6.5em;
    font-weight:bold;
  }
}
.pagination {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  >span {
    padding: 0.3em
  }
}
.item-detail-review {
  display: flex;
  flex-direction: row;
  .item-table-container {
    flex-grow:3;
    margin-top:2em;
    position:relative;
    max-height: 90vh;
    &.is-shared-w { max-width: 70vw; }
    overflow: auto;
    .item-table {
      td {
        height: 3.5em;
        vertical-align: middle;
      }
    }
  }
  .question-review {
    flex-grow:0;
    width: 32em;
    padding: 0.5em;
    padding-top: 2em;
    max-height: 90vh;
    overflow:auto;
  }
}
.framework-container {
  margin-top: 1em;
  margin-bottom: 1em;
  background-color: rgba(0,0,0,0.1);
  padding: 1em;
}
.param-card-container {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  .param-card {
    margin: 1em;
    background-color: #fff;
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
    padding: 0.5em;

    // :nth-child(3n+1) { order: 1; }
    // :nth-child(3n+2) { order: 2; }
    // :nth-child(3n)   { order: 3; }
  }
}

a.close {
  color: #ccc;
}

tr.is-reviewed td {
  background-color: #dbfad7;
}

.stat-header-indic {
  margin-bottom: -0.6em;
  font-size: 0.875em;
  font-weight: 300;
}

.checkbox-icon {
  font-size:1.25em;
  .fa-check-square {
    color: #3b80ff;
  }
}

.audit-table {
  .audit-table-col-num {width:3em;}
  .audit-table-col-name {width:16em;}
  .audit-table-col-btn {width:7em;}
  .audit-table-col-result {}
}

.print-question-table {
  .question-row {
    display: flex;
    flex-direction:row;
    // page-break-inside : avoid;
    border-bottom: 1px solid #ccc;
    padding-bottom: 1em;
    margin-bottom: 3em;

    .question-row-info {
      width: 18em;
      flex-grow: 0;
    }
    .question-row-content {
      // page-break-inside : avoid;
      flex-grow: 0;;
    }

  }
}

.pre-table-strip {
  margin-bottom:1em;
  display: flex;
  flex-direction:row;
  justify-content: space-between;
}

.buttons .button {
  margin: 5px;
}

.scroll-table-container {
  margin-bottom:2em; max-height:70vh; overflow: auto;
}

.section-settings-container {
  border-top: 1px solid #f1f1f1;
  border-bottom: 1px solid #f1f1f1;
  padding: 1em 0.5em;
  margin: 1em 0em;
  font-size: 0.8em;
  .section-setting {
    display: flex;
    flex-direction: row;
    align-items: center;
    .first-el { margin-right: 0.5em}
    margin-bottom:0.5em;
  }
  .section-setting-sub {
    border-left:1px dotted rgb(73, 168, 78);
    // margin-top:0.3em;
    padding-left:1em;
    margin-left:0.6em;
    margin-bottom:1em;
  }
}

.section-question-row {
  display: flex;
  flex-direction: row;
  margin-bottom:0.5em;
  &:hover {
    background-color: #f1f1f1;
  }
  button {
    flex-grow:0;
  }
  .question-label {
    flex-grow:1;
    padding:0.3em;
  }
}

.section-question-review {
  flex-grow: 0;
  width: 30em;
  padding: 1em;
  border-top: 1px solid #ccc;
  max-height: 90vh;
  overflow: auto;

}

.slide-top {
  display:none;
  &.is-active {
    position:absolute;
    top:0px;
    left:0px;
    display:block;
    padding:0.3em 0.5em;
    width:10em;
    background-color:#f1f1f1;
    border: 1px solid #ccc;
    box-shadow: 5px 5px 20px rgba(0,0,0,0.2);
    animation: slide-top 1000ms cubic-bezier(0.455, 0.030, 0.515, 0.955) both;
  }
}

@keyframes slide-top {
  0% {
    opacity: 0;
    transform: translateY(0);
  }
  20% {
    opacity: 1;
    transform: translateY(-10px) rotate(0deg);
  }
  80% {
    opacity: 1;
    transform: translateY(-15px) rotate(2deg) translateX(0px);
  }
  100% {
    opacity: 0;
    transform: translateY(-200px) rotate(0deg) translateX(40px);
  }
}


.new-comment-buttons {
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.6em;
  margin: 0.6em 0;
}

.comment-preview-img{
  max-height: 300px;
  object-fit: contain;
}