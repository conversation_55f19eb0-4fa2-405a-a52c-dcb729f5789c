<div class="admin-session">
    <div class="left-side">
        <div class="icon">
            <div class="calendar"></div>
            <div class="calendar-inner"></div>
        </div>
    </div>
    <div class="right-side">
        <div class="page-header">
            <div class="window-details">
                <div class="window-title"><tra-md slug="da_a_assessment_window"></tra-md></div>
                <div class="date-container">
                    <span class="caption">From:</span>
                    <span class="date">{{adminSessionStart}}</span>
                </div>
                <div class="date-container">
                    <span class="caption">To:</span>
                    <span class="date">{{adminSessionEnd}}</span>
                </div>
            </div>
            <div class="password-info"><a
                    href="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/2021-22 Sample Password Information Sheet/1629284794721/2021-22 Sample Password Information Sheet.pdf">
                    Password Info Sheet</a>
            </div>
        </div>

        <table class="component-table">
            <tr>
                <th>Component</th>
                <th>Component Code</th>
                <th>Session Password</th>
                <th>Status <span (click)="popupNotif()"><i class="fas fa-question-circle"></i></span></th>
            </tr>
            <tr *ngFor="let component of components, index as i">
                <td class="name">{{component.name}}</td>
                <td class="componentCode">{{component.componentCode}}</td>
                <td class="accessCode">
                    <div class="contents">
                        <div class="backgr">{{component.accessCode}}</div>
                    </div>
                </td>
                <td [ngClass]="{'active-tag': component.activityState, 'inactive-tag': !component.activityState }">
                    {{component.activityState ? 'Active' : 'Inactive'}}</td>
            </tr>
        </table>
    </div>

    <div class="custom-modal" *ngIf="cModal()">
        <div class="modal-contents">
            <div [ngSwitch]="cModal().type">
                <div *ngSwitchCase="CHANGE_ACCESS" style="width: 30em; text-align: left;">
                    <h3>Manually Editing Session Password</h3>
                    <br />
                    <div>Current Session Password: <span
                            class='access-code-tag'>{{components[cModal().config.index].accessCode}}</span></div>
                    <br />
                    <div>Please enter the new Session Password:</div>
                    <input [value]="components[cModal().config.index].accessCode" (keyup)="change($event)" />
                    <div class="s" *ngIf="newCodeError">{{newCodeError}}</div>
                </div>
            </div>
            <br />
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <button class="button is-info" (click)="finishChangeAccessCodeModal(cModal().config.index)"
                    [disabled]="!validateSessionPassword(newCode)"> Save
                    Password </button>
                <button class="button is-info" (click)="closeModal()"> Cancel </button>
            </div>
        </div>
    </div>
</div>