import { Component, OnInit } from '@angular/core';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { Router } from '@angular/router';
import { LangService } from '../../core/lang.service';
import { StudentG9ConnectionService } from '../../ui-student/student-g9-connection.service';
import { SidepanelService } from '../../core/sidepanel.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { FIELD_TEST_ASSESSMENTS } from '../view-field-test/data/assessments';

@Component({
  selector: 'view-login-router-st',
  templateUrl: './view-login-router-st.component.html',
  styleUrls: ['./view-login-router-st.component.scss']
})
export class ViewLoginRouterStComponent implements OnInit {

  constructor(
    private breadcrumbsService: BreadcrumbsService,
    private router:Router,
    public lang:LangService,
    private sidePanel: SidepanelService,
    private whiteLabel: WhitelabelService,
    private studentG9Connection: StudentG9ConnectionService
  ) { }

  public breadcrumb = [];
  dismissMessage:boolean;

  ngOnInit(): void {
    this.sidePanel.deactivate();
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.whiteLabel.isABED() ? this.lang.tra('abed_login_button') : this.lang.tra('lbl_login'), this.router.url),
    ];
    if (this.studentG9Connection.isConnected()) {
      this.studentG9Connection.disconnect();
    }
  }


  getAdminLoginSlug = () => this.whiteLabel.getSiteText( 'login_admins', 'lbl_administrators');

  ifFieldTest(isForced:boolean=false){
    if (!isForced && this.dismissMessage){
      return false;
    }
    return this.whiteLabel.getSiteFlag('IS_BC_FIELD_TEST_2021');;
  }
  getFieldTestAssessmentList(){
    return FIELD_TEST_ASSESSMENTS
  }

  getLoginAsSlug(){
    if(this.whiteLabel.isABED()){
      return "abed_login_as_ABED"
    }
    return "login_as_a"
  }

  getStudentButtonSlug(){
    if(this.whiteLabel.isABED()){
      return "abed_student"
    }
    return "lbl_student_1"
  }
  getGuideSlug() {
    return this.whiteLabel.isABED() ? 'abed_tech_guides_public_login_pg' : 'abed_tech_guides_logged_in'
  }
  getClickText() {
    return "abed_login_click_txt"
  }
  getPracticeTestTxt() {
    return this.whiteLabel.isABED() ? 'abed_login_practice_test_txt_ABED' : 'abed_login_practice_test_txt'
  }
  isTestCenter(){
    return this.whiteLabel.getSiteFlag('isTestCenter');
  }
  getPublicPracticeLink = () => {
    return `/${this.lang.c()}/${this.isTestCenter() ? 'candidate-public-practice' : 'public-practice'}`
  }

  runRespondusDiagnostics() {
    // redirect to /#/en/ldb-launch-page/debug/debug/0/2024-01-01
    this.router.navigate([this.lang.c(), 'ldb-launch-page', 'debug', 'debug', 0, '2024-01-01'])
  }
  isABED() {
    return this.whiteLabel.isABED();
  }
}
