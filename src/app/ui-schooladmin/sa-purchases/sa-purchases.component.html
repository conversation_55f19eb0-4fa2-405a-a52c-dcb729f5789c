<div class="assessment-sessions-view">
  <div *ngIf="!isPrivateSchool" style = "display:table">
    <div style = "display: table-cell;">
        <filter-toggles 
            [state]="getClassFilterToggles()"
            (id)="setClassFilter($event)"
        ></filter-toggles>
    </div>
    <div style = "display: table-cell;">
        <sa-test-window-filter
          [currentClassFilter] = "currentClassFilter"
          (setTestWindowEvent) = "setTestWindowFilter($event)"
        ></sa-test-window-filter>
    </div>   
  </div>
  <div *ngIf="isPrivateSchool" style = "display:table">
    <div style = "display: table-cell;">
        <filter-toggles 
            [state]="getPrivateSchoolClassFilterToggles()"
            (id)="setClassFilter($event)"
        ></filter-toggles>
    </div>
    <div style = "display: table-cell;">
        <sa-test-window-filter
        [currentClassFilter] = "currentClassFilter"
        (setTestWindowEvent) = "setTestWindowFilter($event)"
        ></sa-test-window-filter>
    </div>
  </div>

  <div *ngIf="!currentClassFilter">
    <tra-md slug="txt_msg_purchases_req_filter"></tra-md>
  </div>

  <div *ngIf="currentClassFilter">
    <tra-md slug="sa_purchases_header_line1"></tra-md>
    <p>
      <tra slug="sa_purchases_header_line2"></tra><a (click)="showRefundPolicy()"><tra slug="lbl_here"></tra>.</a>
    </p>
    <br>
    <div>
      <button class="button is-small has-icon" [disabled]="noInvoiceSelected()" (click)="viewInvoiceModalStart()">
        <span class="icon" style="margin-right: 0.3em"><i class="fas fa-archive"></i></span>
        <span><tra slug="btn_sa_view_invoice"></tra></span>
      </button>
    </div>
  </div>
  <div>
    <div class="pre-table-strip">
      <div>
        <b><tra slug="lbl_sa_purchases"></tra></b>
      </div>
      <div>
        <button class="button is-small has-icon" (click)="exportPurchases()">
            <span class="icon"><i class="fas fa-table"></i></span>
            <span><tra slug="btn_sa_payment_export"></tra></span>
        </button>
      </div>  
    </div>
    <ag-grid-angular
      class="ag-theme-alpine ag-grid-halfpage-vertical"
      [rowData]="purchases"
      [gridOptions]="purchasesGridOptions"
      [columnDefs]="purchasesColumnDefs"
      [defaultColDef]="purchasesDefaultColDef"
      [rowSelection]="'multiple'"
      [rowHeight]="40"
      (gridReady)="onPurchasesGridReady($event)"
      (rowSelected)="onPurchasesRowClick($event)">
    </ag-grid-angular>
  </div>
  <div>
    <div class="pre-table-strip" style="margin-top: 1em;">
      <div>
        <b><tra slug="lbl_pc_student_attempts"></tra></b>
      </div>
      <div>
        <button class="button is-small has-icon" (click)="exportStudentAttempts()">
            <span class="icon"><i class="fas fa-table"></i></span>
            <span><tra slug="btn_sa_payment_export"></tra></span>
        </button>
      </div>  
    </div>
    <ag-grid-angular
      class="ag-theme-alpine ag-grid-halfpage-vertical"
      [rowData]="studentAttempts"
      [gridOptions]="studentAttemptsGridOptions"
      [columnDefs]="studentAttemptsColumnDefs"
      [defaultColDef]="studentAttemptsDefaultColDef"
      [rowSelection]="'multiple'"
      [rowHeight]="40"
      (gridReady)="onStudentAttemptsGridReady($event)"
      (rowSelected)="onStudentAttemptsRowClick($event)">
    </ag-grid-angular>
  </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div [ngSwitch]="cModal().type" class="modal-contents" style="width:42em;">
    <div>
      <div *ngSwitchCase="PurchaseModal.PURCHASE_REFUND_POLICY">
          <sa-modal-purchase-refund-overview></sa-modal-purchase-refund-overview>
      </div>
      <div *ngSwitchCase="PurchaseModal.VIEW_INVOICE">
        <sa-modal-view-invoice [config]="cmc()"></sa-modal-view-invoice>
      </div>
    </div>
    <modal-footer *ngSwitchCase="PurchaseModal.PURCHASE_REFUND_POLICY" 
                  class="modal-refund-policy" 
                  [pageModal]="pageModal" 
                  [confirmButton]="false" 
                  [closeMessage]="'btn_close'">
    </modal-footer>
    <modal-footer *ngSwitchCase="PurchaseModal.VIEW_INVOICE" 
                  class="modal-refund-policy" 
                  [pageModal]="pageModal" 
                  [confirmButton]="false" 
                  [closeMessage]="'lbl_back'">
    </modal-footer>
  </div>
</div>