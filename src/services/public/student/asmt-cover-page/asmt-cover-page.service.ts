// Initializes the `public/student/asmt-cover-page` service on path `/public/student/asmt-cover-page`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { AsmtCoverPage } from './asmt-cover-page.class';
import hooks from './asmt-cover-page.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/asmt-cover-page': AsmtCoverPage & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/asmt-cover-page', new AsmtCoverPage(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/asmt-cover-page');

  service.hooks(hooks);
}
