import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { LangService } from 'src/app/core/lang.service';
import { PageModalController } from 'src/app/ui-partial/page-modal.service';
import { G9DemoDataService, G9_SLUG_TO_CAPTION } from '../g9-demo-data.service';
import { ClassFilterId } from '../my-school.service';
@Component({
  selector: 'sa-modal-session-payment-overview',
  templateUrl: './sa-modal-session-payment-overview.component.html',
  styleUrls: ['./sa-modal-session-payment-overview.component.scss']
})
export class SaModalSessionPaymentOverviewComponent implements OnInit {

  @Input() pageModal:PageModalController;
  @Input() sessionList: any[] = [];
  @Input() schoolName: string = '';
  @Input() assessmentName: string = '';
  @Input() administrationWindow: string = '';
  @Input() currentClassFilter: string = '';
  @Input() currentTestWindow: any;

  @Output() onStripePayment = new EventEmitter();
  @Output() onAlternativePayment = new EventEmitter();

  constructor(
    private g9DemoData: G9DemoDataService,
    public lang: LangService,
  ) { }
  
  studentList: any[] = [];
  totalCost: number;
  isSelectingSession: boolean;
  selectedSession: any;

  ngOnInit(): void {
    this.initSelectedSession();
  }

  getStudents() {
    const classroomIds = this.selectedSession.classroom_id;
    const students = [];

    this.g9DemoData.schoolAdminStudents.list.map(student => {
      if (this.currentClassFilter == ClassFilterId.G9 && student.eqao_g9_class_code){
        if (+classroomIds === +student.eqao_g9_class_code) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.OSSLT && student._g10_eqao_g9_class_code){
        if (+classroomIds === +student._g10_eqao_g9_class_code) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.Primary && student._g3_eqao_g9_class_code){
        if (+classroomIds === +student._g3_eqao_g9_class_code) {students.push(student)}
      } else if (this.currentClassFilter == ClassFilterId.Junior && student._g6_eqao_g9_class_code){
        if (+classroomIds === student._g6_eqao_g9_class_code) {students.push(student)}
      }
    });

    this.studentList = students.filter(student => !student.isPaid);
  }

  getTotalCost() {
    this.totalCost = this.studentList.length * this.currentTestWindow.price_per_student;
  }

  initSelectedSession() {
    if (this.sessionList) {
      if (this.sessionList.length === 1) {
        this.selectedSession = this.sessionList[0];
        this.getStudents();
        this.getTotalCost(); 
        return this.isSelectingSession = false;
      }
      if (this.sessionList.length > 1) {
        return this.isSelectingSession = true;
      }
    }
  }

  selectSession(session) {
    if(session){
      this.selectedSession = session;
      this.getStudents();
      this.getTotalCost(); 
      this.isSelectingSession = false;
    }
  }

  getAssessmentType(type_slug){
    switch (type_slug){
      case 'PRIMARY_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.PRIMARY_OPERATIONAL)
      case 'JUNIOR_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.JUNIOR_OPERATIONAL)
      case 'G9_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.G9_OPERATIONAL)
      case 'OSSLT_OPERATIONAL':
        return this.lang.tra(G9_SLUG_TO_CAPTION.OSSLT_OPERATIONAL)
      default:
        return ''  
    }
  }

  beginStripePayment() {
    this.onStripePayment.emit({
      totalCost: this.totalCost
    });
  }

  beginAlternativePayment() {
    const currentDate = new Date().toISOString();
    let sessionStartDate;
    this.sessionList.forEach(session => {
      if (!sessionStartDate) {
        sessionStartDate = session.startTimeUTC;
      }
      if (sessionStartDate) {
        if (sessionStartDate > session.startTimeUTC) {
          sessionStartDate = session.startTimeUTC;
        }
      }
    })

    const DAY_UNIT_IN_MILLISECONDS = 24 * 3600 * 1000
    const diffInMilliseconds = new Date(sessionStartDate).getTime() - new Date(currentDate).getTime()
    const diffInDays = diffInMilliseconds / DAY_UNIT_IN_MILLISECONDS

    this.onAlternativePayment.emit({
      totalCost: this.totalCost,
      diffInDays
    });
  }
}
