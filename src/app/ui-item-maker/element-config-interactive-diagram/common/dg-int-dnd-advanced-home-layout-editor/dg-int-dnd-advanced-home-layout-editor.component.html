<div>
  <div class="preview">
    <span>Preview</span>
    <div>
      <svg #svgElement class="interactive-diagram-svg-element"></svg>
    </div>
  </div>
  <div class="editor-container">
    <span>Editor</span>
    <div class="editor" *ngIf="homeConfig">
      <ng-container *ngTemplateOutlet="itemMakerUi; context: {element: homeConfig.advancedLayoutConfig}"></ng-container>
    </div>
  </div>
</div>

<ng-template #itemMakerUi let-element="element">
  <div class="element-header">
    <span>{{elementTypeCaption[element.type]}}</span>
  </div>
  <ng-switch [ngSwitch]="element.type">
    <div *ngSwitchCase="'vertical_container'"><ng-container *ngTemplateOutlet="verticalContainer; context: {element: element}"></ng-container></div>
    <div *ngSwitchCase="'grid_container'"><ng-container *ngTemplateOutlet="gridContainer; context: {element: element}"></ng-container></div> 
    <div *ngSwitchCase="'target_row'"><ng-container *ngTemplateOutlet="targetRow; context: {element: element}"></ng-container></div>
    <div *ngSwitchCase="'text_row'"><ng-container *ngTemplateOutlet="textRow; context: {element: element}"></ng-container></div>
    <div *ngSwitchCase="'left_label'"><ng-container *ngTemplateOutlet="leftLabel; context: {element: element}"></ng-container></div>
  </ng-switch>
</ng-template>

<ng-template #verticalContainer let-element="element">
  <div>
    <span>Vertical Container</span>
    <div class="children-container">
      <div *ngFor="let child of element.contents" class="space-between">
        <div class="child-element">
          <ng-container *ngTemplateOutlet="itemMakerUi; context: {element: child}"></ng-container>
        </div>
        <a class="button">
            <i class="fas fa-trash"  aria-hidden="true"></i>
        </a>   
      </div>
    </div>
  </div>
</ng-template>

<ng-template #gridContainer let-element="element">
  <div style="display: flex; gap: 1em;">
    <span>x padding: </span>
    <input
        type="number" step="0.1"  class="input is-small" 
        style="text-align:center; max-width: 10em;"
        [(ngModel)]="element.horizontalGap"
        (mousedown)="$event.stopPropagation()"
        (input)="rerender()"
    >
  </div>
  <div style="display: flex; gap: 1em;">
    <span>y padding: </span>
    <input
        type="number" step="0.1"  class="input is-small" 
        style="text-align:center; max-width: 10em;"
        [(ngModel)]="element.verticalGap"
        (mousedown)="$event.stopPropagation()"
        (input)="rerender()"
    >
  </div>
  <div>
    <div class="children-container" cdkDropList (cdkDropListDropped)="drop($event, element.contents)">
      <div *ngFor="let child of element.contents" class="space-between" style="align-items: flex-start;" cdkDrag>
        <div class="child-element list-item" style="flex-grow: 1;">
          <ng-container *ngTemplateOutlet="itemMakerUi; context: {element: child}"></ng-container>
        </div>
        <a class="button" (click)="removeElement(child, element.contents)" >
            <i class="fas fa-trash"  aria-hidden="true"></i>
        </a>   
      </div>
    </div>
    
    <dropdown-select-button
      [list]="gridContainerElementList"
      buttonCaption="Add"
      (onClick)="addElementFromType($event, element.contents)"
    ></dropdown-select-button>
    
    
  </div>
</ng-template>

<ng-template #targetRow let-element="element">
  <div class="space-between">
    <span>delimiter: </span>
    <input
        type="text" 
        class="input is-small" 
        style="text-align:center; max-width: 10em;"
        [(ngModel)]="element.delimiter"
        (mousedown)="$event.stopPropagation()"
        (input)="rerender()"
    >
  </div>
  <div class="space-between">
    <span>count: </span>
    <input
        type="number" 
        class="input is-small" 
        style="text-align:center; max-width: 10em;"
        [(ngModel)]="element.count"
        (mousedown)="$event.stopPropagation()"
        (input)="rerender()"
    >
  </div>
</ng-template>

<ng-template #textRow let-element="element">
  <div class="space-between">
    <span>delimiter: </span>
    <input
        type="text"  class="input is-small" 
        style="text-align:center; max-width: 10em;"
        [(ngModel)]="element.delimiter"
        (mousedown)="$event.stopPropagation()"
        (input)="rerender()"
    >
  </div>
  <div class="space-between">
    <span>font size: </span>
    <input
        type="number" step="0.1"  class="input is-small" 
        style="text-align:center; max-width: 10em;"
        [(ngModel)]="element.fontSize"
        (mousedown)="$event.stopPropagation()"
        (input)="rerender()"
    >
  </div>
  <div class="space-between" style="align-items: flex-start;">
    <span>texts: </span>
    <div>
      <div *ngFor="let txt of element.texts; let i = index; trackBy: trackByFn" class="text-row-text"
        style="display: flex; gap: 0.5em;"
      >
        <textarea
          textInputTransform
          [(ngModel)]="element.texts[i]"
          (mousedown)="$event.stopPropagation()"
          (input)="rerender()"
          style="min-width: 10em; max-width: 20em; margin-bottom: 0.5em"
          class="textarea is-small"
          cdkTextareaAutosize
          [cdkTextareaAutosize]="true"
          [cdkAutosizeMinRows]="2"
        ></textarea>
        <a class="button" style="margin-right: 0" (click)="removeElement(element.texts[i], element.texts)">
            <i class="fas fa-trash"  aria-hidden="true"></i>
        </a>   
      </div>
      <div>
        <button class="button" (click)="addEmptyString(element.texts)" style="float: right; margin-right: 0;">
          <i class="fas fa-plus" aria-hidden="true"></i>
        </button>
      </div>
    </div>
  </div>
</ng-template>

<ng-template #leftLabel let-element="element">
  <div class="space-between">
    <span>label: </span>
    <textarea
      textInputTransform
      [(ngModel)]="element.text"
      (mousedown)="$event.stopPropagation()"
      (input)="rerender()"
      style="min-width: 10em; max-width: 20em; margin-bottom: 0.5em"
      class="textarea is-small"
      cdkTextareaAutosize
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
    ></textarea>
  </div>
</ng-template>
