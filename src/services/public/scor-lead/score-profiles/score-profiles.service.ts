// Initializes the `score-profiles` service on path `/score-profiles`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ScoreProfiles } from './score-profiles.class';
import hooks from './score-profiles.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/scor-lead/score-profiles': ScoreProfiles & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/scor-lead/score-profiles', new ScoreProfiles(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/scor-lead/score-profiles');

  service.hooks(hooks);
}
