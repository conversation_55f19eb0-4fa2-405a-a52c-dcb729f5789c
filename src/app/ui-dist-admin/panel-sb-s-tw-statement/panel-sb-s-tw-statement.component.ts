import { Component, Input, OnInit } from '@angular/core';
import { AuthService } from 'src/app/api/auth.service';
import { ITestWindow, MyCtrlOrgService } from 'src/app/ui-testctrl/my-ctrl-org.service';

@Component({
  selector: 'panel-sb-s-tw-statement',
  templateUrl: './panel-sb-s-tw-statement.component.html',
  styleUrls: ['./panel-sb-s-tw-statement.component.scss']
})
export class PanelSbSTwStatementComponent implements OnInit {

  @Input() schoolDistrictGroupId: number;

  selectedTestWindowId:number;
  testWindows?:ITestWindow[]
  
  constructor(
    private auth: AuthService,
    private myCtrlOrg: MyCtrlOrgService,
  ) { }

  ngOnInit(): void {
    this.loadRecords()
  }

  async loadRecords(){
    this.testWindows = await this.auth.apiFind('public/dist-admin/sa-kit/test-windows', this.configureQueryParams())
  }

  selectTestWindow(){
    this.myCtrlOrg.selectedWindow = this.testWindows.find(r => r.id == this.selectedTestWindowId);
  }

  configureQueryParams(){
    return {
      query: {
        schl_dist_group_id: this.schoolDistrictGroupId,
      }
    }
  }

}
