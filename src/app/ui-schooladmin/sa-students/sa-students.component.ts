import { Component, OnInit, ElementRef, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import * as _ from 'lodash';
import * as moment from 'moment-timezone';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import {
  IAvailableSession,
  randId,
} from "../../ui-testadmin/demo-data.service";
import { LangService } from "../../core/lang.service";
import {
  IClassroom,
  IEQAOStudentAccommodationFilter,
  IStudentAccount,
  IStudentTestSession,
  IValidationError,
  IValidationResult,
  IValidationResults
} from '../data/types';
import { IColumnFilter, IColumnFilterType } from '../../ui-partial/custom-column-filter/custom-column-filter.component';
import { ActivatedRoute} from "@angular/router";
import { FilterSettingMode, IFilterSettingConfigValue } from '../../ui-partial/capture-filter-range/capture-filter-range.component';
import { IActiveModal } from "../../ui-partial/modal/types.ts/model";
import { Subscription } from "rxjs";
import { AvailableSessionsService } from "../../ui-testadmin/available-sessions.service";
import { AuthService, getFrontendDomain } from "../../api/auth.service";
import { RoutesService } from "../../api/routes.service";
import {
  AccountModal,
  IModalAssignClassroom,
  IModalAssignSession,
  IModalNewAccount,
  TestSessionSelection,
  UserAttr,
  IModalImport
} from './model/types';
import { PageModalController, PageModalService } from '../../ui-partial/page-modal.service';
import { ListSelectService } from '../../ui-partial/list-select.service';
import { G9DemoDataService, overlapVariables } from '../g9-demo-data.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { IMappedList, renderMappedValue, initMappedList } from "../data/util";
import { EQAO_DB_NULL, STUDENT_ASSIST_TECH_1, STUDENT_G9_COURSES, STUDENT_YES_NO, STUDENT_OSSLT_SUCC, Learning_Format_Primary, Learning_Format_Junior, EQAO_ASSESSMENTS, NBED_ASSESSMENTS, classCodeABEDKeys, classLabelABEDKeys } from '../data/constants';
import { downloadFromExportData, generateExportFileName, IExportColumn, saveDataAsTsv, downloadExportData } from "../../ui-testctrl/tc-table-common/tc-table-common.component";
import { APIStudentColumnOSSLTList,  APIStudentColumnG9List, APIStudentColumnPJList} from "./export/config";
import { /*accommodationProps,*/ getActivatedAccommodations, getValProps, getAccommodationProps } from '../data/mappings/accommodations';
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { MySchoolService, ClassFilterId } from '../my-school.service';
import {WhitelabelService} from "../../domain/whitelabel.service";
import {v4 as uuidv4} from 'uuid';
enum IStudentAccommodationFilter {
  IEP,
  BRAILLE,
  ANY_ASSISTIVE,
};
enum FlaggedItemCase {
  ALL = 'ALL',
  ALL_FLAG = 'ALL_FLAG',
  IMPORT_FLAG = 'IMPORT_FLAG',
};

import { downloadFile } from '../../core/download-string';
import { isBuffer } from 'cypress/types/lodash';
import { data } from 'jquery';
import { C } from '@angular/cdk/keycodes';
import { DATA_MAPPING_EQAO_G9_STUDENT, AssessmentCode } from '../data/mappings/eqao-g9';
import {
  Math_Class_When,
  Learning_Format_G9,
  Learning_Format_OSSLT,
} from '../data/constants';
import { applyMask } from 'src/app/core/util/mask'
import { isAdminAltReqAllowed } from 'src/app/ui-alt-version-ctrl/feature-flag-config';
@Component({
  selector: 'sa-students',
  templateUrl: './sa-students.component.html',
  styleUrls: ['./sa-students.component.scss']
})

export class SaStudentsComponent implements OnInit {
  /*
  assistiveTechLabels = {
    'eqao_acc_assistive_tech_1_chrome': 'sdc_assist_tech_chrome',
    'eqao_acc_assistive_tech_2_kurz_dl': 'sdc_assist_tech_kurz_dl',
    'eqao_acc_assistive_tech_2_kurz_ext': 'sdc_assist_tech_kurz_ext',
    'eqao_acc_assistive_tech_2_nvda': 'sdc_assist_nvda',
    'eqao_acc_assistive_tech_2_voiceover': 'sdc_assist_voiceover',
    'eqao_acc_assistive_tech_2_readaloud': 'sdc_assist_readaloud',
    'eqao_acc_assistive_tech_2_jaws': 'sdc_jaws',
    'eqao_acc_assistive_tech_2_chromevox': 'sdc_chromevox',
    'eqao_acc_assistive_tech_2_read': 'sdc_natural_reader',
    'eqao_acc_assistive_tech_custom': 'sdc_1_assist_tech_other',
  };
  */
  
  

  @ViewChild('semesterFilterContainer', { static: false }) semesterFilterContainer:ElementRef<HTMLElement>;
  @Input() filterPredefinedValue: string;
  static overlapVariables: any;

  constructor(
    public lang: LangService,
    private availSess: AvailableSessionsService,
    private auth: AuthService,
    private routes: RoutesService,
    private g9demoService: G9DemoDataService,
    private pageModalService: PageModalService,
    private listSelectService: ListSelectService,
    private loginGuard: LoginGuardService,
    public mySchool: MySchoolService,
    protected route: ActivatedRoute,
    public whitelabelService: WhitelabelService,
    public fb: FormBuilder,
  ) { }

  public accountsTable: MemDataPaginated<IStudentAccount>;
  public students: IStudentAccount[] = [];
  public isAllSelected = false;
  public isAnySelected = false;
  public isLoaded: boolean;
  public isInited: boolean;
  public baseFilter:string;
  queryParamFilter: Map<string, string> = new Map();
  public currentModal: IActiveModal;
  AccountModal = AccountModal;
  UserAttr = UserAttr;
  school_group_id: number;
  availableSessions: IAvailableSession[] = [];
  availableClassrooms: IClassroom[] = [];
  classRoomsToAssign: IClassroom[] = [];
  public columnLabels;
  public filterLabels;
  public columnFilters: Map<string, IColumnFilter> = new Map();
  testSessionCaptionRef: Map<number, string> = new Map();
  testSessionTimeRef: Map<number, string> = new Map();
  testSessionWindowRef: Map<number, number> = new Map();
  isSaving: boolean;
  isRemoving: boolean = false;
  showFlaggedItems: FlaggedItemCase;
  selectedStudentIndex:number;
  oldStudentClassCode: string = "";
  additionalDownload = {
    urlSlug: '', // import   setClassFilter() to set this
    urlSlugExport: '', // export setClassFilter() to set this
    captionSlug: 'sa_student_sdc_caption',
    captionSlugExport: 'sa_student_sdc_export',
  };
  vrs: IValidationResults<IStudentAccount> = {
    // passed: [],
    successCreate: [],
    successUpdate: [],
    noChange: [],
    failed: [],
    results: []
  };
  subscription = new Subscription();
  pageModal:PageModalController;
  schoolType = this.route.snapshot.data['schoolType']

  assignedForm = new FormGroup({
    assignedClass: new FormControl()
  });
  IS_SESSION_STATUS_IMPLEMENTED = false;
  currentClassFilter:ClassFilterId;
  isShowingReports:boolean;
  isPrivateSchool = false;
  isShowingSemesterFilter:boolean;
  ClassFilterId = ClassFilterId;
  isEditDisable = true;
  currentImportNumber = 0
  totalImportNumber = 0;
  submissionDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_yes'}, {val: '#', display: 'lbl_no'}];
  selectedSubmissionVal = 'any';
  paymentDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_yes'}, {val: '#', display: 'lbl_no'}, {val: 'p', display: 'lbl_pending'}];
  selectedPaymentVal = 'any';
  accommodationDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_student_has_accommodation'}, {val: '#', display: 'lbl_student_has_no_accommodation'}];
  selectedAccommodationVal = 'any';
  students_before_date = new FormControl();
  
  currentTestWindow;
  selectedIsFlagged = false;
  isSingleSelect = true;
  isSecreteUser;
  disableImportExport = false;

  accommodationList: any[];

  lookupButtonValue:string = this.whitelabelService.isABED() ? this.lang.tra('abed_lookup') : this.lang.tra('student_account_look');

  semesterValues = {"First semester": "sdc_student_math_class_when_1", "Second semester": "sdc_student_math_class_when_2", 
  "First quadmester": "sdc_student_math_class_when_A", "Second quadmester": "sdc_student_math_class_when_B", "Third quadmester": "sdc_student_math_class_when_C", "Fourth quadmester": "sdc_student_math_class_when_D", 
  "First octomester": "sdc_student_math_class_when_E", "Second octomester": "sdc_student_math_class_when_F", "Third octomester": "sdc_student_math_class_when_G", "Fourth octomester": "sdc_student_math_class_when_H",
  "Fifth octomester": "sdc_student_math_class_when_I", "Sixth octomester": "sdc_student_math_class_when_J", "Seventh octomester": "sdc_student_math_class_when_K", "Eighth octomester": "sdc_student_math_class_when_L",
  "First hexamester": "sdc_student_math_class_when_M", "Second hexamester": "sdc_student_math_class_when_N", "Third hexamester": "sdc_student_math_class_when_O", "Fourth hexamester": "sdc_student_math_class_when_P",
  "Fifth hexamester": "sdc_student_math_class_when_Q", "Sixth hexamester": "sdc_student_math_class_when_R", "Full year": "lbl_full_year", };

  isPaymentModuleEnabled: boolean = false;

  allowPASIUpdates: boolean = true;

  @Output() unPaidStudentCurrentFilter = new EventEmitter<string>();
  @Output() onSetClassFilter = new EventEmitter();

  ngOnInit() {
    this.initRouteView();
    this.isPrivateSchool = this.g9demoService.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.setTestWindowFilter(this.mySchool.getCurrentTestWindow())
    this.mySchool.initStuAsmtSignoff();
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.wipeSelections();
    // console.log(this.isBCSite())
    const schoolLang = this.g9demoService.schoolData["lang"];
    this.allowPASIUpdates = this.g9demoService.getPASILock();
    this.lang.setCurrentLanguage(schoolLang);
    this.showFlaggedItems = FlaggedItemCase.ALL;
  }

  ngOnChanges(){
    this.initRouteView();
    this.isPrivateSchool = this.g9demoService.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }
    this.mySchool.initStuAsmtSignoff();
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.wipeSelections()
    console.log(this.isBCSite())
    const schoolLang = this.g9demoService.schoolData["lang"];
    this.lang.setCurrentLanguage(schoolLang);
  }

  initRouteView() {
    this.columnLabels = {
      id: this.lang.tra('sa_students_col_id'),
      courseType: this.lang.tra('Course Type'),
    };


    if (this.isBCSite()) {
        this.filterLabels = {};
    } 
    else {
      this.filterLabels = {
        [IStudentAccommodationFilter.IEP]: this.lang.tra('student_acc_filter_iep'),
        [IStudentAccommodationFilter.BRAILLE]: this.lang.tra('student_acc_filter_braille'),
        [IStudentAccommodationFilter.ANY_ASSISTIVE]: this.lang.tra('student_acc_filter_any_assist')
      }
  
      // custom column filters
      this.columnFilters.set('accommodation', {
        type: IColumnFilterType.BOOLEAN_LIST,
        options: [{
          key: IStudentAccommodationFilter.IEP,
          label: this.filterLabels[IStudentAccommodationFilter.IEP],
          checked: false
        }, {
          key: IStudentAccommodationFilter.BRAILLE,
          label: this.filterLabels[IStudentAccommodationFilter.BRAILLE],
          checked: false
        }, {
          key: IStudentAccommodationFilter.ANY_ASSISTIVE,
          label: this.filterLabels[IStudentAccommodationFilter.ANY_ASSISTIVE],
          checked: false
        }]
      });
    }

    this.route.queryParams.subscribe(queryParams => {
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
      }
    })
    
    if (!this.isInited) {
      this.isInited = true;
      this.school_group_id = randId();
      this.loadAccounts();
      this.cacheAvailableSessions();
      this.applyFilterQueryParams();
    }
  }

  // filterLoadedTime(student: IStudentAccount){
  //   if ( student.created_loaded_time && student.created_loaded_time < this.students_before_date.value){
  //     return true;
  //   }else{
  //     return false;
  //   }
  // }

  studentsPriorToDate(){
    if(this.students_before_date.value){
      this.accountsTable.activeFilters['studentsBeforeDate'] = { mode: FilterSettingMode.VALUE, config: { value: this.students_before_date.value } }
    }
    this.accountsTable.refreshFilters();
  }

  toggleSemesterFilter(){
    if (this.isShowingSemesterFilter){
      this.changeCategory(null)
      this.isShowingSemesterFilter = false; // to do: clear filter as well
    }
    else{
      this.isShowingSemesterFilter = true;
      setTimeout(()=>{
        const el = this.semesterFilterContainer.nativeElement;
        if (el){ el.scrollIntoView({behavior: "smooth"}); }
      }, 500);
    }
  }

  isSignoffAvail(){
    return this.currentClassFilter && !this.isStuAsmtInfoSignoff(); // to do, should show alternate version if signed off
  }

  isStuAsmtInfoSignoff(){
    return this.mySchool.checkStuAsmtSignoff(this.currentClassFilter);
  }

  getStuAsmtInfoSignoffLog(){
    return this.mySchool.getStuAsmtSignoffHistory(this.currentClassFilter);
  }

  applyFilterQueryParams() {
    // session_id
    const session_id = this.route.snapshot.queryParamMap.get("session_id");
    if (session_id) {
      this.queryParamFilter.set('session_id', session_id);
    }
    if (this.queryParamFilter.size) {
      this.accountsTable.activeFilters['test_session'] = {
        mode: FilterSettingMode.VALUE,
        config: { value: session_id }
      }
      this.accountsTable.refreshFilters();
    }
  }

  cacheAvailableSessions() {
    this.subscription.add(this.availSess.sub().subscribe(availableSessions => this.availableSessions = availableSessions));
  }

  isEQAOAssessment() {
    return EQAO_ASSESSMENTS.includes(this.currentClassFilter);
  }
  
  // private getFilterSettings(): {[propName: string]: (IStudentAccount, any) => boolean} {
  //   if (this.isBCSite()) {
  //       return {}
  //   } 
  //   else {
  //     return {
  //         accommodation: (student: IStudentAccount, val: any) => {
  //             let include = false;
  //             const filterIEP = val.find(el => el.key === IEQAOStudentAccommodationFilter.IEP).checked;
  //             const filterBraille = val.find(el => el.key === IEQAOStudentAccommodationFilter.BRAILLE).checked;
  //             const filterAnyAssistive = val.find(el => el.key === IEQAOStudentAccommodationFilter.ANY_ASSISTIVE).checked;
  //             // All filters off - include all students 
  //             if (!filterIEP && !filterBraille && !filterAnyAssistive) {
  //                 return true;
  //             }
  //             const checkMarkMapping =this.g9demoService.checkMarkMapping;
  //             if (filterIEP) {
  //                 if (checkMarkMapping["eqao_iep"][student.eqao_iep]) {
  //                     include = true;
  //                 }
  //             }
  //             if (filterBraille) {
  //                 if (checkMarkMapping["eqao_acc_braille"][student.eqao_acc_braille]) {
  //                     include = true;
  //                 }
  //             }
  //             const assistivePropList = getValProps('eqao_acc_assistive_tech', STUDENT_ASSIST_TECH);
  //             if(filterAnyAssistive) {
  //                 for(let prop of assistivePropList) {
  //                     if(checkMarkMapping[prop][student[prop]]) {
  //                         include = true;
  //                         break;
  //                     }
  //                 }
  //                 if(!!student.eqao_acc_assistive_tech_custom) {
  //                     include = true;
  //                 }
  //             }
  //             return include;
  //         },
  //         eqao_g9_class_code: (student: IStudentAccount, val: string) => _.includes(student.eqao_g9_class_code, val),
  //         test_session: (student: IStudentAccount, val: string) => _.includes(student.test_sessions, +val),
  //         test_window: (student: IStudentAccount, val: string) => _.includes(student.test_sessions.map(tsid => +this.testSessionWindowRef.get(tsid)), +val),
  //     }        
  //   }
  // }
  

  setClassFilter(filterId){
    this.onSetClassFilter.emit(filterId);

    const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9demoService.schoolData;

    this.currentClassFilter = filterId;

    this.isShowingReports = false;
    this.accountsTable.activeFilters['is_g3'] = null;
    this.accountsTable.activeFilters['is_g6'] = null;
    this.accountsTable.activeFilters['is_g9'] = null;
    this.accountsTable.activeFilters['is_g10'] = null;
    this.accountsTable.activeFilters['group_type'] = null;

    if (this.currentClassFilter){
      if(this.currentClassFilter === ClassFilterId.Primary){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g3'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_pj_sdc_url', 'sa_student_pj_sdc_url');
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if(this.currentClassFilter === ClassFilterId.Junior){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g6'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_pj_sdc_url', 'sa_student_pj_sdc_url');
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if(this.currentClassFilter === ClassFilterId.OSSLT){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g10'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_sdc_osslt_export_url', 'sa_student_sdc_osslt_export_url');
        this.isPrivateSchool && payment_req_osslt ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if(this.currentClassFilter === ClassFilterId.G9){
        this.isShowingReports = true;
        this.accountsTable.activeFilters['is_g9'] = { mode: FilterSettingMode.VALUE, config: { value: '1' } }
        this.setAdditionalDownload('sa_student_sdc_url', 'sa_student_sdc_url');
        this.isPrivateSchool && payment_req_g9 ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else {
        this.isShowingReports = false;
        this.accountsTable.activeFilters['group_type'] = { mode: FilterSettingMode.VALUE, config: { value: this.currentClassFilter } };

        if (this.whitelabelService.isABED()) {
          this.hideAdditionalDownload();
        }
        
        // this.setAdditionalDownload('sa_student_sdc_url', 'sa_student_sdc_url'); // to do
      }
    }
    this.accountsTable.refreshFilters();
  }

  hideAdditionalDownload() {
    this.additionalDownload = null;
  }

  generateAdditionalInstrSlug(): null | string {
    // we do not want this slug to appear for ABED, as there is no additional download
    if (this.whitelabelService.isABED()) {
      return null;
    }

    return "txt_import_instr_students";
  }

  setTestWindowFilter(tw){
    this.currentTestWindow = tw;
    if (this.whitelabelService.isEQAO() || this.whitelabelService.isABED())
    {
      this.accountsTable.activeFilters['testWindowFilter'] = {mode: FilterSettingMode.VALUE, config: { value: this.currentTestWindow } };
    }
    else{
      this.accountsTable.activeFilters['testWindowFilter'] = null;
    }
    this.accountsTable.refreshFilters();
  }

  viewStudentReport(acct:any){
    return `/${this.lang.c()}/school-admin/reports`
    // this.loginGuard.quickPopup(this.lang.tra('msg_report_in_process'));
  }
  setAdditionalDownload(slugUrlImportTemplate:string, slugUrlExportTemplate:string){
    this.additionalDownload.urlSlug = slugUrlImportTemplate;
    this.additionalDownload.urlSlugExport = slugUrlExportTemplate;
  }
  loadAccounts() {
    this.students = [];
    this.g9demoService.schoolAdminStudents.list.forEach(student => this.processNewStudentAccount(student));
    const accomKey = this.whitelabelService.isABED() ? 'accommodations' : 'accommodation';
    const DOBKey = this.renderDobProp();
    console.log(this.students);
    // console.log('loadAccounts::this.students', this.students.filter(s => s.osslt__is_submitted))
    // sampleTestSessions.forEach(testSession => this.processNewTestSessions(testSession));
    this.accountsTable = new MemDataPaginated({
      data: this.students,
      configurablePageSize: true,
      filterSettings: {
        diplomaExams: (student: IStudentAccount, val: any) => _.includes(this.displayStudentDiplomaExams(student), val),
        [accomKey]: (student: IStudentAccount, val: any) => _.includes(this.filterAccommodations(student), val),
        [DOBKey]: (student: IStudentAccount, val: any) => _.includes(this.filterDOB(student), ""+val.replace(/-/g, "")),
        eqao_g9_class_code: (student: IStudentAccount, val: string) => _.includes(student[this.getClassCodePrefix(this.currentClassFilter) + 'eqao_g9_class_code'], val),
        // #MERGE_20220524 : refactor
        // eqao_g9_class_code_label: (student: IStudentAccount, val: string) => {
        //   switch(this.currentClassFilter){
        //     case ClassFilterId.TCLE:
        //       return _.includes(student._tcle_eqao_g9_class_code_label, val);
        //     case ClassFilterId.TCN:
        //       return _.includes(student._tcn_eqao_g9_class_code_label, val);
        //     case ClassFilterId.SCIENCES8:
        //       return _.includes(student._sciences8_eqao_g9_class_code_label, val);
        //     case ClassFilterId.G9:
        //       return _.includes(student.eqao_g9_class_code_label, val);
        //     // For OSSLT, Primary and Junior
        //     default:
        //       return _.includes(student._g10_eqao_g9_class_code_label, val)
        //   }
        // },
        isPaid: (student: IStudentAccount, val: any) => _.includes(this.filterPayments(student), val),
        // eqao_g9_class_code: (student: IStudentAccount, val: string) => _.includes(student.eqao_g9_class_code, val),
        class_code_label: (student: IStudentAccount, val: string) => _.includes(this.filterStudentClassCodeLabel(student), val),
        is_g3: (student: IStudentAccount) => student.eqao_is_g3 === '1',
        is_g6: (student: IStudentAccount) => student.eqao_is_g6 === '1',
        is_g9: (student: IStudentAccount) => student.eqao_is_g9 === '1',
        is_g10: (student: IStudentAccount) => student.eqao_is_g10 === '1',
        group_type: (student: IStudentAccount, val: any) => this.filterStudentByGrade(student),
        eqao_math_class_when: (student: IStudentAccount, val: string) => _.includes(this.getMathClassWhenLabel(+student.eqao_math_class_when), val),
        TermFormat: (student: IStudentAccount, val: string) => _.includes(this.getMathClassWhenLabel(+student.eqao_math_class_when||+student.TermFormat), val),
        test_session: (student: IStudentAccount, val: string) => _.includes(student.test_sessions, +val),
        test_window: (student: IStudentAccount, val: string) => _.includes(student.test_sessions.map(tsid => +this.testSessionWindowRef.get(tsid)), +val),
        //showFlaggedItems:  (student: IStudentAccount) => (JSON.parse(student.errMsg).length > 0 ||student.SDC_conflict!= undefined) ,
        showFlaggedItems:  (student: IStudentAccount) => this.filterFlaggedItems(student),
        is_submitted: (student:IStudentAccount, val:string) => this.isSubmitted(student,val), // this.currentClassFilter == ClassFilterId.G9 ? _.includes('('+student.haveG9Submitted+')'+ this.renderSubmStatus(student.haveG9Submitted), val) : _.includes('('+student.haveOSSLTSubmitted+')'+this.renderSubmStatus(student.haveOSSLTSubmitted), val),
        //studentsBeforeDate:  (student: IStudentAccount) => this.filterLoadedTime(student), 
        testWindowFilter:  (student:IStudentAccount) => this.filterStudentTestWindow(student),
        invigilators: (student: IStudentAccount, val: string) => _.includes(this.getTeachers(student, true).toLocaleLowerCase(), val.toLocaleLowerCase()),
        teacher: (student: IStudentAccount, val: string) => _.includes(this.getTeachers(student).toLocaleLowerCase(), val.toLocaleLowerCase()),

      },
      sortSettings: {
        // accommodation: (student:IStudentAccount) => this.hasAccommodations(student),
        // isPaid: (student: IStudentAccount) => _.sortBy(student.isPaid),
        diplomaExams: (student: IStudentAccount) => _.orderBy(this.displayStudentDiplomaExams(student).toLocaleUpperCase(), ['asc']),
        eqao_learning_format: (student:IStudentAccount) => _.sortBy(student.eqao_learning_format),
        _g10_eqao_learning_format: (student:IStudentAccount) => _.sortBy(student._g10_eqao_learning_format),
        TermFormat: (student: IStudentAccount) => _.sortBy(student.eqao_math_class_when||student.TermFormat),
        class_code_label: (student: IStudentAccount) => _.orderBy((this.filterStudentClassCodeLabel(student) || "").toLowerCase(), ['asc']),
        // teacher: (student:IStudentAccount) => _.orderBy(this.getTeachers(student).toLowerCase(), ['asc']),
        is_submitted: (student:IStudentAccount) => this.currentClassFilter == ClassFilterId.G9 ? student.haveG9Submitted === '1' : student.haveOSSLTSubmitted === '1',
        // invigilators: (student:IStudentAccount) => _.orderBy(this.getTeachers(student, true).toLowerCase(), ['asc']),
      }
    });
    // this.students = this.students.filter(student => { return (student.semester === 'First semester') || (student.semester === 'Third hexamester') })
    // this.accountsTable.refreshFilters();
    // this.accountsTable.injestNewData(this.processFilterConditions(''))
    this.getSemester(this.students)
    this.isLoaded = true;
  }

  private filterAccommodation(student: IStudentAccount, val: any) {
    let include = false;
    const filterIEP = val.find(el => el.key === IStudentAccommodationFilter.IEP).checked;
    const filterBraille = val.find(el => el.key === IStudentAccommodationFilter.BRAILLE).checked;
    const filterAnyAssistive = val.find(el => el.key === IStudentAccommodationFilter.ANY_ASSISTIVE).checked;
    // All filters off - include all students
    if (!filterIEP && !filterBraille && !filterAnyAssistive) {
      return true;
    }
    const checkMarkMapping = this.g9demoService.checkMarkMapping;
    if (filterIEP) {
      if (checkMarkMapping["eqao_iep"][student.eqao_iep]) {
        include = true;
      }
    }
    if (filterBraille) {
      if (checkMarkMapping["eqao_acc_braille"][student.eqao_acc_braille]) {
        include = true;
      }
    }
    const assistivePropList = getValProps('eqao_acc_assistive_tech', STUDENT_ASSIST_TECH_1);
    if (filterAnyAssistive) {
      for (let prop of assistivePropList) {
        if (checkMarkMapping[prop][student[prop]]) {
          include = true;
          break;
        }
      }
      if (!!student.eqao_acc_assistive_tech_custom) {
        include = true;
      }
    }
    return include;
  }

  renderSemester(id) {
   let semesterslug = this.semesterValues[renderMappedValue(this.g9demoService.semesters, id)];
   return this.lang.tra(semesterslug);
  }

  processNewStudentAccount(student: IStudentAccount) 
  {
    if (student.test_sessions)
    {
      student.test_sessions = _.sortBy(student.test_sessions.map(ts_id => +ts_id));
    }  

    student.semester_label = this.renderSemester(student.semester) //only G9 semester

    if (student?.course?.GroupType != null)
    {
      student.course[student.course.GroupType] = "1";
    }

    if (student?.course != null)
    {
      for (let key of Object.keys(student.course))
      {
        student[key] = student.course[key];
      }

      for (let classLabel of classLabelABEDKeys)
      {
        if (student[classLabel] != null)
        {
          student['classLabel'] = student[classLabel];
        }
      }

      for (let classCode of classCodeABEDKeys)
      {
        if (student[classCode] != null)
        {
          student['classCode'] = student[classCode];
        }
      }

    }
    
    this.students.splice(0,0,student);
  }

  processNewTestSessions(testSession: IStudentTestSession) {
    const titleRef = JSON.parse(testSession.test_window_title);
    const title = titleRef[this.lang.c()] || titleRef['en'] || 'Unnamed Test Window';
    const dateTimeStr = this.renderTestSessionDate(testSession.date_time_start);
    this.testSessionCaptionRef.set(testSession.id, title);
    this.testSessionTimeRef.set(testSession.id, dateTimeStr);
    this.testSessionWindowRef.set(testSession.id, 72);
  }
  semesterFilters = []
  filteredStudents = []
  filteredStudentsList = []

  processFilterConditions(config) {
    this.filteredStudentsList = []
    //this.semesterFilters.push(value)
    if (config.checked) {
      this.semesterFilters.push(config.semester_group)
    }
    else {
      const i = this.semesterFilters.indexOf(config.semester_group)
      this.semesterFilters.splice(i, 1);
    }
    let filterCondition
    let filteredStudents
    if (this.semesterFilters.length > 0) {
      this.semesterFilters.forEach(item => {
        filterCondition = (student) => student.semester_label === item
        filteredStudents = this.filteredStudentsList;
        this.filteredStudentsList = filteredStudents.concat(this.students.filter(filterCondition))
      })
      this.accountsTable.injestNewData(this.filteredStudentsList)
    }
    else{
      this.accountsTable.injestNewData(this.students);
    }
  }

  changeCategory($event) {
    //this.processFilterConditions({checked:true,semester_group:this.baseFilter})
    this.semesterFilters = []
    this.accountsTable.injestNewData(this.students);
  }

  getSemester(students) {
    var numMapping = {};
    for (var i = 0; i < students.length; i++) {
      if (numMapping[students[i].semester_label] === undefined) {
        if (students[i].semester_label !== undefined) {
          numMapping[students[i].semester_label] = 0;
        }
      }
      numMapping[students[i].semester_label] += 1;
    }
    var greatestFreq = 0;
    var mode;
    for (var prop in numMapping) {
      if (numMapping[prop] > greatestFreq) {
        greatestFreq = numMapping[prop];
        mode = prop;
      }
    }
    //console.log(numMapping, mode)
    this.baseFilter = mode;
    // this.processFilterConditions({checked:true,semester_group:mode})
    // this.semesterFilters=[];
  }
  visibleStudents() {
    console.log("Visible students: ", this.accountsTable.getCurrentPageData())
    return this.accountsTable.getCurrentPageData();
  }
  toggleSelectAll() {
    this.setSelectAll(this.isAllSelected);
  }
  setSelectAll(state: boolean) {
    this.isAllSelected = state;
    this.students.forEach(student => student.__isSelected = state);
    this.isAnySelected = state;
  }
  checkSelection() {
    this.isAnySelected = false;
    this.isAllSelected = false
    this.students.forEach(student => {
      if (student.__isSelected) {
        this.isAnySelected = true;
      }
    });
  }
  wipeSelections() {
    this.students.forEach(student => {
      student.__isSelected = false
    });
  }

  renderCourse(id){
    return renderMappedValue(STUDENT_G9_COURSES, id);
  }

  renderSubmStatus(id) {
    /*
    const slug = this.currentClassFilter === ClassFilterId.G9? 'G9_OPERATIONAL':'OSSLT_OPERATIONAL'
    const haveSubmitted = acct.testAttempts.filter(ta => ta.slug === slug && (ta.submitted_test_session_id != undefined ||ta.submitted_test_session_id != null))
    if(haveSubmitted!=undefined){
      return renderMappedValue(STUDENT_YES_NO, 1);
    }
    else{
      return renderMappedValue(STUDENT_YES_NO, 0);
    }
    */
    return renderMappedValue(STUDENT_YES_NO, id);
  }

  unrenderCourse(label) {
    const relevantCourses = STUDENT_G9_COURSES.list
      .filter((course) => {
        return course.label === label
      });
    if (!relevantCourses || relevantCourses.length === 0) {
      return;
    }

    return relevantCourses[0].id;
  }


  renderClassCode(classId: number) {
    const classrooms = this.g9demoService.classrooms;
    let classCodeName: string;
    classrooms.forEach(classroom => {
      if (+classroom.id === +classId) {
        classCodeName = classroom.class_code;
      }
    });
    return classCodeName;
  }

  unrenderClassCode(classCodeName: string) {
    const classrooms = this.g9demoService.classrooms;
    let classId;
    classrooms.forEach(classroom => {
      if (('' + classroom.class_code).trim() === ('' + classCodeName).trim()) {
        classId = classroom.id;
      }
    });
    return classId;
  }

  hasAccommodations(account: IStudentAccount) {
    /*
    const accProps = getAccommodationProps(this.whitelabelService);
    return !!getActivatedAccommodations(accProps, account, this.g9demoService.checkMarkMapping, this.whitelabelService).length;
    */
    /*
    return account.eqao_acc_braille == '3' ||account.eqao_acc_braille == '4'||account.AccAudioVersion == '1'||
           account._extended_time =='1' || account.eqao_pres_format == '1' || account._audio_recording_of_resp == '1'||
           account.AccVideotapeResponse == '1' || account.eqao_acc_scribing =='1' || account.AccOther == '1'||
           account.AccOther == '1'||account._audio_recording_of_resp == '1' ;
    */

    //return  this.currentClassFilter === ClassFilterId.G9? account.eqao_dyn_linear === '1' : account._g10_eqao_dyn_linear === '1'

    const curricShort = this.g9demoService.classFilterToCurricShort(this.currentClassFilter);

    const accProps = getAccommodationProps(this.whitelabelService, curricShort, this.g9demoService);
    const hasAccom = !!getActivatedAccommodations(accProps, account, this.g9demoService.checkMarkMapping, this.whitelabelService, curricShort, this.g9demoService).length;
    
    return hasAccom;
    /*
    if(hasAccom){
      account['eqao_dyn_linear'] = '1'
    }else{
      account['eqao_dyn_linear'] = '#'
    }
    */
  }
  filterAccommodations(account: IStudentAccount) {
    if(this.isABED()){
      if(account.accommodations?.length) return '1';
      else return '#';
    }
    const curricShort = this.g9demoService.classFilterToCurricShort(this.currentClassFilter);

    const accProps = getAccommodationProps(this.whitelabelService, curricShort, this.g9demoService);
    const hasAccom = !!getActivatedAccommodations(accProps, account, this.g9demoService.checkMarkMapping, this.whitelabelService, curricShort, this.g9demoService).length;
    let filterResult = null;
    if(hasAccom){
      filterResult = '1'
    }else{
      filterResult = '#'
    }
    return filterResult;
  }

  filterDOB(student: IStudentAccount) {
    return student[this.renderDobProp()];
  }

  filterPayments(account: IStudentAccount) {
    const isPaid = account.isPaid;
    let filterResult = null;
    if(isPaid === 1){
      filterResult = '1'
    } else {
      filterResult = '#'
    }
    if (account.altPaymentStatus == 1 || account.altPaymentStatus == 2) {
      filterResult = 'p'
    } 
    return filterResult;
  }

  renderIsPaid(student) {
    if(student.altPaymentStatus == 1 || student.altPaymentStatus == 2)
      return 'lbl_pending'
    
    return student.isPaid ? 'lbl_yes' : 'lbl_no'
  }

  renderTestSessionDate(date_time_start: string) {
    return moment.tz(date_time_start, moment.tz.guess()).format(this.lang.tra('datefmt_dashboard_long'));
  }

  renderQuickDate(date: string) {
    return moment.tz(date, moment.tz.guess()).format('MMM D');
  }

  getStudentTestSessions(student: IStudentAccount) {
    return student.test_sessions;
  }

  getTSCaption(testSessionId: number) {
    return this.testSessionCaptionRef.get(testSessionId);
  }
  getTSTime(testSessionId: number) {
    return this.testSessionTimeRef.get(testSessionId);
  }
  getTSWindow(testSessionId: number) {
    return this.testSessionWindowRef.get(testSessionId);
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  getSelectedStudents(throwNullSet: boolean = false) {
    const students: IStudentAccount[] = [];
    this.visibleStudents().forEach(student => {
      if (student.__isSelected) {
        students.push(student);
      }
    });
    if (throwNullSet && students.length === 0) {
      //alert('Please select a student before proceeding with this action.');
      alert(this.lang.tra("mng_select_student"));
      throw new Error('No account selected');
    }
    return students;
  }

  isABED(){
    return this.whitelabelService.isABED();
  }
  // accountDetail
  accountDetailModalStart(student?: IStudentAccount) {
    this.selectedStudentIndex = this.accountsTable.getEntries().indexOf(student);
    this.oldStudentClassCode = student?.classCode as string;
    
    let students;
    if (student) 
    {
      students = [student];
      this.selectedIsFlagged = this.isRedAlertItem(student) || this.isYellowAlertItem(student)
      this.isSingleSelect = true;
    } 
    
    else 
    {
      students = this.getSelectedStudents(true);
      this.selectedIsFlagged = false;
      this.isSingleSelect = students.length < 2;
    }

    if (this.isSingleSelect)
    {
      // guranteed that students only has 1 student in the array
      if (this.isABED())
      {
        this.loadStudentAccommodations(students[0].uid);
      }

      this.oldStudentClassCode = students[0]?.classCode;
    }

    let config: IModalNewAccount = { accounts: students };
    if (this.isBCSite()) {
        config.confirmationCaption = this.lang.tra('Save & Close');
    }   

    this.pageModal.newModal({
      type: AccountModal.ACCOUNT_DETAILS,
      config,
      finish: this.accountDetailModalFinish,
      cancel: this.resetAccommodations,
    });
  }
  configureQueryParams(isBulk = false) {
    const schoolData: any = this.g9demoService.schoolData;
    let namespace
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        namespace = 'eqao_sdc_g3'
      break;
      case ClassFilterId.Junior:
        namespace = 'eqao_sdc_g6'
      break;  
      case ClassFilterId.G9:
        namespace = 'eqao_sdc'
      break;
      case ClassFilterId.OSSLT:
        namespace = 'eqao_sdc_g10'
      break;
      case ClassFilterId.TCLE:
        namespace = 'nbed_sdc';
        break;
      case ClassFilterId.TCN:
        namespace = 'nbed_sdc';
        break;
      case ClassFilterId.SCIENCES8:
        namespace = 'nbed_sdc';
        break;
      case ClassFilterId.ABED_GRADE_6:
        namespace = 'abed_sdc';
        break;
      case ClassFilterId.ABED_GRADE_9:
        namespace = 'abed_sdc';
        break;
      case ClassFilterId.ABED_GRADE_12:
        namespace = 'abed_sdc';
        break;
      default:
        namespace = 'eqao_sdc_g3'
      break; 
    }
    if (schoolData) {
      return {
        query: {
          schl_group_id: schoolData.group_id,
          lang: this.lang.c(),
          mode: isBulk ? 'Import' : '',
          //namespace: this.currentClassFilter === ClassFilterId.G9?'eqao_sdc':'eqao_sdc_g10'
          namespace,
          isSecreteUser: this.isSecreteUser,
          test_windows_id: this.currentTestWindow.id,
          groupType: this.currentClassFilter
        }
      }
    }
    return null;
  }
  accountDetailModalFinish = (config: { payload: { record: Partial<IStudentAccount>, data: any } }, closeModelAfter = true) => {
    const payload = config.payload;
    const record = payload.record;
    const data = payload.data;


    // The below was implemented for NBED because editing students and not assigning them a class would make them not show up in the students list. 
    // Added whitelabel check for NBED only to support backwards compatibility.
    if(!payload.data.class_code && this.whitelabelService.isNBED()){
      console.log(_.get(config, 'accounts[0]._tcle_eqao_g9_class_code'))
      payload.data.class_code = _.get(config, `accounts[0].${this.getClassCodePrefix(this.currentClassFilter)}eqao_g9_class_code`);
    }

    if (NBED_ASSESSMENTS.includes(this.currentClassFilter)) {
      data[this.currentClassFilter] = 1;
      data['nbed_group_type'] = this.currentClassFilter; // DEPRICATE THIS
    }

    if (this.whitelabelService.isABED()) {
      data[this.currentClassFilter] = 1;
      data['abed_group_type'] = this.currentClassFilter; // DEPRICATE THIS
    }

    data['GroupType'] = this.currentClassFilter;

    if(!this.clientRuleCheck(data)){
      return;
    }
    this.modifyStudentData(data, false);
    this.updateAccTechAndNPS(data);
    this.setupLinear(data);
    const apiPayload = this.getApiData(data);
    const schoolClass = this.g9demoService.classOptions.map[data.eqao_g9_class_code || data.classCode];
    if (schoolClass) {
      data.eqao_g9_class_code_label = schoolClass.label;
    }
    if(this.isABED()) this.finishAccommodations(record);
    //when pasi updates are on, we block student edits, we should block the api call as well
    if(this.allowPASIUpdates) {
      this.pageModal.closeModal();
      return;
    }
    this.auth
      .apiPatch(
        this.routes.SCHOOL_ADMIN_STUDENT,
        record.id,
        apiPayload,
        this.configureQueryParams()
      ).then(result =>{
        const course_type = this.currentClassFilter == ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter == ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter == ClassFilterId.G9?'EQAO_G9':'EQAO_G10'
        if(result.ErrorMessages.length > 0){
          const errSlug =  this.apiErrMsgToSlug(result.ErrorMessages[0]);
          const popupMsg = this.lang.tra(errSlug);
          setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
        }
        else if(result.warningMessages.length > 0){
          data['errMsg'] = JSON.stringify([]);
          Object.keys(data).forEach(prop => {
            if(this.currentClassFilter == ClassFilterId.G9 || overlapVariables.indexOf(prop) > -1){
              record[prop] = data[prop];
            }else if(this.currentClassFilter == ClassFilterId.Primary){
              record['_g3_'+prop] = data[prop]
            }else if(this.currentClassFilter == ClassFilterId.Junior){
              record['_g6_'+prop] = data[prop]
            }else{
              record['_g10_'+prop] = data[prop]
            }  
          });
          if (schoolClass) {
            const classroom = this.g9demoService.classrooms.find (cr => {
              const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
              const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
              const tw_isActive = (new Date(test_window.date_end) > new Date ())
              if(cr.class_code == schoolClass.label && cr.course_type == course_type && tw_isActive){
                return cr;
              }
            })
            //const classroom = this.getClassroom(data.eqao_g9_class_code_label)
            if(classroom){
              if(this.currentClassFilter == ClassFilterId.Primary){
                record._g3_eqao_pj_french = classroom.is_fi?'1':'0'
              }
              record.teacher = classroom.educator;
              if(this.currentClassFilter == ClassFilterId.G9){
                record.semester_label = this.renderSemester(classroom.semester)
              }  
            }
          }
          const warnSlug =  this.apiErrMsgToSlug(result.warningMessages[0]);
          const popupMsg = this.lang.tra(warnSlug);
          setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
          if(closeModelAfter){
            this.pageModal.closeModal();
          }else{
            this.pageModal.closeModal();
            const student  = this.accountsTable.getEntries()[this.selectedStudentIndex]
            this.accountDetailModalStart(student)
          }  
        }else{
          data['errMsg'] = JSON.stringify([]);
          Object.keys(data).forEach(prop => {
            if(this.currentClassFilter == ClassFilterId.G9 || overlapVariables.indexOf(prop) > -1){
              record[prop] = data[prop];
            }
            else if(this.currentClassFilter == ClassFilterId.TCLE){
              record['_tcle_' + prop] = data[prop];
            }
            else if(this.currentClassFilter == ClassFilterId.Primary){
              record['_g3_'+prop] = data[prop]
            }else if(this.currentClassFilter == ClassFilterId.Junior){
              record['_g6_'+prop] = data[prop]
            }
            
            else if (this.currentClassFilter === ClassFilterId.ABED_GRADE_12 || 
            this.currentClassFilter === ClassFilterId.ABED_GRADE_6 ||
            this.currentClassFilter === ClassFilterId.ABED_GRADE_9)
            {
              record[prop] = data[prop];
            }

            else{
              record['_g10_'+prop] = data[prop]
            } 
          });
          if (schoolClass) {
            const classroom = this.g9demoService.classrooms.find (cr => {
              const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
              const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
              const tw_isActive = (new Date(test_window.date_end) > new Date ())
              if(cr.class_code == schoolClass.label && cr.course_type == course_type && tw_isActive){
                return cr;
              }
            })

            payload.record[this.getClassCodePrefix(this.currentClassFilter) + "eqao_g9_class_code_label"] = schoolClass.label;
            payload.record["classLabel"] = schoolClass.label;
            payload.record[this.getClassCodePrefix(this.currentClassFilter) + "eqao_g9_class_code"] = schoolClass.id;

            //const classroom = this.getClassroom(data.eqao_g9_class_code_label)
            if(classroom){
              if(this.currentClassFilter == ClassFilterId.Primary){
                record._g3_eqao_pj_french = classroom.is_fi?'1':'0'
              }
              record.teacher = classroom.educator;
              if(this.currentClassFilter == ClassFilterId.G9){
                record.semester_label = this.renderSemester(classroom.semester)
              } 
            }
          }else{
            this.autoPlaceholderAssignment(payload.record);
          }
          
          this.accountsTable.refreshFilters();
          if (closeModelAfter)
          {
            this.pageModal.closeModal();
          }
          
          else
          {
            this.pageModal.closeModal();
            const student  = this.accountsTable.getEntries()[this.selectedStudentIndex];
            
            if (this.whitelabelService.isABED() && this.selectedStudentIndex !== -1)
            {
              student[this.findStudentClassCode(student)] = student['class_code'];
            }

            this.accountDetailModalStart(student);
          }  

          // console.log(this.g9demoService.classrooms.find(classroom => classroom.id == apiPayload.school_class_id))

          const oldClassCode = this.whitelabelService.isABED() && this.oldStudentClassCode != null ?
          this.oldStudentClassCode : payload.data.class_code;
          if(+apiPayload.school_class_id !== +oldClassCode)
          {
            this.increaseClassStudentCount(apiPayload.school_class_id);
            this.decreaseClassStudentCount(oldClassCode);
          }
          
          // console.log(this.g9demoService.classrooms);
        }   
      }).catch((err:Error)=>{
        if(err.message.includes("Invalid Secrete User")){
          setTimeout(() => this.loginGuard.quickPopup("Invalid Secrete User"), 0);  //this message is for support when they try to unsubmit and have wrong secrete code 
        }      
      });
  }

  getCurrentPlaceholderClass(){
    const schl_group_id = this.mySchool.getCurrentSchoolGroupId();
    return this.g9demoService.classrooms.find (cr => {
      const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
      const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
      const tw_isActive = (new Date(test_window.date_end) > new Date ())
      if(cr.is_placeholder === 1 && tw_isActive && this.currentTestWindow.id == test_window.id && cr.schl_group_id == schl_group_id){
        return cr;
      }
    })
  }

  autoPlaceholderAssignment(student){
    const classroom = this.getCurrentPlaceholderClass();
    student[this.getClassCodePrefix(this.currentClassFilter) + "eqao_g9_class_code_label"] = classroom.class_code;
    student[this.getClassCodePrefix(this.currentClassFilter) + "eqao_g9_class_code"] = classroom.id;
    student["classLabel"] = classroom.class_code;
    student["class_code_label"] = classroom.class_code;
    student["class_code"] = classroom.id;
    student["classCode"] = classroom.id;
    this.increaseClassStudentCount(classroom.id);
  }

  findStudentClassCode(studentRecord: any): null | string
  {
    return "classCode";
  }

  getApiData(record: Partial<IStudentAccount>, fromApplyBtn = false, forBulkStudentImport: boolean = false) {
    //Convert the record back to a format which is acceptable for the API
    let forApi: any = null;
    let data: any = null;
    
    if (!this.isABED() || !forBulkStudentImport)
    {
      forApi = this.g9demoService.clientToApiPayloadStudent(record); 
      
      const student = {         
        id: forApi.id, 
        first_name: forApi.first_name, 
        middle_name: forApi.middle_name, 
        last_name: forApi.last_name 
      };

      const key_namespace = this.whitelabelService.getSiteText('key_namespace') || 'abed_sdc';

      const studentAggData = {
        ...student,
        ...record,
        key_namespace,
      }      
      // let student_meta = [];
      // let non_meta_props = ["id", "first_name", "middle_name", "last_name", "roles", "class_code", "teacher", "teacher_notes"];

      // this is now done on API side
      // for (let [key, value] of Object.entries(forApi)) {
      //   if (non_meta_props.includes(key) || value === undefined) {
      //     continue;
      //   }
      //   const uid = forApi.id;
      //   const key_namespace = forApi.key_namespace ||  "eqao_sdc";
      //   student_meta.push({
      //     uid,
      //     key_namespace,
      //     key,
      //     value,
      //   });
      // }
      
      // To ensure the class_code is present in the payload, otherwise the student will be revoked 
      if(!forApi.class_code) {
        forApi.class_code = record.class_code;
      }
      
      data = {
        student,
        student_meta: this.g9demoService.extractStudentMeta(studentAggData),
        school_class_name: record.eqao_g9_class_code_label,
        school_class_id: forApi.class_code,
        sch_group_id: this.mySchool.getCurrentSchoolGroupId()
      }

      if (isAdminAltReqAllowed()){
        data.alt_version_req = this.clientToApiAltVersionRequest(record)
      }

    }

    else 
    // ABED bulk import only
    {
      // note: for ABED bulk import, we should use placeholder class as default class
      data = {
       studentInfo: record,
       sch_group_id: this.mySchool.getCurrentSchoolGroupId(),
      };
    }
    
    // console.log(data);
    return data;
  }

  private getStudentDefaults() {
    if (!EQAO_ASSESSMENTS.includes(this.currentClassFilter)) return {};

    return {
      eqao_is_g10: (this.currentClassFilter === ClassFilterId.OSSLT) ? '1' : '#',
      eqao_is_g9: (this.currentClassFilter === ClassFilterId.G9) ? '1' : '#',
    }
  } 
  
  // newAccount
  newAccountModalStart() {    
    if(this.isABED()){
      this.loadStudentAccommodations();
    }
    const config: IModalNewAccount = {
      studentDefaults: this.getStudentDefaults()      
    };
    this.pageModal.newModal({
      type: AccountModal.NEW_ACCOUNT,
      config,
      finish: this.newAccountModalFinish,
      cancel: this.resetAccommodations
    });
  }

  resetAccommodations = () =>{
    this.accommodationList = [];
  }
  
  async loadStudentAccommodations(uid?){
    this.accommodationList = await this.g9demoService.loadStudentAccommodations(this.currentClassFilter, uid);
  }

  async finishAccommodations(student)
  {
    const uid = student.uid;
    if(!this.accommodationList) 
    {
      await this.loadStudentAccommodations(uid);
    }
    const validAccommodations = this.g9demoService.validationAccomm(this.accommodationList);
    if(validAccommodations){
      student.accommodations = [];
      for(let accomm of this.accommodationList){
        if(accomm.value) student.accommodations.push(accomm.name);
      }
      this.g9demoService.updateStudentAccommodations(uid, this.accommodationList);
      this.accommodationList = [];
    }else{
      this.loginGuard.quickPopup('abed_invalid_accommodations');
    }
  }

  newAccountModalFinish = (config: { payload: { record: Partial<IStudentAccount>, data: any } }) => {
    if(!this.clientRuleCheck(config.payload.data)){
      return;
    }
    const newStudentData = config.payload.data;
    if(this.currentClassFilter === ClassFilterId.Primary){
      newStudentData.eqao_is_g3 = '1';
    }
    if(this.currentClassFilter === ClassFilterId.Junior){
      newStudentData.eqao_is_g6 = '1';
    }
    if(this.currentClassFilter === ClassFilterId.G9){
      newStudentData.eqao_is_g9 = '1';
    }
    if(this.currentClassFilter === ClassFilterId.OSSLT){
      newStudentData.eqao_is_g10 = '1';
    }
    this.addNewAccounts([newStudentData]);
  }

  async addNewAccounts(dataList: any[], isBulk = false) {
    if (!dataList) {
      return;
    }

    let matchPropName = this.isBCSite() ? '' : 'eqao_student_gov_id'; // TODO: Add match prop for BC!
    let apiRecords = [];
    const oenMap = {};
    const matchPropMap = oenMap;
    for (let data of dataList) {
      if (NBED_ASSESSMENTS.includes(this.currentClassFilter)) {
        data[this.currentClassFilter] = 1;
        data['nbed_group_type'] = this.currentClassFilter; // DEPRICATE THIS
      }

      if (this.whitelabelService.isABED()) {
        data[this.currentClassFilter] = 1;
        data['abed_group_type'] = this.currentClassFilter; // DEPRICATE THIS
      }
      
      data['GroupType'] = this.currentClassFilter; // GENERALIZED

      if (data.eqao_student_gov_id) {
        oenMap[data.eqao_student_gov_id] = data;
      }

      if (!this.whitelabelService.isABED()) {
        data.test_sessions = [];
      }
      
      if (data.CreatedDate === undefined || data.CreatedDate === '#') {
        data.CreatedDate =  moment().utc().format();
      }

      this.modifyStudentData(data, true);
      this.updateAccTechAndNPS(data);
      this.setupLinear(data);
      const apiRecord = this.getApiData(data, false, isBulk);
      apiRecords.push(apiRecord);
      const uuid = uuidv4(); //to pair up data and apiRecord.
      data['uuid'] = uuid;
      apiRecord['uuid'] = uuid;
    }

    const importSize = 20;
    let returnResLength = 0;
    this.totalImportNumber = apiRecords.length;
    this.currentImportNumber = 0;
    this.vrs = {
      successCreate: [],
      successUpdate: [],
      noChange: [],
      failed: [],
      results: []
    };
    let newClasses:any = [];
    for(let i = 0; i < apiRecords.length; i+=importSize){
      this.currentImportNumber = i;
      var portionApiRecords = apiRecords.length >= i+importSize ? apiRecords.slice(i, i+importSize): apiRecords.slice(i, apiRecords.length)
      await this.auth
      .apiCreate(this.routes.SCHOOL_ADMIN_STUDENT, portionApiRecords, this.configureQueryParams(isBulk))
      .then((result) => {
        if(result.newClassRecords.length > 0){
          result.newClassRecords.forEach(classRecord => {
            if(newClasses.find(newClass => +newClass.id === +classRecord.id) === undefined){
              newClasses.push(classRecord);
              // Create new added Class from Student Import
              const semester = this.g9demoService.semesters.list.find(s => s.id == classRecord.semester_id);
              const newClassroomRecord:any = {
                assessment: 0,
                class_code: classRecord.name,
                course_type: classRecord.group_type,
                currentStudents: initMappedList([]),
                id: classRecord.id,
                is_grouping: classRecord.is_grouping,
                onboarding: 0,
                semester: String(classRecord.semester_id),
                semester_label: semester ? semester.label : '',
                students: 0,
                educator: "",
                course: "",
                is_fi: classRecord.is_fi
              }

              this.g9demoService.classrooms.push(newClassroomRecord);
              this.g9demoService.teacherClassrooms.list.push(newClassroomRecord);

              const classOptionList = [];
              const classOptionEl = {id: classRecord.id, label: classRecord.name, group_type: classRecord.group_type};
              classOptionList.push(classOptionEl);

              const mappedClassEl = initMappedList(classOptionList);
              this.g9demoService.classOptions = {map: {...this.g9demoService.classOptions.map, ...mappedClassEl.map}, list: this.g9demoService.classOptions.list.concat(mappedClassEl.list)}
              this.g9demoService.teacherClassrooms.map[newClassroomRecord.id] = newClassroomRecord;
            }
          });
        }

        const res = result.studentRecords;
        if (res && res.length) 
        {
          let validateResult;
          //let successImport = 0;
          //let vrs: IValidationResults<IStudentAccount> = {passed: [],failed: [],results: []};
          returnResLength += res.length

          res.forEach(resStudent => {
            if(resStudent.uid){
              this.finishAccommodations(resStudent);
            }
            //*** Sucesss Created/Imported********************************************************/     
            if (resStudent.validateResult.ErrorMessages.length === 0){ 
              //***update the client side students data ********************************************/

              // handle the Invalid OEN pass exception 
              const meta_errMsg = resStudent.user_meta.find(meta => meta.key =='errMsg')
              const errMsg = meta_errMsg!= undefined?meta_errMsg.value:JSON.stringify([])
              //************************************************************************ */  

              let student = dataList.find(obj=>{return obj.uuid === resStudent.uuid})
              if(!student.eqao_g9_class_code) {
                const newClass = newClasses.find(cl => 
                  cl.name === student.eqao_g9_class_code_label
                );

                if (newClass != null)
                {
                  student.eqao_g9_class_code = newClass.id;
                }
                
              }
              
              let schoolClass = null;

              if (this.whitelabelService.isABED())
              {
                let keyName = "class_code";
                if (isBulk)
                {
                  schoolClass = this.g9demoService.classOptions.map[resStudent[keyName]];
                }

                else
                {
                  schoolClass = this.g9demoService.classOptions.map[student[keyName]];
                }      
              }

              else 
              {
                schoolClass = this.g9demoService.classOptions.map[student.eqao_g9_class_code];
              }

              if (student) {  
                if(this.currentClassFilter !== ClassFilterId.G9){
                    const prefix = this.getClassCodePrefix(this.currentClassFilter);
                    for (const key in student) {
                      if(overlapVariables.indexOf(key) == -1 && key != 'uuid'){
                        student[prefix+key] = student[key];
                      }
                    }
                    student[prefix+"errMsg"] = errMsg;
                  }else{
                    student.errMsg = errMsg;
                  }
                  student.id = resStudent.uid;
                  student.uid = resStudent.uid;
              }
              if (NBED_ASSESSMENTS.includes(this.currentClassFilter) || this.whitelabelService.isABED()) 
              {
                student["course"] = {};
                student["course"][this.currentClassFilter] = 1;
              }

              if (this.whitelabelService.isABED())
              {
                // student.GroupType = student.abed_group_type; // TODO: This should already be there
                student["course"] = {};
                student["course"][this.currentClassFilter] = 1;
                student["course"]["DateofBirth"] = student.DateofBirth;
              }
              
              if(!student.groupings && schoolClass) {
                
                student['groupings'] = [
                  {
                    classCode: schoolClass.label,
                    id: student.school_class_id,
                    test_window_id: this.currentTestWindow.id
                  }
                ]
              }
              
              if (this.isABED() && isBulk)
              {
                student = this.adjustStudentAsNeededForABEDImport(resStudent, schoolClass);
              }
              
              this.students.splice(0, 0, student);
              this.g9demoService.schoolAdminStudents.list.push(student);
              if(schoolClass) this.increaseClassStudentCount(schoolClass.id);

              const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9demoService.schoolData;
              let showAlertForUnPaidStudent
              let classroom;
              let course_type = this.currentClassFilter == ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter == ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter == ClassFilterId.G9?'EQAO_G9':'EQAO_G10'
              resStudent.user_meta.map((um) => {
                if (um.key_namespace === 'nbed_course') {
                  course_type = um.value;
                }
              })

              if (schoolClass) 
              {
                student[this.getClassCodePrefix(this.currentClassFilter) + "eqao_g9_class_code_label"] = schoolClass.label;
                student[this.getClassCodePrefix(this.currentClassFilter) + "eqao_g9_class_code"] = schoolClass.id;

                if (this.currentClassFilter == ClassFilterId.Primary) {
                  showAlertForUnPaidStudent = payment_req_pj ? true : false
                } else if (this.currentClassFilter == ClassFilterId.Junior) {
                  showAlertForUnPaidStudent = payment_req_pj ? true : false
                } else if(this.currentClassFilter == ClassFilterId.G9) {
                  showAlertForUnPaidStudent = payment_req_g9 ? true : false
                } else if (this.currentClassFilter == ClassFilterId.OSSLT) {
                  showAlertForUnPaidStudent = payment_req_osslt ? true : false
                }

                classroom = this.g9demoService.classrooms.find (cr => {
                  const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
                  const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
                  const tw_isActive = (new Date(test_window.date_end) > new Date ())
                  if(cr.class_code == schoolClass.label && cr.course_type == course_type && tw_isActive){
                    return cr;
                  }
                })  
                //classroom = this.getClassroom(schoolClass.label)
                if(classroom){
                  if(showAlertForUnPaidStudent){
                    this.promptForUnPaidStudent(this.currentClassFilter, classroom.id);
                  }
                  student.teacher = classroom.educator;
                  student.semester_label = this.renderSemester(classroom.semester)
                  const teacherClassrooms = this.g9demoService.teacherClassrooms.map[classroom.id]
                  teacherClassrooms.currentStudents.list.push(student)
                  teacherClassrooms.currentStudents.map[student.id]= data;
                  if(this.currentClassFilter == ClassFilterId.Primary){
                    student._g3_eqao_pj_french = classroom.is_fi?'1':'0'
                  }
                }
              }else{
                this.autoPlaceholderAssignment(student);
              }
            
              //*********************************************************************************/ 
              //*** success messages **************************************************************/
              if (!isBulk) {
                validateResult = resStudent.validateResult;
              }else{
                if (this.whitelabelService.isABED()) {
                  this.vrs.successCreate.push(student.StudentIdentificationNumber);
                }

                else {
                  if(this.isSasnLogin){
                    this.vrs.successCreate.push(student.SASN);
                  } else {
                    this.vrs.successCreate.push(student.eqao_student_gov_id);
                  }
                }
              }
              //*********************************************************************************/ 
            }
            //*** fail messages *****************************************/
            else{
              if(!isBulk){
                validateResult = resStudent.validateResult;
              }else{
                const student = dataList.find(obj=>{return obj.uuid === resStudent.uuid})
                let errors = [];
                resStudent.validateResult.ErrorMessages.forEach(errMsg => {
                  const errSlug =  this.apiErrMsgToSlug(errMsg);
                  const message = this.lang.tra(errSlug);
                  const error = {'':'', message};
                  errors.push(error);
                });

                
                this.vrs.failed.push(student);
                const validationResult: IValidationResult<IStudentAccount> = {
                  obj: student,
                  errors
                };
                this.vrs.results.push(validationResult);    
              }
            }
            //************************************************************************************/   
          });

          //handle Messages
          this.accountsTable.injestNewData(this.students);
          if(!isBulk){
            if(validateResult.ErrorMessages.length > 0){
              const errSlug =  this.apiErrMsgToSlug(validateResult.ErrorMessages[0]);
              const popupMsg = this.lang.tra(errSlug);
              setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
            }else if(validateResult.warningMessages.length > 0){
              const warnSlug =  this.apiErrMsgToSlug(validateResult.warningMessages[0]);
              const popupMsg = this.lang.tra(warnSlug);
              setTimeout(() => this.loginGuard.quickPopup(popupMsg), 0);
              this.pageModal.closeModal()
            }else{
              alert(this.lang.tra("sa_student_new_acc_alert_single"))
              this.pageModal.closeModal()
            }
          }
          else{  
            // if(successImport >0) {
            //   alert(this.lang.tra("sa_student_new_acc_alert", undefined, { NUM_ACCOUNTS: successImport }));
            // }
            // if ((res.length - successImport) > 0) {
            //   setTimeout(() => { this.importValidationModalStart(vrs);}, 0)
            // }  
          }  
        }

        if((result.SDC_conflicts_g3 && this.currentClassFilter === ClassFilterId.Primary)
         ||(result.SDC_conflicts_g6 && this.currentClassFilter === ClassFilterId.Junior)
         ||(result.SDC_conflicts_g9 && this.currentClassFilter === ClassFilterId.G9)
         ||(result.SDC_conflicts_g10 && this.currentClassFilter === ClassFilterId.OSSLT)
         ){
          let SDC_conflicts_key;
          let SDC_conflict_key;
          switch(this.currentClassFilter){
            case ClassFilterId.Primary:
              SDC_conflicts_key = 'SDC_conflicts_g3'
              SDC_conflict_key = 'SDC_conflict_g3'
              break;
            case ClassFilterId.Junior:
              SDC_conflicts_key = 'SDC_conflicts_g6'
              SDC_conflict_key = 'SDC_conflict_g6'
              break;
            case ClassFilterId.G9:
              SDC_conflicts_key = 'SDC_conflicts_g9'
              SDC_conflict_key = 'SDC_conflict_g9'
              break;
            case ClassFilterId.OSSLT:
            default:
              SDC_conflicts_key = 'SDC_conflicts_g10'
              SDC_conflict_key = 'SDC_conflict_g10'
              break
          }
          
          //check if anyupdate student have class switch from old test window to new test window
          const prefix = this.currentClassFilter === ClassFilterId.Primary?"_g3_":this.currentClassFilter === ClassFilterId.Junior?"_g6_":this.currentClassFilter === ClassFilterId.G9?"":"_g10_";
          const classCodePropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code' // TODO: Add prop for BC!
          const classCodeLabelPropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code_label' // TODO: Add prop for BC!
          result.changeGrpStudents.forEach( cgs => {
            const student:any = this.students.find(st => st.id == cgs.uid);
            const classroom = this.g9demoService.classrooms.find(cr=> cr.id == cgs.class_id)
            if(student && classroom) {
              student[classCodePropName] = classroom.id;
              student[classCodeLabelPropName] = classroom.class_code;
              student.teacher = classroom.educator;
              if(this.currentClassFilter == ClassFilterId.Primary){
                student._g3_eqao_pj_french = classroom.is_fi?'1':'0'
              }
              if(this.currentClassFilter == ClassFilterId.G9){
                student.semester_label = this.renderSemester(classroom.semester)
              }  
            }  
          });

          
          if(result[SDC_conflicts_key].length > 0 && !this.whitelabelService.isABED()) {
            result[SDC_conflicts_key].forEach(conflict => {
              if(conflict.compare_result !== '[]') {
                const student:any = this.students.find(st => conflict.uid == st.id);
                if(student) {
                  student[SDC_conflict_key] = conflict;
                  if(this.isSasnLogin){
                    this.vrs.successUpdate.push(student.SASN);
                  } else {
                    this.vrs.successUpdate.push(student.eqao_student_gov_id);
                  }
                  let index = result.updateStudentsIds.indexOf(student.id)
                  if(index > -1) {
                    result.updateStudentsIds.splice(index, 1);
                  }
                }
              }
            })
          }

          result.updateStudentsIds.forEach(stuId => {
            const student:any = this.students.find(st => stuId == st.id);
            if(student) {
              if (this.whitelabelService.isABED()) {
                this.vrs.noChange.push(student.StudentIdentificationNumber);
              }

              else {
                if(this.isSasnLogin){
                  this.vrs.noChange.push(student.SASN);
                } else {
                  this.vrs.noChange.push(student.eqao_student_gov_id);
                }
              }
            }
          }); 
        }
      });// end api call
    }// end for loop for each import
    if(isBulk){
      if(this.pageModal){
        this.pageModal.closeModal();
      }  
      if(this.vrs.successUpdate.length > 0) {
        this.showFlaggedItems = FlaggedItemCase.IMPORT_FLAG;
        this.accountsTable.activeFilters['showFlaggedItems'] = { mode: FilterSettingMode.VALUE, config: { value: '#' } }
        this.accountsTable.refreshFilters();
      }
      // Error message Summary: 
      // if ((returnResLength - this.successCreateNum) > 0) {
      setTimeout(() => { this.importValidationModalStart(this.vrs);}, 0)
      // }  

    }
    this.mySchool.initStuAsmtSignoff()
  }

  adjustStudentAsNeededForABEDImport(res: any, schoolClass: any)
  {
    // setting important information from response, specifically for ABED import, to normalize student
    // with the rest of SA view (all these keys are expected).
    let student = {};
    student["classCode"] = schoolClass.id
    student["classLabel"] = schoolClass.label;
    student[this.currentClassFilter] = "1";
    student["first_name"] = res.user_entry.first_name;
    student["middle_name"] = res.user_entry.middle_name;
    student["last_name"] = res.user_entry.last_name;
    student["id"] = res.uid;
    student["uid"] = res.uid;
    student["__isSelected"] = false;
    student["errMsg"] = res.validateResult.ErrorMessages;

    student["course"] = {};
    for (let userMeta of res.user_meta) {
      student[userMeta.key] = userMeta.value;
      student["course"][userMeta.key] = userMeta.value;
    }
    student["course"][this.currentClassFilter] = "1";
    
    return student;
  }

  getTotalImportStudentNumber() {
    return this.lang.tra("sa_student_total_acc_alert", undefined, { NUM_ACCOUNTS: this.totalImportNumber });
  }

  getSuccessCreateStudentNumber() {
    return this.lang.tra("sa_student_new_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.successCreate.length });
  }

  getSuccessUpdateStudentNumber() {
    return this.lang.tra("sa_student_update_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.successUpdate.length });
  }

  getNoChangeStudentNumber() {
    return this.lang.tra("sa_student_no_change_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.noChange.length });
  }

  getFailedImportStudentNumber() {
    return this.lang.tra("sa_student_fail_import_acc_alert", undefined, { NUM_ACCOUNTS: this.vrs.failed.length });
  }

  processSelectedTestSessions(testSessionsSelected: TestSessionSelection) {
    const testSessionIds = [];
    Object.keys(testSessionsSelected).forEach(testSessionId => {
      if (testSessionsSelected[testSessionId]) {
        testSessionIds.push(+testSessionId);
      }
    });
    return testSessionIds;
  }
  getClassroom(classCode){
    const classroom = this.g9demoService.classrooms.find(classroom => classroom.class_code == classCode);
    if(classroom){
      return classroom
    }
    return null;
  }
  // assign session
  assignSessionModalStart() {
    const students = this.getSelectedStudents(true);
    const config: IModalAssignSession = {
      accounts: students,
      testSessionsSelected: {}
    };
    this.pageModal.newModal({
      type: AccountModal.ASSIGN_SESSION,
      config,
      finish: this.assignSessionModalFinish
    });
  }
  assignSessionModalFinish = (config: IModalAssignSession) => {
    const uids = config.accounts.map(acct => acct.uid);
    const testSessionIds = this.processSelectedTestSessions(config.testSessionsSelected);
    this.auth.apiCreate(this.routes.TEST_ADMIN_STUDENTS_BOOKING, {
      isRevoke: false,
      testSessionIds,
      uids,
    }, this.mySchool.constructPermissionsParams())
      .then(res => {
        config.accounts.forEach(student => student.test_sessions = _.union(student.test_sessions, testSessionIds));
        this.pageModal.closeModal();
      });
  }

  // unassign session
  unassignSessionModalStart() {
    const students = this.getSelectedStudents(true);
    const availableTestSessions: number[] = _.uniq(_.flatten(students.map(student => student.test_sessions)));
    const config: any = {
      accounts: students,
      availableTestSessions,
      testSessionsSelected: {}
    };
    this.pageModal.newModal({
      type: AccountModal.UNASSIGN_SESSION,
      config,
      finish: this.unassignSessionModalFinish
    });
  }

  unassignSessionModalFinish = (config: any) => {
    const uids = config.accounts.map(student => student.uid);
    const testSessionIds = this.processSelectedTestSessions(config.testSessionsSelected);
    this.auth.apiCreate(this.routes.TEST_ADMIN_STUDENTS_BOOKING, {
      isRevoke: true,
      testSessionIds,
      uids,
    }, this.mySchool.constructPermissionsParams())
      .then(res => {
        config.accounts.forEach(student => {
          _.pull(student.test_sessions, ...testSessionIds);
        });
        this.pageModal.closeModal();
      });
  }

  assignClassroomModalStart() {
    console.log("Assign classroom modal start...")
    let type_slug; // = this.currentClassFilter === ClassFilterId.G9?"EQAO_G9M":"EQAO_G10L"; 
    switch(this.currentClassFilter){
      case ClassFilterId.G9:
        type_slug = "EQAO_G9M";
        break;
      case ClassFilterId.TCLE:
        type_slug = "NBED_TCLE";
        break;
      case ClassFilterId.TCN:
        type_slug = "NBED_TCN";
        break;
      case ClassFilterId.SCIENCES8:
        type_slug = "NBED_SCIENCES8";
        break; 
      case ClassFilterId.Primary:
        type_slug = "EQAO_G3P"
        break;
      case ClassFilterId.Junior:
        type_slug = "EQAO_G6J"
        break
      case ClassFilterId.OSSLT:
        type_slug = "EQAO_G10L"
        break
      default:
        type_slug = this.currentClassFilter
        break;
    }
    
    let testWindowFilter = this.accountsTable.activeFilters['testWindowFilter'].config as IFilterSettingConfigValue;
    let activeTestWindow = testWindowFilter.value;
    if (testWindowFilter == null || activeTestWindow == null) {
      activeTestWindow = this.g9demoService.testWindows.find(tw => (new Date(tw.date_end) > new Date ()) && tw.type_slug === type_slug);
    }
    
    // console.log('this.g9demoService.testWindows: ', this.g9demoService.testWindows)
    const students = this.getSelectedStudents(true);
    this.availableClassrooms = this.g9demoService.classrooms;
    // console.log("Available classrooms: ", this.availableClassrooms)
    // console.log("Active filter: ", (this.accountsTable.activeFilters.group_type.config as IFilterSettingConfigValue).value)
    this.classRoomsToAssign = [];
    // #MERGE_20220524 : refactor
    
    // if( this.accountsTable.activeFilters.is_g9){
    //   this.availableClassrooms.forEach((classroom) =>{
    //     const semester = this.g9demoService.semesters.map[+classroom.semester]
    //     if(classroom.course_type == 'EQAO_G9' && semester && activeTestWindow.id === semester.testWindowId){
    //       this.classRoomsToAssign.push(classroom)
    //     }
    //   })
    // } else if(this.accountsTable.activeFilters.is_g10){
    //   this.availableClassrooms.forEach((classroom) =>{
    //     const semester = this.g9demoService.semesters.map[+classroom.semester]
    //     if(classroom.course_type == 'EQAO_G10' && semester && activeTestWindow.id === semester.testWindowId){
    //       this.classRoomsToAssign.push(classroom)
    //     }
    //   })
    // } else if((this.accountsTable.activeFilters.group_type.config as IFilterSettingConfigValue).value == 'NBED_TCLE'){
    //   this.availableClassrooms.forEach((classroom) =>{
    //     console.log('classroom.course_type == NBED_TCLE', classroom.course_type == 'NBED_TCLE')
    //     const semester = this.g9demoService.semesters.map[+classroom.semester]
    //     if(classroom.course_type == 'NBED_TCLE' && semester && activeTestWindow.id === semester.testWindowId){
    //       this.classRoomsToAssign.push(classroom)
    //     }
    //   })
    // } else if((this.accountsTable.activeFilters.group_type.config as IFilterSettingConfigValue).value == 'NBED_TCN'){
    //   this.availableClassrooms.forEach((classroom) =>{
    //     const semester = this.g9demoService.semesters.map[+classroom.semester]
    //     // console.log('activeTestWindow.id: ', activeTestWindow.id)
    //     // console.log('currentTestWindow.id: ', this.currentTestWindow.id)
    //     // console.log('semester.testWindowId: ', semester.testWindowId)
    //     // console.log('activeTestWindow.id === semester.testWindowId: ', activeTestWindow.id === semester.testWindowId)
    //     if(classroom.course_type == 'NBED_TCN' && semester && activeTestWindow.id === semester.testWindowId){
    //       this.classRoomsToAssign.push(classroom)
    //     }
    //   })
    // } else if((this.accountsTable.activeFilters.group_type.config as IFilterSettingConfigValue).value == 'NBED_SCIENCES8'){
    //   this.availableClassrooms.forEach((classroom) =>{
    //     const semester = this.g9demoService.semesters.map[+classroom.semester]
    //     if(classroom.course_type == 'NBED_SCIENCES8' && semester && activeTestWindow.id === semester.testWindowId){
    //       this.classRoomsToAssign.push(classroom)
    //     }
    //   })
    // }
    const classroomSelected = {};
    const courseType = this.getCourseType(this.currentClassFilter) //this.currentClassFilter === ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter === ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter === ClassFilterId.G9?'EQAO_G9':'EQAO_G10';
    this.availableClassrooms.forEach((classroom) =>{
      const classroomCopy = JSON.parse(JSON.stringify(classroom));
      const semester = this.g9demoService.semesters.map[+classroomCopy.semester]
      if(classroomCopy.course_type == courseType && semester && activeTestWindow.id === semester.testWindowId){
        for(let student of students){
          const locateClass = student.groupings.find(g => g.id == classroomCopy.id);
          if(locateClass){ //if any of the selected students already have the class then make sure that class is not selectable on the popup
            classroomCopy.isDisabled = true;
          }
        }
        this.classRoomsToAssign.push(classroomCopy);
      }
    })
    const config: IModalAssignClassroom = {
      accounts: students,
      classroomSelected,
      isCheckForExisting: true
    };
    this.pageModal.newModal({
      type: AccountModal.ASSIGN_CLASSROOM,
      config,
      finish: this.assignClassroomModalFinish
    });
  }
  assignClassroomModalFinish = (config: IModalAssignClassroom) => {
    
    // #MERGE_20220524 : refactor

    // let classCodePropName = this.isBCSite() ? '' : (this.accountsTable.activeFilters.is_g9 ? 'eqao_g9_class_code' : '_g10_'+'eqao_g9_class_code'); // TODO: Add prop for BC!
    // let classCodeLabelPropName = this.isBCSite() ? '' : (this.accountsTable.activeFilters.is_g9 ? 'eqao_g9_class_code_label' : '_g10_'+'eqao_g9_class_code_label'); // TODO: Add prop for BC!
    
    // const activeFilterGroupType = _.get(this.accountsTable.activeFilters, 'group_type.config.value', null);
    // let oldClassIDs = []
    // if (activeFilterGroupType){
    //   switch (activeFilterGroupType){
    //     case 'NBED_TCLE':
    //       classCodePropName = '_tcle_eqao_g9_class_code';
    //       classCodeLabelPropName = '_tcle_eqao_g9_class_code_label'
    //       oldClassIDs = config.accounts.map(student => student._tcle_eqao_g9_class_code)
    //       break;
    //     case 'NBED_TCN':
    //       classCodePropName = '_tcn_eqao_g9_class_code';
    //       classCodeLabelPropName = '_tcn_eqao_g9_class_code_label'
    //       oldClassIDs = config.accounts.map(student => student._tcle_eqao_g9_class_code)
    //       break;
    //     case 'NBED_SCIENCES8':
    //       classCodePropName = '_sciences8_eqao_g9_class_code'
    //       classCodeLabelPropName = '_sciences8_eqao_g9_class_code_label'
    //       oldClassIDs = config.accounts.map(student => student._tcle_eqao_g9_class_code)
    //       break;
    //   }
    // }

    const prefix =  this.getClassCodePrefix(this.currentClassFilter); // this.currentClassFilter === ClassFilterId.Primary?"_g3_":this.currentClassFilter === ClassFilterId.Junior?"_g6_":this.currentClassFilter === ClassFilterId.G9?"":"_g10_";
    const classCodePropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code' // TODO: Add prop for BC!
    const classCodeLabelPropName = this.isBCSite() ? '' : prefix+'eqao_g9_class_code_label' // TODO: Add prop for BC!
    const oldClassIDs = (this.isNBED() || this.whitelabelService.isABED()) ? config.accounts.map(student => student[classCodePropName]) : []

    const classroomId = Object.entries(config.classroomSelected)[0][0];
    const classroom = this.classRoomsToAssign.find(classroom => {
      return classroom.id === Number(classroomId);
    })
    if (classroom) {
      // config.accounts.forEach(account => {
      //   account[classCodePropName] = classroom.id;
      //   account[classCodeLabelPropName] = classroom.class_code;
      //   account.teacher = classroom.educator;
      //   if(this.currentClassFilter == ClassFilterId.Primary){
      //     account._g3_eqao_pj_french = classroom.is_fi?'1':'0'
      //   }
      // });
    }
    let schoolData: any = this.g9demoService.schoolData

    const schl_group_id = schoolData.group_id;
    const schl_dist_group_id = schoolData.schl_dist_group_id;
    // data.educator
    let namespace= this.currentClassFilter === ClassFilterId.Primary?"eqao_sdc_g3":this.currentClassFilter === ClassFilterId.Junior?"eqao_sdc_g6":this.currentClassFilter === ClassFilterId.G9?"eqao_sdc":"eqao_sdc_g10";
    if (this.whitelabelService.isABED()) {
      namespace = "abed_sdc";
    }
    const configs = { ...config, schl_group_id, schl_dist_group_id, namespace}
    this.auth.apiCreate(this.routes.SCHOOL_ADMIN_STUDENT_ASSIGN, configs, this.configureQueryParams()).then(res => {
      config.accounts.forEach(account => {
        if (classroom){
          const oldClassroom = this.g9demoService.classrooms.find(classroom => {
            return classroom.id === account[classCodePropName];
          })
          account[classCodePropName] = classroom.id;
          account[classCodeLabelPropName] = classroom.class_code;
          account.teacher = classroom.educator;
          if(this.currentClassFilter == ClassFilterId.Primary){
            account._g3_eqao_pj_french = classroom.is_fi?'1':'0'
          }
          if (oldClassroom) {
            this.g9demoService.removeStudentFromClassroom(account, oldClassroom.id);
          }
          this.g9demoService.teacherClassrooms.map[classroomId].currentStudents.list.push(account)
        }  
        if(this.currentClassFilter == ClassFilterId.G9){
          account['eqao_g9_class_code'] = classroom.id;
          account['eqao_g9_class_code_label'] = classroom.class_code;
          account.semester_label = this.renderSemester(classroom.semester)
        }
        else if(this.currentClassFilter == ClassFilterId.TCLE){
          account['_tcle_'+'eqao_g9_class_code'] = classroom.id;
          account['_tcle_'+'eqao_g9_class_code_label'] = classroom.class_code;
          account.semester_label = this.renderSemester(classroom.semester)
        }
        else if (this.currentClassFilter === ClassFilterId.OSSLT){
          account['_g10_'+'eqao_g9_class_code'] = classroom.id;
          account['_g10_'+'eqao_g9_class_code_label'] = classroom.class_code;
        } else {
          account[this.getClassCodePrefix(this.currentClassFilter) + 'eqao_g9_class_code'] = classroom.id;
          account[this.getClassCodePrefix(this.currentClassFilter) + 'eqao_g9_class_code_label'] = classroom.class_code;
          account.classCode = ""+classroom.id;
          account.classLabel = classroom.class_code;
          this.addClassroomToStudents(account, config.classroomSelected);
        }
      });
      this.accountsTable.injestNewData(this.students); 
      oldClassIDs.forEach(classId => {
        this.decreaseClassStudentCount(classId);
      })
      config.accounts.forEach(account => {
        this.increaseClassStudentCount(classroom.id);
      })
    })
    .catch((err:Error)=>{
      if(err.message.includes("INVALID_CLASS")){
        setTimeout(() => this.loginGuard.quickPopup(this.lang.tra('lbl_assign_class_error')), 0);
      }

      if (err.message.includes("secondary ASNs")) {
        // TODO: translations
        setTimeout(() => this.loginGuard.quickPopup(err.message), 0);        
      }
      console.error(err);
    });
    // console.log(this.students);  
    this.pageModal.closeModal();
  }
  addClassroomToStudents(account, classroomSelected){
    const classroomIds = Object.keys(classroomSelected);
    const classrooms = this.classRoomsToAssign.filter(classroom => {
      return classroomIds.findIndex(c => Number(c) == classroom.id) != -1;
    })
    classrooms.map(classroom => {
      const classIsNewToStudent = account.groupings.findIndex(g => g.id == classroom.id && !classroom.isDisabled) == -1;
      if(classIsNewToStudent){
        account.groupings.push({id: classroom.id, classCode: classroom.class_code, test_window_id: this.currentTestWindow.id});
      }
    })
  }
  getAssignModalTitle(){
    if(this.currentClassFilter === ClassFilterId.G9){
      return 'sa_assign_students'
    } else if(this.currentClassFilter === ClassFilterId.OSSLT){
      return  'sa_assign_students_grouping'
    }
  }
  getAssignModalTitle2(){
    if(this.currentClassFilter === ClassFilterId.G9){
      return 'sa_student_assign_class'
    } else if(this.currentClassFilter === ClassFilterId.OSSLT){
      return  'sa_student_assign_grouping'
    }

  }

  // unassign classroom
  unassignClassroomModalStart() {
    const students = this.getSelectedStudents(true);
    const config: any = {
      accounts: students
    };
    this.pageModal.newModal({
      type: AccountModal.UNASSIGN_CLASSROOM,
      config,
      finish: this.unassignClassroomModalFinish
    });
  }

  unassignClassroomModalFinish = (config: any) => {
    const students = this.getSelectedStudents(true);
    students.forEach(students => {
      students.classCode = null;
    });
    this.pageModal.closeModal();
  }

  removeStudent = () => {
    let confirmationMsg;
    if(this.whitelabelService.isABED()){
      confirmationMsg = this.lang.tra('abed_remove_students_confirm');
    }else{
      confirmationMsg =  this.lang.tra('sa-students-remove-student-conf-message');
    }
    const targets: any = this.getSelectedStudents(false);
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      confirm: async () => {
        if(this.isRemoving) {
          console.log("is removing");
          return;
        }
        console.log("confirm start");
        this.isRemoving = true;
        //const removePromises = [];
        const studentClasses :any[] =[];
        let namespace;
        let classCodeVarName;

        // console.log(this.currentClassFilter);

        switch(this.currentClassFilter){
          case ClassFilterId.ABED_SAMPLE:
          case ClassFilterId.ABED_GRADE_6:
          case ClassFilterId.ABED_GRADE_9:
          case ClassFilterId.ABED_GRADE_12:
            namespace='abed_sdc';
            classCodeVarName='classCode';
            break;
          case ClassFilterId.Primary:
            namespace = 'eqao_sdc_g3';
            classCodeVarName = '_g3_eqao_g9_class_code';
          break;
          case ClassFilterId.Junior:
            namespace = 'eqao_sdc_g6';
            classCodeVarName = '_g6_eqao_g9_class_code';
          break;  
          case ClassFilterId.G9:
            namespace = 'eqao_sdc';
            classCodeVarName = 'eqao_g9_class_code';
          break;
          case ClassFilterId.OSSLT:
            namespace = 'eqao_sdc_g10';
            classCodeVarName = '_g10_eqao_g9_class_code';
          break;
          default:
            namespace = 'eqao_sdc_g3';
            classCodeVarName = '_g3_eqao_g9_class_code';
          break; 
        }
      
        for (let student of targets) {
          // console.log(student);
          // console.log(student[classCodeVarName], classCodeVarName);
          studentClasses.push({
            classIds:student[classCodeVarName],
            //classIds:this.currentClassFilter == ClassFilterId.G9? student.eqao_g9_class_code:student._g10_eqao_g9_class_code,
            studentId: student.id
          })
         
          //removePromises.push(promise);
        }

        // console.log(studentClasses);
        
        let query =  { 
          schl_group_id: this.mySchool.getCurrentSchoolGroupId(), 
          //class_id: this.currentClassFilter == ClassFilterId.G9? student.eqao_g9_class_code:student._g10_eqao_g9_class_code,
          studentClasses: null,
          namespace,
          //namespace: this.currentClassFilter == ClassFilterId.G9? 'eqao_sdc':'eqao_sdc_g10'
        }
        if(studentClasses.length > 1) {
          await this.auth.apiCreate(this.routes.SCHOOL_ADMIN_STUDENT_DELETE, studentClasses, { query: query });
          //await this.auth.apiUpdate(this.routes.SCHOOL_ADMIN_STUDENT_DELETE, -1, studentClasses, { query: query });
        } else {
          query.studentClasses = JSON.stringify(studentClasses);
          await this.auth.apiRemove(this.routes.SCHOOL_ADMIN_STUDENT, -1, { query: query });
        }        
        alert(this.lang.tra("sa_student_remove_success", undefined, {['studentNum']:targets.length}));

        console.log(targets);
        this.removeStudents(targets.map(row => row.uid));
        // console.log(this.g9demoService.schoolAdminStudents);
        studentClasses.forEach(studentClass => this.decreaseClassStudentCount(studentClass.classIds));
        this.checkSelection();
        this.accountsTable.refreshFilters();
        this.isRemoving = false;
        // console.log("confirm finished");
      }
    })
  }

  removeStudents(studentsToRemove: number[])
  {
    // console.log(studentsToRemove, this.accountsTable, this.g9demoService.schoolAdminStudents);
    this.accountsTable.removeData((row) => 
    {
      return studentsToRemove.includes(row.uid); 
    });

    studentsToRemove.forEach(uid => 
    {
      const deletedStudentIdx = this.g9demoService.schoolAdminStudents.list.findIndex((student) => 
      {
        return +student.uid === +uid;
      });

      if (deletedStudentIdx !== -1)
      {
        this.g9demoService.schoolAdminStudents.list.splice(deletedStudentIdx, 1);
        delete this.g9demoService.schoolAdminStudents.map[+uid];
      }
    });
  }

  async importStudentsModal() {
    const columns = await this.getStudentExportColumns();
    const config: IModalImport = {
      hideTable: false,
      columns,
      useConfirmAlert: true
    }
    // console.log(config);

    this.pageModal.newModal({
      type: AccountModal.IMPORT,
      config,
      finish: this.importStudentsModalFinish
    })
  }

  importStudentsModalFinish = (config: { importData: any[] }) => {
    config.importData.forEach(data => {
      if (data.eqao_g9_class_code) {
        data.eqao_g9_class_code = data.eqao_g9_class_code.trim();
      }  
      if(this.currentClassFilter === ClassFilterId.OSSLT){
        data.eqao_is_g10 = '1';
        data.eqao_is_g9 = '0';
        data.eqao_is_g3 = '0';
        data.eqao_is_g6 = '0';
      }
      if(this.currentClassFilter === ClassFilterId.G9){
        data.eqao_is_g10 = '0';
        data.eqao_is_g9 = '1';
        data.eqao_is_g3 = '0';
        data.eqao_is_g6 = '0';
      }
      if(this.currentClassFilter === ClassFilterId.Primary){
        data.eqao_is_g10 = '0';
        data.eqao_is_g9 = '0';
        data.eqao_is_g3 = '1';
        data.eqao_is_g6 = '0';
      }
      if(this.currentClassFilter === ClassFilterId.Junior){
        data.eqao_is_g10 = '0';
        data.eqao_is_g9 = '0';
        data.eqao_is_g3 = '0';
        data.eqao_is_g6 = '1';
      }
      if(data.NonParticipationStatus === '1'){
        data.NonParticipationStatus_exempted = '1'
      }
      if(data.NonParticipationStatus === '2'){
        data.NonParticipationStatus_deferred = '1'
      } 
      if(data.NonParticipationStatus === '3'){
        data.NonParticipationStatus_osslc = '1'
      }
      if(data.eqao_acc_assistive_tech === '2') {
        data.eqao_acc_assistive_tech_1_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech === '1') {
        data.eqao_acc_assistive_tech_1_other  = '1'
      }

      if(data.eqao_acc_assistive_tech_pj_reading === '2') {
        data.eqao_acc_assistive_tech_1_pj_reading_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_reading === '1') {
        data.eqao_acc_assistive_tech_1_pj_reading_other  = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_writing === '2') {
        data.eqao_acc_assistive_tech_1_pj_writing_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_writing === '1') {
        data.eqao_acc_assistive_tech_1_pj_writing_other  = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_mathematics === '2') {
        data.eqao_acc_assistive_tech_1_pj_mathematics_chrome = '1'
      }
      if(data.eqao_acc_assistive_tech_pj_mathematics === '1') {
        data.eqao_acc_assistive_tech_1_pj_mathematics_other  = '1'
      }

      // not relevant for ABED
      let classType = (this.currentClassFilter === ClassFilterId.Primary) ? 'EQAO_G3' : (this.currentClassFilter === ClassFilterId.Junior) ?'EQAO_G6':(this.currentClassFilter === ClassFilterId.G9) ?'EQAO_G9':'EQAO_G10';
      
      // const schoolClass = this.g9demoService.classOptions.list.find( 
      //   c => c.label == data.eqao_g9_class_code && c.group_type == classType
      // );
      if (NBED_ASSESSMENTS.includes(this.currentClassFilter) || this.whitelabelService.isABED()) {
        classType = this.currentClassFilter;
      }
      const schoolClass = this.g9demoService.classrooms.find (cr => {
        const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
        const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
        const tw_isActive = (new Date(test_window.date_end) > new Date ()) && test_window.id == this.currentTestWindow.id
        if(cr.class_code == data.eqao_g9_class_code && cr.course_type == classType && tw_isActive){
          return cr;
        }
      });

      if (schoolClass) {
        data.eqao_g9_class_code = schoolClass.id
        data.eqao_g9_class_code_label = schoolClass.class_code
      } else if (!this.whitelabelService.isABED()) {
        data.eqao_g9_class_code_label = data.eqao_g9_class_code;
        data.eqao_g9_class_code = '';
      }
    })
    this.pageModal.closeModal();

    this.pageModal.newModal({
       type: AccountModal.IMPORT_PROGRESS,
       config: {
        useConfirmAlert: false
       },
       finish: this.importProgressFinish
    })

    this.addNewAccounts(config.importData, true);
  }

  renderStudentId(acct:any){
    if(this.isTestCenter()){
      return acct[this.renderStudentIdProp()];
    }
    else if (this.isSasnLogin){
      return acct.SASN;
    }
    else {
      return applyMask(acct[this.renderStudentIdProp()], this.whitelabelService.getSiteText('STU_ID_MASK'))
    }
  }

  renderStudentDob(acct:any){
    let val = acct[this.renderDobProp()];
    let age = -1;
    if (this.isABED()){
      val = acct.course['DateofBirth'] // todo:DB_DATA_MODEL
      if(!val) {
        // tmp tw_user_metas doesn't get populated if the student account is created from admin view
        val = acct.sdc['DateofBirth']
      }
      if(val) age = moment().diff(moment(val), 'years');
      if(age >= 18) return `${applyMask(val, this.whitelabelService.getSiteText('DOB_MASK'))} (18+)`;
    }
    return applyMask(val, this.whitelabelService.getSiteText('DOB_MASK'))
  }

  renderStudentIdProp(){
    // todo:DB_DATA_MODEL
    // todo:WHITELABEL 
    if (this.isABED()){
      return this.whitelabelService.getSiteText("studentIdProp");;
    }
    else{
      return 'eqao_student_gov_id';
    }
  }

  renderStudentIdLabel(){
    return this.lang.traWithWhitelabel('student_ident', 'student_ident');
    // EQAO: sa_students_col_oen
  }

  renderDobProp(){
    // todo:DB_DATA_MODEL
    // todo:WHITELABEL 
    if (this.isABED()){
      return 'DateofBirth';
    }
    else{
      return 'date_of_birth';
    }
  }

  

  importProgressFinish(){
    this.pageModal.closeModal();
  }

  isImportModal(cModal){
    return cModal == AccountModal.IMPORT
  }
  isExportModal(cModal){
    return cModal == AccountModal.EXPORT
  }

  async exportStudentsModal() {
    const students = this.accountsTable.getFilteredData();
    const columns = await this.getStudentExportColumns();
    let exportData = this.getExportData(students, columns);
    downloadFromExportData(exportData, columns, 'students-export', this.auth);
  }

  async exportStudentsImportSummaryModal() {
    let failedStudents: any = [];
    let columns = [];
    let unusedColumns = [];

    // Excel Column Headers & Object Key Names
    if (this.whitelabelService.isABED()) {
      columns = ["CreatedASNs", "UpdatedASNs", "UnchangedASNs", "FailedASNs"];
      unusedColumns = ["UpdatedASNs", "UnchangedASNs"];
    }

    else if (this.isSasnLogin) {
      columns = ["CreatedSASNs", "UpdatedSASNs", "NoChangesSASNs", "FailedSASNs"];
    }

    else {
      columns = ["CreatedOENs", "UpdatedOENs", "NoChangesOENs", "FailedOENs"];
    }

    if (this.whitelabelService.isABED()) {
      failedStudents = this.vrs.failed.map(st => this.parseStudentASN(st.ASN));
    }

    else if (this.isSasnLogin){
      failedStudents = this.vrs.failed.map(st => {return st.SASN});

    } else {
      failedStudents = this.vrs.failed.map(st => {return st.eqao_student_gov_id});
    }

    // Deep Clone the arrays
    let source = {};
    source[columns[0]] = this.vrs.successCreate;
    source[columns[1]] = this.vrs.successUpdate;
    source[columns[2]] = this.vrs.noChange;
    source[columns[3]] = failedStudents;
    this.removeUnneccesaryColumns(source, columns, unusedColumns); //only applicable for ABED
    source = JSON.parse(JSON.stringify(source));
    
    let longestList = 0;
    for (var key of Object.keys(source)) {
      const value = source[key];
      if (value && value.length > longestList) {
        longestList = value.length;
      }
    }
    // Find the next element in the passed array
    const getNextValue = (sourceList: any[]): string => {
      if (sourceList && sourceList.length > 0) {
        const nextValue = sourceList[sourceList.length - 1];
        sourceList.pop();
        return `${nextValue}`;
      }
      return null;
    };

    let exportData = [];
    // Make Excel data
    for(let i = 0; i < longestList; i++) {
      let ed = {};
      columns.forEach((c: string) => {
        ed[c] = getNextValue(source[c]);
      });
      exportData.push(ed);
    }
    downloadExportData(exportData, 'import-summary', this.auth);
  }

  // for ABED
  public parseStudentASN(ASN: string): string {
    // ASN - the input from the CSV - looks like "3139-5484-4"
    // output, to be stored in the DB, should be "313954844", no dashes

    const regexPattern = /-/g;
    return ASN.replace(regexPattern, "");
  }

  public removeUnneccesaryColumns(source: any, allColumns: string[], columnsToRemove: string[]): void
  {
    for (let col of columnsToRemove) {
      delete source[col];

      const foundIdx = allColumns.findIndex(each => each === col);
      if (foundIdx !== -1) {
        allColumns.splice(foundIdx, 1);
      }
    }
  }

  async exportStudentsModalStart() {
    const columns = await this.getStudentExportColumns();
    const config: IModalImport = {
      hideTable: false,
      columns,
      useConfirmAlert: false,
    }

    this.pageModal.newModal({
      type: AccountModal.EXPORT,
      config,
      confirmationCaption: this.lang.tra('btn_close'),
      isProceedOnly: true,
      finish: this.exportStudentsModalFinish
    })
  }

  exportStudentsModalFinish = (config: { importData: any[] }) => {
    this.pageModal.closeModal();
  }

  async importValidationModalStart(data: IValidationResults<IStudentAccount>) {
    const columns = await this.getStudentExportColumns();
    const formattedResults = [];
    data.results.forEach(result => {
      formattedResults.push(result.obj || {});
      formattedResults.push(result.errors || []);
    });

    const config = {
      successCreate: data.successCreate,
      successUpdate: data.successUpdate,
      noChange: data.noChange,
      results: formattedResults,
      columns,
      useConfirmAlert: false
    };
    this.pageModal.newModal({
      type: AccountModal.IMPORT_VALIDATION,
      config,
      isProceedOnly: true,
      finish: this.importValidationModalFinish
    })
  }
  importValidationModalFinish = () => {
    this.pageModal.closeModal();
  }

  private updateAccTechAndNPS(record){
    if (!EQAO_ASSESSMENTS.includes(this.currentClassFilter)) return;

    if(this.currentClassFilter===ClassFilterId.G9||this.currentClassFilter===ClassFilterId.OSSLT){
      // const otherTechfields = [
      //   "eqao_acc_assistive_tech_1_other", "eqao_acc_assistive_tech_2_kurz_dl",
      //   "eqao_acc_assistive_tech_2_kurz_ext","eqao_acc_assistive_tech_2_nvda",
      //   "eqao_acc_assistive_tech_2_voiceover","eqao_acc_assistive_tech_2_readaloud",
      //   "eqao_acc_assistive_tech_2_jaws","eqao_acc_assistive_tech_2_chromevox",
      //   "eqao_acc_assistive_tech_2_read","eqao_acc_assistive_tech_2_other",
      // ]
      const otherTechfields = [
           "eqao_acc_assistive_tech_1_other", 
      ]
      const hasOtherTech:boolean = (otherTechfields.find( field => record[field] === '1') !== undefined )
      if(record.eqao_acc_assistive_tech_1_chrome === '1'){
        record.eqao_acc_assistive_tech = '2';
      }else if(hasOtherTech) {
        record.eqao_acc_assistive_tech = '1';
      }else{
        record.eqao_acc_assistive_tech = '#';
      }
    }
    if(this.currentClassFilter===ClassFilterId.Primary||this.currentClassFilter===ClassFilterId.Junior){
      const prefixs = [ {source:"eqao_acc_assistive_tech_1_pj_reading", target:"eqao_acc_assistive_tech_pj_reading"},
                        {source: "eqao_acc_assistive_tech_1_pj_writing",target :"eqao_acc_assistive_tech_pj_writing"},
                        {source: "eqao_acc_assistive_tech_1_pj_mathematics", target:"eqao_acc_assistive_tech_pj_mathematics"}]
      prefixs.forEach(prefix =>{
        const otherTechfields = [ prefix.source+"_other" ]
        const hasOtherTech:boolean = (otherTechfields.find( field => record[field] === '1') !== undefined )
        if(record[prefix.source+"_chrome"] === '1'){
          record[prefix.target] = '2';
        }else if(hasOtherTech) {
          record[prefix.target]  = '1';
        }else{
          record[prefix.target]  = '#';
        }
      })
    }

    //NonParticipationStatus OSSLT ONLY
    record.NonParticipationStatus = '#';
    if(record.NonParticipationStatus_exempted =='1'){
      record.NonParticipationStatus = '1';
    }
    if(record.NonParticipationStatus_deferred =='1'){
      record.NonParticipationStatus = '2';
    }
    if(record.NonParticipationStatus_osslc =='1'){
      record.NonParticipationStatus = '3';
    }
  }

  private getExportData( students: Partial<IStudentAccount>, columns: IExportColumn[]): IStudentAccount[] {
    return students.map((student): Partial<IStudentAccount> => {
      //const forApi = this.g9demoService.clientToApiPayloadStudent(student);
      let entry: Partial<IStudentAccount> = {};
      columns.forEach(col => {
        const cap = col.caption;
        const prop = col.prop;
        //const inputVal = (this.currentClassFilter ==ClassFilterId.G9 || overlapVariables.indexOf(prop) > -1)? student[prop]:student['_g10_'+prop];
        let inputVal = student['_g10_'+prop]
        if(overlapVariables.indexOf(prop) > -1){
          inputVal = student[prop]
        }else{
          switch(this.currentClassFilter){
            case ClassFilterId.Primary:
              inputVal = student['_g3_'+prop]
              break;
            case ClassFilterId.Junior:
              inputVal = student['_g6_'+prop]
              break;
            case ClassFilterId.G9:
              inputVal = student[prop]
              break;    
            case ClassFilterId.OSSLT:
              inputVal = student['_g10_'+prop]
              break
            case ClassFilterId.TCN:
              inputVal = student['_tcn_'+prop]
              break;
            case ClassFilterId.TCLE:
              inputVal = student['_tcle_'+prop]
              break;
            case ClassFilterId.SCIENCES8:
              inputVal = student['_sciences8_'+prop]
              break;
            default:
              inputVal = student['_g10_'+prop]
              break
          }
        }
        let exportVal;

        if(cap === 'ClassCode' || cap === 'Grouping'){
          //modify ClassCode and Grouping
          exportVal = this.renderClassCode(inputVal);
        }
        else if(cap === 'SchMident'){
          exportVal = this.g9demoService.schoolData.foreign_id
        } 
        else if(cap === 'BrdMident'){
          exportVal = this.g9demoService.schoolDistrict.foreign_id  
        }
        else if(cap === 'MathTeacherLastName'|| cap === 'MathTeacherFirstName'|| cap === 'ClassTeacherLastName'||cap === 'ClassTeacherFirstName'){
          const namespace = this.currentClassFilter === ClassFilterId.Primary? '_g3_':this.currentClassFilter === ClassFilterId.Junior?'_g6_':this.currentClassFilter === ClassFilterId.G9?'':'_g10_'
          const classroom = this.g9demoService.classrooms.find(classroom => classroom.id == (student[namespace+"eqao_g9_class_code"]))
          var teacher_uid
          if(classroom){
            teacher_uid = this.g9demoService.classrooms.find(classroom => classroom.id == student[namespace+"eqao_g9_class_code"]).teacher_uid;
          }  
          if(teacher_uid){
            const teacher = this.g9demoService.teachers.map[+teacher_uid]
            if(cap === 'MathTeacherLastName' && teacher != undefined){
              exportVal = teacher.lastName;
            }
            if(cap === 'MathTeacherFirstName' && teacher != undefined){
              exportVal = teacher.firstName;
            }
            if(cap === 'ClassTeacherLastName' && teacher != undefined){
              exportVal = teacher.lastName;
            } 
            if(cap === 'ClassTeacherFirstName' && teacher != undefined){
              exportVal = teacher.firstName;
            }  
          }else{
            exportVal = '#'
          }
        }
        else if (cap === 'AccAssistiveTech'){
          const namespace = this.currentClassFilter ==ClassFilterId.G9?'':'_g10_'
          //modify AccAssistiveTech
          if(student[namespace+'eqao_acc_assistive_tech_1_chrome'] === '1'){
            exportVal = '2';
          }else if(student[namespace+'eqao_acc_assistive_tech_1_other'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_kurz_dl']==='1'||
                   student[namespace+'eqao_acc_assistive_tech_2_kurz_ext']==='1'||student[namespace+'eqao_acc_assistive_tech_2_nvda'] === '1'||
                   student[namespace+'eqao_acc_assistive_tech_2_voiceover'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_readaloud'] ==='1'||
                   student[namespace+'eqao_acc_assistive_tech_2_jaws'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_chromevox'] === '1'||
                   student[namespace+'eqao_acc_assistive_tech_2_read'] === '1' || student[namespace+'eqao_acc_assistive_tech_2_other'] === '1'||
                   student[namespace+'eqao_acc_assistive_tech_3_chrome_2'] === '1'){
            exportVal = '1';
          }else{
            exportVal = '#';
          }
        } else if(cap === 'NonParticipationStatus'){
          //modify NonParticipationStatus
          if(student['_g10_NonParticipationStatus_exempted'] === '1'){
            exportVal = '1';
          }else if(student['_g10_NonParticipationStatus_deferred'] === '1'){
            exportVal = '2';
          }else if(student['_g10_NonParticipationStatus_osslc'] === '1'){
            exportVal = '3';
          }else{
            exportVal = '#'
          }
        }
        else {
          exportVal = inputVal;
        }
        //modify unassign value
        if(exportVal == undefined || exportVal == null||exportVal == ''){
          exportVal = '#'
        }

        if(prop!=""){
          entry[prop] = exportVal;
        }else{
          entry[cap] = exportVal;
        }  
      });
      return entry;
    });
  }

  initStudentInfoSignoffs(){

  }

  private isNullUndefinePound(data):boolean{
    return (data === null || data === undefined || data ==='' || data ==='#')
  }

  private async getExportColumns(form_type: string, class_type: string) {
    return await this.auth.apiFind(this.routes.SCHOOL_ADMIN_STUDENT_CREATION_FORM, {
      query: {
        schl_group_id: this.g9demoService.schoolData.group_id,
        form_type: form_type,
        class_type: class_type
      }
    }).then((result) => {
      const sortedColumns = result.sort((a, b) => {
        const colA = JSON.parse(a.config);
        const colB = JSON.parse(b.config);
        return colA.order - colB.order;
      });

      const columnLabels = sortedColumns.map((column) => {
        if(this.isABED()){ //this should be used for all systems, once they unify the lables in the sitetext context area
          const config = JSON.parse(column.config);
          const siteTextLabel = this.whitelabelService.getSiteText(config.label);
          return this.lang.tra(siteTextLabel ? siteTextLabel : config.label);
        }else{
          return column.form_key;
        }
      });

      return columnLabels;
    });
  }

  private getStudentExportColumns = async (): Promise<IExportColumn[]> => {


// config





    let exportColumnsList = [];
    switch(this.currentClassFilter)
    {
      case ClassFilterId.Primary:
        exportColumnsList = await this.getExportColumns("import_export", "eqao_pj");
        break;
      case ClassFilterId.Junior:
        exportColumnsList = await this.getExportColumns("import_export", "eqao_pj");
        break;
      case ClassFilterId.G9:
        exportColumnsList = await this.getExportColumns("import_export", "eqao_g9");
        break;
      case ClassFilterId.OSSLT:
        exportColumnsList = await this.getExportColumns("import_export", "eqao_osslt");
        break;        
      default:
        exportColumnsList = await this.getExportColumns("import_export", 
        this.whitelabelService.getWhitelabelFlag().toLocaleLowerCase());
        break;
    }
    
    let columns: IExportColumn[] = [];
    
    let modifiedLabels:{[key:string]:any} = {
      'FirstName': 'first_name',
      'MiddleName': 'middle_name',
      'LastName': 'last_name',
    };
    if (NBED_ASSESSMENTS.includes(this.currentClassFilter)) {
      modifiedLabels["ClassCode"] = 'class_code';
      modifiedLabels["Username"] = 'NBED_UserId';
    }
    if (this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
      modifiedLabels["ClassCode"] = 'class_code'
    }
    if (this.currentClassFilter === ClassFilterId.OSSLT) {
      modifiedLabels["Grouping"] = 'class_code'
      const RemovedColums = ["ClassCode","FrenchImmersionOrExtended","MathTeacherFirstName","MathTeacherLastName","LearningFormat_G9"]
      RemovedColums.forEach(colums => {
        const index = exportColumnsList.indexOf(colums);
        if(index!= -1){
          exportColumnsList.splice(index,1)
        }  
      })
    }
    if (this.currentClassFilter === ClassFilterId.G9) {
      modifiedLabels["ClassCode"] = 'class_code'
      const RemovedColums = ["AccOther", "DateOfFTE", "EligibilityStatus", "Graduating", "Grouping", 
      "Homeroom", "LevelofStudyLanguage", "NonParticipationStatus", "SpecPermIEP", "LearningFormat_G10"]
      RemovedColums.forEach(colums => {
        const index = exportColumnsList.indexOf(colums);
        if(index!= -1){
          exportColumnsList.splice(index,1)
        }  
      })
      if(this.lang.c() == 'fr'){
        const index = exportColumnsList.indexOf('FrenchImmersionOrExtended');
          if(index!= -1){
            exportColumnsList.splice(index,1)
          }  
      }
    }

    exportColumnsList.forEach(caption => {
      let source = modifiedLabels[caption] || caption;
      let target = caption;
      
      if (!this.whitelabelService.isABED())
      {
        target = this.g9demoService.getAPITargetMapping(source)?this.g9demoService.getAPITargetMapping(source):caption;
      }
   
      columns.push({
        prop: target,
        caption: caption,
        isClickable: false
      });
    });
    return columns;
  }

  public apiErrMsgToSlug(errMsg:string):string{
    //FirstName
    if(errMsg =='FirstName_NOT_NULL'){
      if(this.isPimary()||this.isJunior()){
        return 'brc_pj_firstname'
      }
      return 'brc_firstname';
    }
    if(errMsg =='FirstName_CHAR_LEN_RANGE'){
      return 'brc_firstname_char_len_range';
    }
    //LastName
    if(errMsg =='LastName_NOT_NULL'){
      if(this.isPimary()||this.isJunior()){
        return 'brc_pj_lastname'
      }
      return 'brc_lastname';
    } 
    if(errMsg =='LastName_CHAR_LEN_RANGE'){
      return 'brc_lastname_char_len_range';
    }

    //Student Type
    if(errMsg =='StudentType_VAL_VALIDATE'){
      return 'brc_studenttype_val_invalid_1';
    }

    //StudentOEN
    if(errMsg =='StudentOEN_NOT_NULL'||errMsg =='StudentOEN_CHAR_LEN_RANGE'||errMsg =='StudentOEN_CHAR_PATTERN'
       ||errMsg =='StudentOEN_VAL_RANGE'||errMsg =='StudentOEN_OEN_UNIQUE1'){
      return 'brc_student_oen_1_ABED';
    }
    if(errMsg =='StudentOEN_OEN_UNIQUE2'){
      return 'brc_student_oen_2';
    }
    if(errMsg =='StudentOEN_VAL_RANGE2'){
      return 'brc_student_oen_3';
    }

    //UserId (username)
    if (errMsg == 'UserId_UNIQUE') {
      return 'brc_username_unique';
    }

    //StudentIdentificationNumber
    if (errMsg == 'Student_Identification_Number_NOT_NULL') {
      return 'brc_student_identification_number_null';
    }
    
    if (errMsg == 'Student_Identification_Number_CHAR_LEN_RANGE') {
      if (this.whitelabelService.isABED()) return 'brc_student_identification_number_char_len_range_abed';
      
      return 'brc_student_identification_number_char_len_range';
    }

    if (errMsg == 'Student_Identification_Number_UNIQUE') {
      return 'brc_student_identification_number_unique';
    }

    //StudentGrade
    if (errMsg == 'StudentGrade_NOT_NULL') {
      return "brc_student_grade_null";
    }

    if(errMsg == 'StudentGrade_VAL_RANGE') {
      return "brc_student_grade_val_range";
    }

    //SASN
    if(errMsg =='SASN_NOT_NULL'){
      return 'brc_student_sasn_1';
    }
    if(errMsg =='SASN_VAL_RANGE'){
      return 'brc_sasn';
    }
    if(errMsg =='SASN_UNIQUE'){
      return 'brc_sasn_unique';
    }

    //DateofBirth                                                                        
    if(errMsg =='DateofBirth_NOT_NULL'||errMsg =='DateofBirth_CHAR_LEN_RANGE'||errMsg =='DateofBirth_DATE_VALIDATE'){
      return 'brc_dateofbirth_1';
    }
    if(errMsg =='DateofBirth_DATE_DIFF_GREATER'){
      return 'brc_dateofbirth_2';
    }
    if(errMsg =='DateofBirth_DATE_DIFF_SMALLER'){
      return 'brc_dateofbirth_3';
    }
    if (errMsg == 'DateofBirth_DATE_DIFF_GREATER_ABED') {
      return 'brc_dateofbirth_2_abed';
    }

    if (errMsg == 'DateofBirth_DATE_DIFF_SMALLER_ABED') {
      return 'brc_dateofbirth_3_abed';
    }

    if(errMsg =='DateofBirth_DATE_DIFF_GREATER_PJ'){
      return 'brc_dateofbirth_4';
    }
    if(errMsg =='DateofBirth_DATE_DIFF_SMALLER_PJ'){
      return 'brc_dateofbirth_5';
    }

    //Gender
    if(errMsg =='Gender_NOT_NULL'||errMsg =='Gender_CHAR_LEN_RANGE'||errMsg =='Gender_VAL_RANGE'){
      return 'brc_gender';
    }

    //ClassCode
    if(errMsg =='ClassCode_NOT_NULL'||errMsg =='ClassCode_VALIDE_CLASS'){
      if (this.whitelabelService.isABED()) return 'brc_classcode_abed';

      return 'brc_classcode';
    }
    if(errMsg =='ClassCode_VALIDE_CLASS_2'){
      return 'brc_classcode_3';
    }
    if(errMsg =='ClassCode_CHAR_LEN_RANGE'){
      return 'brc_classcode_2';
    }
    
    //TermFormat
    if(errMsg =='TermFormat_VAL_1'){
      return 'brc_mathclasswhen_2';
    }

    //LearningFormat
    if(errMsg =='LearningFormat_NOT_NULL'||errMsg =='LearningFormat_CHAR_LEN_RANGE'||errMsg =='LearningFormat_VAL_RANGE'){
      return 'brc_learningformat';
    }
    //Grouping
    if(errMsg =='Grouping_NOT_NULL'||errMsg =='Grouping_VALIDE_GRPING'){
      return 'brc_grouping';
    }
    if(errMsg =='Grouping_VALIDE_GRPING_2'){
      return 'brc_grouping_3';
    }
    if(errMsg =='Grouping_CHAR_LEN_RANGE'){
      return 'brc_grouping_2';
    }
    //Homeroom
    if(errMsg =='Homeroom_NOT_NULL'||errMsg =='Homeroom_VALIDE_HOMEROOM'){
      return 'brc_homeroom';
    }
    if(errMsg =='Homeroom_CHAR_LEN_RANGE'){
      return 'brc_homeroom_char_len_range';
    }
    
    //Grade
    if(errMsg =='Grade3_VAL_RANGE'||errMsg =='Grade6_VAL_RANGE') {
      return 'brc_grade_value';
    }

    //DateEnteredSchool
    if(errMsg =='DateEnteredSchool_CHAR_LEN_RANGE'||errMsg =='DateEnteredSchool_DATE_VALIDATE'){
      return 'brc_dateenteredschool_1';
    }
    if(errMsg =='DateEnteredSchool_DATE_DIFF_SMALLER'){
      return 'brc_dateenteredschool_2';
    }
    if(errMsg =='DateEnteredSchool_DATE_DIFF_SMALLER2'){
      return 'brc_dateenteredschool_3';
    }
    if(errMsg == 'DateEnteredSchool_DATE_SMALLER_THAN_BIRTHDAY'){
      return 'brc_dateenteredschool_4';
    }

    //DateEnteredBoard
    if(errMsg =='DateEnteredBoard_CHAR_LEN_RANGE'||errMsg =='DateEnteredBoard_DATE_VALIDATE'){
      return 'brc_dateenteredboard_1';
    }
    if(errMsg =='DateEnteredBoard_DATE_DIFF_SMALLER'){
      return 'brc_dateenteredboard_2';
    }
    if(errMsg =='DateEnteredBoard_DATE_GREATER'){
      return 'brc_dateenteredboard_3';
    }
    if(errMsg =='DateEnteredBoard_DATE_DIFF_SMALLER2'){
      return 'brc_dateenteredboard_4';
    }
    if(errMsg == 'DateEnteredBoard_DATE_SMALLER_THAN_BIRTHDAY'){
      return 'brc_dateenteredboard_5';
    }

    //EligibilityStatus
    if(errMsg =='EligibilityStatus_NOT_NULL'||errMsg =='EligibilityStatus_CHAR_LEN_RANGE'||errMsg =='EligibilityStatus_VAL_RANGE'){
      return 'brc_eligibilitystatus_1';
    }
    if(errMsg =='EligibilityStatus_VAL_VALIDATE'){
      return 'brc_eligibilitystatus_2';
    }
    //LevelofStudyLanguage
    if(errMsg =='LevelofStudyLanguage_VAL_VALIDATE'){
      return 'brc_levelofstudylanguage';
    }
    if(errMsg =='LevelofStudyLanguage_VAL_VALIDATE_2'){
      return 'brc_levelofstudylanguage_2';
    }
    //DateOfFTE
    if(errMsg =='DateOfFTE_VAL_VALIDATE'){
      return 'brc_dateoffte';
    }
    if(errMsg =='DateOfFTE_VAL_VALIDATE_2'){
      return 'brc_dateoffte_2';
    }
    //Graduation
    if(errMsg =='Graduating_VAL_VALIDATE'){
      return 'brc_grad_eligstatus';
    }
    //AccBraille
    if(errMsg =='AccBraille_VAL_RANGE_2' || errMsg =='AccBraille_CHAR_LEN_RANGE' || errMsg =='AccBraille_VAL_RANGE'){
      return 'brc_braille_invalid';
    }
    //AccScribing_VAL_VALIDATE
    if(errMsg =='AccScribing_VAL_RANGE'){
      return 'brc_scribing_invalid';
    }
    //IEP
    if(errMsg =='IEP_VAL_VALIDATE'){
      return 'brc_iep_1';
    }
    if(errMsg =='IEP_VAL_VALIDATE_2'){
      return 'brc_iep_2';
    }
    if(errMsg =='IEP_VAL_VALIDATE_3'){
      return 'brc_iep_3';
    }
    if(errMsg =='IEP_VAL_VALIDATE_4'){
      return 'brc_iep_4';
    }
    //AccBraille AccBreaks AccSign AccAudioResponse AccVideotapeResponse AccScribing AccOther
    if(errMsg =='AccBraille_VAL_VALIDATE'||errMsg =='AccAudioVersion_VAL_VALIDATE'||errMsg =='AccBreaks_VAL_VALIDATE'||errMsg =='AccSign_VAL_VALIDATE'||
       errMsg =='AccAudioResponse_VAL_VALIDATE'||errMsg =='AccVideotapeResponse_VAL_VALIDATE'||errMsg =='AccScribing_VAL_VALIDATE'||
       errMsg =='AccOther_VAL_VALIDATE' || errMsg == 'AccAssistiveTech_VAL_VALIDATE'){
      return 'brc_accbraille';
    }
    //PJ AccAssistiveTech_VAL_VALIDATE, AccBraille_VAL_VALIDATE_PJ, AccAudioVersion_VAL_VALIDATE_PJ, AccSign_VAL_VALIDATE_PJ, AccScribing_VAL_VALIDATE_PJ
    if(errMsg =='AccAssistiveTech_VAL_VALIDATE_PJ'||errMsg =='AccBraille_VAL_VALIDATE_PJ'||errMsg =='AccAudioVersion_VAL_VALIDATE_PJ'||
       errMsg =='AccSign_VAL_VALIDATE_PJ'||errMsg =='AccScribing_VAL_VALIDATE_PJ'){
      return 'brc_accbraille_pj';
    }
    //AccScribing_VAL_VALIDATE_2
    if(errMsg =='AccBraille_VAL_VALIDATE_2'){
      return 'brc_accbraille_2';
    }
    //AccAudioVersion_VAL_VALIDATE_2
    if(errMsg =='AccAudioVersion_VAL_VALIDATE_2'){
      return 'brc_accaudioversion_2';
    }

    //Exemption_VAL_VALIDATE
    if(errMsg =='Exemption_VAL_VALIDATE'){
      return 'brc_exemption';
    }

    //Exemption_VAL_VALIDATE_2
    if(errMsg =='Exemption_VAL_VALIDATE_2'){
      return 'brc_exemption_2';
    }
    //Exemption_VAL_VALIDATE_3
    if(errMsg =='Exemption_VAL_VALIDATE_3'){
      return 'brc_exemption_3';
    }

    //SpecEdNoExpectationReadWrite
    if(errMsg =='SpecEdNoExpectationReadWrite_VAL_VALIDATE'){
      return 'brc_spec_no_expectation_readwrite';
    }

    //SpecEdNoExpectationMath
    if(errMsg =='SpecEdNoExpectationMath_VAL_VALIDATE'){
      return 'brc_spec_no_expectation_math';
    }

    //SpecPermIEP SpecPermTemp SpecPermMoved
    if(errMsg =='SpecPermIEP_VAL_VALIDATE'||errMsg =='SpecPermTemp_VAL_VALIDATE'||errMsg =='SpecPermMoved_VAL_VALIDATE'){
      return 'brc_specpermiep';
    }
    //SpecProvBreaks
    if(errMsg =='SpecProvBreaks_VAL_VALIDATE'){
      return 'brc_specprovbreaks';
    }
    //NonParticipationStatus
    if(errMsg =='NonParticipationStatus_VAL_VALIDATE'){
      return 'brc_nonparticipationstatus_1';
    }
    if(errMsg =='NonParticipationStatus_VAL_VALIDATE_2'){
      return 'brc_nonparticipationstatus_2';
    }
    if(errMsg =='NonParticipationStatus_VAL_VALIDATE_3'){
      return 'brc_nonparticipationstatus_3';
    }
     //
    if(errMsg =='NonParticipationStatus_VAL_VALIDATE_4'){
      return 'brc_exempt_iep';
    }
    
    if(errMsg.includes('NOT_NULL')||errMsg.includes('CHAR_LEN_RANGE')||errMsg.includes('VAL_RANGE')){
      var FieldName = errMsg.split('_')[0]; 
      return this.lang.tra('brc_char_len_range',this.lang.c(),{FieldName});
    }

    return errMsg;
  } 

  private setupLinear(account){
    if (!EQAO_ASSESSMENTS.includes(this.currentClassFilter)) return;

    if(account.eqao_alternate_form_pj_mathematics == '2'){
      account['eqao_dyn_linear'] = '2'
    }
    else if(account.eqao_acc_braille == '1'||account.eqao_acc_braille == '2'||account.eqao_acc_braille == '3'||account.eqao_acc_braille == '4'
      ||account.eqao_acc_braille_pj_reading == '1'||account.eqao_acc_braille_pj_reading == '2'||account.eqao_acc_braille_pj_reading == '3'||account.eqao_acc_braille_pj_reading == '4'
      ||account.eqao_acc_braille_pj_writing == '1'||account.eqao_acc_braille_pj_writing == '2'||account.eqao_acc_braille_pj_writing == '3'||account.eqao_acc_braille_pj_writing == '4'
      ||account.eqao_acc_braille_pj_mathematics == '1'||account.eqao_acc_braille_pj_mathematics == '2'||account.eqao_acc_braille_pj_mathematics == '3'||account.eqao_acc_braille_pj_mathematics == '4'
      ||account.AccAudioVersion == '1'||account.eqao_alternate_form_pj_reading == '1'||account.eqao_alternate_form_pj_writing == '1'||account.eqao_alternate_form_pj_mathematics == '1'
      ||account.osslt_alternative_version_test == '1'
      ){
      account['eqao_dyn_linear'] = '1'
    }
    else{
      account['eqao_dyn_linear'] = '#'
    }
  }

  private clientRuleCheck(record){
    if(record.eqao_acc_assistive_tech_2_other === '1'){
      if(record.eqao_acc_assistive_tech_custom === undefined ||  record.eqao_acc_assistive_tech_custom.trim().length < 1){
        setTimeout(() => this.loginGuard.quickPopup('sa_oat_string_empty'), 0);
        return false;
      }
    }
    return true;
  }

  private modifyStudentData(record, isBulk = true){
    //***Modify Data Base on the Business Rule *******************************/
    /* 
     Re- set Unified English Braille Value
    */

    if(record.first_name){
      record.first_name = record.first_name.trim();
    }

    if(record.last_name){
      record.last_name = record.last_name.trim();
    }

    if (!EQAO_ASSESSMENTS.includes(this.currentClassFilter)) return;

    if (this.lang.c() === 'fr' && (record.eqao_acc_braille === '3' || record.eqao_acc_braille === '4')){
      record.eqao_acc_braille = '#';
    }
    if (this.lang.c() === 'en' && (record.eqao_acc_braille === '1' || record.eqao_acc_braille === '2')){
      record.eqao_acc_braille = '#';
    }

    /*
    OutOfProvinceResidence field value must be “1” if the value in the field StatusInCanada is “4," "5" or "6."
    */
    let warnMsgs: string = '';
    const StatusInCanada = record.eqao_status_in_canada
    if (StatusInCanada =='4'||StatusInCanada == '5'||StatusInCanada == '6'){
      record.eqao_out_province_residence = '1';
    }
    /*
    If BornOutsideCanada = 1 (Yes), did this student's family come to Canada as refugees? 
    */
    const Refugee = record.eqao_refugee
    if(Refugee == '1'){
      record.eqao_born_outside_canada = '1';
    }
    /*
    If “BornOutsideCanada” = # (missing/unknown), then “TimeInCanada” must be # (missing/unknown/not applicable).
    If “BornOutsideCanada” = 1 (Yes), then “TimeInCanada” must be 
        1 (less than one year),
        2 (one year or more but less than two years),
        3 (two years or more but less than three years),
        4 (three years or more but less than five years),
        5 (five years or more), 
        6. or # (missing/unknown/not applicable).
    If “BornOutsideCanada” = 2 (No), then “TimeInCanada” must be # (missing/unknown/not applicable).
    */
    let BornOutsideCanada = record.eqao_born_outside_canada;
    if (BornOutsideCanada == undefined || BornOutsideCanada =='' || BornOutsideCanada=='#'||BornOutsideCanada == '2'){
      record.eqao_time_in_canada = '#';
    }
    /*
    Student cannot have special provisions and be exempted, deferred or OSSLC. Only the non-participation status will be saved.
    Student cannot be accommodated and exempted, deferred or OSSLC. Only the non-participation status will be saved.
    Student cannot have a special consideration and be exempted, deferred or OSSLC. Only the non-participation status will be saved.
    */
    const NonParticipationStatus_exempted = record.NonParticipationStatus_exempted;
    const NonParticipationStatus_deferred = record.NonParticipationStatus_deferred;
    const NonParticipationStatus_osslc = record.NonParticipationStatus_osslc;
    const NonParticipationStatus = (NonParticipationStatus_exempted == '1'|| NonParticipationStatus_deferred == '1'||NonParticipationStatus_osslc == '1')
    if (NonParticipationStatus){
      if(record._periodic_breaks == '1'){
        warnMsgs += (this.lang.tra('brc_nonparticipationstatus_3'))
        warnMsgs += '\n';
      }

      if(record.eqao_acc_assistive_tech_1_chrome == '1'|| record.eqao_acc_assistive_tech_1_other == '1'||record.eqao_acc_assistive_tech_2_kurz_dl == '1'|| record.eqao_acc_assistive_tech_2_kurz_ext == '1'
         ||record.eqao_acc_assistive_tech_2_nvda == '1'||record.eqao_acc_assistive_tech_2_voiceover == '1'||record.eqao_acc_assistive_tech_2_readaloud == '1'
         ||record.eqao_acc_assistive_tech_2_jaws== '1'||record.eqao_acc_assistive_tech_2_chromevox == '1'||record.eqao_acc_assistive_tech_2_read == '1'
         ||record.eqao_acc_assistive_tech_2_other == '1'||record.eqao_acc_assistive_tech_3_chrome_2 == '1'||record.eqao_acc_braille == '3'||record.eqao_acc_braille == '4'
         ||record.AccAudioVersion == '1'||record._extended_time == '1'||record.eqao_pres_format == '1'||record._audio_recording_of_resp == '1'||record.AccVideotapeResponse == '1'
         ||record._audio_recording_of_resp == '1'||record.eqao_acc_scribing == '1'||record.AccOther == '1'||record.eqao_acc_braille == '1'||record.eqao_acc_braille == '2'){
        warnMsgs += (this.lang.tra('brc_nonparticipationstatus_1'))
        warnMsgs += '\n';
      }

      if(record.SpecPermIEP == '1' || record._no_iep_temp_inj == '1'||record._no_iep_recent_arriv == '1' ){  //SpecPermMoved
        warnMsgs += (this.lang.tra('brc_nonparticipationstatus_2'))
        warnMsgs += '\n';
      }

      //record._ell = '#';  //'ESLELD'
      //record.ALFPANA = '#';  
      record._periodic_breaks = '#'; //SpecProvBreaks   
      record.eqao_acc_assistive_tech_1_chrome = '#';
      record.eqao_acc_assistive_tech_1_other = '#';
      record.eqao_acc_assistive_tech_2_kurz_dl = '#';
      record.eqao_acc_assistive_tech_2_kurz_ext = '#';
      record.eqao_acc_assistive_tech_2_nvda = '#';
      record.eqao_acc_assistive_tech_2_voiceover = '#';
      record.eqao_acc_assistive_tech_2_readaloud = '#';
      record.eqao_acc_assistive_tech_2_jaws = '#';
      record.eqao_acc_assistive_tech_2_chromevox = '#';
      record.eqao_acc_assistive_tech_2_read = '#';
      record.eqao_acc_assistive_tech_2_other = '#';
      record.eqao_acc_assistive_tech_3_chrome_2 = '#';
      record.eqao_acc_braille = '#';
      record._audio_recording_of_resp = '#';
      record.AccAudioVersion = '#';
      record._extended_time = '#'; //AccBreaks
      record.eqao_pres_format = '#'; //AccSign
      record._audio_recording_of_resp = '#'; //AccAudioResponse
      record.AccVideotapeResponse = '#'; //AccVideotapeResponse
      record.eqao_acc_scribing = '#'; //AccScribing
      record.AccOther = '#'; //AccOther
      record.SpecPermIEP = '#'; //SpecPermIEP
      record._no_iep_temp_inj = '#'; //SpecPermTemp
      record._no_iep_recent_arriv = '#';  //SpecPermMoved
    }
    if(warnMsgs.length>0 && !isBulk){
      // setTimeout(() => this.loginGuard.quickPopup(warnMsgs), 0);
      window.confirm(warnMsgs);
    }
    //***End of Modify Data Base on the Business Rule *******************************/
  }

  isConfirmAlert(): boolean {
    const defaultConfirmAlert = true;
    let confirmAlert: boolean;
    if (typeof this.cmc().useConfirmAlert === 'boolean') {
      confirmAlert = this.cmc().useConfirmAlert;
    } else {
      confirmAlert = defaultConfirmAlert;
    }
    return confirmAlert;
  }

  isOdd(i): boolean {
    return i % 2 !== 0;
  }

  pageChanged() {
    if (!this.isAllSelected) {
      this.students.forEach(student => student.__isSelected = false);
    }
  }
  
  isBCSite(): boolean {
    return this.whitelabelService.getSiteFlag('BC_STUDENT') 
  }

  totolErrorStudentRecords():number{
    const errMsgKey = this.currentClassFilter == ClassFilterId.Primary?"_g3_errMsg":this.currentClassFilter == ClassFilterId.Junior?"_g6_errMsg":this.currentClassFilter == ClassFilterId.G9?"errMsg":"_g10_errMsg";
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary?"SDC_conflict_g3":this.currentClassFilter == ClassFilterId.Junior?"SDC_conflict_g6":this.currentClassFilter == ClassFilterId.G9?"SDC_conflict_g9":"SDC_conflict_g10";
    const studentCourse = this.currentClassFilter == ClassFilterId.Primary?"eqao_is_g3":this.currentClassFilter == ClassFilterId.Junior?"eqao_is_g6":this.currentClassFilter === ClassFilterId.G9? "eqao_is_g9":"eqao_is_g10"
    if(this.currentTestWindow){
      return this.students.filter((student) => 
        student[studentCourse] =='1' && this.filterStudentTestWindow(student) && ((student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0)||student[conflictKey]!= undefined)
      ).length
    } 
    return 0;

    // return this.accountsTable.getEntries().filter((student) =>  
    //   (student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0)||student[conflictKey]!= undefined
    // ).length;

  }

  numberOfInvalude():number{
    /*
    const errMsgKey = this.currentClassFilter == ClassFilterId.G9?"errMsg":"_g10_errMsg";
    const totalInvalid = this.accountsTable.getEntries().filter((student) => { 
      (student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0)
    }).length;
    return totalInvalid - this.numberOfDiscrepancy();
    */
   return this.totolErrorStudentRecords() - this.numberOfDiscrepancy()
  }
  numberOfDiscrepancy():number{
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary?"SDC_conflict_g3":this.currentClassFilter == ClassFilterId.Junior?"SDC_conflict_g6":this.currentClassFilter == ClassFilterId.G9?"SDC_conflict_g9":"SDC_conflict_g10";
    if(this.currentTestWindow){
      return this.students.filter((student) => 
        this.filterStudentTestWindow(student) && (student[conflictKey]!= undefined)
      ).length
    } 
    return 0;
    //return this.accountsTable.getEntries().filter((student) => (student[conflictKey]!= undefined)).length;
  }

  filterFlaggedItems(student: IStudentAccount){
    const errMsgKey = this.currentClassFilter==ClassFilterId.Primary? "_g3_errMsg" :this.currentClassFilter == ClassFilterId.Junior ? "_g6_errMsg" :this.currentClassFilter == ClassFilterId.G9 ? "errMsg" : "_g10_errMsg";
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary ? "SDC_conflict_g3" :this.currentClassFilter == ClassFilterId.Junior ? "SDC_conflict_g6" :this.currentClassFilter == ClassFilterId.G9 ? "SDC_conflict_g9" : "SDC_conflict_g10";
    if(this.showFlaggedItems){
      if(this.showFlaggedItems === FlaggedItemCase.ALL_FLAG) {
        if ((student[errMsgKey] && JSON.parse(student[errMsgKey]).length > 0) || student[conflictKey]!= undefined){
          return true;
        }
      }
      if(this.showFlaggedItems === FlaggedItemCase.IMPORT_FLAG) {
        if (student[conflictKey] && student[conflictKey].IsImportUpdate == 1){
          return true;
        }
      }
    }
    return false;
  }

  setShowFlaggedItems(input:FlaggedItemCase){
    this.showFlaggedItems = input;
    if(input != FlaggedItemCase.ALL){
      this.accountsTable.activeFilters['showFlaggedItems'] = { mode: FilterSettingMode.VALUE, config: { value: '#' } }
    } else {
      this.accountsTable.activeFilters['showFlaggedItems'] = null;
    } 
    this.accountsTable.refreshFilters();
  }

  async confirmDatamodify(){
    if(this.totolErrorStudentRecords() > 0){
      this.loginGuard.quickPopup(this.lang.tra('ab_confirm_alert'))
    }
    else { 
      await this.mySchool.signoffStuAsmt(this.currentClassFilter);
    }
  }

  deadlineCompleteValidate(){
    //TODO
    return '';
  }

  isYellowAlertItem(student){
    if(this.currentClassFilter == ClassFilterId.Primary && student.SDC_conflict_g3 !=  undefined ){
      return true
    }
    if(this.currentClassFilter == ClassFilterId.Junior && student.SDC_conflict_g6 !=  undefined ){
      return true
    }
   if(this.currentClassFilter == ClassFilterId.G9 && student.SDC_conflict_g9 !=  undefined ){
     return true
   }
   if(this.currentClassFilter == ClassFilterId.OSSLT && student.SDC_conflict_g10 !=  undefined){
     return true
   }
   return false;
  }

  isRedAlertItem(student){
    const a = this.currentClassFilter == ClassFilterId.Primary;
    const b = student._g3_errMsg
    if(student._g3_errMsg) {const c = JSON.parse(student._g3_errMsg)}
    if(this.currentClassFilter == ClassFilterId.Primary && student._g3_errMsg && JSON.parse(student._g3_errMsg).length > 0){
      return true;
    }
    if(this.currentClassFilter == ClassFilterId.Junior && student._g6_errMsg && JSON.parse(student._g6_errMsg).length > 0){
      return true;
    }
    if(this.currentClassFilter == ClassFilterId.G9 && student.errMsg && JSON.parse(student.errMsg).length > 0){
      return true;
    }
    if(this.currentClassFilter == ClassFilterId.OSSLT && student._g10_errMsg && JSON.parse(student._g10_errMsg).length > 0){
      return true;
    }
    return false; 
  }

  

  confirmStudentInformation(){
    this.loginGuard.confirmationReqActivate({
      caption: this.whitelabelService.isABED() ? this.lang.tra('abed_save_confirm') : this.lang.tra('alert_kk_confirm_save'),
      confirm: () => this.pageModal.confirmModal(false)
    })
  }

  preRecord(){
    if(this.selectedStudentIndex > 0){
      this.pageModal.closeModal();
      this.selectedStudentIndex--;
      const student  = this.accountsTable.getEntries()[this.selectedStudentIndex]
      this.accountDetailModalStart(student)
    }
  }

  nextRecord(){
    if(this.selectedStudentIndex < this.accountsTable.numEntries()-1){
      this.pageModal.closeModal();
      this.selectedStudentIndex ++;
      const student  = this.accountsTable.getEntries()[this.selectedStudentIndex]
      this.accountDetailModalStart(student)
    }
  }
  clickApplyBtn(event: {studentRecord:any, choosedConflicts:any}){
    const config = this.pageModal.currentModal.config;
    const record = config.payload.record;
    const data = config.payload.data;
    const conflictKey = this.currentClassFilter == ClassFilterId.Primary ? "SDC_conflict_g3" :this.currentClassFilter == ClassFilterId.Junior ? "SDC_conflict_g6" :this.currentClassFilter == ClassFilterId.G9 ? "SDC_conflict_g9" : "SDC_conflict_g10";
    const courseType = this.currentClassFilter == ClassFilterId.Primary ? 'EQAO_G3' :this.currentClassFilter == ClassFilterId.Junior ? 'EQAO_G6' :this.currentClassFilter == ClassFilterId.G9 ? 'EQAO_G9' : 'EQAO_G10';
    var uid = record.id
    var user_metas_import_disc_id = record[conflictKey].id
    event.choosedConflicts.forEach(conflict => {
      if(conflict.fieldname =='FirstName'){
        data.first_name = conflict.value
      }
      else if(conflict.fieldname =='LastName'){
        data.last_name = conflict.value
      }
      else if(conflict.fieldname == 'ClassCode' || conflict.fieldname == 'Grouping'){
        let class_id;
        if(conflict.fieldname == 'ClassCode') {
          const theClass = this.g9demoService.classrooms.find (cr => {
            const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
            const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
            const tw_isActive = (new Date(test_window.date_end) > new Date ())
            if(cr.class_code == conflict.value && cr.course_type == courseType && tw_isActive){
              return cr;
            }
          })
          if(theClass){
            class_id = theClass.id
          }
        }
        if(conflict.fieldname == 'Grouping') {
          const theClass = this.g9demoService.classrooms.find (cr => {
            const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
            const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
            const tw_isActive = (new Date(test_window.date_end) > new Date ())
            if(cr.class_code == conflict.value && cr.course_type == 'EQAO_G10' && tw_isActive){
              return cr;
            }
          })
          if(theClass){
            class_id = theClass.id
          }
        }
        data["eqao_g9_class_code"] =  class_id ? class_id : null;
        // event.studentRecord["class_code"] =  class_id ? class_id : null;
        data["eqao_g9_class_code_label"] = conflict.value;
      } 
      else{
        const map_result = DATA_MAPPING_EQAO_G9_STUDENT.find(r=>r.source==conflict.fieldname)
        // const namespace2 = (overlapVariables.indexOf(map_result.target) > -1 || this.currentClassFilter == ClassFilterId.G9)?'':'_g10_'
        // event.studentRecord[namespace2+map_result.target] = conflict.value;
        data[map_result.target] = conflict.value;
      }
    });

    if(data.NonParticipationStatus === '1'){
      data.NonParticipationStatus_exempted = '1'
    }
    if(data.NonParticipationStatus === '2'){
      data.NonParticipationStatus_deferred = '1'
    } 
    if(data.NonParticipationStatus === '3'){
      data.NonParticipationStatus_osslc = '1'
    }
    if(data.eqao_acc_assistive_tech === '#') {
      data.eqao_acc_assistive_tech_1_chrome = '#';
      data.eqao_acc_assistive_tech_1_other = '#';
      data.eqao_acc_assistive_tech_2_kurz_dl = '#';
      data.eqao_acc_assistive_tech_2_kurz_ext = '#';
      data.eqao_acc_assistive_tech_2_nvda = '#';
      data.eqao_acc_assistive_tech_2_voiceover = '#';
      data.eqao_acc_assistive_tech_2_readaloud = '#';
      data.eqao_acc_assistive_tech_2_jaws = '#';
      data.eqao_acc_assistive_tech_2_read = '#';
      data.eqao_acc_assistive_tech_2_other = '#';
      data.eqao_acc_assistive_tech_3_chrome_2 = '#';
    }
    if(data.eqao_acc_assistive_tech === '2') {
      data.eqao_acc_assistive_tech_1_chrome = '1';
    }
    if(data.eqao_acc_assistive_tech === '1') {
      data.eqao_acc_assistive_tech_1_chrome = '#';
      data.eqao_acc_assistive_tech_1_other  = '1';
    }
    if(data.eqao_acc_assistive_tech_pj_reading === '2') {
      data.eqao_acc_assistive_tech_1_pj_reading_chrome = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_reading === '1') {
      data.eqao_acc_assistive_tech_1_pj_reading_other  = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_writing === '2') {
      data.eqao_acc_assistive_tech_1_pj_writing_chrome = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_writing === '1') {
      data.eqao_acc_assistive_tech_1_pj_writing_other  = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_mathematics === '2') {
      data.eqao_acc_assistive_tech_1_pj_mathematics_chrome = '1'
    }
    if(data.eqao_acc_assistive_tech_pj_mathematics === '1') {
      data.eqao_acc_assistive_tech_1_pj_mathematics_other  = '1'
    }

    this.modifyStudentData(data, false);
    this.updateAccTechAndNPS(data);
    this.setupLinear(data);
    const apiPayload:any = this.getApiData(data, true);
    apiPayload.user_metas_import_disc_id = user_metas_import_disc_id;
    this.auth.apiPatch(
      this.routes.SCHOOL_ADMIN_STUDENT,
      uid,
      apiPayload,
      this.configureQueryParams()
    ).then(result=>{
      const student  = this.accountsTable.getEntries()[this.selectedStudentIndex];
      Object.keys(data).forEach(prop => {
        const name_space = (overlapVariables.indexOf(prop) > -1 || this.currentClassFilter == ClassFilterId.G9) ? '' : this.currentClassFilter == ClassFilterId.Primary?'_g3_':this.currentClassFilter == ClassFilterId.Junior?'_g6_':'_g10_';
        student[name_space+prop] = data[prop];
      });
      const course_type = this.currentClassFilter == ClassFilterId.Primary?'EQAO_G3':this.currentClassFilter == ClassFilterId.Junior?'EQAO_G6':this.currentClassFilter == ClassFilterId.G9?'EQAO_G9':'EQAO_G10'
      const classroom = this.g9demoService.classrooms.find (cr => {
        const semester = this.g9demoService.semesters.list.find( sm => sm.id == +cr.semester)
        const test_window = this.g9demoService.testWindows.find( tw => tw.id == semester.testWindowId)
        const tw_isActive = (new Date(test_window.date_end) > new Date ())
        if(cr.class_code == data["eqao_g9_class_code_label"] && cr.course_type == course_type && tw_isActive){
          return cr;
        }
      })
      //const classroom = this.getClassroom(data.eqao_g9_class_code_label)
      if(classroom){
        if(this.currentClassFilter == ClassFilterId.Primary){
          record._g3_eqao_pj_french = classroom.is_fi?'1':'0'
        }
      }
      student[conflictKey] = undefined;
      const namespace = this.currentClassFilter == ClassFilterId.Primary?'_g3_':this.currentClassFilter == ClassFilterId.Junior?'_g6_':this.currentClassFilter == ClassFilterId.G9?'':'_g10_';
      if(result.ErrorMessages.length > 0){
        student[namespace+"errMsg"] = JSON.stringify(result.ErrorMessages)
      }else{
        student[namespace+"errMsg"] = JSON.stringify([])
      }
      this.pageModal.closeModal();
      this.accountDetailModalStart(student);
    })
  }
  getNumbersubmit(){
    const filteredStudents = this.accountsTable.getFilteredData();
    const submittedStudents = filteredStudents.filter(student => this.isSubmitted(student, '1'))
    return submittedStudents.length;
  }

  getProgress(){
    const total = this.accountsTable.numEntries();
    if(total != 0){
      return this.getNumbersubmit()*100/this.accountsTable.numEntries()
    }else{
      return 0;
    }  
  } 
  getMathClassWhenLabel(id){
    const mcw = Math_Class_When.list.find(e => e.id == id)
    if(mcw){
      return this.lang.tra(mcw.label);
    }else{
      return '';
    }
  }

  getLearningFormatLabel(acct){
    //const id :any =  this.currentClassFilter === ClassFilterId.G9?acct.eqao_learning_format:acct._g10_eqao_learning_format;
    //const Learning_Format:any  = this.currentClassFilter === ClassFilterId.G9?Learning_Format_G9:Learning_Format_OSSLT;
    let id, Learning_Format;
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        id = acct._g3_eqao_learning_format
        Learning_Format = Learning_Format_Primary
      break;
      case ClassFilterId.Junior:
        id = acct._g6_eqao_learning_format
        Learning_Format = Learning_Format_Junior
      break;  
      case ClassFilterId.G9:
        id = acct.eqao_learning_format
        Learning_Format = Learning_Format_G9
      break;
      case ClassFilterId.OSSLT:
        id = acct._g10_eqao_learning_format
        Learning_Format = Learning_Format_OSSLT
      break;
      default:
        id = acct._g3_eqao_learning_format
        Learning_Format = Learning_Format_Primary
      break; 
    }
    const lf = Learning_Format.list.find(e => e.id == id)
    if(lf){
      return this.lang.tra(lf.label);
    }else{
      return '';
    }
  }

  getTeachers(acct, isInvigilators = false){
    //const classID = this.currentClassFilter == ClassFilterId.G9?acct.eqao_g9_class_code:acct._g10_eqao_g9_class_code
    let classID
    switch(this.currentClassFilter){
      case ClassFilterId.Primary:
        classID = acct._g3_eqao_g9_class_code
      break  
      case ClassFilterId.Junior:
        classID = acct._g6_eqao_g9_class_code
      break
      case ClassFilterId.G9:
        classID = acct.eqao_g9_class_code
      break
      case ClassFilterId.OSSLT:
        classID = acct._g10_eqao_g9_class_code
      break
      default:
        classID = acct[this.getClassCodePrefix(this.currentClassFilter) + "eqao_g9_class_code"]
      break
    }
    var classroom;
    if(classID){
      classroom = this.g9demoService.classrooms.find(theClass => theClass.id == classID )
      if(isInvigilators) {
        let invigilators = '';
        const class_group_id = this.g9demoService.teacherClassrooms.map[classroom.id].group_id
        const classInvigilators = class_group_id?this.g9demoService.invigilators.filter(invig => invig.group_id == class_group_id):[]
        classInvigilators.forEach(invig => {
          const invigName = invig.first_name + " " + invig.last_name;
          invigilators = invigilators == '' ? invigName : invigilators + ', '+ invigName
        })
        return invigilators;
      }
      if(classroom.teacher_uid != undefined){
        const teacher = this.g9demoService.teachers.list.find(teacher => teacher.id == classroom.teacher_uid )
        if(teacher != undefined){
          return teacher.firstName+ ' '+teacher.lastName
        }
      }
    } 
    return ''; 
  }

  getCourseType = (classFilterId : ClassFilterId) => {
    switch(classFilterId){
      case ClassFilterId.Primary:
        return AssessmentCode.EQAO_G3
      case ClassFilterId.Junior:
        return AssessmentCode.EQAO_G6
      case ClassFilterId.G9:
        return AssessmentCode.EQAO_G9
      case ClassFilterId.TCLE:
        return AssessmentCode.NBED_TCLE
      case ClassFilterId.TCN:
        return AssessmentCode.NBED_TCN
      case ClassFilterId.SCIENCES8:
        return AssessmentCode.NBED_SCIENCES8
      case ClassFilterId.OSSLT:
        return AssessmentCode.EQAO_G10
      default:
        return this.currentClassFilter;
    }
  }

  getClassCodePrefix = (classFilterId: ClassFilterId) => {
    switch(classFilterId){
      case ClassFilterId.Primary:
        return '_g3_';
      case ClassFilterId.Junior:
        return '_g6_';
      case ClassFilterId.G9:
        return ClassFilterId.G9
      case ClassFilterId.TCLE:
        return '_tcle_'
      case ClassFilterId.TCN:
        return '_tcn_'
      case ClassFilterId.SCIENCES8:
        return '_sciences8_'
      case ClassFilterId.ABED_SAMPLE:
        return '_abed_sample_'
      case ClassFilterId.ABED_GRADE_6:
        return '_abed_grade_6_'
      case ClassFilterId.ABED_GRADE_9:
        return '_abed_grade_9_'
      case ClassFilterId.ABED_GRADE_12:
        return '_abed_grade_12_'
      case ClassFilterId.OSSLT:
      default:
        return '_g10_'
    }
  }

  
  renderClassCodeAndGroupingLabel(acct: any) {
    const renderDefault = () => {
      if (this.isNBED()){ return acct._g10_eqao_g9_class_code_label; }
      else {              return acct._g3_eqao_g9_class_code_label }
    }
    const classes = [];
    for(let grouping of acct.groupings){
      if(grouping.test_window_id == this.currentTestWindow.id){
        classes.push(grouping.classCode);
      }
    }
    return classes.join(", ");

    switch(this.currentClassFilter){
      case ClassFilterId.Primary: 
        return acct._g3_eqao_g9_class_code_label;
      case ClassFilterId.Junior: 
        return acct._g6_eqao_g9_class_code_label;
      case ClassFilterId.G9: 
        return acct.eqao_g9_class_code_label;
      case ClassFilterId.OSSLT: 
        return acct._g10_eqao_g9_class_code_label;
      case ClassFilterId.TCLE: 
        return acct._tcle_eqao_g9_class_code_label;
      case ClassFilterId.TCN: 
        return acct._tcn_eqao_g9_class_code_label;
      case ClassFilterId.SCIENCES8: 
        return acct._sciences8_eqao_g9_class_code_label;
      case ClassFilterId.ABED_SAMPLE: 
        return acct._abed_sample_eqao_g9_class_code_label;
      case ClassFilterId.ABED_GRADE_6:
        return acct._abed_grade_6_eqao_g9_class_code_label;
      case ClassFilterId.ABED_GRADE_9:
        return acct._abed_grade_9_eqao_g9_class_code_label;
      case ClassFilterId.ABED_GRADE_12:
        return acct._abed_grade_12_eqao_g9_class_code_label;
      default: return renderDefault();
    }
    // if (this.currentClassFilter === ClassFilterId.G9) return acct.eqao_g9_class_code_label;
    // else return acct._g10_eqao_g9_class_code_label;
  }

  getImportProgress(){
    return this.currentImportNumber*100/this.totalImportNumber
  }

  filterStudentByGrade(student: IStudentAccount) {
    return this.filterStudentTestWindow(student);
  }

  filterStudentTestWindow(student: IStudentAccount) {
    if(!this.currentTestWindow){
      return false;
    }
    for(let grouping of student.groupings){
      if(grouping.test_window_id == this.currentTestWindow.id){
        return true;
      }
    }
    return false;
    //rest of this code is outdated
    const allStudentClasses = this.g9demoService.getStudentByUID(student.uid).classroomIds;
    if(allStudentClasses.length > 1){
      for (let classGroupId of allStudentClasses) {
        let theClass = this.g9demoService.classrooms.find(classroom => classroom.group_id === classGroupId);
        if (theClass) {
          const semester = this.g9demoService.semesters.map[+theClass.semester];
          if (semester && this.currentTestWindow.id === semester.testWindowId) {
            if(student.uid == 237576) console.log(theClass);
            student[this.getClassCodePrefix(this.currentClassFilter) + 'eqao_g9_class_code'] = theClass.id;
            student[this.getClassCodePrefix(this.currentClassFilter) + 'eqao_g9_class_code_label'] = theClass.class_code;
            student.teacher = this.getTeachers(student);
            student.invigilators = this.getTeachers(student, true);
            return true;
          }
        }
      }
    }else{
      const classcode = student[this.getClassCodePrefix(this.currentClassFilter) + 'eqao_g9_class_code'];
      let theClass;
      if (classcode) {
        theClass = this.g9demoService.classrooms.find(classroom => classroom.id === classcode);
      }
      if(theClass){
        const semester = this.g9demoService.semesters.map[+theClass.semester];
        if(semester){
          return this.currentTestWindow.id === semester.testWindowId;
        }
      }
    }
    return false;
  }

  filterStudentClassCodeLabel(student):string
  {
    if(!this.currentTestWindow){
      return "";
    }
    const classes = [];
    for(let grouping of student.groupings){
      if(grouping.test_window_id == this.currentTestWindow.id){
        classes.push(grouping.classCode);
      }
    }
    return classes.join(", ");
    // if(this.currentClassFilter === ClassFilterId.Primary) return '_g3_eqao_g9_class_code_label';
    // if(this.currentClassFilter === ClassFilterId.Junior) return '_g6_eqao_g9_class_code_label';
    // if(this.currentClassFilter === ClassFilterId.G9) return 'eqao_g9_class_code_label';
    // if(this.currentClassFilter === ClassFilterId.OSSLT) return '_g10_eqao_g9_class_code_label';
    //  #MERGE_20220524 : refactor
    switch(this.currentClassFilter){      
      case ClassFilterId.G9:
        return 'eqao_g9_class_code_label'
      case ClassFilterId.Primary:
        return '_g3_eqao_g9_class_code_label'
      case ClassFilterId.Junior:
        return '_g6_eqao_g9_class_code_label'
      case ClassFilterId.OSSLT:
        return '_g10_eqao_g9_class_code_label'
      case ClassFilterId.TCLE:
        return '_tcle_eqao_g9_class_code_label';
      case ClassFilterId.TCN:
        return '_tcn_eqao_g9_class_code_label';
      case ClassFilterId.SCIENCES8:
        return '_sciences8_eqao_g9_class_code_label';
      case ClassFilterId.ABED_SAMPLE:
        return '_abed_sample_eqao_g9_class_code_label';
      case ClassFilterId.ABED_GRADE_6:
        return '_abed_grade_6_eqao_g9_class_code_label';
      case ClassFilterId.ABED_GRADE_9:
        return '_abed_grade_9_eqao_g9_class_code_label';
      case ClassFilterId.ABED_GRADE_12:
        return '_abed_grade_12_eqao_g9_class_code_label';
      default:
        return '_g10_eqao_g9_class_code_label'
    }
  }
  /*
  isSubmitted(acct: any){
    const slug = this.currentClassFilter === ClassFilterId.G9? 'G9_OPERATIONAL':'OSSLT_OPERATIONAL'
    const haveSubmitted = acct.testAttempts.find(ta => ta.slug === slug && (ta.submitted_test_session_id != undefined ||ta.submitted_test_session_id != null))
    if(haveSubmitted!=undefined){
      return '1'
    }
    else{
      return '#'
    }
  }
  */

  isCurrentTestWindowActive(){
    if(this.currentTestWindow){
      return new Date(this.currentTestWindow.date_end) > new Date ()
    }
    return false 
  }

  isCreateEdit(){
    return (this.cModal().type === AccountModal.NEW_ACCOUNT || this.cModal().type === AccountModal.ACCOUNT_DETAILS)
  }

  selectedRecord(student){
    this.pageModal.closeModal();
    this.accountDetailModalStart(student)
  }

  public get FlaggedItemCase():typeof FlaggedItemCase {
    return FlaggedItemCase;
  }

  isNBED()
  {
    return this.whitelabelService.getSiteFlag('IS_NBED');
  }
  
  public isPimary(){
    return this.currentClassFilter == ClassFilterId.Primary
  }

  public isJunior(){
    return this.currentClassFilter == ClassFilterId.Junior
  }  
  haveSubmission(student){
    const ta_slug = this.currentClassFilter === ClassFilterId.Primary?'PRIMARY_OPERATIONAL':this.currentClassFilter === ClassFilterId.Junior?'JUNIOR_OPERATIONAL':this.currentClassFilter === ClassFilterId.G9?'G9_OPERATIONAL':'OSSLT_OPERATIONAL';
    if(student.testAttempts){
      const haveSubmitted = student.testAttempts.find(ta => ta.slug === ta_slug && +ta.is_submitted === 1 && ta.test_window_id == this.currentTestWindow.id && +ta.is_submitted === 1)
      if(haveSubmitted){
        return '1'
      }
    }
    return '#';
  }

  isSubmitted(student,val){
    if(val.includes('All')){
      return true
    }
    const haveSubmission = this.haveSubmission(student)
    if(val.includes('1') &&  haveSubmission =='1'){
      return true
    }
    if(val.includes('#') &&  haveSubmission == '#'){
      return true
    }
    return false
  }

  getClassroomsColClassCodeSlug(){
    if(this.isNBED()){
      return 'sa_classrooms_col_class_code_nbed';
    }

    else if (this.whitelabelService.isABED())
    {
      return 'lbl_sdc_grouping_ab';
    }

    return 'sa_classrooms_col_class_code';
  }

  increaseClassStudentCount(class_id: any){
    let classRoomToUpdate = this.g9demoService.classrooms.find(classroom => classroom.id == class_id);
    if(classRoomToUpdate)
    {
      let oldCount = this.g9demoService.classrooms.find(classroom => classroom.id == class_id).students;
      if(oldCount){
        this.g9demoService.classrooms.find(classroom => classroom.id == class_id).students = oldCount + 1;
      } else {
        this.g9demoService.classrooms.find(classroom => classroom.id == class_id).students = 1;
      }
    }
  }

  decreaseClassStudentCount(classId: any){
    let classRoomToUpdate = this.g9demoService.classrooms.find(classroom => classroom.id == classId);
    if(classRoomToUpdate){
      let oldCount = this.g9demoService.classrooms.find(classroom => classroom.id == classId).students
      if(oldCount){
        this.g9demoService.classrooms.find(classroom => classroom.id == classId).students = oldCount - 1;
      } else {
        this.g9demoService.classrooms.find(classroom => classroom.id == classId).students = undefined;
      }
    }
  }

  get isSasnLogin():boolean {
    return this.mySchool.isSASNLogin();
  }

  getSlugProps() {
    return {
      DOMAIN: getFrontendDomain(),
      LANG_CODE: this.lang.c(),
      SCHOOL_GROUP_ID: this.g9demoService.schoolData.group_id
    }
  }

  private promptForUnPaidStudent( currentClassFilter: string, classRoomID?: number, groupID?: number): boolean {
    let activeSession = null;
    if (classRoomID) {
      activeSession = this.g9demoService.schoolSessions.find(sc => sc.school_class_id === classRoomID && sc.is_closed === 0);
    }
    if (activeSession) {
      let classFilterSlug:string = null;
      if(currentClassFilter == 'Primary') {
        classFilterSlug = 'lbl_primary'
      }
      if(currentClassFilter == 'Junior') {
        classFilterSlug = 'lbl_junior'
      }
      if(currentClassFilter == 'G9') {
        classFilterSlug = 'txt_g9_math'
      }
      if(currentClassFilter == 'OSSLT') {
        classFilterSlug = 'lbl_osslt'
      }
      this.unPaidStudentCurrentFilter.emit(classFilterSlug);
      return true;
    }
    return false;
  }

  getCreateNewAccountSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_create_new_acct"
    }
    return "btn_creat_a_new_account"
  }

  getEditSudentInfoSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_txt_edit_student"
    }
    return "sa_edit_student_info"
  }

  getAssignToClassroomSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_assign_to_class"
    }
    return "sa_assign_to_classroom"
  }

  getStudentRemoveSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_remove_student"
    }
    return "sa-students-remove-student"
  }

  getTemplateUrlSlug(): string {
    if (this.whitelabelService.isABED()) {
      return "abed_sa_student_template_url"
    }

    return "sa_student_template_url";
  }

  renderAccommodations(acct){
    return acct.accommodations?.join(', ');
  }

  isABEDG12() {
    return this.currentClassFilter === ClassFilterId.ABED_GRADE_12;
  }

  displayStudentDiplomaExams(student: any): string {
    const exams = student.diplomaExamInfo.filter(exams => +exams.test_window_id === +this.currentTestWindow.id);
    return exams.map(exam => exam.display_course_name).join(", ");
  }

  isTestCenter(){
    return this.whitelabelService.getSiteFlag('isTestCenter');
  }

  renderStudentIdent(student){
    if(this.whitelabelService.isEQAO()){
      return student.eqao_student_gov_id;
    }else if(this.whitelabelService.isABED()){
      return student[this.whitelabelService.getSiteText('studentIdProp')];
    }
  }

  renderASNHeader(){
    if(this.whitelabelService.isEQAO()){
      return 'sa_asession_modal_oen';
    }else if(this.whitelabelService.isABED()){
      return this.whitelabelService.getSiteText('student_ident_full');
    }
  }

  clientToApiAltVersionRequest(record){
    const altVersionRequestName = this.g9demoService.getStudentAltVersionRequestName(this.currentTestWindow)
    const studentAltVersionRequest = record[altVersionRequestName]
    return studentAltVersionRequest
  }
}
