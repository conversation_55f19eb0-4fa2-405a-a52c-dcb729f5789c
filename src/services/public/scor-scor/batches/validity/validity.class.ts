import { Id, NullableId, <PERSON><PERSON>ated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { dbRawRead, dbRawReadSingle, dbRawWriteMulti } from '../../../../../util/db-raw';
import { SQL_ITEM_BATCH_POLICIES, SQL_SCORER_SCORING_TASKS } from '../../../../../sql/scoring';
import translationHooks from '../../../translation/translation.hooks';
import { Errors } from '../../../../../errors/general';
import { arrToAggrMap, arrToMap, arrToStringMap, arrUnique } from '../../../../../util/param-sanitization';
import { dateAsUtcDbString, dbDateNow, dbDateOffsetDays } from '../../../../../util/db-dates';
import { AssignedItemComponentType, AssignedItemStatus } from '../../summary/types/assigned-tasks';
import allService from '../../../test-admin/test-sessions/all/all.service';
import { IMarkingItemMarkerTasks } from '../../summary/types/db';
import logger from '../../../../../logger';

const CT = AssignedItemComponentType;

const scoring_block_type_id = 3;

interface Data {}

interface ServiceOptions {}

export class Validity implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async computeAllRollingValidity(uid:number, window_item_id:number) {
    // get all scorers
    const scorers = await dbRawRead(this.app, [window_item_id], SQL_SCORER_SCORING_TASKS);
    // run rolling validity
    Promise.all(
      scorers.map(async scorer => {
        const stats = await this.computeRollingValidity(scorer.uid, scorer.window_item_id);
      })
    )
    return true;
  }



  async computeRollingValidity(uid:number, window_item_id:number) {

    const markingTask = await this.getScoringTask(uid, window_item_id); // will error out if scorer has no active/non-revoked tasks
    const {marking_window_id} = markingTask;

    const marking_window = await dbRawReadSingle(this.app, [window_item_id], `
      select mw.* from marking_window_items mwi 
        join marking_windows mw on mwi.marking_window_id = mw.id
      where mwi.id = ?;
    `);

    const langCode = marking_window.lang;

    const lastPerfBlock = await this.getLastPerfBlock(uid, window_item_id);

    // get batch policy to identify number of responses
    const batchPolicy = await dbRawReadSingle(this.app, [window_item_id], SQL_ITEM_BATCH_POLICIES);
    const minResponsesNeeded = Math.min(
      batchPolicy.access_rolling_batch_num,
      batchPolicy.rescore_rolling_batch_num,
    );

    // determine last time the scorer was blocked

    // determine if there have been at least N responses (limit to target + 1, grabbing the full list of ids ordered by most recent)
    // get the validity responses
    const AND_GT_PERF_BLOCK_CREATED_ON = () => {
      if (lastPerfBlock){
        return `AND mcbr.marked_on > '${dateAsUtcDbString(lastPerfBlock.created_on)}'`
      }
      return ''
    }
    const markedResponses = await dbRawRead(this.app, [uid, window_item_id, minResponsesNeeded+1], `
      select mcbr.id
           , mcbr.is_validity_exact
           , mcbr.is_validity_adj
           , mcb.id batch_id
      from marking_claimed_batches mcb
      join marking_claimed_batch_responses mcbr
        on mcbr.claimed_batch_id = mcb.id
        and mcbr.is_marked = 1
        and mcb.is_training = 0
      where mcb.uid = ?
        and mcb.marking_window_item_id = ?
        and mcbr.is_validity = 1
        ${AND_GT_PERF_BLOCK_CREATED_ON()}
      order by mcbr.id desc
      limit ?
    `);

    // check that there are enough
    if(markedResponses.length < minResponsesNeeded) {
      const task = await this.app
        .service('db/write/marking-item-marker-tasks')
        .patch(markingTask.id, {
          is_cache_roll_val_current: 0,
        });
      return true;
    }

    // compute valited
    let sum_exact = 0;
    let sum_adj = 0;
    let sum_validity = 0;
    for(let r of markedResponses) {
      sum_validity++;
      if(r.is_validity_exact==1) {
        sum_exact++;
        sum_adj++;
      }
      else if(r.is_validity_adj==1) {
        sum_adj++;
      }
    }

    const checkThresholds = (checks:{rate:number, rateMin:number}[]) => {
      let isAnyBelow = false;
      checks.forEach(check =>{
        if (check.rate < (check.rateMin-0.0001)){
          isAnyBelow = true;
        }
      })
      return isAnyBelow;
    }

    if(sum_validity > 0 ) {
      const cache_roll_val_exact = sum_exact/sum_validity;
      const cache_roll_val_adj = sum_adj/sum_validity;
      const isAccessFail = checkThresholds([
        {rate:cache_roll_val_exact, rateMin:batchPolicy.access_rolling_min_exact_rate},
        {rate:cache_roll_val_adj,   rateMin:batchPolicy.access_rolling_min_exact_adj_rate},
      ])
      const isAccessRescoreReq = checkThresholds([
        {rate:cache_roll_val_exact, rateMin:batchPolicy.rescore_rolling_min_exact_rate},
        {rate:cache_roll_val_adj,   rateMin:batchPolicy.rescore_rolling_min_exact_adj_rate},
      ])
      const task = await this.app
        .service('db/write/marking-item-marker-tasks')
        .patch(markingTask.id, {
          is_cache_roll_val_current: 1,
          cache_roll_val_exact,
          cache_roll_val_adj,
        });

      if(isAccessFail) {
        await this.blockScorerAccessOrSendToTraining({
          marking_window_id,
          uid,
          window_item_id,
          langCode,
          batch_policy_max_sendbacks: batchPolicy.max_sendbacks,
          blockReasonData: {
            cache_roll_val_exact,
            cache_roll_val_adj,
            markedResponses,
          }
        })
      }
      if (isAccessRescoreReq){ // this should NOT be an else if
        const batchIds = arrUnique(
          markedResponses.map(r => r.batch_id)
        );
        await this.flagInvalidMarks(uid, window_item_id, batchIds);
      }

      return task;

    }

    return true;
  }

  async getScoringTask(uid:number, marking_window_item_id:number) {
    const markingTasks = <any[]> await this.app
      .service('db/read/marking-item-marker-tasks')
      .find({
        paginate: false,
        query: {
          marking_window_item_id,
          uid,
          component_type: CT.SCORING,
          is_revoked: 0,
          is_removed: 0,
        }
      })
    if (markingTasks.length === 0){
      throw new Errors.GeneralError('SCORER_IS_INACTIVE');
    }
    return markingTasks[0];
  }

  async getLastPerfBlock(uid:number, window_item_id:number){
    const perfBlockRecords = <any[]> await this.app
      .service('db/read/marking-item-marker-perf-blocks')
      .find({
        paginate: false,
        query: {
          uid,
          window_item_id,
          $sort: {
            created_on: -1
          }
        }
      })
    return perfBlockRecords[0]
  }

  async flagInvalidMarks(uid:number, window_item_id:number, batchIds:number[]){ // intentional redundancies
    logger.silly('revokeInvalidMarks', {uid, window_item_id, batchIds});
    const responsesToRevokeRecords = <any[]> await this.app
      .service('db/read/marking-claimed-batch-responses')
      .find({
        paginate: false,
        query: {
          $limit: 10000,
          $select: ['id'],
          claimed_batch_id: { $in: batchIds },
          is_revoked: 0,
          // is_marked: 1, // not depending on this because batch expiry is not entirely reliable
        }
      });
    for (let record of responsesToRevokeRecords){
      // slow... but safe
      await this.app
        .service('db/read/marking-claimed-batch-responses')
        .patch(record.id, {
          is_invalid: 1,
          is_invalid_for_reporting: 1,
          invalidated_on: dbDateNow(this.app),
        })
    }
  }

  async getNumPrevBlocks(uid:number, window_item_id:number){
    const perfBlockRecords = <Paginated<any>> await this.app
      .service('db/read/marking-item-marker-perf-blocks')
      .find({
        query: {
          uid,
          window_item_id,
          $limit: 0,
        }
      });
    return  perfBlockRecords.total;
  }

  async getScorerTasksMap(uid:number, marking_window_item_id:number,){
    const markingTasks = <any[]> await this.app
      .service('db/read/marking-item-marker-tasks')
      .find({
        paginate: false,
        query: {
          marking_window_item_id ,
          uid,
          is_revoked: 0,
          is_removed: 0
        }
      })
    return arrToStringMap(markingTasks, 'component_type');
  }

  async revokeTask(taskId:number){
    await this.app
      .service('db/write/marking-item-marker-tasks')
      .patch(taskId, {
        is_revoked: 1,
        revoked_on: dbDateNow(this.app),
      })
  }

  async releaseClaimedResponses(uid:number, marking_window_item_id:number) {
    // fetch
    const taskRecords = <Paginated<IMarkingItemMarkerTasks>> await this.app
      .service('db/read/marking-item-marker-tasks')
      .find({query: {
          uid,
          is_revoked: 0,
          is_removed: 0,
          $limit: 120,
      }})

    const windowItemIds = taskRecords.data.map(r => r.marking_window_item_id);

    // get available batches
    const claimedBatches = await this.app.service('public/scor-scor/batches/available').identifyBatchesClaimed(uid, windowItemIds);
    const itemToClaimedBatches = arrToAggrMap(claimedBatches, 'marking_window_item_id');
    const targetClaimedBatches = itemToClaimedBatches.get(marking_window_item_id);
    const batchesToBeReleased:any[] = [];
    if (targetClaimedBatches && targetClaimedBatches.length > 0) {
      targetClaimedBatches.forEach(batch => {
        if (!batch.is_marked){
          batchesToBeReleased.push(batch);
        }
      });
      if (batchesToBeReleased.length > 0) {
        for (let batch of batchesToBeReleased) {
          await this.app
          .service('db/write/marking-claimed-batches')
          .patch(batch.id, {
            is_revoked: 1,
            revoked_on: dbDateNow(this.app),
          })

          const responsesToRevoke = await dbRawRead(this.app, [batch.id], `
            select mcbr.id
            from mpt_dev.marking_claimed_batches mcb
            join mpt_dev.marking_claimed_batch_responses mcbr
              on mcb.id = mcbr.claimed_batch_id
            where claimed_batch_id = ?
              and mcbr.is_marked = 0
              and mcbr.is_revoked = 0;
          `)

          for (let response of responsesToRevoke) {
            await this.app.service('db/write/marking-claimed-batch-responses').patch(response.id, {
              revoked_on: dbDateNow(this.app),
              is_revoked: 1
            })
          }
        }
      }
    }
  }

  async changeTaskStatus(taskId:number, status:string){
    await this.app
      .service('db/write/marking-item-marker-tasks')
      .patch(taskId, {
        status,
      })
  }

  async logAutoPerfBlock(uid:number, window_item_id:number, blockReasonData:any){
    await this.app
      .service('db/write/marking-item-marker-perf-blocks')
      .create({
        uid,
        window_item_id,
        created_on: dbDateNow(this.app),
        is_auto: 1,
        data: JSON.stringify(blockReasonData),
      })
  }

  async blockScorerAccessOrSendToTraining(config:{marking_window_id:number, uid:number, window_item_id:number, langCode:string, batch_policy_max_sendbacks:number, blockReasonData:any}){

    const {
      marking_window_id,
      uid,
      window_item_id,
      langCode,
      batch_policy_max_sendbacks,
      blockReasonData
    } = config;

    logger.silly('blockScorerAccessOrSendToTraining', {uid, window_item_id, langCode, batch_policy_max_sendbacks});

    const numPreviousBlocks = await this.getNumPrevBlocks(uid, window_item_id);

    await this.logAutoPerfBlock(uid, window_item_id, {
      ... blockReasonData,
      numPreviousBlocks,
    })

    // setup message sending
    const tra = async (slug:string) => await this.app
      .service('public/translation')
      .getOneBySlug(slug, langCode)
    const sendMessage = async (config: {subj:string, body:string}) => {
      return this.app
        .service('public/scor-lead/message-centre')
        .sendMessage({
          sender_uid: 0, // system message
          recipient_uids: [uid],
          subject:  config.subj,
          message: config.body,
          is_auto: true,
        })
    }

    // 1) check number of fail scoring (use new table for send backs, that's the cleanest option here)
    const taskMap = await this.getScorerTasksMap(uid, window_item_id)
    if (numPreviousBlocks >= batch_policy_max_sendbacks){
      await this.releaseClaimedResponses(uid, window_item_id);
      await this.revokeTask(taskMap.get(CT.SCORING).id);
      await this.changeTaskStatus(taskMap.get(CT.SCORING).id, AssignedItemStatus.NOT_AVAILABLE);
      await this.changeTaskStatus(taskMap.get(CT.QUALIFYING).id, AssignedItemStatus.NOT_AVAILABLE);
      await this.changeTaskStatus(taskMap.get(CT.PRACTICE).id, AssignedItemStatus.NOT_AVAILABLE);
      await this.changeTaskStatus(taskMap.get(CT.TRAINING_MATERIALS).id, AssignedItemStatus.NOT_AVAILABLE);
      await sendMessage({
        subj: await tra('msg_low_roll_val_final_subj'),
        body: await tra('msg_low_roll_val_final_body'),
      });
    }
    else{
      await this.changeTaskStatus(taskMap.get(CT.SCORING).id, AssignedItemStatus.NOT_AVAILABLE);
      await this.changeTaskStatus(taskMap.get(CT.QUALIFYING).id, AssignedItemStatus.NOT_AVAILABLE);
      await this.changeTaskStatus(taskMap.get(CT.PRACTICE).id, AssignedItemStatus.NOT_AVAILABLE);
      await this.changeTaskStatus(taskMap.get(CT.TRAINING_MATERIALS).id, AssignedItemStatus.PENDING);
      await sendMessage({
        subj: await tra('msg_low_roll_val_subj'),
        body: await tra('msg_low_roll_val_body'),
      });
    }

    await this.app.service('db/write/scorer-status-by-item-log').create({
      uid,
      marking_window_item_id: window_item_id,
      timestamp: dbDateNow(this.app),
      is_blocked: 1,
      scoring_block_type_id,
      marking_window_id
    })

  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    // return [await this.computeRollingValidity(503674, 42)];
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    return <any> this.computeRollingValidity(504632, 60)
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if( ! (params &&  params.query && (params.query.uid || params.query.window_item_id))) {
      throw new Error('Please provide params.query.uid and params.query.window_item_id');
    }

    return await this.computeRollingValidity(params.query.uid, params.query.window_item_id)
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
