import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { QuestionResponseEntry } from '../../test-taker/invigilation/question-response/question-response.class';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';
import { isArray } from 'lodash';
import { AssessmentType, getAssessmentTypeFromStr } from '../../bc-admin-coordinator/test-window/assessment-type';
import { generateSecretCode } from '../../../../util/secret-codes';
import { lzDecompressProps } from '../../../../util/lzstring';
import logger from '../../../../logger';
import type Redis from 'ioredis';
import hasher from 'node-object-hash';
import { SubmData } from '../../../../redis/redis';

const submHasher = hasher({ alg: 'sha1' });

interface Data { }

interface ServiceOptions { }

export interface ISSConfig {
  subsession_order?: number,
  subsession_slug?: string
}

export interface SubSessionData {
  activeSubSessionAttempt: any;
  is_last: boolean;
  order: number;
}

export type AttemptValidator = (uid: number, test_attempt_id: number) => Promise<{
  attemptRecord: any;
  subSessionData: SubSessionData | null;
}>;

export interface QuestionSubmissionData {
    test_attempt_id: number,
    test_question_id: number,
    test_question_version_id: number,
    response_raw: string,
    response: string,
    question_index: number,
    question_caption: string,
    section_index: number,
    module_id: number,
    uid: number,
    created_by_uid?: number,
    taqr_id?: number,
    isPaper: boolean
}

const IS_BCED_CONTEXT = false;

export class SessionQuestion implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get(id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  validateAttId:AttemptValidator = async (uid: number, test_attempt_id: number, subsession_config:ISSConfig = {}) => {
    return await this.app
      .service('public/student/session')
      .validateAttemptId(<number>uid, test_attempt_id, false, subsession_config);
  };

  async assignTestAttemptId(uid: number, test_attempt_id: number){
    const startedOn =  await dbRawRead(this.app, [test_attempt_id], `
      select ta.started_on
        from test_attempts ta
      where ta.id = ?
      ;`);
    const existingRecords =  await dbRawRead(this.app, [test_attempt_id, uid], `
      select sap.*
        from student_attempt_purchases sap
      join test_session_purchases tsp on tsp.id = sap.ts_purchase_id
      join test_sessions ts on ts.test_window_id = tsp.test_window_id
      join test_attempts ta on ta.test_session_id = ts.id and ta.id = ?
      where sap.uid = ?
        and sap.is_revoked != 1
        and tsp.is_revoked != 1
        and tsp.is_approved = 1
    ;`);

    const patchRecord ={
      assigned_ta_id: test_attempt_id,
      used_on: startedOn.length > 0 ? startedOn[0].started_on : null,
    }

    await Promise.all(existingRecords.map(async record =>{
      await this.app.service('public/payment-ctrl/alternative-payments').updateStudentAttempt({
        assigned_ta_id: test_attempt_id,
        used_on: startedOn.length > 0 ? startedOn[0].started_on : null,
        is_used: 1
      }, {
        id: record.id
      });
    }))
  }

  async create(data: QuestionResponseEntry, params?: Params): Promise<Data> {
    // to do: validate
    if (params && data) {
      const { schl_class_group_id } = (<any>params).query;
      const uid = await currentUid(this.app, params);
      if(data.question_caption == 'Question 1'){
        await this.assignTestAttemptId(uid, data.test_attempt_id);
      }
      return this.submitQuestionReq(uid, data);
    }
    throw new Errors.BadRequest();
  }
  submitQuestionReq(uid:number, data: QuestionResponseEntry){
    data = lzDecompressProps(data, ['response_raw', 'response'])
    return this.submitQuestion(
      {
        ... this.getReqData(data),
        uid,
      },
      this.validateAttId
    )
  }


  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    // to do: validate
    if (params) {
      const { schl_class_group_id } = (<any>params).query;
      return this.submitTest({
        ... this.getReqData(data),
        uid: await currentUid(this.app, params),
        forceKeepActiveSubsession: params?.query?.forceKeepActiveSubsession
      }, this.validateAttId)
    }
    throw new Errors.BadRequest();
  }
  submitTestReq(uid:number, data: Data){
    return this.submitTest(
      {
        ... this.getReqData(data),
        uid,
      },
      this.validateAttId
    )
  }

  async remove(id: NullableId, params?: Params): Promise<Data> {
    // return this.removeDuplicateQuestionAttemptRecords(0);
    throw new Errors.MethodNotAllowed();
  }

  public async removeDuplicateQuestionAttemptRecords(attemptId: number) {
    // to do (remove test session id restriction, do for entire db, then apply attempt id restriction so that it makes sense in this context)
    const records = await dbRawRead(this.app, [attemptId], `
      select taqr2.test_attempt_id, taqr2.test_question_id, taqr2.id, taqr2.is_invalid
      from (
        select t.*
        from (
          SELECT taqr.test_attempt_id, taqr.test_question_id, count(0) as tally
          FROM test_attempts ta
          join test_attempt_question_responses taqr
            on taqr.test_attempt_id = ta.id
            and taqr.is_invalid = 0
          where ta.id = ?
          group by taqr.test_attempt_id, taqr.test_question_id
        ) t
        where tally > 1
      ) t2
      join test_attempt_question_responses taqr2
        on taqr2.test_attempt_id = t2.test_attempt_id
      and taqr2.test_question_id = t2.test_question_id
      order by taqr2.test_attempt_id, taqr2.test_question_id, taqr2.id desc
    ;`);
    const renderRecordKey = (entry: any) => [entry.test_attempt_id, entry.test_question_id].join(';')
    const recordsToInvalidate: number[] = [];
    const recordKeysSeen = new Map();
    records.forEach(record => {
      const key = renderRecordKey(record);
      if (recordKeysSeen.get(key)) {
        recordsToInvalidate.push(record.id)
      }
      else {
        recordKeysSeen.set(key, true);
      }
    });
    if (recordsToInvalidate && recordsToInvalidate.length > 0) {
      await dbRawWrite(this.app, [recordsToInvalidate], `
        UPDATE test_attempt_question_responses
        SET is_invalid = '1'
        WHERE id IN (?)
      ;`);
    }
    return {
      invalidated: recordsToInvalidate.length,
      total: records.length,
    }
  }

  getReqData(data: any) {
    return this.app.service('public/test-taker/invigilation/question-response').getReqData(data);
  }

  // async submitQuestion(data:QuestionResponseEntry){
  //   const {uid, test_attempt_id} = data;
  //   return this.app
  //     .service('public/test-taker/invigilation/question-response')
  //     .submitQuestion(data);
  // }

  async submitTest(data: any, validateAttemptId: any) {
    const { uid, test_attempt_id, subsession_slug, subsession_order, forceKeepActiveSubsession } = data;
    const { attemptRecord, subSessionData } = await validateAttemptId(<number>uid, <number>test_attempt_id, {subsession_slug, subsession_order});
    let tassConfig = undefined;
    if (subSessionData) {
      tassConfig = {
        id: subSessionData.activeSubSessionAttempt === undefined? undefined:subSessionData.activeSubSessionAttempt.id,
        closesTestAttempt: subSessionData.is_last,
        autoOpenNext: !attemptRecord.is_no_td_order,
        twtdar_order: attemptRecord.twtdar_order,
        test_session_id: attemptRecord.test_session_id,
        attemptUid: uid,
        ssOrder: subSessionData.order
      };
    }
    await this.app.service('public/student/session')
      .closeAttempt(<number> data.test_attempt_id, <number> uid, false, {}, attemptRecord.test_session_id, tassConfig, forceKeepActiveSubsession );
    await this.removeDuplicateQuestionAttemptRecords(data.test_attempt_id);

    if (IS_BCED_CONTEXT){
      await this.wrapUpBcedAttempt(uid, test_attempt_id, attemptRecord.test_session_id);
    }

    return <any>{};
  }

  async wrapUpBcedAttempt(uid:number, test_attempt_id:number, test_session_id:number ){
// create confirmation code on submission
    // BR[28]-2
    const confirmationCode = generateSecretCode(7);
    await this.app.service('db/write/test-attempt-confirmation-codes').create({
      uid,
      test_session_id,
      test_attempt_id,
      confirmation_code: confirmationCode,
    })

    // check if we need to issue an "X" code
    const percentageResponded = await this.getPercentageResponded(test_attempt_id as number);
    if (percentageResponded < 0.5) {
      // assign x code
      await this.app.service('public/bc-admin-coordinator/test-attempt-metas').createCode(
        test_attempt_id, 'X', 1, uid, 'bced', false
      );
    }

    // // Logged event for BR[43]-7
    // Logged event code 'LP0' to be shown for students who submit the written component
    //  of the LTP assessment, but not the oral component on the same day.
    // We create the event code on submission of the written component and delete it on
    //  submission of the oral component if it is submitted on the same day.
    const res = await dbRawRead(this.app, [test_attempt_id], `
      select
        twtar.test_window_id,
        twtar.type_slug,
        scts.school_class_id,
        ta.created_on
      from test_attempts ta
      join test_window_td_alloc_rules twtar on ta.twtdar_id = twtar.id
      join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
      where ta.id = ?
      limit 1
    `);
    const { test_window_id, type_slug, school_class_id, created_on } = res[0];
    if (['GRAD_LTP10 WRITTEN', 'GRAD_LTP12 WRITTEN'].includes(type_slug)) {
      await this.app.service('public/bc-admin-coordinator/validation-code').createValidationCode(test_window_id, uid, school_class_id, 'LP0', 'log', test_attempt_id);
    }
    else if (['GRAD_LTP10 ORAL', 'GRAD_LTP12 ORAL'].includes(type_slug)) {
      const written_slug = type_slug == 'GRAD_LTP10 ORAL' ? 'GRAD_LTP10 WRITTEN' : 'GRAD_LTP12 WRITTEN'
      const written_res = await dbRawRead(this.app, [uid, test_window_id, written_slug, created_on], `
        select
          twsc.id code_id,
          twtar.test_window_id,
          twtar.type_slug,
          scts.school_class_id
        from test_window_student_codes twsc
        from test_attempts ta on twsc.test_attempt_id = ta.id
        join test_window_td_alloc_rules twtar on ta.twtdar_id = twtar.id
        join school_class_test_sessions scts on scts.test_session_id = ta.test_session_id
        where twsc.uid = ? and twsc.code = 'LP0' and twsc.code_type = 'log'
          and twtar.test_window_id = ? and twtar.type_slug = ? and datediff(day, ?, ta.created_on) = 0
        limit 1
      `);
      if (written_res.length > 0) {
        await dbRawWrite(this.app, [written_res[0].code_id], `
          DELETE FROM test_window_student_codes WHERE id = ?
        `);
      }
    }
  }

  async submitQuestion_noRedis (data: QuestionSubmissionData, subSessionData:any) {
    const {
      test_attempt_id,
      test_question_id,
      test_question_version_id,
      response_raw,
      response,
      question_index,
      question_caption,
      section_index,
      module_id,
      uid,
      isPaper
    } = data;
    const is_paper_format = isPaper ? 1 : 0;
    const is_real_time_audit_processed = 0;

    const created_by_uid = data.created_by_uid || uid;

    //  sub session patch
    if (subSessionData && subSessionData.activeSubSessionAttempt) {
      const tass = subSessionData.activeSubSessionAttempt;
      if (!tass.started_on) {
        await this.app
          .service('db/write/test-attempt-sub-sessions')
          .patch(tass.id, {
            started_on: dbDateNow(this.app),
            last_touch_on: dbDateNow(this.app),
          });
      }
      else {
        await this.app
          .service('db/write/test-attempt-sub-sessions')
          .patch(tass.id, { last_touch_on: dbDateNow(this.app), });
      }
    }

    await this.app.service('public/student/attempt').updatePos(test_attempt_id, section_index, question_index, question_caption, module_id);

    // update the question responses
    const { score, weight } = this.aggregateScoreAndWeightFromResponseRaw(response_raw);
    const questionResponse = {
      test_attempt_id,
      test_question_id,
      test_question_version_id,
      module_id,
      response_raw,
      score,
      weight,
      response,
      section_id: section_index,
      updated_on: dbDateNow(this.app),
      updated_by_uid: created_by_uid,
      is_paper_format,
      is_real_time_audit_processed
    }
    var attempts_q_res:any[] = [];
    if(test_attempt_id !== undefined && test_question_id !== undefined && section_index !== undefined){
      attempts_q_res = <any[]>await this.app
        .service('db/write/test-attempt-question-responses')
        .find({
          query: {
            test_attempt_id,
            test_question_id,
            is_invalid: 0,
            section_id: section_index,
            $sort: { id: -1, }
          },
          paginate: false
        });
    }

    await Promise.all(
      attempts_q_res.map((aqr, i) => {
        if (i === 0) {
          return this.app
            .service('db/write/test-attempt-question-responses')
            .patch(aqr.id, questionResponse)
        }
        else {
          return this.app
            .service('db/write/test-attempt-question-responses')
            .patch(aqr.id, { is_invalid: 1 })
        }
      })
    )
    if (attempts_q_res.length === 0) {
      await this.app
        .service('db/write/test-attempt-question-responses')
        .create(questionResponse)
    }
    return true
  }

  async submitQuestion(data: QuestionSubmissionData, validateAttemptId: AttemptValidator) {
    const {
      test_attempt_id,
      test_question_id,
      test_question_version_id,
      response_raw,
      response,
      question_index,
      question_caption,
      section_index,
      module_id,
      uid,
      taqr_id,
      isPaper
    } = data;
    const is_paper_format = isPaper ? 1 : 0;
    const is_real_time_audit_processed = 0;
    const created_by_uid = data.created_by_uid || uid;
    logger.info('test-attempt-question-responses-log', {
      test_attempt_id,
      test_item_id: test_question_id,
      response_raw,
      question_index,
      section_index,
      module_id,
      uid
    });

    // validate
    const { attemptRecord, subSessionData } = await validateAttemptId(data.uid, data.test_attempt_id);

    const subRecords: any = await this.app.service('public/test-ctrl/schools/student-attempts').processResponseRaw({
      response: {
        response_raw: data.response_raw,
        item_id: data.test_question_id
      },
      responseTypes: <{ [key: string]: boolean }>{}
    })
    const record = subRecords[0];
    const is_nr = record?.no_response ? 1 : 0;

    // //A test attempt with a new/updated response should have RT audit flag reset to 0 so that the next audit reaches its responses
    // if (test_attempt_id) {
    //   await this.app
    //   .service('db/write/test-attempts')
    //   .patch(test_attempt_id, { is_real_time_audit_processed: 0 })
    // }

    const redis:Redis = this.app.get('redis');
    if (!redis) {
      return this.submitQuestion_noRedis(data, subSessionData);
    }

    const undefinedOrNanToNull = (v:any) => v === undefined || isNaN(v) ? null : v;
    const tass = subSessionData?.activeSubSessionAttempt || null;
    const { score, weight } = this.aggregateScoreAndWeightFromResponseRaw(response_raw);
    const bufferData:SubmData = {
      taqr_id,
      test_attempt_id,
      uid,
      test_session_id: attemptRecord.test_session_id,
      test_question_id,
      test_question_version_id: undefinedOrNanToNull(test_question_version_id),
      module_id: undefinedOrNanToNull(module_id),
      response_raw,
      score,
      weight,
      response,
      section_id: section_index,
      timestamp: Date.now(),
      updated_by_uid: created_by_uid,
      tass_id: undefinedOrNanToNull(tass?.id),
      tass_started_on: undefinedOrNanToNull(tass?.started_on),
      question_index,
      question_caption,
      sub_session_id: undefinedOrNanToNull(tass?.sub_session_id),
      is_nr,
      is_paper_format,
      is_real_time_audit_processed
    }

    await redis.pipeline()
      .hset(`submData:${test_attempt_id}`, test_question_id, JSON.stringify(bufferData))
      .lpush('pendingSubmWrites', `${test_attempt_id}:${test_question_id}`)
      .set(`attemptPos:${test_attempt_id}`, JSON.stringify({
        test_attempt_id,
        section_index,
        question_index,
        question_caption,
        module_id: undefinedOrNanToNull(module_id),
        timestamp: bufferData.timestamp,
        uid
      }))
      .exec()

    return true;
  }

  aggregateScoreAndWeightFromResponseRaw(response_raw: string): {
    score: number,
    weight: number,
  } {
    let score = 0;
    let weight = 0;
    const responseRaw = JSON.parse(response_raw) as { [key: string]: any };
    for (const [key, value] of Object.entries(responseRaw)) {
      if (key === '__meta') continue;
      if ('score' in value) score += value.score;
      if ('weight' in value) weight += value.weight;
    }
    return {
      score,
      weight,
    };
  }

  async getPercentageResponded(testAttemptId: number): Promise<number> {
    // test attempt must exist
    const testAttempt = await this.app.service('db/read/test-attempts').get(testAttemptId, {
      query: {
        $select: ['uid', 'test_session_id', 'is_closed'],
      }
    });
    if (!testAttempt) throw new Errors.BadRequest("Test attempt not found.");

    const {
      uid,
      test_session_id,
      is_closed,
    } = testAttempt;

    // test attempt must be closed
    if (is_closed !== 1) throw new Errors.BadRequest("Test attempt not closed");

    // count sr total
    // find test design
    const records = await dbRawRead(this.app, [testAttemptId], `
      select
      td.framework, scts.slug
      from test_sessions ts join test_attempts ta on ta.test_session_id = ts.id and ta.id = ?
      join school_class_test_sessions scts on scts.test_session_id = ts.id
      join test_window_td_alloc_rules twtdar on twtdar.type_slug = scts.slug and twtdar.test_window_id = ts.test_window_id
      join test_designs td on td.id = twtdar.test_design_id
      limit 1
    `);
    if (records.length !== 1) throw new Errors.BadRequest("Test design does not exist.");
    let framework;
    try {
      framework = JSON.parse(records[0].framework);
    } catch {
      throw new Errors.BadRequest("Invalid test design framework.");
    }
    if (!framework.sectionItems) throw new Errors.BadRequest("Invalid test design.");
    let totalSr = 0;
    const srQuestionIds = new Set<number>();
    Object.values(framework.sectionItems).forEach((section: any) => {
      const { questions } = section;
      if (!questions || !Array.isArray(questions)) throw new Errors.BadRequest("Invalid test design framework.");
      totalSr += questions.length;
      questions.map(q => {
        srQuestionIds.add(q.id);
      });
    });

    // count sr responded
    const responses = <any[]>await this.app.service('db/read/test-attempt-question-responses').find({
      query: {
        $select: ['id'],
        test_attempt_id: testAttemptId,
        response: {
          $ne: '',
        },
      },
      paginate: false,
    });
    let numSrResponded = 0;
    responses.map(res => {
      if (!res.test_question_id) return;
      if (srQuestionIds.has(res.test_question_id)) {
        numSrResponded++;
      }
    });

    // count cr total
    const testSessionSlug = records[0].slug as string;
    const assessmentType = getAssessmentTypeFromStr(testSessionSlug.split('_')[0]);
    const assessmentCode = testSessionSlug.split('_')[1];

    if (assessmentType === AssessmentType.GRAD) {
      const totalCr = this.getNumCrQuestions(assessmentCode);

      // count cr responded
      let crCount: (any[] | number) = await dbRawRead(this.app, [testAttemptId], `
        select
        count(*)
        from test_attempt_question_responses taqr join marking_responses mr on taqr.test_attempt_id = ? and taqr.id = mr.id
      `);
      const numCrResponded = crCount[0]['count(*)'] as number;

      const percentage = (numSrResponded + numCrResponded) / (totalSr + totalCr);
      return percentage;
    } else {
      return 0;
    }


  }

  // temporary
  // TODO: get these values from path selections in test design
  // temporary values come from https://docs.google.com/presentation/d/1Vz2zUrR8zfeGbCvx7gw9bn6CPLbA6T6lfbzaOIDt_0k/edit#slide=id.gceae8e22dd_0_0
  private getNumCrQuestions(assessmentCode: string): number {
    switch (assessmentCode) {
      case 'LTE10':
        return 2;
      case 'LTP10':
        return 3;
      case 'NME10':
      case 'NMF10':
        return 4;
      case 'LTE12':
        return 3;
      case 'LTP12':
        return 3;
      case 'LTF12':
        return 2;
      default: return 0;
    }
  }

}
