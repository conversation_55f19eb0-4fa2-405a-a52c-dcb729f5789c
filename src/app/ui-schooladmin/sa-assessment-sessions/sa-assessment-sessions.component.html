<div class="assessment-sessions-view">
    <div *ngIf="!isPrivateSchool" style = "display:table">
        <div style = "display: table-cell;">
            <filter-toggles 
                [state]="mySchool.getClassFilterToggles({isExcludeClassroomAssessment: true})"
                (id)="setClassFilter($event)"
                (assessmentDefId)="setAssessmentDefId($event)"
            ></filter-toggles>
        </div>
        <div style = "display: table-cell;">
            <sa-test-window-filter
              [currentClassFilter] = "currentClassFilter"
              (setTestWindowEvent) = "setTestWindowFilter($event)"
            ></sa-test-window-filter>
        </div>   
    </div>
    <div *ngIf="isPrivateSchool" style = "display:table">
        <div style = "display: table-cell;">
            <filter-toggles 
                [state]="mySchool.getPrivateSchoolClassFilterToggles()"
                (id)="setClassFilter($event)"
                (assessmentDefId)="setAssessmentDefId($event)"
            ></filter-toggles>
        </div>
        <div style = "display: table-cell;">
            <sa-test-window-filter
            [currentClassFilter] = "currentClassFilter"
            (setTestWindowEvent) = "setTestWindowFilter($event)"
            ></sa-test-window-filter>
        </div>
    </div>

    <div *ngIf="!currentClassFilter">
        <tra-md slug="txt_msg_sessions_req_filter"></tra-md>
    </div>

    <div *ngIf="currentClassFilter">

        <p><tra-md [slug]="getSaAssessmentsHeaderSlug()"></tra-md></p>
        <p *ngIf="isPaymentModuleEnabled">
          <tra-md slug="sa_assessments_payment_header"></tra-md>
        </p>
        <p *ngIf="isPaymentModuleEnabled">
            <tra slug="sa_assessments_payment_refund_policy_link"></tra><a (click)="showRefundPolicy()"><tra slug="lbl_here"></tra>.</a>
        </p>
        <br>

        <div class="pre-table-strip">
            <div>
                <button *ngFor="let sessionTab of sessionTabs"
                  class="button is-small has-icon"
                  (click)="selectSessionTab(sessionTab.id)"
                  [class.is-info]="selectedSessionType == sessionTab.id"
                >
                  <tra [slug]="sessionTab.caption"></tra>&nbsp;
                  ({{sessionTab.count}})
                </button>
                <button *ngIf="isPaymentModuleEnabled" class="button is-small has-icon" [disabled]="!isPaymentAvailable()" (click)="showSessionPaymentModal()">
                    <span class="icon" style="margin-right: 0.3em"><i class="fas fa-archive"></i></span>
                    <tra slug="btn_sa_pay_sessions"></tra>
                </button>
            </div>
            <div>
              <div>
                <mat-slide-toggle [(ngModel)]="isShowTeacherNames">
                  Show teacher and supervisors?
                </mat-slide-toggle>
              </div>
              <div *ngIf="selectedSessionType == SESSION_TYPE.REQ_UPLOADS">
                <mat-slide-toggle [(ngModel)]="isShowCompletedUploads">
                  Show Completed Uploads?
                </mat-slide-toggle>
              </div>
              <button *wlCtx="'IS_SA_SESISON_IMPORT'" class="button is-small has-icon" (click)="loginGuard.disabledPopup()">
                  <span class="icon"><i class="fas fa-table"></i></span>
                  <span><tra slug="sa_sessions_import"></tra></span><!--Import-->
              </button>
              <button *wlCtx="'IS_SA_SESISON_EXPORT'" class="button is-small has-icon" (click)="loginGuard.disabledPopup()">
                  <span class="icon"><i class="fas fa-table"></i></span>
                  <span><tra slug="sa_sessions_export"></tra></span><!--Export-->
              </button>
            </div>
        </div>

        <div class="pre-table-strip">
          <div>
            <button  class="button is-small has-icon is-success"  *ngIf="!isSaSessionCreationDisabled()" (click)="newSessionModalStart()" [disabled]="!isCurrentTestWindowActive()">
                <span class="icon"><i class="fas fa-plus-square"></i></span>
                <span *ngIf="whitelabelService.isABED()"><tra slug="abed_create_new_session"></tra><!--New Session--></span>
                <span *ngIf="!whitelabelService.isABED()"><tra slug="sa_sessions_new"></tra><!--New Session--></span>
            </button>
            <button 
              *ngIf="selectedSessionType == SESSION_TYPE.UPCOMING"
              class="button is-small has-icon" 
              [disabled]="shouldEditBtnBeDisabled()" 
              (click)="editSessionModalStart()">
                <span class="icon"><i class="fas fa-edit"></i></span>
                <span *ngIf="whitelabelService.isABED()"><tra slug="abed_edit_selection"></tra><!--Edit Selected--></span>
                <span *ngIf="!whitelabelService.isABED()"><tra slug="sa_sessions_edit"></tra><!--Edit Selected--></span>
            </button>
            <button 
                *ngIf="selectedSessionType == SESSION_TYPE.UPCOMING"
                class="button is-small has-icon" [disabled]="!isAnySelected || !allowCancelSession" (click)="cancelSelectedSessions()">
                <span class="icon"><i class="fas fa-trash"></i></span>
                <span *ngIf="whitelabelService.isABED()"><tra slug="abed_cancel_session"></tra><!--Cancel Selected--></span>
                <span *ngIf="!whitelabelService.isABED()"><tra slug="btn_cancell_session"></tra><!--Cancel Selected--></span>
            </button>
          </div>
        </div>

        <paginator *ngIf="isLoaded" [model]="sessionsTable.getPaginatorCtrl()" [page]="sessionsTable.getPage()" [numEntries]="sessionsTable.numEntries()"></paginator>
        <div style="overflow-x: auto; width: 100%;">
          <table *ngIf="isLoaded" class="sessions-table table is-hoverable">
              <tr>
                  <th> 
                    <table-row-selector 
                      [entry]="this" prop="isAllSelected" (toggle)="toggleSelectAll()">
                    </table-row-selector> 
                  </th>
  
                  <th *ngIf="(selectedSessionType !== SESSION_TYPE.SUBMITTED) && (selectedSessionType != SESSION_TYPE.REQ_UPLOADS)" class="flush"> 
                    <!--Status--> 
                    <table-header 
                      id = "status"       
                      [caption] = "columnLabels.status"         
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true">
                    </table-header>
                  </th>

                  <th *ngIf="(selectedSessionType == SESSION_TYPE.REQ_UPLOADS)" class="flush">
                    <table-header 
                      id = "scan_progress"       
                      [caption] = "columnLabels.uploadProgress"         
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true" [disableFilter]="true">
                    </table-header>
                  </th>
                  <th *ngIf="selectedSessionType == SESSION_TYPE.REQ_UPLOADS" class="flush" > 
                    <table-header id = "days_remaining" [caption]="columnLabels.uploadDue" [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header>
                  </th>

                  <!-- <th *ngIf="!currentClassFilter" class="flush"> <table-header id = "slug" [caption] = "columnLabels.slug"      [ctrl] = "sessionsTable" [isSortEnabled]="true"></table-header> </th>--><!--ASSESSMENT-TYPE-->
                  <th class="flush" *ngIf="isPaymentModuleEnabled"> 
                    <!--Paid?--> 
                    <table-header 
                      id = "isPaid"  
                      [caption] = "columnLabels.isPaid"   
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="false"></table-header>
                    </th>
                  <th class="flush" *ngIf="isPaymentModuleEnabled"> 
                    <!--Payment Status--> 
                    <table-header 
                      id = "paymentStatus"  
                      [caption] = "columnLabels.paymentStatus"   
                      [ctrl] = "sessionsTable" [isSortEnabled]="true">
                    </table-header>
                  </th>
                  <th class="flush" *ngIf="!whitelabelService.isABED() && isShowTeacherNames"> 
                    <table-header 
                        id = "invigilator"  
                        [caption] = "columnLabels.invigilators"   
                        [ctrl] = "sessionsTable" [isSortEnabled]="true">
                    </table-header>
                  </th>
                  <th class="flush" *ngIf="whitelabelService.isABED()  && isShowTeacherNames">
                    <table-header 
                      id = "teacher"  
                      [caption] = "columnLabels.teacher"   
                      [ctrl] = "sessionsTable" [isSortEnabled]="true">
                    </table-header>
                  </th>
                  <th class="flush" *ngIf="whitelabelService.isABED()  && isShowTeacherNames"> 
                    <table-header 
                      id = "invigilators"  
                      [caption] = "columnLabels.invigilators"   
                      [ctrl] = "sessionsTable" [isSortEnabled]="true">
                    </table-header>
                  </th>
                  <th class="flush"> 
                    <!--Classroom--> 
                    <table-header 
                      id = "classCode" 
                      [caption]="getColHeaderClassroomSlug()"     
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true">
                    </table-header>
                  </th>
                  <th class="flush"> 
                    <!--Access Code--> 
                    <table-header 
                      id = "access_code" 
                      caption="Class Access Code"     
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true">
                    </table-header>
                  </th>
                  <th class="flush" *ngIf="false"> 
                    <!--Students--> 
                    <table-header 
                      id = "students" 
                      [caption] = "columnLabels.students"       
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"
                      [customSortFunction]="sortNumericallyByStudents">
                    </table-header>
                  </th>
  
                  <!-- <th class="flush" *ngIf="isShowAssessmentType" id="Assessment Types">
                    <table-header 
                      id = "isFieldTest"     
                      [caption] = "columnLabels.assessmentType"       
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"
                      [customSortFunction]="sortNumericallyByAssessmentType">
                    </table-header>
                  </th> -->
  
                  <th class="flush" *ngIf="isShowAssessmentType && !(selectedSessionType == SESSION_TYPE.REQ_UPLOADS)"> 
                    <table-header 
                      id = "slug" 
                      [caption] = "getAssessmentColumnCaption()"      
                      [ctrl] = "sessionsTable"  
                      [isSortEnabled]="true">
                    </table-header>
                  </th>
  
                  <th *ngIf="(selectedSessionType !== SESSION_TYPE.SUBMITTED) && (selectedSessionType != SESSION_TYPE.REQ_UPLOADS) && (selectedSessionType != SESSION_TYPE.CANCELLED)" class="flush" style="min-width: 15em;"> 
                    <table-header 
                      id = "startTimeUTC"       
                      caption = "Start Time"          
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"  
                      [disableFilter]="true">
                    </table-header><!--Times--> 
                  </th>
                  <th *ngIf="selectedSessionType == SESSION_TYPE.CANCELLED" class="flush"> 
                    <table-header 
                      id = "cancelled_on_raw"       
                      [caption] = "columnLabels.cancelledOn"          
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"  
                      [disableFilter]="true">
                    </table-header>
                  </th>
                  <th *ngIf="selectedSessionType == SESSION_TYPE.SUBMITTED" class="flush"> 
                    <table-header 
                      id = "closed_on_raw"       
                      [caption] = "columnLabels.submittedOn"          
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"  
                      [disableFilter]="true">
                    </table-header><!--Times--> 
                  </th>
                  <th class="flush" *ngIf="isG9OrOsslt()"> 
                    <table-header 
                      id = "submissions"       
                      [caption] = "columnLabels.submissions"          
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"  
                      [disableFilter]="true">
                    </table-header>
                  </th>
                  <th class="flush" *ngIf="isPrimaryOrJunior()"> 
                    <table-header 
                      id = "submissions_lang"       
                      [caption] = "columnLabels.submissions_lang"          
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"  
                      [disableFilter]="true">
                    </table-header>
                  </th>
                  <th class="flush" *ngIf="isPrimaryOrJunior()"> 
                    <table-header 
                      id = "submissions_math"        
                      [caption] = "columnLabels.submissions_math"          
                      [ctrl] = "sessionsTable" 
                      [isSortEnabled]="true"  
                      [disableFilter]="true">
                    </table-header>
                  </th>
                  <th *ngIf="selectedSessionType == SESSION_TYPE.REQ_UPLOADS" class="flush" > 
                    <table-header id = "uploadSheets" [caption] = "columnLabels.uploadSheets" [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header>
                  </th>

                  <th *ngIf="selectedSessionType !== SESSION_TYPE.SUBMITTED && selectedSessionType !== SESSION_TYPE.CANCELLED" class="flush"> 
                    <table-header 
                      id = "invigilate" [caption] = "getInvigilateCaption()" [ctrl] = "sessionsTable" [isSortEnabled]="true"  [disableFilter]="true"></table-header>
                  </th>
              </tr>
              <ng-container *ngFor="let session of sessionsTable.getCurrentPageData();">
                <tr *ngIf="!((selectedSessionType == SESSION_TYPE.REQ_UPLOADS) && !isShowCompletedUploads && (session.n_scans_received >= session.n_scans_expected))">
                    <td>
                        <table-row-selector [entry]="session" prop="__isSelected" (toggle)="checkSelection()"></table-row-selector>
                    </td>
                    <td *ngIf="(selectedSessionType !== SESSION_TYPE.SUBMITTED) && (selectedSessionType != SESSION_TYPE.REQ_UPLOADS)" class="status">
                        <div class="content">
                            <span class="fa fa-circle" [ngClass]="session.status">
                            </span>{{columnStatusLabel[session.status]}}
                        </div>
                    </td>
                    <td *ngIf="selectedSessionType == SESSION_TYPE.REQ_UPLOADS" class="has-text-centered">
                      {{session.n_scans_received || 0}} / {{session.n_scans_expected || 0}} scans
                    </td>
                    <td *ngIf="selectedSessionType == SESSION_TYPE.REQ_UPLOADS" class="has-text-centered" > 
                      <ng-container *ngIf="!isSessionUploadReadyToStart(session)">
                        Session Not Started
                      </ng-container>
                      <ng-container *ngIf="isSessionUploadReadyToStart(session)">
                        <ng-container *ngIf="isSessionUploadComplete(session)">
                          Complete
                        </ng-container>
                        <ng-container *ngIf="!isSessionUploadComplete(session)">
                          <ng-container>
                            <span *ngIf="calculateDateEndForUpload(session) && !isDateEndForUploadPassed(session)">
                              <tra slug="lbl_prefix_due"></tra> {{calculateDateEndForUpload(session)}}
                            </span>
                            <span *ngIf="isDateEndForUploadPassed(session)" class="has-text-danger">
                              <tra slug="lbl_past_due"></tra>
                            </span>
                          </ng-container>
                        </ng-container>
                      </ng-container>
                    </td>
  
                    <!-- <td *ngIf="!currentClassFilter"> {{ getSessionSlug(session.slug) }}</td> -->
                    <td *ngIf="isPaymentModuleEnabled">
                        <div class="space-between">
                            <div>{{ session.studentsPaid }}/{{session.students}}</div>
                            <!-- <div>{{session.isPaid}}</div> -->
                        </div>
                    </td>
                    <td *ngIf="isPaymentModuleEnabled">
                        <div>
                            <span *ngIf="session.paymentStatus == 'lbl_required'" class="fa fa-circle" style="color: red; font-size: 9px; margin-right: 7px;"></span>
                            <span><tra [slug]="session.paymentStatus"></tra></span>
                        </div>
                    </td>
                    <td *ngIf="whitelabelService.isABED() && isShowTeacherNames">
                        <div class="space-between">
                            <div>{{session.invigilator}}</div>
                        </div>
                    </td>
                    <td *ngIf="whitelabelService.isABED() && isShowTeacherNames">
                      <div class="space-between">
                          <div>{{session.invigilators}}</div>
                      </div>
                    </td>
                    <td> {{session.classCode}} </td>
                    <td class="has-text-centered"> {{session.access_code}} </td>
                    <td class="studentId" *ngIf="false"> 
                        {{session.students}}
                    </td>
                    <!-- <td *ngIf="whitelabelService.isABED() && isShowAssessmentType">
                      {{ getAssessmentType(session.isFieldTest) }}
                    </td> -->
    
                    <!-- && !(selectedSessionType == SESSION_TYPE.REQ_UPLOADS) -->
                    <td *ngIf="isShowAssessmentType  && !(selectedSessionType == SESSION_TYPE.REQ_UPLOADS)" >
                      {{ getAssessmentTitle(session) }}
                    </td>
  
                    <td *ngIf="(selectedSessionType !== SESSION_TYPE.SUBMITTED) && (selectedSessionType != SESSION_TYPE.REQ_UPLOADS) && (selectedSessionType != SESSION_TYPE.CANCELLED)">    
                        <div *ngIf="session.session_b && isG9OrOsslt()"> 
                            <div><span style="margin-right: 4px;">{{ session.session_a.slug }}:</span><span>{{ session.session_a.datetime_start }}</span></div>
                            <div><span style="margin-right: 4px;">{{ session.session_b.slug }}:</span><span>{{ session.session_b.datetime_start }}</span></div>
                        </div>
                        <div *ngIf="session.session_b && isPrimaryOrJunior()"> 
                            <ng-container *ngIf="isLanguageVisibleForFIClass(session.classroom_id)">
                                <div>
                                    <span style="margin-right: 4px;">{{ session.session_a.slug }}:</span>
                                    <span *ngIf="session.session_a.datetime_start">{{ session.session_a.datetime_start }}</span>
                                    <span *ngIf="!session.session_a.datetime_start">Not Scheduled</span>
                                </div>
                                <div *ngIf="session.session_a.datetime_end">
                                    <span style="margin-right: 4px;">{{ session.session_a.slug }}:</span>
                                    <span>{{ session.session_a.datetime_end }}</span>
                                </div>
                            </ng-container>
                            <div>
                                <span style="margin-right: 4px;">{{ session.session_b.slug }}:</span>
                                <span *ngIf="session.session_b.datetime_start">{{ session.session_b.datetime_start }}</span>
                                <span *ngIf="!session.session_b.datetime_start">Not Scheduled</span>
                            </div>
                            <div *ngIf="session.session_b.datetime_end">
                                <span style="margin-right: 4px;">{{ session.session_b.slug }}:</span>
                                <span>{{ session.session_b.datetime_end }}</span>
                            </div>
                        </div>
                        <div *ngIf="!session.session_b">
                            <div>{{ session.startTime }}</div> 
                        </div>
                    </td>
                    <td *ngIf="selectedSessionType == SESSION_TYPE.CANCELLED">
                      <div>{{session.cancelled_on}}</div>
                    </td>
                    <td *ngIf="selectedSessionType == SESSION_TYPE.SUBMITTED">
                        <div>{{ session.closed_on }}</div>
                    </td>
                    <td *ngIf="isG9OrOsslt()"> 
                        <div>{{ session.submissions }}</div>
                    </td>
                    <td *ngIf="isPrimaryOrJunior()"> 
                        <div>{{ session.submissions }}</div>
                    </td>
                    <td *ngIf="isPrimaryOrJunior()"> 
                        <div>{{ session.submissions_1 }}</div>
                    </td>
                    <td *ngIf="selectedSessionType == SESSION_TYPE.REQ_UPLOADS" class="has-text-centered"> 
                      <span *ngIf="!session.is_scan_session" class="tag"><tra slug="sa_lbl_no_resp_sheet_sess"></tra></span>
                      <button *ngIf="session.is_scan_session" style="margin-top: 0.5em;" class="button is-small is-primary" (click)="onClickInvigilate(session, true)" [disabled]="isUploadSheetsDisabled(session)">
                        <tra slug="lbl_bulk_upload"></tra>
                      </button>
                    </td>
  
                    <td *ngIf="selectedSessionType !== SESSION_TYPE.SUBMITTED && selectedSessionType !== SESSION_TYPE.CANCELLED" class="has-text-centered"> 
                        <button style="margin-top: 0.5em;" class="button is-small is-success" (click)="onClickInvigilate(session)">
                          <!-- <tra slug="lbl_school_admin_invigilate"></tra> -->
                           {{getInvigilateCaption()}}
                        </button>
                    </td>
                </tr>
              </ng-container>
          </table>
        </div>
    </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
    <div 
      [ngSwitch]="cModal().type" 
      class="modal-contents" 
      style="width:42em;">

        <div>
            <div *ngSwitchCase="SessionModal.NEW_SESSION">
              <!-- New Assessment Session-->
              <tra-md
                *ngIf="whitelabelService.isABED()"  
                class="new-assessment-session"
                slug="new_assessment_sessions_ABED">
              </tra-md>
              <sa-modal-session 
                *ngIf="!whitelabelService.isABED()" 
                [savePayload]="cmc()" 
                saveProp="payload" 
                (isDateValid)="setDateValidity($event)">
              </sa-modal-session>
              <view-new-session-modal 
                *ngIf="whitelabelService.isABED()" 
                [savePayload]="cmc()" 
                saveProp="payload">
              </view-new-session-modal>
            </div>
            <div *ngSwitchCase="SessionModal.EDIT_SESSION">
                <sa-modal-session 
                  [isEditing]="true" 
                  [isActive]="isActive" 
                  [isPartialActive]="isPartialActive"  
                  (isDateValid)="setDateValidity($event)" 
                  [sessions]="cmc().sessions" 
                  [savePayload]="cmc()" 
                  saveProp="payload">
                </sa-modal-session>
            </div>
            <div *ngSwitchCase="SessionModal.STUDENTS_SESSION">
                <sa-modal-student-count 
                  [studentList]="studentList">
                </sa-modal-student-count>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_OVERVIEW">
                <sa-modal-session-payment-overview 
                  [pageModal]="pageModal" 
                  [sessionList]="getSelectedSessions(true)" 
                  [schoolName]="schoolName" 
                  [assessmentName]="getAssessmentName(getSelectedSessions(true)[0].slug)" 
                  [administrationWindow]="getTestWindowViewText(currentTestWindow)" 
                  [currentClassFilter]="currentClassFilter" 
                  [currentTestWindow]="currentTestWindow" 
                  (onStripePayment)="onStripePayment($event)" 
                  (onAlternativePayment)="onAlternativePayment($event)">
                </sa-modal-session-payment-overview>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_STRIPE_OVERVIEW">
                <sa-modal-session-payment-stripe-overview 
                    [sessionList]="getSelectedSessions(true)" 
                    [schoolName]="schoolName" 
                    [assessmentName]="getAssessmentName(getSelectedSessions(true)[0].slug)" 
                    [administrationWindow]="getTestWindowViewText(currentTestWindow)" 
                    [currentClassFilter]="currentClassFilter" 
                    [totalCost]="cmc().totalCost" 
                    (onBack)="showSessionPaymentModal()">
                </sa-modal-session-payment-stripe-overview>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_ALTERNATIVE_OVERVIEW">
                <sa-modal-session-payment-alternative-overview 
                    [sessionList]="getSelectedSessions(true)" 
                    [schoolName]="schoolName" 
                    [assessmentName]="getAssessmentName(getSelectedSessions(true)[0].slug)" 
                    [administrationWindow]="getTestWindowViewText(currentTestWindow)" 
                    [currentClassFilter]="currentClassFilter"
                    [totalCost]="cmc().totalCost"
                    (onBack)="showSessionPaymentModal()"
                    (onCompletePaymentAlt)="onCompletePaymentAlt($event)">
                </sa-modal-session-payment-alternative-overview>
            </div>
            <div *ngSwitchCase="SessionModal.SESSION_PAYMENT_ALTERNATIVE_WARNING">
                <sa-modal-session-payment-alternative-warning [alternative_days_before_session]="alternative_days_before_session" (onBack)="showSessionPaymentModal()"></sa-modal-session-payment-alternative-warning>
            </div>
            <div *ngSwitchCase="PurchaseModal.PURCHASE_REFUND_POLICY">
                <sa-modal-purchase-refund-overview></sa-modal-purchase-refund-overview>
            </div>
        </div>
        <modal-footer *ngSwitchCase="SessionModal.NEW_SESSION" [pageModal]="pageModal"></modal-footer>
        <modal-footer *ngSwitchCase="SessionModal.EDIT_SESSION" [pageModal]="pageModal"></modal-footer>
        <modal-footer *ngSwitchCase="SessionModal.STUDENTS_SESSION" [pageModal]="pageModal"></modal-footer>
        <modal-footer *ngSwitchCase="PurchaseModal.PURCHASE_REFUND_POLICY" 
            class="modal-refund-policy" 
            [pageModal]="pageModal" 
            [confirmButton]="false" 
            [closeMessage]="'btn_close'">
        </modal-footer>
    </div>
</div>