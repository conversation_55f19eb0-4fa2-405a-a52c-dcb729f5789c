import { Component, ElementRef, Input, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { AbstractControl, FormControl } from '@angular/forms';
import { bindFormControls } from '../../../services/data-bind';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { 
  IDgIntElementDnDTable, IDgIntElementDnDTableCell,
  isPossibleToAlignHomeLabelToFirstRow,
  resolveDnDTableTargets,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-table';
import { 
  IDgIntElementDnDTarget, DND_UNUSED_ID,
  isUnusedId,
  IDgIntElementDnDAnswerSet,
  generateDefaultAnswerSet,
  DND_OPTION_TEXT_MODE,
  IDgIntElementDnDOption,
  homeLabelPositionOptions,
  HOME_LABEL_POSITION,
  unusedId,
  diagramLabelPositionOptions,
  dndIsFilledModeOptions,
  dndIsFilledModeCaptionMap,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { ElementType } from 'src/app/ui-testrunner/models';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { contentJustifyOptions, flattenArray } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { IInteractiveDiagramHiddenConfigs } from '../../element-config-interactive-diagram.component';
import { SpecialKeyboardService } from 'src/app/ui-item-maker/special-keyboard.service';
import { DgAlignment } from 'src/assets/lib/diagramatics/config';
import { setTextFocusForDnd } from '../../common/dg-int-dnd-util/util';


const FOCUS_TIMEOUT = 100;

@Component({
  selector: 'dg-int-config-dnd-table',
  templateUrl: './dg-int-config-dnd-table.component.html',
  styleUrls: ['./dg-int-config-dnd-table.component.scss']
})
export class ElementConfigDgIntDndTableComponent implements OnInit {
  @Input() element: IDgIntElementDnDTable;
  @Input() parentElement: IContentElementInteractiveDiagram;
  @Input() hiddenConfigs?: IInteractiveDiagramHiddenConfigs;
  @ViewChildren('cellText') cellText: QueryList<ElementRef>;
  @ViewChildren('cellTextWithImg') cellTextWithImg: QueryList<ElementRef>;
  @ViewChild('label') label: ElementRef;
  @ViewChild('tableDOM') tableDOM: ElementRef;

  twiddleTargets = new TwiddleState(false);
  twiddleAnswers = new TwiddleState(false);
  twiddleHome = new TwiddleState(false);
  twiddleStyle = new TwiddleState(false);
  twiddleConfig = new TwiddleState(false);
  defaultAnswerSet: IDgIntElementDnDAnswerSet;
  targetIdList: string[] = [];
  optionIdList: string[] = [];
  homeIdList: string[] = [];
  sortedOptionIdList: string[] = [];
  idToLabelMap = new Map<string, string>();
  idToUrlMap = new Map<string, string>();
  idIsMathSet = new Set<string>();
  nonUniqueOptionTexts: string[] = [];
  DND_OPTION_TEXT_MODE = DND_OPTION_TEXT_MODE;
  
  public alignments = [
    {id:'left', icon:'fa-align-left'},
    {id:'center', icon:'fa-align-center'},
    {id:'right', icon:'fa-align-right'},
  ]
  
  isToggleButtonHovered = false;
  isReordering = false;
  
  homeLabelPositionOptions = homeLabelPositionOptions;
  diagramLabelPositionOptions = diagramLabelPositionOptions;
  contentJustifyOptions = contentJustifyOptions;
  dndIsFilledModeOptions = dndIsFilledModeOptions;
  dndIsFilledCaptionMap = dndIsFilledModeCaptionMap;
  isShowAnswerForm = new FormControl(false);
  labelForm = new FormControl('');
  isUsingReusableDraggableForm: FormControl;
  
  public tableForm = [];
  public dndTargetOptionsForm : {label:string, content:FormControl[]}[] = [];
  focusedRow : number|undefined = undefined;
  focusedCol : number|undefined = undefined;
  focusTimer = null;
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private sharedObjectMap: SharedObjectMapService,
    private specialKeyboard: SpecialKeyboardService
  ){}
  
  ngOnInit(): void {
    this.validateConfig();
    
    this.isShowAnswerForm.valueChanges.subscribe(() => {
      if (this.isShowAnswerForm.value) {
        this.element._isShowAnswer = true;
      } else {
        // we don't need to store this value
        delete this.element._isShowAnswer;
      }
    });
    
    this.isUsingReusableDraggableForm = new FormControl(this.element.config.isUsingReusableDraggable);
    this.isUsingReusableDraggableForm.valueChanges.subscribe(() => {
      if (this.isUsingReusableDraggableForm.value) {
        const confirm = window.confirm('Are you sure you want to enable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(false, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = true;
      } else {
        const confirm = window.confirm('Are you sure you want to disable reusable draggable?');
        if (!confirm) {
          this.isUsingReusableDraggableForm.setValue(true, {emitEvent: false});
          return;
        }
        this.element.config.isUsingReusableDraggable = false;
      }
      this.validateConfig();
    });

    bindFormControls(this.element.label, [
      {f: this.labelForm, p: 'text'}
    ]);
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  
  initTableForm() {
    this.tableForm = [];
    this.element.table.forEach(row =>{
      const inputRow = [];
      row.forEach(cell => {
        const inputCell = this.initCellForm(cell);
        inputRow.push(inputCell)
      });
      this.tableForm.push(inputRow);
    })
  }
  
  initCellForm(cell : IDgIntElementDnDTableCell){
    const input = new FormControl(cell.text);
    
    input.valueChanges.subscribe(obs => {
      cell.text = input.value;
      this.validateConfig();
    })
    return input;
  }
  
  initDndTargetOptionsForm(){
    this.dndTargetOptionsForm = [];
    this.element.dndTargets.forEach((target) => {
      let optionsForm = [];
      target.content.forEach((option) => {
        const input = new FormControl(option.value);
        input.valueChanges.subscribe(obs => {
          option.value = input.value;
          this.validateConfig();
        });
        optionsForm.push(input);
      });
      this.dndTargetOptionsForm.push({
        label: target.label,
        content: optionsForm
      })
    });
  }
  
  requestResetState() {
    this.sharedObjectMap.set(this.element, <IDgIntSharedObject>{ _resetState: true });
  }
  
  elementEditDeleteRow(rowIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this row?");
    if (!confirm) return;
    this.element.table.splice(rowIndex, 1);
    this.validateConfig();
  }
  
  elementEditDeleteColumn(colIndex: number){
    const confirm = window.confirm("Are you sure you want to delete this column?");
    if (!confirm) return;
    this.element.table.forEach(row => {
      row.splice(colIndex, 1);
    });
    this.validateConfig();
  }
  
  elementEditAddRow(index?: number){
    const columnAlignment = this.element.table[0]?.map(cell => cell.alignment) ?? [];
    const ncols = this.element.table[0]?.length ?? 2;
    const row : IDgIntElementDnDTableCell[] = Array.from({length: ncols}).map((_,i) => {
      return { text: '', alignment: columnAlignment[i] }
    });
    this.element.table.splice(index ?? this.element.table.length, 0, row);
    this.validateConfig();
  }
  
  elementEditAddColumn(index?: number){
    this.element.table.forEach(row => {
      row.splice(index ?? row.length, 0, { text: '' });
    });
    this.validateConfig();
  }
  
  buttonAddRow(){
    this.elementEditAddRow();
  }
  buttonAddColumn(){
    this.elementEditAddColumn();
  }
  
  elementEditToggleTarget(rowIndex:number, colIndex:number){
    const cell = this.element.table[rowIndex][colIndex];
    if (!cell.isTarget) {
      cell.isTarget = true;
    } 
    else {
      const confirm = window.confirm("Are you sure you want to convert this cell to text?");
      if (!confirm) return;
      delete cell.isTarget;
    }
    this.validateConfig();
  }
  elementEditToggleMath(rowIndex:number, colIndex:number){
    const cell = this.element.table[rowIndex][colIndex];
    cell.isMath = !cell.isMath;
    this.validateConfig();
  }
  
  elementEditInitImage(rowIndex:number, colIndex:number){
    const cell = this.element.table[rowIndex][colIndex];
    cell.image = createDefaultElement(ElementType.IMAGE);
    cell.image.scale = 25;
    this.rerender();
  }
  elementEditRemoveImage(rowIndex:number, colIndex:number){
    const cell = this.element.table[rowIndex][colIndex];
    delete cell.image;
    delete cell.imageLabelPosition;
    this.rerender();
  }
  
  elementEditToggleTargetOnFocusedCell(){
    if (!this.isFocusedCellExist()) return;
    this.elementEditToggleTarget(this.focusedRow!, this.focusedCol!);
  }
  elementEditToggleMathOnFocusedCell(){
    if (!this.isFocusedCellExist()) return;
    this.elementEditToggleMath(this.focusedRow!, this.focusedCol!);
  }

  elementEditSetColumnAlignment(colIndex: number, alignment: string) {
    const dgAlignment = `center-${alignment}` // always vertically centered for now
    this.element.table.forEach(row => {
      if (row[colIndex] != undefined) {
        row[colIndex].alignment = dgAlignment as DgAlignment;
      }
    })
    this.rerender();
  }
  
  setUseImage(value : boolean) {
    if (value == this.element.config.isUseImage) return;
    if (value){
      const confirm = window.confirm("Are you sure you want to change to image mode?");
      if (!confirm) return;
      this.element.config.isUseImage = true;
    } else {
      const confirm = window.confirm("Are you sure you want to change to text only mode? (this will remove your images)");
      if (!confirm) return;
      this.element.config.isUseImage = false;
      // delete all images
      for (let target of this.element.dndTargets){
        for (let option of target.content){
          delete option.image;
          delete option.imageLabelPosition;
        }
      }
      for (let row of this.element.table) {
        for (let cell of row) {
          delete cell.image;
          delete cell.imageLabelPosition;
        }
      }
      for (let target of this.element.dndTargets){
        for (let c of target.content){
          delete c.image;
          delete c.imageLabelPosition;
        }
      }
    }
    this.validateConfig();
  }
  
  isUseImage() : boolean {
    return this.element.config.isUseImage ?? false;
  }
  
  isTargetCell(rowIndex: number, colIndex: number) {
    return this.element.table[rowIndex][colIndex].isTarget ?? false;
  }
  isImageCell(rowIndex: number, colIndex: number) : boolean {
    const cell = this.element.table[rowIndex][colIndex];
    return Boolean(cell.image);
  }
  isMathCell(rowIndex: number, colIndex: number) : boolean {
    const cell = this.element.table[rowIndex][colIndex];
    return cell.isMath;
  }
  
  isOptionsContainsError(): boolean {
    return this.nonUniqueOptionTexts.length > 0;
  }
  isAnswersContainsError(): boolean { 
    for (let answerSet of this.element.altAnswers ?? []){
      for (let targetData of answerSet){
        if (targetData.optionIds.length == 0) return !this.element.config.isAllowEmptyTarget;
        for (let optionId of targetData.optionIds){
          if (this.isInvalidOptionId(optionId)) return true;
        }
      }
    }
    return false;
  }
  isInvalidOptionId(optionId: string): boolean {
    if (!optionId) return !this.element.config.isAllowEmptyTarget;
    return !this.optionIdList.includes(optionId);
  }
  
  isFocusedCellExist() {
    return this.focusedRow !== undefined && this.focusedCol !== undefined;
  }
  isFocusedCellCanToggleMath() {
    if (!this.isFocusedCellExist()) return false;
    const focusedCell = this.element.table[this.focusedRow!][this.focusedCol!];
    return !focusedCell.isTarget;
  }
  isFocusedCellCanToggleTarget() {
    if (!this.isFocusedCellExist()) return false;
    return !this.isImageCell(this.focusedRow!, this.focusedCol!) && !this.isMathCell(this.focusedRow!, this.focusedCol!);
  }

  setFocusedCell(rowIndex?: number, colIndex? : number, cell?: FormControl | AbstractControl, htmlElement?: string) {
    clearTimeout(this.focusTimer);
    this.focusedRow = rowIndex;
    this.focusedCol = colIndex;
    if (cell && htmlElement) {
      this.setTextFocus(cell, htmlElement);
    }
  }

    /*
    Create a mapping between a key and a text element reference.
    Return the requested text element reference.
  */
  getTextElementReference(text_element: string) {
    const elements = {
      label: this.label
    }

    let cellElements = this.cellText;
    let prefix = 'cell_';
    if (text_element.startsWith('imgCell')) {
      cellElements = this.cellTextWithImg;
      prefix = 'imgCell_';
    }

    let cellCount = 0;
    this.element.table.forEach((row, i) => {
      row.forEach((cell, j) => {
        elements[`${prefix}${i}_${j}`] = cellElements.toArray()[cellCount];
        cellCount += 1;
      });
    });

    return elements[text_element];
  }
  
  setTextFocus(cell: FormControl | AbstractControl, htmlElement: string) {
    setTextFocusForDnd(
      this.specialKeyboard,
      this.element,
      this.getTextElementReference(htmlElement).nativeElement,
      cell
    );
  }

  resetFocusedCell() {
    clearTimeout(this.focusTimer);
    this.focusTimer = setTimeout(() => {
      if (!this.isToggleButtonHovered) {
        this.setFocusedCell(undefined, undefined);
      } else {
        this.resetFocusedCell();
      }
    }, FOCUS_TIMEOUT);
  }
  setToggleButtonHoverState(state: boolean) {
    this.isToggleButtonHovered = state;
  }
  
  getCellImageElement(rowIndex: number, colIndex: number) : IContentElementImage {
    const cell = this.element.table[rowIndex][colIndex];
    if (cell?.image == undefined) {
      this.elementEditInitImage(rowIndex, colIndex);
    }
    return cell.image;
  }
  getCellObject(rowIndex: number, colIndex: number) : IDgIntElementDnDTableCell {
    const cell = this.element.table[rowIndex][colIndex];
    return cell;
  }
  
  validateConfig() {
    this.resolveData();
    this.updateForms();
    this.rerender();
  }
  
  rerender() {
    this.parentElement._changeCounter += 1;
  }
  
  updateForms() {
    this.initTableForm();
    this.initDndTargetOptionsForm();
    this.updateIdToLabelMap();
    this.updateSortedOptionIdList();
    this.updateDefaultAnswerSet();
    this.updateNonUniquieOptionLabel();
  }
  resolveData() {
    resolveDnDTableTargets(this.element);
  }
  
  updateDefaultAnswerSet() {
    this.defaultAnswerSet = generateDefaultAnswerSet(this.element.dndTargets);
  }
  
  updateNonUniquieOptionLabel(){
    const options = flattenArray(this.element.dndTargets.map(t => t.content));
    const optionTexts = options.filter(o => !o.image?.url).map(o => o.value);
    this.nonUniqueOptionTexts = optionTexts.filter((item, index) => optionTexts.indexOf(item) != index)
      .filter(x => x);
  }
  updateSortedOptionIdList() {
    this.sortedOptionIdList = flattenArray(this.element.homeConfig.element);
  }
  updateIdToLabelMap() {
    this.targetIdList = [];
    this.optionIdList = [];
    this.idToLabelMap.clear();
    this.idIsMathSet.clear();
    for (let target of this.element.dndTargets){
      this.idToLabelMap.set(target.id, target.label);
      if (!isUnusedId(target.label)) this.targetIdList.push(target.id);
      for (let option of target.content){
        this.optionIdList.push(option.id);
        this.idToLabelMap.set(option.id, option.value);
        this.idToUrlMap.set(option.id, option.image?.url);
        if (option.textMode == DND_OPTION_TEXT_MODE.MATH) this.idIsMathSet.add(option.id);
      }
    }
    this.homeIdList = this.element.homeConfig.element.map((_,i) => unusedId(i));
  }
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  getOptionImageURLFromId(id: string) : string {
    return this.idToUrlMap.get(id);
  }
  
  isUsingReusableDraggable() {
    return this.element.config.isUsingReusableDraggable ?? false;
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  isShowConfigPreview(){
    return true;
  }
  
  startReordering() {
    this.isReordering = true;
    requestAnimationFrame(() => {
      this.calculateTableDOMSize();
    })
  }
  endReordering() {
    this.isReordering = false;
  }
  
  tableDOMRowSizes = [];
  tableDOMColumnSizes = [];
  calculateTableDOMSize() {
    const tableDOM: HTMLTableElement = this.tableDOM.nativeElement;
    this.tableDOMRowSizes = [];
    this.tableDOMColumnSizes = [];
    for (let i = 0; i < tableDOM.rows.length; i++) {
      const row = tableDOM.rows[i];
      this.tableDOMRowSizes.push(row.clientHeight);
    }
    for (let i = 0; i < tableDOM.rows[0].cells.length; i++) {
      const cell = tableDOM.rows[0].cells[i];
      this.tableDOMColumnSizes.push(cell.clientWidth);
    }
  }
  
  dropRow(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.element.table, event.previousIndex, event.currentIndex);
    requestAnimationFrame(() => {
      this.calculateTableDOMSize();
    })
    this.validateConfig();
  }
  dropColumn(event: CdkDragDrop<string[]>) {
    for (let row of this.element.table) {
      moveItemInArray(row, event.previousIndex, event.currentIndex);
    }
    this.validateConfig();
  }
  
  get isPossibleToAlignHomeLabelToFirstRow(): boolean {
    return isPossibleToAlignHomeLabelToFirstRow(this.element);
  }
}
