export interface IScorerSummary {
  assignedItems: IAssignedItem[],
  scorerDashboardDocs: IDashboardDoc[]
}

export interface IDashboardDoc {
  title: any,
  link_slug: string
}
export interface IAssignedItem {
  id: number,
  name: string,
  components: IAssignedItemComponent[],
  isAvailable:boolean,
  dateTimeAssigned: string,
  numBatchesAvail: number, // int
  numBatchesClaimed: number, // int
  numBatchesScored: number,
  dateTimeBatchesDue?: string,
  __desiredClaimQuantity?: number, // int
  is_revoked?: number,
}
export interface IAssignedItemComponent {
  id: number,
  order: number,
  componentType: AssignedItemComponentType,
  caption: string,
  status: AssignedItemStatus,
  allowRevisit?:boolean,
  isPassFail?:boolean,
  stats?: {
    caption:string,
  }[]
}
export enum AssignedItemStatus {
  PENDING = 'PENDING',
  NOT_AVAILABLE = 'NOT_AVAILABLE',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}
export enum AssignedItemComponentType {
  TRAINING_MATERIALS = 'TRAINING_MATERIALS',
  PRACTICE = 'PRACTICE',
  QUALIFYING = 'QUALIFYING',
  SCORING = 'SCORING',
}
export const getTrainingTypeCaption = (trainingType:AssignedItemComponentType) => {
  switch (trainingType){
    case AssignedItemComponentType.TRAINING_MATERIALS: return 'Training Materials';
    case AssignedItemComponentType.PRACTICE: return 'Practice Test';
    case AssignedItemComponentType.QUALIFYING:  return 'Qualifying Test';
  }
}
