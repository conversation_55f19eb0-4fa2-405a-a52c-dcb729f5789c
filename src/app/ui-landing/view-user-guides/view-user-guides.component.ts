import { Component, OnInit } from "@angular/core";
import { BreadcrumbsService } from "../../core/breadcrumbs.service";
import { Router, ActivatedRoute } from "@angular/router";
import { LangService } from "../../core/lang.service";
import { AuthService } from "../../api/auth.service";
import { LoginGuardService } from "../../api/login-guard.service";
import { RoutesService } from "src/app/api/routes.service";
import { WhitelabelService } from "../../domain/whitelabel.service";
import { PageModalController, PageModalService } from "src/app/ui-partial/page-modal.service";

enum IAssetTypes{
  VIDEO= 'Video',
  LINK = 'Link'
}
@Component({
  selector: 'view-user-guides',
  templateUrl: './view-user-guides.component.html',
  styleUrls: ['./view-user-guides.component.scss']
})
export class ViewUserGuidesComponent implements OnInit {

  AssetTypes = IAssetTypes;
  constructor(
    private breadcrumbsService: BreadcrumbsService, 
    private router: Router, 
    private route: ActivatedRoute,
    private auth: AuthService, 
    public lang: LangService, 
    public whiteLabelService: WhitelabelService,
    private pageModalService: PageModalService,
  ) {}

  breadcrumb = [];
  userGuides = [];
  sectionModels = {};
  pageModal: PageModalController;

  ngOnInit(): void {
    this.pageModal = this.pageModalService.defineNewPageModal();
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(this.lang.tra("lbl_login"), `/${this.lang.c()}/login-router-st`), 
      this.breadcrumbsService._CURRENT(this.lang.tra("title_help_abed"), this.router.url)
    ];
    this.initKnowledgeBase()
  }

  async initKnowledgeBase(){
    const records = await this.auth.unauthenticatedGet('public/landing/knowledge-base')
    const lang = this.lang.c()
    const defaultLang = 'en';
    const getVal = (data: any) => {
      let translations = {};
      if (typeof data === 'string') {
        translations = JSON.parse(data || '{}');
      } else if (typeof data === 'object' && data !== null) {
        translations = data;
      }
      return translations[lang] || translations[defaultLang];
    }

    this.userGuides = (<any>records).map(dbRecord =>{
      return {
        section: getVal(dbRecord.section),
        caption: getVal(dbRecord.caption),
        file_url: getVal(dbRecord.file_url),
        asset_type: dbRecord.asset_type,
        orders: dbRecord.orders
      }
    })

    // Initial state of the sections. Expanded if the query param is set to that section
    const queryParams = this.route.snapshot.queryParams;
    for(let section of this.guideSections){
      this.sectionModels[section] = queryParams["expanded"] == section;
    }
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  introVideoModalStart(file_url) {
    const config = {file_url: file_url}
    this.pageModal.newModal({
      type: "DISPLAY_VIDEO",
      config,
      confirmationCaption: this.lang.tra("abed_dismiss"), 
      isProceedOnly: true,
      finish: () => {this.pageModal.closeModal()},
    })
  }

  getUserGuideSubtitleSlug(){
    return this.whiteLabelService.isABED() ? 'txt_sub_help_abed_ABED' : 'txt_sub_help_abed';
  }

  get guideSections() {
    const sections = this.userGuides.map(guide => guide.section).sort()
    return [...new Set(sections)]
  }

  userGuidesBySection(section: string, type = this.AssetTypes.LINK) {
    const userGuides = this.userGuides
      .filter(guide => guide.section == section && guide.asset_type == type) 
      .sort((a, b) => (a.orders - b.orders))

    return userGuides
  }

  /** Toggle collapsed state for all sections */
  collapseAll() {
    const first = this.sectionModels[Object.keys(this.sectionModels)[0]];
    for(let section in this.sectionModels){
      this.sectionModels[section] = !first;
    }
  }
  isABED() {
    return this.whiteLabelService.isABED();
  }
}