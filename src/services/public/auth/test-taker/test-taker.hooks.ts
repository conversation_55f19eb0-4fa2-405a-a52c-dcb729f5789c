import isRegEmailUnique from "../../../../hooks/is-reg-email-unique";

const { disallow, required } = require('feathers-hooks-common');

export default {
  before: {
    all: [],
    find: [],
    get: [],
    create: [
      // required('collegeApplicationNumber'),
      required('firstName'),
      required('lastName'),
      required('email'),
      required('password'),
      required('langCode'),
      required('domain'),
      isRegEmailUnique(),
    ],
    update: [],
    patch: [],
    remove: []
  },

  after: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  },

  error: {
    all: [],
    find: [],
    get: [],
    create: [],
    update: [],
    patch: [],
    remove: []
  }
};
