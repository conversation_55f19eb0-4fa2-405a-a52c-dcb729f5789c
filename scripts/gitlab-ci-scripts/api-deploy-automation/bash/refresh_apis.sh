#!/bin/bash

# Description:  Given an ASG name, this script will refresh all instances running in the ASG through the following:
#
#               1. Query the ASG for any instances that are running > Store these in an array
#               2. Double the Desired Capacity of the ASG so that new instances are launched
#               3. Once new instances are InService and Healthy, allow them to warm up and download the latest API build
#               4. Enable Scale-In Protection on the new API instances
#               4. The Desired Capacity of the ASG is reset back to what it was
#               5. Once old instances are in the Terminating state, Scale-In protection is disabled on the new instances
#
#               If an ASG had an initial Desired Capacity of 0, this is set to 1.
#
#               During development, of all methods tested for redeploying APIs after updating API builds on S3, this
#               was the fastest. When testing the Instance Refresh feature on AWS for Autoscaling Groups it was
#               very cumbersome to deal with as it requries detaching Standby instances prior to the refresh, and we
#               want Standby instances to be available for testing API builds on. The Instance Refresh feature also was
#               extremely slow, taking over 12 minutes to refresh 2 live APIs.
#
#               This method refreshes instances in ~3 - 5 minutes

# 2023/05/04: The Instance Refresh feature on AWS for ASG now allows ignoring of Standby
# instances when performing the refresh. As well, the settings for the refresh can be
# adjusted to aggressively terminate and restart all instances, taking 5-7 minutes
# on 2 live APIs.
#
# The key reason to use this method instead of the previous scale up/scale down is because
# the scaling process has a tendency to fail at the deploy step, which may not be caught
# immediately. Furthermore, the reason for the deploy step failure is not immediately
# clear when examining the pipeline.
#
# The production settings for instance refresh are enabled through Gitlab CI/CD
# Settings for vea-api:
#
# CI_API_IS_PROD = true
#
# This will require a minimum healthy percentage of 75%, slowing the instance refresh
# process but ensuring the vea client remains functional during that time.

# Defined named parameters that can optionally be passed from commandline to this script
api_asg_nm=${CI_API_ASG_NM:-""}
  # (required) Name of AWS ASG to query for running instances; default value is CI_API_ASG_NM

while [ $# -gt 0 ]; do

  if [[ $1 == *"--"* ]]; then
    param="${1/--/}"
    declare $param="$2"
    # echo $1 $2 // Optional to see the parameter:value result
  fi

  shift
done

# Exit script if any mandatory args are not defined
if [[ -z $api_asg_nm ]]; then

  echo -e "\nOne or more arguments missing when calling script! Exiting."
  exit 1

fi

echo -e "\nExecuting instance refresh for the $api_asg_nm ASG..."

preferences="{\"MinHealthyPercentage\": 100,\"MaxHealthyPercentage\": 200,\"InstanceWarmup\": 0,\"SkipMatching\": false,\"ScaleInProtectedInstances\": \"Ignore\",\"StandbyInstances\": \"Ignore\"}"
echo -e "\nInstance refresh preferences to use: $preferences"

refreshID=`aws autoscaling start-instance-refresh \
  --auto-scaling-group-name $api_asg_nm \
  --strategy 'Rolling' \
  --preferences "$preferences" \
  --output text`

# If refreshID is empty, the instance refresh did not execute correctly - fail pipeline
if [[ -z $refreshID ]]; then
  echo -e "\nInstance refresh failed! Exiting."
  exit 1
fi

instance_refresh_pref=$(aws autoscaling describe-instance-refreshes --auto-scaling-group-name $api_asg_nm --instance-refresh-ids $refreshID --query "InstanceRefreshes[0].Preferences")
echo -e "\n$api_asg_nm ASG initiated instance refresh id: $refreshID\nInstance refresh config: $instance_refresh_pref"

# Wait for instance refresh status to update
while true;
do
    instance_refresh=$(aws autoscaling describe-instance-refreshes --auto-scaling-group-name $api_asg_nm --instance-refresh-ids $refreshID --query "InstanceRefreshes[0]")
    instance_refresh_status=$(echo $instance_refresh | jq -r '.Status')
    case $instance_refresh_status in
      # Exit with code=1 if instance refresh failed
      "Failed" | "Cancelled" | "RollbackFailed" | "RollbackSuccessful")
        status_reason=$(echo $instance_refresh | jq -r '.StatusReason')
        echo -e "\nInstance Refresh $instance_refresh_status: $status_reason. \nExiting..."
        exit 1
        ;;
      # Exit do-while loop if instance refresh successful
      "Successful")
        refresh_endtime=$(echo $instance_refresh | jq -r '.EndTime')
        echo -e "\nInstance refresh completed at $refresh_endtime."
        break
      ;;
      # If instance refresh is still ongoing, show status
      *)
        refresh_progress=$(echo $instance_refresh | jq -r '.PercentageComplete')
        echo -e "Checking if instance refresh has completed (Status: $instance_refresh_status, Progress: $refresh_progress%)"
      ;;
    esac
    sleep 10
done

# 2023/05/04: Starting here, we do not need the below anymore; see note above

# # Store current Desired Capacity setting of ASG, this will be doubled for the instance refresh

# asg_desired_capacity=$(echo $(aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names $api_asg_nm \
# --query "AutoScalingGroups[*].DesiredCapacity" --output text) | xargs)

# if [[ $asg_desired_capacity == 0 ]]; then

#   new_desired_capacity=1

# else

#   new_desired_capacity=$((asg_desired_capacity * 2))

# fi

# echo -e "\nCurrent ASG desired capacity is $asg_desired_capacity"
# echo "Capacity will be increased to $new_desired_capacity for instance refresh"

# # Find any existing instances in the ASG, store their InstanceIDs and output these to console

# live_instances_found="false"

# echo -e "\nQuerying $api_asg_nm for existing InService API instances..."

# live_asg_instance_ids=$(echo $(aws autoscaling describe-auto-scaling-groups \
# --auto-scaling-group-names "$api_asg_nm" \
# --query "AutoScalingGroups[*].Instances[?LifecycleState == 'InService'].InstanceId" \
# --output text) | xargs)

# read -r -a live_asg_instance_ids <<< "$live_asg_instance_ids"   # Converts the return output to an array

# if [[ $live_asg_instance_ids != "" ]]; then

#   live_instances_found="true"
#   echo -e "\nThe following live instances were found: \n"

#   for instance_id in "${live_asg_instance_ids[@]}"; do

#     echo "$instance_id"

#   done

# fi

# # Double ASG count to launch new EC2 instances

# echo -e "\nUpdating the $api_asg_nm ASG from $asg_desired_capacity to $new_desired_capacity instances...\n"

# aws autoscaling update-auto-scaling-group \
# --auto-scaling-group-name $api_asg_nm \
# --desired-capacity $new_desired_capacity

# # Find new Instance IDs by waiting for the ASG to launch new instances and put them in a "Pending" state
# # AWS launches all the instances at once so we can capture all new instances in a single command

# new_instances_launched=""
# timeout=500

# echo -e "\nWaiting until new instances are launched. Timing out after $timeout seconds..."

# while [[ $new_instances_launched == "" ]]; do

#   new_instances_launched=$(echo $(aws autoscaling describe-auto-scaling-groups \
#   --auto-scaling-group-names "$api_asg_nm" \
#   --query "AutoScalingGroups[*].Instances[?LifecycleState == 'Pending'].InstanceId" \
#   --output text) | xargs)

#   if [[ $new_instances_launched != "" ]]; then

#     read -r -a new_instance_ids <<< "$new_instances_launched" # Converts the return output to an array

#     echo -e "\nNew API instances deployed. Here are their IDs: \n"

#     for instance_id in "${new_instance_ids[@]}"; do

#       echo "$instance_id"

#     done

#   else

#     sleep 5
#     timeout=$((timeout - 5))

#     if [ $timeout == 0 ]; then

#       echo -e "\nTimeout while awaiting the new API instances to launch. Exiting"
#       exit 1

#     fi

#   fi

# done

# # Allow new instances to reach InService, Healthy, and Protected states before proceeding

# new_instances_ready="false"

# # Query warmup time given to instances in the scaling group via the Lifecycle hook configured on the ASG

# warmup=$(echo $(aws autoscaling describe-lifecycle-hooks \
#       --auto-scaling-group-name "$api_asg_nm" \
#       --query "LifecycleHooks[*].HeartbeatTimeout" \
#       --output text) | xargs)

# if [[ $warmup == "" ]]; then

#   # If no lifecycle hook was found output warning that instances will receive traffic immediately

#   echo -e "\nWARNING: No Lifecycle Hook configuration found for $api_asg_nm ASG."
#   echo -e "\nAPIs will be deployed directly to InService and receive traffic immediately."
#   echo -e "\nThis may result in client errors"

# else

#   echo -e "\nThe $api_asg_nm has a Lifecycle Hook configured with a $warmup second Heartbeat timeout."
#   echo -e "\nAPIs will take roughly this long to deploy and reach the InService and Healthy states"

# fi

# echo -e "\nWaiting until new instances are InService, and Healthy."

# while [[ $new_instances_ready == "false" ]]; do

#   for instance_id in "${new_instance_ids[@]}"; do

#     instance_ready='false'
#     timeout=500
#     echo -e "\nWaiting for $instance_id to reach Healthy and Inservice state. Timing out after $timeout seconds..."

#     while [[ $instance_ready == 'false' ]]; do

#       instance_state=$(echo $(aws autoscaling describe-auto-scaling-groups \
#       --auto-scaling-group-names "$api_asg_nm" \
#       --query "AutoScalingGroups[*].Instances[?InstanceId == '$instance_id'].LifecycleState" \
#       --output text) | xargs)

#       if [[ $instance_state == 'InService' ]]; then

#         echo -e "\n$instance_id is InService and is Healthy"
#         instance_ready='true'

#       elif [[ $instance_state == 'Terminating' ]]; then

#         echo -e "\nAn unknown error occurred. $instance_id failed to deploy and is in the Terminating state. Exiting."
#         echo -e "\nYou can retry the pipeline but there may be other issues preventing the deployment (failed build, AWS issue, etc.)"
#         exit 1

#       else

#         sleep 5
#         timeout=$((timeout - 5))

#         if [ $timeout == 0 ]; then

#           echo -e "\nTimeout while awaiting $instance_id to reach InService and Healthy statuses. Exiting."
#           echo -e "\nYou can retry the pipeline but there may be other issues preventing the deployment (failed build, AWS issue, etc.)"
#           exit 1

#         fi

#       fi

#     done

#   done

#   echo -e "\nAll new instances are InService, and Healthy."
#   new_instances_ready="true"

# done

# # If there were any existing APIs running, enable scale in protection on the new instances, return the ASG desired count
# # back to what it was, and then disable scale in protection on the new instances

# if [ $live_instances_found == "true" ]; then

#   # Enable scale in protection on the new instances

#   for instance_id in "${new_instance_ids[@]}"; do

#     echo -e "\nEnabling scale-in protection for instance $instance_id..."

#     aws autoscaling set-instance-protection \
#     --instance-ids "$instance_id" \
#     --auto-scaling-group-name "$api_asg_nm" \
#     --protected-from-scale-in

#   done

#   sleep 5

#   # Set ASG Desired Capacity count back to it's original number to terminate the old API instances

#   echo -e "\nUpdating the $api_asg_nm ASG from $new_desired_capacity back to $asg_desired_capacity instances...\n"

#   aws autoscaling update-auto-scaling-group \
#   --auto-scaling-group-name $api_asg_nm \
#   --desired-capacity $asg_desired_capacity

#   # Wait until the old API instances are Terminating before disabling Scale-In protection on new API instances

#   old_instances_terminating="false"
#   echo -e "\nWaiting for all old instances to reach a Terminating state."
#   echo "Each instance will be allowed 90 seconds to reach this state..."

#   while [[ $old_instances_terminating == "false" ]]; do

#     for instance_id in "${live_asg_instance_ids[@]}"; do

#       instance_terminating=""
#       timeout=90

#       while [[ $instance_terminating == "" ]]; do

#         instance_terminating=$(echo $(aws autoscaling describe-auto-scaling-groups \
#         --auto-scaling-group-names "$api_asg_nm" \
#         --query "AutoScalingGroups[*].Instances[?LifecycleState == 'Terminating' && InstanceId == '$instance_id']" \
#         --output text) | xargs)

#         if [[ $instance_terminating != "" ]]; then

#           echo -e "\n$instance_id is Terminating"

#         else

#           sleep 5
#           timeout=$((timeout - 5))

#           if [ $timeout == 0 ]; then

#             echo -e "\nTimeout while awaiting $instance_id to enter the Terminating state. Exiting"
#             exit 1

#           fi

#         fi

#       done

#     done

#     echo -e "\nAll old API instances are Terminating."
#     old_instances_terminating="true"

#   done

#   # Disable Scale-In Protection on New API instances

#   echo -e "\nDisabling Scale-In Protection on New API instances..."

#   for instance_id in "${new_instance_ids[@]}"; do

#     echo -e "\nDisabling scale-in protection for instance $instance_id..."

#     aws autoscaling set-instance-protection \
#     --instance-ids "$instance_id" \
#     --auto-scaling-group-name "$api_asg_nm" \
#     --no-protected-from-scale-in

#   done

# fi

echo -e "\nAPI refresh complete!"
