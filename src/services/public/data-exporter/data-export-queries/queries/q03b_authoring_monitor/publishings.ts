import { IExportQueryDef } from "../../types/type";

export const SQL_03B_AUTH_MONIT_PUBLISHINGS: IExportQueryDef = {
  requiredInputs: [],
  optionalInputs: ["days_back"],
  queryGen: (config: any) => `
    SELECT 
        tqs.name AS item_set_name
      , ag.name AS authoring_group_name
      , td.id AS test_design_id
      , td.name AS test_design_name
      , td.created_on published_on
      , td.is_public
      , twtar.id
      , td.lang
      , td.is_revoked
      , tqs.twtar_type_slug
      , ag.type_slug AS authoring_group_type
      , ag.group_id AS authoring_group_id
      , td.source_item_set_id
    FROM test_designs td
    LEFT JOIN temp_question_set tqs
      ON td.source_item_set_id = tqs.id
    LEFT JOIN test_window_td_alloc_rules twtar 
      ON twtar.test_design_id = td.id
    LEFT JOIN authoring_groups ag
      ON ag.group_id = tqs.group_id
    WHERE td.created_on > DATE_ADD(NOW(), INTERVAL -${config.days_back || 7} DAY)
      AND td.created_on < DATE_ADD(NOW(), INTERVAL 0 DAY)
    group by td.id 
  `
};
