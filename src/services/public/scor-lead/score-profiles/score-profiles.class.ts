import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { PatchOption } from '../../test-auth/batch-alloc-policies/batch-alloc-policies.class';
import { DEFAULT_CTRL_GROUP_ID } from '../score-options/score-options.class';

interface Data {
  mspg_id?: number,
  mbap_id?: number,
  option?: string
}

interface ServiceOptions {}

export class ScoreProfiles implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const records = await dbRawRead(this.app, [], `
      select msp.id,
            msp.description,
            msp.short_name,
            msp.skill_code,
            msp.type_slug,
            mspo.score_option_id,
            mspf.score_option_id score_flag_id
      from marking_score_profiles msp
      left join marking_score_profile_options mspo
        on mspo.score_profile_id = msp.id
        and mspo.is_revoked = 0
      left join marking_score_profile_flags mspf
        on mspf.score_profile_id = msp.id
        and mspf.is_revoked = 0
      where msp.is_active = 1
      order by msp.id
    `);
    return records;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<any> {
    const records = await dbRawRead(this.app, [], `
      select msp.id msp_id
        , msp.description
        , msp.short_name
        , msp.skill_code
        , msp.type_slug
        , msp.order
        , msp.description
        , msp.guide1_url
        , msp.guide2_url
        , msp.is_non_scored_profile
      from marking_score_profiles msp
      where msp.is_active = 1
      order by msp.id
    `);
    return records;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<any> {
    if (data && params) {
      const {
        description,
        short_name,
        score_option_ids,
        score_flag_ids,
        skill_code,
        type_slug,
        guide1_url,
        guide2_url,
        is_non_scored_profile,
        order,
        batch_allocation_policy_id,
        score_profile_group_id
      } = <any> data;
  
      const currentUID = await currentUid(this.app, params);
  
      const profileCreateFields = {
        description: description == '' ? null : description,
        short_name: short_name == '' ? null : short_name,
        skill_code: skill_code == '' ? null : skill_code,
        type_slug: type_slug == '' ? null : type_slug,
        is_active: 1,
        ctrl_group_id: DEFAULT_CTRL_GROUP_ID,
        created_by_uid: currentUID,
        guide1_url,
        guide2_url,
        is_non_scored_profile,
        order
      }
      
      const score_profile = await this.app
        .service('db/write/marking-score-profiles')
        .create(profileCreateFields);

      for (let i = 0; i < score_option_ids.length; i++) {
        const profileOptionCreateFields = {
          score_profile_id: score_profile.id,
          score_option_id: score_option_ids[i],
          order: i,
          is_offset: 0,
          created_by_uid: currentUID
        }
    
        const score_profile_option = await this.app
        .service('db/write/marking-score-profile-options')
        .create(profileOptionCreateFields);  
      }

      for (let i = 0; i < score_flag_ids.length; i++) {
        const profileOptionCreateFields = {
          score_profile_id: score_profile.id,
          score_option_id: score_flag_ids[i],
          order: i,
          is_offset: 0,
          created_by_uid: currentUID
        }
    
        const score_profile_flag_option = await this.app
        .service('db/write/marking-score-profile-flags')
        .create(profileOptionCreateFields);  
      }
      if(batch_allocation_policy_id && score_profile_group_id){
        await this.app.service("public/test-auth/batch-alloc-policies").patch(batch_allocation_policy_id, 
          {
            option: PatchOption.ADD_TO_POLICY,
            score_profile_group_id,
            score_profile_id: score_profile.id
          }
          ,params
        )
      }
      return score_profile;
    }
    throw new Errors.BadRequest('NO_PARAMS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<any> {
    const msp_id = id;
    const {mbap_id, mspg_id, option} = data;
    if(!mbap_id || !mspg_id){
      throw new Errors.BadRequest('NO_PARAMS');
    }
    await this.app.service("public/test-auth/batch-alloc-policies").patch(mbap_id, 
      {
        option: option,
        score_profile_group_id: mspg_id,
        score_profile_id: msp_id,
      }
      ,params
    )
    return { message: "success"}
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<any> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {
      description,
      short_name,
      score_option_ids,
      score_flag_ids,
      skill_code,
      type_slug,
      guide1_url,
      guide2_url,
      is_non_scored_profile,
      order
    } = <any> data;

    const uid = await currentUid(this.app, params);

    const score_profile = await this.app.service('db/write/marking-score-profiles').patch(id, {
      description: description == '' ? null : description,
      short_name: short_name == '' ? null : short_name,
      skill_code: skill_code == '' ? null : skill_code,
      type_slug: type_slug == '' ? null : type_slug,
      guide1_url: guide1_url == ''? null : guide1_url,
      guide2_url: guide2_url == ''? null : guide2_url,
      is_non_scored_profile,
      order
    })

    const existingScoreOptionRecords: any[] = await dbRawRead(this.app, {score_profile_id:id},`
        SELECT * FROM marking_score_profile_options mspo
        where mspo.score_profile_id = :score_profile_id
        and mspo.is_revoked = 0;
    `)

    const existingScoreFlagOptionRecords: any[] = await dbRawRead(this.app, {score_profile_id:id},`
      SELECT * FROM marking_score_profile_flags mspf
      where mspf.score_profile_id = :score_profile_id
      and mspf.is_revoked = 0;
  `)

    if(score_flag_ids){
      const existingScoreFlagRecordsToRevokeIds = existingScoreFlagOptionRecords.filter(mspf => !score_flag_ids.includes(mspf.score_option_id)).map(mspf => mspf.id);
      if (existingScoreFlagRecordsToRevokeIds && existingScoreFlagRecordsToRevokeIds.length > 0) {
        await this.app
        .service('db/write/marking-score-profile-flags')
        .db()
        .whereIn('id', existingScoreFlagRecordsToRevokeIds)
        .update({
          is_revoked: 1,
          revoked_on: dbDateNow(this.app),
          revoked_by_uid: uid
        })
      }
      for (let i = 0; i < score_flag_ids.length; i++) {
        const profileFlagOptionCreateFields = {
          score_profile_id: score_profile.id,
          score_option_id: score_flag_ids[i],
          order: i,
          created_by_uid: uid
        }
        // If exist, patch
        const existingFlagRecords = existingScoreFlagOptionRecords.filter(mspf => mspf.score_option_id == score_flag_ids[i]).map(mspf => mspf.id);
        if(existingFlagRecords && existingFlagRecords.length > 0){
          await this.app
          .service('db/write/marking-score-profile-flags')
          .patch(existingFlagRecords[0], profileFlagOptionCreateFields);  
        }
        else{
          const score_profile_flag_option = await this.app
          .service('db/write/marking-score-profile-flags')
          .create(profileFlagOptionCreateFields);  
        }
      }
    }

    if(score_option_ids){
      const existingScoreOptionRecordsToRevokeIds = existingScoreOptionRecords.filter(mspo => !score_option_ids.includes(mspo.score_option_id)).map(mspo => mspo.id);

      // Revoking All mspo?
      if (existingScoreOptionRecordsToRevokeIds && existingScoreOptionRecordsToRevokeIds.length > 0) {
        await this.app
        .service('db/write/marking-score-profile-options')
        .db()
        .whereIn('id', existingScoreOptionRecordsToRevokeIds)
        .update({
          is_revoked: 1,
          revoked_on: dbDateNow(this.app),
          revoked_by_uid: uid
        })
      }

      for (let i = 0; i < score_option_ids.length; i++) {
        const profileOptionCreateFields = {
          score_profile_id: score_profile.id,
          score_option_id: score_option_ids[i],
          order: i,
          is_offset: 0,
          created_by_uid: uid
        }
        // If exist, patch
        const existingOptionRecords = existingScoreOptionRecords.filter(mspo => mspo.score_option_id == score_option_ids[i]).map(mspo => mspo.id);
        if(existingOptionRecords && existingOptionRecords.length > 0){
          await this.app
          .service('db/write/marking-score-profile-options')
          .patch(existingOptionRecords[0], profileOptionCreateFields);  
        }
        else{
          const score_profile_option = await this.app
          .service('db/write/marking-score-profile-options')
          .create(profileOptionCreateFields);  
        }
      }
    }
    
    return score_profile;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<any> {
    if (id && params) {
      const { batch_allocation_policy_id, score_profile_group_id, is_keep_score_option} = params.query!;
      const currentUID = await currentUid(this.app, params);
      
      const score_profile = await this.app
      .service('db/write/marking-score-profiles')
      .patch(id, {
        is_active: 0,
        revoked_on: dbDateNow(this.app),
        revoked_by_uid: currentUID
      });
      if(!is_keep_score_option){
        const existingProfileOptionRecords = <any> await this.app.service('db/write/marking-score-profile-options').find({
          query: {
            score_profile_id: id,
          }
        });
  
        const existingProfileFlagOptionRecords = <any> await this.app.service('db/write/marking-score-profile-options').find({
          query: {
            score_profile_id: id,
          }
        });
  
        if (existingProfileOptionRecords.total > 0) {
          await this.app
          .service('db/write/marking-score-profile-options')
          .db()
          .where('score_profile_id', id)
          .update({
            is_revoked: 1,
            revoked_on: dbDateNow(this.app),
            revoked_by_uid: currentUID
          })
        }
  
        if (existingProfileFlagOptionRecords.total > 0) {
          await this.app
          .service('db/write/marking-score-profile-flags')
          .db()
          .where('score_profile_id', id)
          .update({
            is_revoked: 1,
            revoked_on: dbDateNow(this.app),
            revoked_by_uid: currentUID
          })
        }
      }
      

      if(batch_allocation_policy_id && score_profile_group_id){
        await this.app.service("public/test-auth/batch-alloc-policies").patch(batch_allocation_policy_id, 
          {
            option: PatchOption.REMOVE_FROM_POLICY,
            score_profile_group_id,
            score_profile_id: id
          }
          ,params
        )
      }

      return score_profile; 
    }
    throw new Errors.BadRequest('MISSING_SCORE_PROFILE_ID');
  }
}
