import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import axios from 'axios';
import { dbRawRead, dbRawWrite } from '../../../../util/db-raw';

// export const VEA_SCANNING_API = 'http://localhost:5000';
export const VEA_SCANNING_API = 'http://EC2Co-EcsEl-1Q6AU0CS1BCTK-866007992.ca-central-1.elb.amazonaws.com:5000';

interface Data { }

interface ServiceOptions { }

interface StudentData {
  questionNumber: number,
  schoolCode: string,
  schoolName: string,
  session: string,
  litho: string,
  studentName: string,
  pen: string,
  code: string,
}

const sampleData: Record<string, StudentData> = {
  '888123842': {
    'questionNumber': 13,
    'schoolCode': '07123456',
    'schoolName': 'Example Secondary School',
    'session': '202102',
    'litho': '123456',
    'studentName': '<PERSON><PERSON>, <PERSON>',
    'pen': '888123842',
    'code': 'VEA',
  },
  '361213348': {
    'questionNumber': 13,
    'schoolCode': '07123456',
    'schoolName': 'Example Secondary School',
    'session': '202102',
    'litho': '123456',
    'studentName': 'Leola, Leanne',
    'pen': '361213348',
    'code': 'VEA',
  },
  '520981489': {
    'questionNumber': 13,
    'schoolCode': '07123456',
    'schoolName': 'Example Secondary School',
    'session': '202102',
    'litho': '123456',
    'studentName': 'Jacquiline, Jacelyn',
    'pen': '520981489',
    'code': 'VEA',
  }
}

export class Genpdf implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor(options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find(params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query && params.query.test_window_id && params.query.assessment_codes) {
      // these find requests are limited to 100 students to avoid having the request to the pdf generation service timeout
      let limit: number = 0;
      if (params.query.page !== undefined) {
        limit = params.query.page * 100;
      }
      // get information for each found student
      const assessment_codes = params.query.assessment_codes as string[];
      const test_window_id = params.query.test_window_id;
      const byDistrict = params.query.district !== undefined;
      const bySchool = params.query.school !== undefined;
      const byPEN = params.query.pen !== undefined;
      const unsubmitted = params.query.unsubmitted !== undefined;

      const selectSql = `
        SELECT
          u.id,
          'Registered' AS studentStatus,
          um1.value as pen,
          u.first_name as firstName,
          u.last_name as lastName,
          s.name as schoolName,
          s.foreign_id as schoolCode,
          tw.window_code as session,
          '' as litho,
          scts.slug AS code
        FROM
          school_districts sd
          join schools s on sd.group_id = s.schl_dist_group_id
          join school_classes sc on s.group_id = sc.schl_group_id
          join school_class_test_sessions scts on scts.school_class_id = sc.id
          join test_sessions ts on scts.test_session_id = ts.id
          join test_windows tw on tw.id = ts.test_window_id
          join user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_student'
          join users u on u.id = urt.uid
          join user_metas um1 on um1.uid = u.id and um1.\`key\` = 'StudentPEN'
          left join student_constructed_response scr on scr.uid = u.id
        WHERE tw.id = ?
          AND scts.slug in (?)
          ${byPEN ? 'AND um1.value = ?' : ''}
          ${bySchool ? 'AND s.group_id = ?' : (byDistrict ? 'AND sd.group_id = ?' : '')}
          ${unsubmitted ? 'AND (scr.scan_front is null OR scr.scan_back is null)' : ''}
        ORDER BY pen DESC
        LIMIT ?, 100
      `;

      let sqlParams: (string)[] = [test_window_id, assessment_codes, limit];
      if (bySchool) {
        sqlParams.push(params.query.school);
      }
      else if (byDistrict) {
        sqlParams.push(params.query.district);
      }
      if (byPEN) { sqlParams.push(params.query.pen); }

      const studentData = await dbRawRead(this.app, sqlParams, selectSql);

      // the pdf generation service will generate a pdf per element and return a final concatenated pdf of all elements
      let pdf_gen_data = [];
      for (const student of studentData) {
        for (const question of ['13', '26']) {
          const temp_data = {
            ...student,
             question
            };
          pdf_gen_data.push(temp_data);
        }
      }
      const data = await this.generatePdf(pdf_gen_data);

      return data;
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get(id: Id, params?: Params): Promise<Data> {
    if (params && params.query && params.query.test_window_id && params.query.assessment_code) {
      // get student information
      let sql = `
        SELECT
          u.id,
          'Registered' AS studentStatus,
          um1.value as pen,
          u.first_name as firstName,
          u.last_name as lastName,
          s.name as schoolName,
          s.foreign_id as schoolCode,
          tw.window_code as session,
          '' as litho,
          scts.slug AS code
        FROM school_districts sd
        join schools s on sd.group_id = s.schl_dist_group_id
        join school_classes sc on s.group_id = sc.schl_group_id
        join school_class_test_sessions scts on scts.school_class_id = sc.id
        join test_sessions ts on scts.test_session_id = ts.id
        join test_windows tw on tw.id = ts.test_window_id
        join user_roles urt on urt.group_id = sc.group_id and urt.role_type = 'schl_student'
        join users u on u.id = urt.uid
        join user_metas um1 on um1.uid = u.id and um1.\`key\` = 'StudentPEN'
        left join student_constructed_response scr on scr.uid = u.id
        WHERE u.id = ?
          AND tw.id = ?
          AND scts.slug = ?
      `;
      let sqlParams = [id, params.query.test_window_id, params.query.assessment_code];
      const studentData = await dbRawRead(this.app, sqlParams, sql);

      // the pdf generation service will generate a pdf per element and return a final concatenated pdf of all elements
      let pdf_gen_data = [];
      for (const question of ['13', '26']) {
        const temp_data = {
           ...studentData[0],
           question
          };
        pdf_gen_data.push(temp_data);
      }
      const data = await this.generatePdf(pdf_gen_data);

      return data;
    }
    return [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create(data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch(id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove(id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async generatePdf(studentData: any) {
    // Expected:
    //  studentData: { id, question, studentStatus, pen,
    //    firstName, lastName, schoolName, schoolCode,
    //    session, litho, code, lastDownloaded }
    const res = await axios.post(`${VEA_SCANNING_API}/generate`, { studentData });
    return [{ base64: (res as any).data }];
  }
}
