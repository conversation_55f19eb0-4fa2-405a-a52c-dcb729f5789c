---

# Description:  This playbook copies the already compiled and compressed API build passed from the GitLab pipeline
#               to the Ansible container to the Standby EC2 instance on the ASG in AWS for the API build being tested.
#
#               It also uses the check_api.sh script for validating the API build after the update is complete. If
#               the check fails during the pipeline execution, <PERSON><PERSON> will return an error, and the piepline will
#               stop before the deployment goes live.
#
# Requirements:
#
#   - The API is using pm2 for monitoring the API process
#   - The API is an Ubuntu system with an ubuntu user
#   - ubuntu user has the following added to ~/.profile to allow pm2 command execution from Ansible as the ubuntu user:
#
#        if [ -z "$NVM_DIR" ]; then
#         export NVM_DIR="$HOME/.nvm"
#         [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
#        fi
#
#    - The above added to ~/.profile helps to avoid the "pm2 command not found" error when restarting the API with pm2,
#      as long as we first run ~/.profile after connecting to the instance as the ubuntu user before running any pm2
#      commands.
#
#      Ansible when executed from GitLab and connecting to an EC2 instance using AWS Session Manager will
#      typically connect as root. In this case we do not want this as we need to restart the API service after
#      updating the API using pm2. At the time of writing this, this cannot be done as root on our API
#      instances. We need to change to the ubuntu user and run the restart command as that user instead.

- hosts: "all"
  gather_facts: no
  ignore_errors: no

  vars:
    # Set API build source and destination variables
    api_build_file: "project/api_build/{{ build_file }}"                  # Pass build_file in from command line
    api_build_src: /{{ api_build_file }}
    api_build_dest: /opt/mpt-api
    # Set variables for connecting to AWS EC2 instances using Session Manager Plugin with Ansible
    # ansible_user: 'ubuntu'     # this variable does not work with AWS SSM: https://github.com/ansible-collections/community.aws/issues/853#issuecomment-1205326892
    ansible_become_user: ubuntu                                   # This variable applies to all tasks in this file
    ansible_aws_ssm_bucket_name: vrt-devops-testing
    ansible_connection: aws_ssm
    ansible_aws_ssm_region: ca-central-1
    # API check script variables
    check_api: true # Controls when the check_api script is used for testing API builds. Can be overwritten from CLI
    check_script_src: /project/scripts/gitlab-ci-scripts/api-deploy-automation/bash/check_api.sh
    check_script_dest: /opt/check_api.sh

  tasks:

    - name: Check enable-bracketed-paste setting before bind
      ansible.builtin.shell:
        cmd: "bind -V | grep enable-bracketed-paste"
        executable: /bin/bash
      register: bind_output
    - name: Display enable-bracketed-paste setting before bind
      ansible.builtin.debug:
        msg: "{{ bind_output.stdout }}"

    - name: Disable bracketed paste mode # https://bubo.vretta.com/vea/platform/vea-infrastructure/-/issues/1080
      ansible.builtin.shell: bind 'set enable-bracketed-paste off'
      args:
        executable: /bin/bash

    - name: Check enable-bracketed-paste setting after bind
      ansible.builtin.shell:
        cmd: "bind -V | grep enable-bracketed-paste"
        executable: /bin/bash
      register: bind_output
    - name: Display enable-bracketed-paste setting after bind
      ansible.builtin.debug:
        msg: "{{ bind_output.stdout }}"

    - name: "Find old downloaded API build files to delete in API application directory"
      find:
        paths: /opt/mpt-api
        patterns: '_00_RELEASE-COMMIT-*,api_build*.tgz'
      register: files_to_delete

    - name: "Remove old downloaded API files"
      file:
        path: "{{ item.path }}"
        state: absent
      with_items: "{{ files_to_delete.files }}"

    - name: "Copy API build to the test API"
      copy:
        remote_src: no                  # We want to copy from the Ansible system to the API
        force: yes                      # Overwrites files on the destination system
        src: "{{ api_build_src }}"
        dest: "{{ api_build_dest }}"
        owner: ubuntu
        group: ubuntu
        mode: '0774'

    - name: "Decompress API build archive"
      unarchive:                          # By default this overwrites files
        remote_src: yes                   # This file now exists on the remote API
        src: "{{ api_build_dest }}/{{ build_file }}"
        dest: "{{ api_build_dest }}"

    - name: "Set permissions on API build files"
      shell: chown -R ubuntu "{{ api_build_dest }}" && chgrp -R ubuntu "{{ api_build_dest }}" && chmod -R 775 "{{ api_build_dest }}"

    # Test the API build on the Standby EC2 instance by copying over and running the check_api.sh script on it
    - name: "Copy API check script to the test API system"
      when: check_api == true
      copy:
        remote_src: no
        force: yes
        src: "{{ check_script_src }}"
        dest: "{{ check_script_dest }}"
        owner: ubuntu
        group: ubuntu
        mode: '0774'

    # Restart the API as the ubuntu user. See notes at the top of this playbook for details on how this works
    - name: "Restart API"
      become: true
      # become_user: ubuntu     # no longer required: https://bubo.vretta.com/vea/project-management/vretta-project-notes/unsorted/-/issues/290#note_474367
      shell: . ~/.profile ; pm2 stop /opt/ecosystem.config.js ; pm2 flush ; pm2 startOrRestart /opt/ecosystem.config.js
      register: restart_output
    - debug: msg="{{ restart_output.stdout_lines }}"

    - name: "API validation script"
      become: true
      # become_user: ubuntu     # no longer required: https://bubo.vretta.com/vea/project-management/vretta-project-notes/unsorted/-/issues/290#note_474367
      when: check_api == true
      shell: sleep 30 && chmod a+x "{{ check_script_dest }}" && . ~/.profile && bash "{{ check_script_dest }}"
      register: check_output

    - name: "API validation script results"
      when: check_api == true
      debug: msg="{{ check_output.stdout_lines }}"
