import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import { Errors } from '../../../errors/general';
import logger from '../../../logger';
import { currentUid } from '../../../util/uid';

interface Data {
  slug ?: string,
  data ?: any,
}

interface ServiceOptions {}

export class Log implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (payload: Data, params?: Params): Promise<Data> {
    let created_by_uid : number | null = null;
    let user_agent:string | undefined;
    if (params){
      try {
        created_by_uid = await currentUid(this.app, params);
      }
      catch(e){}
    }
    await this.createLog(payload.slug || '--', JSON.stringify(payload.data), created_by_uid)
    return <any> {};
  }

  async createLog(slug:string, data:any, created_by_uid:number | null, user_agent?:string) {
    console.log(JSON.stringify({
      created_by_uid,
      slug: slug,
      data,
    }))
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
