// Initializes the `public/scor-scor/batches/claim` service on path `/public/scor-scor/batches/claim`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { MarkingClaimedBatchResponses } from './marking-claimed-batch-responses.class';
import hooks from './marking-claimed-batch-responses.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/scor-scor/batches/marking-claimed-batch-responses': MarkingClaimedBatchResponses & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/scor-scor/batches/marking-claimed-batch-responses', new MarkingClaimedBatchResponses(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/scor-scor/batches/marking-claimed-batch-responses');

  service.hooks(hooks);
}
