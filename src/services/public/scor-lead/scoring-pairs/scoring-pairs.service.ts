// Initializes the `public/scor-lead/scoring-pairs` service on path `/public/scor-lead/scoring-pairs`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ScoringPairs } from './scoring-pairs.class';
import hooks from './scoring-pairs.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/scor-lead/scoring-pairs': ScoringPairs & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/scor-lead/scoring-pairs', new ScoringPairs(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/scor-lead/scoring-pairs');

  service.hooks(hooks);
}
