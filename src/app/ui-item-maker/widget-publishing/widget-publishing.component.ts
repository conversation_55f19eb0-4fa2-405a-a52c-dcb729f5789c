import { ChangeDetectorRef, Component, OnInit, Input } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';

import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { IReleaseHistory, ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { FormControl } from '@angular/forms';
import { mtz } from '../../core/util/moment';
import { sampleStudentData } from '../widget-score-entry-settings/data';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { DefaultCategories } from 'src/app/ui-teacher/view-teacher-student-reports/view-teacher-student-reports.component';
import { IContentElementScore } from 'src/app/ui-testrunner/models';
import { IItemContentDiff, IItemSignOff, itemContentDiff, ITestDesignSignOff, legacyItemContentDiff } from '../widget-audits/util/content-diff';

@Component({
  selector: 'widget-publishing',
  templateUrl: './widget-publishing.component.html',
  styleUrls: ['./widget-publishing.component.scss']
})
export class WidgetPublishingComponent implements OnInit {

  

  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl

  constructor(
    public lang: LangService,
    public editingDisabled: EditingDisabledService,
    private auth: AuthService,
    private changeDetector: ChangeDetectorRef,
    private routes: RoutesService,
    private login: LoginGuardService
  ) { }

    filterNum = new FormControl();
    dateSelectorModalIsOpen = false;
    sampleDates: string[] = [];
    dateControl = new FormControl();
    sampleIsrReports = [];
    td_id = null;
    sampleIsrCutScoreId = null;

  ngOnInit(): void {
    console.log('saveload', this.saveLoadCtrl)
    console.log('itembank', this.itemBankCtrl)
    console.log('frameworkctrl', this.frameworkCtrl)
    console.log('publishctrl', this.publishingCtrl)
    this.filterNum.valueChanges.subscribe(()=>{
      if (this.filterNum.value==undefined || this.filterNum.value==null) {
        this.filterNum.setValue('')
        return
      }
      let num = ''
      for (let i = 0;i<this.filterNum.value.toString().length;i++) {
        const c = this.filterNum.value.toString().charAt(i)
        if (c>='0' || c<='9') {
          num+=c
        }
      }
      if (Number(num)!=this.filterNum.value) this.filterNum.setValue(Number(num))
      this.reset()
    })

  }
  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  getReleases(): IReleaseHistory[] {
    const allReleases = this.publishingCtrl.testDesignReleaseHistory
    const numVal = this.filterNum.value ? Number(this.filterNum.value) : undefined
    if (numVal) {
      const filteredReleases = []
      for (let num = 0;num<numVal && num<allReleases.length;num++) {
        filteredReleases.push(allReleases[num])
      }
      return filteredReleases
    }
    return allReleases
  }

  activePublishedTestDesign?:number;
  activeTestDesignForms?:any[]
  async selectPublishedTestDesign(testDesignId: number){
    if (this.activePublishedTestDesign === testDesignId){
      this.activePublishedTestDesign = null;
      return;
    }
    this.activePublishedTestDesign = testDesignId;
    this.activeTestDesignForms = await this.publishingCtrl.loadFormsForPublishedTestDesign(testDesignId);
  }
  async revokeForm(test_form:{id:number, is_revoked:number}){
    await this.publishingCtrl.revokeForm(test_form.id);
    test_form.is_revoked = 1;
  }
  async unrevokeForm(test_form:{id:number, is_revoked:number}){
    await this.publishingCtrl.unrevokeForm(test_form.id);
    test_form.is_revoked = 0;
  }

  renderTwTitle(twTitle:any){
    if (twTitle){
      try {
        const title = JSON.parse(twTitle);
        return title[this.lang.c()] || title['en']
      }
      catch(e){
        return '---'
      }
    }
  }

  renderDate(dateStr){
    return mtz(dateStr).format('MMM D, YYYY')
  }

  resetter = true;
  reset() {
    this.resetter = false;
    this.changeDetector.detectChanges()
    this.resetter = true;
  }

  // getRandomInt(max) {
  //   return Math.floor(Math.random() * max);
  // }

  // addDate(event:MatDatepickerInputEvent<Date>) {
  //   if (event.value) {
  //     this.sampleDates = [...this.sampleDates, event.value.toISOString()];
  //     this.dateControl.reset();
  //     this.changeDetector.detectChanges();
  //   }
  // }

  // openIsrOptions(record) {
  //   this.td_id = record.id
  //   if(!record.twtdars || !(record.twtdars.length)) {
  //     alert("Item set is not allocated, therefore no cut scores associated.")
  //     return;
  //   }
  //   // TODO: Pop up selector for which twtar to use for cut scores
  //   console.log('record', record);
  //   this.sampleIsrCutScoreId = record.twtdars[0].cut_score_def_id
  //   this.dateSelectorModalIsOpen = true;
  // }

  // clearDates() {
  //   this.sampleDates = [];
  //   this.dateControl.reset();
  // }

  // closeDateModal() {
  //   this.dateSelectorModalIsOpen = false;
  //   this.sampleDates = [];
  //   this.dateControl.reset();
  // }

  // async printSampleIsr() {
  //   this.sampleIsrReports = [];
  //   const res = await this.auth.apiFind('public/test-auth/cut-scores-definitions', {
  //     query: {
  //       cut_score_def_id: this.sampleIsrCutScoreId
  //     }
  //   })
  //   const cutScores = JSON.parse(res.cut_config)
  //   const framework = this.frameworkCtrl.asmtFmrk
  //   const sections = framework.partitions
  //   for (let i = 0; i < this.sampleDates.length; i++) {
  //     const sampleIsrData = {}

  //     let scoreAgg = 0;

  //     const overallData = {
  //       details: {
  //         cutOff: [],
  //         weight: 0,
  //         students: {
  //           [sampleStudentData.uid]: 0,
  //         },
  //       }
  //     }
  //     sampleIsrData[DefaultCategories.OVERALL] = overallData
  //     for (const section in sections) {
  //       const testQuestionId = framework.sectionItems[sections[section].id].questions[0].id
  //       const questionData = this.itemBankCtrl.getQuestionById(testQuestionId)
  //       const content = questionData.content[0] as IContentElementScore
  //       const studentScore = this.getRandomInt(+content.scoreWeight)
  //       sampleIsrData[sections[section].description] = {
  //         details: {
  //           cutOff: cutScores[this.lang.c()][sections[section].sectionSlug],
  //           weight: +content.scoreWeight,
  //           students: {
  //               [sampleStudentData.uid]: studentScore,
  //           },
  //         }
  //       }
  //       scoreAgg += +questionData.meta.Cut
  //       sampleIsrData[DefaultCategories.OVERALL].details.weight += +content.scoreWeight
  //       sampleIsrData[DefaultCategories.OVERALL].details.students[sampleStudentData.uid] += studentScore
  //     }

  //     sampleIsrData[DefaultCategories.OVERALL].details.cutOff = [
  //       {
  //         "short": "NA",
  //         "long": "N/A",
  //         "cut_score": null,
  //         "order": 1,
  //         "color": "#808080"
  //       },
  //       {
  //         "short": "RAS",
  //         "long": "Requiring Additional Support",
  //         "cut_score": 0,
  //         "order": 2,
  //         "color": "#FF0000"
  //       },
  //       {
  //         "short": "NRAS",
  //         "long": "Not Requiring Additional Support",
  //         "cut_score": scoreAgg,
  //         "order": 3,
  //         "color": "#008000"
  //       }
  //     ]


  //     const report = {
  //       data: sampleIsrData,
  //       date: this.sampleDates[i],
  //       students: [sampleStudentData],
  //       td_id: this.td_id,
  //       testDesign: {
  //         long_name: this.itemBankCtrl.currentSetName.value,
  //         framework: JSON.stringify(framework),
  //       },
  //     }
  //     this.sampleIsrReports.push(report);
  //   }

  //   try { 
  //     const data = await this.auth.apiCreate('/public/educator/individual-report', {
  //       students: [sampleStudentData],
  //       summary_reports: this.sampleIsrReports,
  //     }, {
  //       query: {
  //         selectedCategory: DefaultCategories.OVERALL,
  //       }
  //     })

  //     if (data.pdfBase64) {
  //       const byteCharacters = atob(data.pdfBase64);
  //       const byteNumbers = new Array(byteCharacters.length);
  //       for (let i = 0; i < byteCharacters.length; i++) {
  //         byteNumbers[i] = byteCharacters.charCodeAt(i);
  //       }
  //       const byteArray = new Uint8Array(byteNumbers);
  //       const blob = new Blob([byteArray], { type: 'application/pdf' });
  //       const url = URL.createObjectURL(blob);
  
  //       // Create a link element and trigger download
  //       const link = document.createElement('a');
  //       link.href = url;
  //       link.download = 'file.pdf';
  //       link.click();
  
  //       // Cleanup
  //       URL.revokeObjectURL(url);
  //     } else {
  //       console.error('PDF data not found');
  //     }
  //   // }
  // }
  // catch (error) {
  //   console.error('Error fetching PDFs:', error);
  // }
  // this.closeDateModal();

  /**
   * 
   * @param testDesign the test design to check.
   * @returns true if the test design has been signed off.
   */
  isSignedOff(testDesign: IReleaseHistory) {
    return testDesign.is_approved;
  }

  /**
   * 
   * @param testDesign the test design to check.
   * @returns true if the test design has been compared to another test design.
   */
  hasComparedTestDesign(testDesign: IReleaseHistory) {
    return testDesign.compared_to_td_id !== null && testDesign.compared_to_td_id !== undefined
  }

  /**
   * 
   * @param testDesign the test design to check.
   * @returns true i the test design has started the sign off process.
   */
  isSignOffStarted(testDesign: IReleaseHistory) {
    return testDesign.tdso_id !== null && testDesign.tdso_id !== undefined;
  }

  /**
   * 
   * @returns true if there are no releases that have been signed off.
   */
  hasNoSignOffs() {
    const releaseHistory = this.getReleases();

    const recentSignOff = releaseHistory.find((release) => 
      release.tdso_id != undefined
    )
    return recentSignOff === undefined;
  }

  isSignOffLoading = false;

  /**
   * Function for starting the sign off process. 
   * If there are no signed off test designs, the first test design will be signed off instantly.
   * Otherwise, the test design will be compared to the latest signed off test design. 
   * Then, the sign off records will be created and the user will be directed to the Sign Off mode.
   * @param testDesign the test design to sign off on
   */
  async startTestDesignSignOff(testDesign: IReleaseHistory) {
    if(this.isReadOnly()) {
      return;
    }
    
    this.isSignOffLoading = true;
    // Instant sign off if no current sign offs
    if(this.hasNoSignOffs()) {
      return this.auth.apiCreate(
        this.routes.TEST_AUTH_TEST_DESIGN_SIGN_OFF, 
        {
          test_design_id: testDesign.id, 
          is_approved: 1,
        }, 
      ).finally(() => {
        this.publishingCtrl.loadTestDesignReleaseHistory();
        this.isSignOffLoading = false;
      });
    }

    // Create test design sign off info
    const releaseHistory = this.getReleases();
    
    const approvedTestDesign = releaseHistory.find((release) => 
      release.is_approved == 1
    )

    if(!approvedTestDesign) {
      return this.login.quickPopup('No valid test designs found.');
    }

    const testDesignIds = [testDesign.id, approvedTestDesign.id];

    const data: any[] = await this.auth.apiFind(this.routes.TEST_DESIGN_QUESTION_VERSIONS, {query: {test_design_ids: testDesignIds}})
    const parsedData = itemContentDiff(data, false, {itemBankCtrl: this.itemBankCtrl}, `${testDesign.id}`);
    const diffDataFiltered = parsedData.filter((data) => data.hasDiff == true);
    const qIds = diffDataFiltered.map((item) => +item.questionId);

    // If no diffs found, instant sign off
    if(diffDataFiltered.length == 0) {
      return this.auth.apiCreate(
        this.routes.TEST_AUTH_TEST_DESIGN_SIGN_OFF, 
        {
          test_design_id: testDesign.id, 
          compared_to_td_id: approvedTestDesign.id,
          is_approved: 1,
        }, 
      ).finally(() => {
        this.publishingCtrl.loadTestDesignReleaseHistory();
        this.isSignOffLoading = false;
      });
    }
    // Initialize data map

    try {
      const signOffRecords = await this.itemBankCtrl.createSignOffRecords(testDesign.id, approvedTestDesign.id, diffDataFiltered);
      this.publishingCtrl.loadTestDesignReleaseHistory();
      
      const dataMap = this.itemBankCtrl.initDiffDataMap(diffDataFiltered);
  
      const itemSignOffRecordMap = this.itemBankCtrl.inititemSignOffRecordMap(signOffRecords.itemSignOffRecords);
  
      // Go to authoring view with items in filter
      this.itemBankCtrl.activateSignOffView(qIds, dataMap, testDesignIds, signOffRecords.signOffRecord, itemSignOffRecordMap);
    } catch (err) {
      this.login.quickPopup(`Error initializing sign off: ${err.message}`);
      this.isSignOffLoading = false;
      throw new Error(err)
    }
    this.isSignOffLoading = false;
  }

  /**
   * Function for restoring/continuing the Sign Off process for a test design that is in progress.
   * @param tdsoId the existing test_design_sign_off ID to restore.
   */
  async restoreTestDesignSignOff(tdsoId: number) {
    if(this.isReadOnly()) {
      return;
    }
    
    try {
      const signOffRecords: {signOffRecord: ITestDesignSignOff, itemSignOffRecords: IItemSignOff[]} = await this.auth.apiGet(this.routes.TEST_AUTH_TEST_DESIGN_SIGN_OFF, tdsoId);

      if(!signOffRecords) {
        throw new Error('MISSING_SIGN_OFF')
      }

      const diffData: IItemContentDiff[] = JSON.parse(signOffRecords.signOffRecord.audit_config);
      const qIds = diffData.map((item) => +item.questionId);

      const dataMap = this.itemBankCtrl.initDiffDataMap(diffData);
      const itemSignOffRecordMap = this.itemBankCtrl.inititemSignOffRecordMap(signOffRecords.itemSignOffRecords);
  
      const testDesignIds = [signOffRecords.signOffRecord.test_design_id, signOffRecords.signOffRecord.compared_to_td_id]

      // Go to authoring view with items in filter
      this.itemBankCtrl.activateSignOffView(qIds, dataMap, testDesignIds, signOffRecords.signOffRecord, itemSignOffRecordMap);
    } catch (err) {
      this.login.quickPopup(`Error loading sign off: ${err.message}`);
      throw new Error(err)
    }
  }

}
