
@import '../../../styles/partials/_margins.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/page-types/standard.scss';
// $h-padding-edge: 1em;
// $v-padding-thin: 0.5em;

.slab-lang {
    padding: $v-padding-thin $h-padding-edge;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.slab-site-account {
    padding: $v-padding-thin $h-padding-edge;
    border-top: 1px solid rgba(121,121,121,0.5);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .site-title {
        line-height: 140%;
        align-self: flex-end;
        strong {
            font-size: 1.4em;
        }
        a { 
            margin-right:0.5em;
            text-decoration: underline;
            &.is-title {
                color: #222;
                text-decoration: none;
            }
        }
        @include viewport-sm { 
            font-size: 0.72em;
            // strong {
            //     font-size: 1.0em;
            // }
        }
    }
}
.profile-formlet {
    text-align:right;
    .user-display-name {
        margin-bottom: 0.5em;
    }
}
.color-strip { 
    height: 0.2em;
    background-color: rgba(64, 143, 236, 0.37);
    
}
.slab-breadcrumb {
    padding: $v-padding-thin $h-padding-edge;
    background-color: #EEEEEE;
    display: flex;
    flex-direction: row;
    white-space: nowrap;
    justify-content: flex-start;
    // overflow:auto;
    @include viewport-sm { 
        justify-content: flex-end;
    }
    .crumb-element {
        display: flex;
        flex-direction: row;
        font-size: 0.8em;
        
        a.crumb {
            color: #3b3b3b;
            font-weight: 400;
            max-width:20em;
            overflow:hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            &:hover {
                color: $link;
            }
        }
        .crumb-delim {
            margin: 0em 0.5em;
            color: #c5c5c5;
        }
    }
 }

 .simple-header { 
    padding:2em;  
    display:flex; 
    flex-direction:row; 
    justify-content: space-between; 
    align-items: flex-start;

    .top-row {
        display: flex;
        justify-content: space-between;
    }

    &.has-sidebar {
        height: 5em;
        align-items: center;
        padding: 0.8em;
        border-bottom: 1px solid #e0e0e0;
        .slab-breadcrumb { 
            font-size: 1.25em;
            background: none;
        }
    }
    &.is-light {
        a { color: #fff; }
        .slab-breadcrumb { 
            background-color: unset;
            .crumb-element a.crumb {
                color: #fff;
                &.is-abed-login {
                    color: #000;
                }
            }
            &.is-abed-login {
                padding: 0;
            }
        }
    }
    &.is-bc-login {
        background-color: #003366;;
    }
    &.is-abed-login {
        a {
            color: #000;
        }
        padding-bottom: 0;
        margin-bottom: 0;
        display: block;
    }
 }

 .mutable-lang-select {
    display:flex; align-items: center; flex-direction: row;
    a {
        margin-left:0.3em
    }
    img {
        width: 1.5em;
    }
 }

 button.support-link {
    @extend %clean-button;
    color: #333;
    font-weight: 600;
 }

 .is-opaque {
    z-index: 1;
    position: relative;
    background-color: #fff;
 }

 .logo-container {
    height: calc(100% + calc(3*9.1%) + 9.1%);
    display: flex;
    align-items: flex-start;
    width: 8%;
    .abed-logo {
        // The blue square of ABED logo is 9.1% of the width of the whole logo
        // These were the requested margins from customer
        width: 100%;
        height: auto;
        margin-bottom: 9.1%;
        margin-top: calc(3 * 9.1%);
    }
 }
