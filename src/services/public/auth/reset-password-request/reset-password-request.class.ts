import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { generateSecretCode } from '../../../../util/secret-codes';
import { IAuth } from '../../../db/schemas/auths.schema';
import { dbDateOffsetHours, dbDateNow } from '../../../../util/db-dates';
import * as Errors from '@feathersjs/errors';
import { defaultDomain } from '../../../../constants/mail-constants';
import { normalizeDomain } from '../../../../util/domain-whitelist';

interface Data {}

interface ServiceOptions {}

interface IRequestData{
  email: string,
  langCode: string
  domain: string
}

export class ResetPasswordRequest implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  private async handleReinvite(email:string, params?:Params): Promise<void> {
    const invitationRecords = <any> await this.app
    .service('db/write/u-invites')
    .find({
      query: {
        invit_email: email,
        used_on: null,
        is_auto_email: true,
        is_revoked: false,
        invit_meta: {
          $ne: ''
        }
      }
    });
    if (invitationRecords.total !== 1) {
      return;
    }
    const invitationRecord = invitationRecords.data[0];
    const invitationMeta = JSON.parse(invitationRecord.invit_meta);
    return this.app.service('auth/invitation').sendEmail(
      email,
      invitationMeta.emailSubjectSlug,
      invitationMeta.emailTemplateSlug,
      invitationMeta.emailTemplateParams,
      invitationRecord.id + 'X' + invitationRecord.secret_key,
      invitationMeta.langCode,
      params && params.headers ? params.headers.host : undefined
    );
  }

  private async handleReconfirm(reqData:IRequestData, authRecord:any): Promise<Data> {
    if (authRecord.email_verif_code){
      return this.app.service('mail/registration-confirmation').create({
        emailAddress: reqData.email,
        registrationHash: authRecord.email_verif_code,
        langCode: reqData.langCode,
        domain: normalizeDomain(reqData.domain || defaultDomain),
      })
    }
    return <any> Promise.resolve();
  }

  async create (data: IRequestData, params?: Params): Promise<Data> {

    const STD_RES = {};
    // We deliberately do not return lookup errors on this end point for the sake of cybersecurity

    // if (Array.isArray(data)) {
    //   return Promise.all(data.map(current => this.create(current, params)));
    // }
    const dbAuthTableWrite = this.app.service('db/write/auths');
    const dbAuthTableRead = dbAuthTableWrite; //this.app.service('db/read/auths');
    const dbUInvitesTableRead = this.app.service('db/read/')

    const authFindFieldQuery:Partial<IAuth> = {
      email: data.email
    }
    const matchingAuthRecord = <any> await dbAuthTableRead.find({
      query: {
        ... authFindFieldQuery
      }
    });
    if (matchingAuthRecord.total === 0){
      await this.handleReinvite(data.email, params);
      return STD_RES;
    }
    if (matchingAuthRecord.total > 1){
      return STD_RES; // to do : log this anomaly
    }
    const retrieveAuthRecord = matchingAuthRecord.data[0];

    if (!retrieveAuthRecord.enabled) {
      await this.handleReconfirm(data, retrieveAuthRecord);
      return STD_RES;
    }
    // generate a unique code and ensure that it is not actively being used
    const resetToken = generateSecretCode(16);
    const currentDbTime = dbDateNow(this.app);
    let isNoMatchConfirmed = false;
    for (let i=0; i<10; i++){
      const authExpiryFieldQuery:Partial<IAuth> = {
        password_reset_token: resetToken,
        password_reset_token_expiry: <any> {
          $gt: currentDbTime
        }
      }
      const matchingRecords = <Paginated<any>> await dbAuthTableRead.find({query: authExpiryFieldQuery})
      if (matchingRecords.total === 0){
        isNoMatchConfirmed = true;
        break;
      }
    }
    if (!isNoMatchConfirmed){
      throw new Errors.TooManyRequests('cannot generate unused token')
    }

    /// use the reset token
    const authPatchFieldQuery:Partial<IAuth> = {
      password_reset_token: resetToken,
      password_reset_token_expiry: dbDateOffsetHours(this.app, 1)
    }
    await dbAuthTableWrite.patch(retrieveAuthRecord.id, {
      ... authPatchFieldQuery
    })

    // send the email
    const resetPasswordService = this.app.service('mail/reset-password');
    let whitelabel = params && params.headers ? params.headers.host : undefined;
    if(params && +params.isSMCS) whitelabel = data.domain;
    await resetPasswordService.create({
      emailAddress  : data.email,
      langCode      : data.langCode,
      domain        : data.domain,
      resetToken    : resetToken,
      whitelabel
    });
    return STD_RES;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
