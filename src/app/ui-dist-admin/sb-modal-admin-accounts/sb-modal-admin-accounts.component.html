<div *ngIf="isSelectingAdmin">
    <div><tra slug="select_admin_edit"></tra></div>
    <ul>
        <li *ngFor="let admin of admins">
        <button class="button" (click)="selectAdmin(admin)">
            {{admin.name}}
        </button>
        </li>
    </ul>
</div>
<div *ngIf="!isSelectingAdmin && formEntries">
    <div class="required-field-container" style="position: unset;">
      <i class="fa fa-asterisk" aria-hidden="true"></i>
      <b><tra slug="lbl_required_field"></tra></b>
    </div>
    <sb-widget-form-entry 
      *ngFor="let entry of formEntries" 
      [formEntry]="entry"
      [formEntryLabelContainerStyle]="{minWidth: '4rem'}"
    ></sb-widget-form-entry>
</div>