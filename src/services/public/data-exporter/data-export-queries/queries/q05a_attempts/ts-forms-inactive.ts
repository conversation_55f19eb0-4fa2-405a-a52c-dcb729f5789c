import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
  test_window_ids: number[]; // or number, depending on usage
}

export const SQL_05A_TA_FORMS_INACTIVE: IExportQueryDef = {
  requiredInputs: ["test_window_ids"],

  // todo: should be doing class filter from a pre asset, should be doing teacher emails from a post asset
  
  queryGen: (config: IQueryConfig) => `
    SELECT * FROM (
        SELECT ta.test_session_id ts_id
            , ts.created_on ts_created_on
            , ts.date_time_start ts_date_time_start
            , scts.slug ts_type_slug 
            , twtar.type_slug twtar_type_slug 
            , (scts.slug = twtar.type_slug) AS is_ts_twtar_slug_match
            , max(ta.started_on IS NOT NULL) AS is_started
            , ta.started_on IS NOT NULL AS is_any_in_class_started
            , ts.is_closed ts_is_closed
            , ts.is_cancelled ts_is_cancelled
            , twtar.id twtar_id
            , max(twtar.is_active) twtar_is_active
            , s.name AS school_name
            , group_concat(distinct a.email) teacher_emails 
            , sc.name sc_name 
            , ts.name_custom ts_name_custom
            , twtar.test_window_id
        FROM test_window_td_alloc_rules twtar
        JOIN test_attempts ta 
            ON twtar.id = ta.twtdar_id
        AND ta.is_invalid = 0
        AND ta.uid > 0
        JOIN test_sessions ts
            ON ts.id = ta.test_session_id
        JOIN test_forms tf
            ON tf.id = ta.test_form_id
        JOIN test_designs td
            ON td.id = twtar.test_design_id
        JOIN school_class_test_sessions scts
            ON scts.test_session_id = ta.test_session_id
        JOIN school_classes sc
            ON sc.id = scts.school_class_id
        JOIN schools s
            ON s.group_id = sc.schl_group_id
        JOIN school_districts sd
            ON sd.group_id = s.schl_dist_group_id
            AND sd.is_sample = 0
        LEFT JOIN user_roles ur 
            on ur.group_id = sc.group_id 
            and ur.is_revoked = 0 
            and ur.role_type in ('schl_teacher','schl_teacher_invig') 
        LEFT JOIN auths a
            on a.uid = ur.uid 
        WHERE twtar.test_window_id IN (:test_window_ids)
        AND twtar.is_sample = 0
        AND (
            (scts.slug != twtar.type_slug) OR 
            (twtar.is_active = 0)
        )
        GROUP BY ts.id 
        ORDER BY twtar.type_slug, s.name
    ) t
     where not(t.is_started = 0 and t.ts_is_closed = 1)
  `,
  // todo: last where clause should be done using a map-col rule
};
