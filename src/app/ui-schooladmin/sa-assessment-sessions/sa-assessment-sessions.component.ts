import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { coinFlip } from '../../ui-testadmin/demo-data.service';
import * as moment from 'moment-timezone';
import { LangService } from "../../core/lang.service";
import {IClassroom, ISession, IStudentAccount, PurchaseModal, SessionModal, SessionStatus} from "../data/types";
import { MemDataPaginated } from "../../ui-partial/paginator/helpers/mem-data-paginated";
import { LoginGuardService } from '../../api/login-guard.service';
import { ListSelectService } from '../../ui-partial/list-select.service';
import { PageModalController, PageModalService } from "../../ui-partial/page-modal.service";
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { G9DemoDataService, G9_SLUG_TO_CAPTION } from '../g9-demo-data.service';
import { MySchoolService, ClassFilterId } from '../my-school.service';
import {FilterSettingMode} from "../../ui-partial/capture-filter-range/capture-filter-range.component";
import { ASSESSMENT, isAssessmentABED } from '../../ui-teacher/data/types';
import { ActivatedRoute, Router } from '@angular/router';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { formatDate } from '@angular/common';
import { ABED_ASSESSMENTS } from '../data/constants';
import { AssessmentTypeOptions } from 'src/app/ui-session-modal/view-new-session-modal/view-new-session-modal.component';
import * as _ from 'lodash';
import { calculateDateEnd } from '../../core/util/date';
import { ScanSessionInfo } from 'src/app/ui-teacher/teacher-classrooms/data/types';

export enum SCHEDULER {
  NOW = 'NOW',
  LATER = 'LATER'
}

enum SESSION_TYPE {
  UPCOMING = "UPCOMING",
  PROGRESS = "PROGRESS",
  REQ_UPLOADS = "REQ_UPLOADS",
  SUBMITTED = "SUBMITTED",
  CANCELLED = "CANCELLED"
}

@Component({
  selector: 'sa-assessment-sessions',
  templateUrl: './sa-assessment-sessions.component.html',
  styleUrls: ['./sa-assessment-sessions.component.scss']
})
export class SaAssessmentSessionsComponent implements OnInit {

  @Output() onSetClassFilter = new EventEmitter();
  @Output() unPaidStudentCurrentFilter = new EventEmitter<string>();
  @Output() unPaidStudentCurrentFilterCancelled = new EventEmitter<string>();

  isActive: boolean;
  allowCancelSession: boolean = false;
  isPartialActive:boolean;
  selectedError: boolean;
  schedulerErrors: string;
  isABEDNonFTAssessmentSelected: boolean = false;
  isPaymentModuleEnabled: boolean = false;
  isShowTeacherNames: boolean = false;
  isShowCompletedScans: boolean = false;
  isShowAssessmentType: boolean = true;
  isShowCompletedUploads: boolean = false;

  SESSION_TYPE = SESSION_TYPE;

  // startTime / startTimeUTC
  // status: active
  selectedSessionType: SESSION_TYPE;
  sessionTabs: {id: SESSION_TYPE, caption: string, count: number, filterFunction: Function}[] = [
    {id: SESSION_TYPE.UPCOMING, caption: "lbl_upcoming", count: 0, filterFunction: session => session.status == SessionStatus.PENDING && !session.is_cancelled && !session.isclosed},
    {id: SESSION_TYPE.PROGRESS, caption: "lbl_progress", count: 0, filterFunction: session => session.status == SessionStatus.ACTIVE && !session.is_cancelled && !session.isclosed},
    {id: SESSION_TYPE.REQ_UPLOADS, caption: "lbl_req_uploads", count: 0, filterFunction: session => !session.is_cancelled && session.is_scan_session},
    {id: SESSION_TYPE.SUBMITTED, caption: "lbl_status_submitted ", count: 0, filterFunction: session => session.isclosed && !session.is_cancelled},
    {id: SESSION_TYPE.CANCELLED, caption: "lbl_canceled ", count: 0, filterFunction: session => session.is_cancelled}
  ]

  sessionsByType = {}

  constructor(
    public lang: LangService,
    public loginGuard: LoginGuardService,
    private listSelectService: ListSelectService,
    private pageModalService: PageModalService,
    public mySchool: MySchoolService,
    private auth: AuthService,
    private routes: RoutesService,
    private g9DemoData: G9DemoDataService,
    private router: Router,
    private route: ActivatedRoute,
    public whitelabelService: WhitelabelService
  ) { }

  public sessionsTable: MemDataPaginated<ISession>;
  public studentList
  public sessions: ISession[] = [];
  public isAllSelected = false;
  public isAnySelected = false;
  public isLoaded: boolean;
  public isInited: boolean;
  public columnLabels;
  isPrivateSchool:boolean = false;
  public columnStatusLabel;
  private initalSessionStatus = SessionStatus.PENDING;
  currentClassFilter: ClassFilterId;
  currentAssessmentDefId: number;
  pageModal: PageModalController;
  SessionModal = SessionModal;
  PurchaseModal = PurchaseModal;
  SCHEUDLER = SCHEDULER;
  currentTestWindow;
  STANDARD_TIMEZONE = this.whitelabelService.getTimeZone();
  AB_session_assess = new Set([
    ASSESSMENT.MBED_SAMPLE,
    ASSESSMENT.G9_OPERATIONAL, ASSESSMENT.G9_SAMPLE,
    ASSESSMENT.OSSLT_OPERATIONAL, ASSESSMENT.OSSLT_SAMPLE,
    ASSESSMENT.TCLE_OPERATIONAL, ASSESSMENT.TCLE_SAMPLE,
    ASSESSMENT.TCN_OPERATIONAL, ASSESSMENT.TCN_SAMPLE,
    ASSESSMENT.SCIENCES8_OPERATIONAL, ASSESSMENT.SCIENCES8_SAMPLE,
    ASSESSMENT.ABED_OPERATIONAL, ASSESSMENT.ABED_SAMPLE
  ]);
  isLangScheduled: boolean;
  isMathScheduled: boolean;
  schoolName: string = this.g9DemoData.schoolData.name;
  paymentPolicy: any;
  alternative_days_before_session: number;
  unpaidPrimaryClassFilterCount: number = 0;
  unpaidJuniorClassFilterCount: number = 0;
  unpaidG9ClassFilterCount: number = 0;
  unpaidOssltClassFilterCount: number = 0;

  ngOnInit() {
    const schoolLang = this.g9DemoData.schoolData["lang"];
    this.lang.setCurrentLanguage(schoolLang);
    this.isPrivateSchool = this.g9DemoData.schoolData["is_private"];
    this.initRouteView();

    // this.isPrivateSchool = this.g9DemoData.schoolData["is_private"];
    if (this.isPrivateSchool) {
      this.setClassFilter(ClassFilterId.OSSLT);
    }

    if (this.mySchool.getClassFilterToggles()) {
      this.mySchool.getClassFilterToggles().map((toggle) => {
        if (toggle.value) {
          this.setAssessmentDefId(toggle.assessmentDefId);
          this.currentClassFilter = toggle.id as ClassFilterId;
        }
      });
    }
 
    this.loadSessions()
    if(this.g9DemoData.assessments){
      this.setTestWindowFilter(this.mySchool.getCurrentTestWindow())
      this.setClassFilter(this.currentClassFilter)
    }

    this.pageModal = this.pageModalService.defineNewPageModal();
    this.getPaymentPolicy();
  }

  initRouteView() {
    this.columnLabels = {
      id: this.lang.tra('sa_sessions_col_id'),
      isPaid: this.lang.tra('sa_private_student_paid'),
      paymentStatus: this.lang.tra('pc_lbl_payment_status'),
      invigilators: this.lang.tra('sa_sessions_col_invig'),
      classroom: this.lang.tra('sa_sessions_col_class'),
      description: this.lang.tra('sa_sessions_col_desc'),
      students: this.lang.tra('sa_sessions_col_stud'),
      times: this.lang.tra('sa_sessions_col_times'),
    submittedOn: this.lang.tra('lbl_date_submission'),
      slug: this.lang.tra('lbl_assessment'),
      assessmentType: this.isABED() ? this.lang.tra("abed_sessions_col_type") : this.lang.tra('lbl_assessment_type'),
      status: this.lang.tra('sa_title_sessions_status'),
      submissions: this.lang.tra('lbl_submissions'),
      submissions_lang: this.lang.tra('lbl_submissions_lang'),
      submissions_math: this.lang.tra('lbl_submissions_math'),
      type: this.lang.tra('lbl_assessment_type'),
      invigilate: this.lang.tra('lbl_school_admin_invigilate'),
      uploadSheets: this.lang.tra('sa_grad_upload_response_sheets'),
      cancelledOn: this.lang.tra('lbl_bulk_upload'),
      uploadProgress: this.lang.tra('lbl_upload_progress'),
      uploadDue: this.lang.tra('lbl_upload_due')
    };

    if(this.whitelabelService.isABED()) {
      this.columnLabels.invigilators = this.lang.tra('lbl_exam_supervisors');
      this.columnLabels.teacher = this.lang.tra('sa_class_educator');
      this.columnLabels.times = this.lang.tra('date_and_time_abed');
      this.columnLabels.assessmentType = this.lang.tra('lbl_assessment_type');
      this.columnLabels.slug = this.lang.tra('lbl_assessment');
    }

    this.columnStatusLabel = {
      [SessionStatus.PENDING]: this.lang.tra('sa_sessions_status_pend'),
      [SessionStatus.ACTIVE]: this.lang.tra('lbl_active'),
      [SessionStatus.CANCELLED]: this.lang.tra('lbl_canceled'),
      [SessionStatus.SUBMITTED]: this.lang.tra('lbl_status_submitted'),
      // [SessionStatus.COMPLETED]: this.lang.tra('sa_sessions_status_comp')
    };
  }

  getInvigilateCaption(){
    if (this.selectedSessionType == SESSION_TYPE.REQ_UPLOADS){
      return this.lang.tra('lbl_school_admin_single_upload')
    }
    else {
      return this.columnLabels.invigilate
    }
  }

  getInvigilators(classId) {
    let invigilators = '';
    const class_group_id = this.g9DemoData.teacherClassrooms.map[classId].group_id
    const classInvigilators = class_group_id?this.g9DemoData.invigilators.filter(invig => invig.group_id == class_group_id):[]
    classInvigilators.forEach(invig => {
      const invigName = invig.first_name + " " + invig.last_name;
      invigilators = invigilators == '' ? invigName : invigilators + ', '+ invigName
    })
    return invigilators == '' ? null : invigilators;
  }

  isG9OrOsslt() { return this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT; }

  isPrimaryOrJunior() { return this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior; }

  getSessionSlug(session) {
    if(session == "G9_OPERATIONAL"){
      return this.lang.tra('lbl_operational');
    } else if(session == "G9_SAMPLE"){
      return this.lang.tra('lbl_sample');
    }
  }

  getAssessmentColumnCaption(): string
  {
    return !this.whitelabelService.isABED() ? this.columnLabels.type : this.columnLabels.slug;
  }

  toggleSelectAll() {
    this.setSelectAll(this.isAllSelected);
  }

  setSelectAll(state: boolean) {
    const sessionSelector = this.visibleSessions()
    this.isAllSelected = state;
    sessionSelector.forEach(session => session.__isSelected = state);
    this.isAnySelected = state;
  }

  checkSelection()
  {
    this.isAnySelected = false;
    this.isAllSelected = false;

    // const sessionSelector = this.sessions
    const sessionSelector = this.visibleSessions();
    const sessions = sessionSelector.filter(session => session.__isSelected);
    if(sessions.length > 0)
      this.isAnySelected = true;
    if(sessions.length == sessionSelector.length)
      this.isAllSelected = true;

    this.allowCancelSessionCheck();
    this.allowABEDOperAssessmentCheck();
  }

  getSessionStatus(session: ISession) {
    const studentMap = this.g9DemoData.getStudentsByClassroomId(String(session.classroom_id));
    if(!studentMap)
      return;

    const students = studentMap.list

    session.studentsPaid = 0;
    session.students = students.length;

    for(let student of students) {
      if(student.isPaid)
        session.studentsPaid++
      if(student.altPaymentStatus == 1)
        session.altPaymentPending = true;
      if(student.altPaymentStatus == 2)
        session.altPaymentContactEqao = true;
    }
    session.paymentStatus = "lbl_required"
    if(session.altPaymentContactEqao)
      session.paymentStatus = "pc_btn_contact_eqao"
    if(session.altPaymentPending)
      session.paymentStatus = "lbl_pending";
    if(session.studentsPaid == students.length)
      session.paymentStatus = "lbl_approved";
    if(this.isSampleSession(session))
      session.paymentStatus = "lbl_not_required";

    const paymentEnabledForSession = this.g9DemoData.schoolData[this.convertPaymentRequiredSlug(session.slug)]
    if(paymentEnabledForSession && session.studentsPaid < students.length && session.paymentStatus == "lbl_required" && !session.isclosed){
      console.log('aSession', session)
      let classFilterSlug:string = null;
      classFilterSlug = this.convertClassFilterSlug(session.slug);
      if(classFilterSlug !== null && classFilterSlug !== undefined){
        if(session.slug == ASSESSMENT.PRIMARY_OPERATIONAL){
          this.unpaidPrimaryClassFilterCount++;
        }
        if(session.slug == ASSESSMENT.JUNIOR_OPERATIONAL){
          this.unpaidJuniorClassFilterCount++;
        }
        if(session.slug == ASSESSMENT.G9_OPERATIONAL){
          console.log(this.unpaidG9ClassFilterCount)
          this.unpaidG9ClassFilterCount++;
          console.log(this.unpaidG9ClassFilterCount)
        }
        if(session.slug == ASSESSMENT.OSSLT_OPERATIONAL){
          this.unpaidOssltClassFilterCount++;
        }
        this.unPaidStudentCurrentFilter.emit(classFilterSlug);
      }
    }
  }

  convertPaymentRequiredSlug(slug: ASSESSMENT | string) {
    let paymentRequiredSlug = "payment_req_";
    if(slug == ASSESSMENT.PRIMARY_OPERATIONAL || slug == ASSESSMENT.JUNIOR_OPERATIONAL){
      paymentRequiredSlug += "pj";
    }
    if(slug == ASSESSMENT.G9_OPERATIONAL) {
      paymentRequiredSlug += "g9";
    }
    if(slug == ASSESSMENT.OSSLT_OPERATIONAL) {
      paymentRequiredSlug += "osslt";
    }
    return paymentRequiredSlug;
  }

  convertClassFilterSlug(slug:string){
    if(slug == ASSESSMENT.PRIMARY_OPERATIONAL){
      return 'lbl_primary';
    }
    if(slug == ASSESSMENT.JUNIOR_OPERATIONAL){
      return 'lbl_junior';
    }
    if(slug == ASSESSMENT.G9_OPERATIONAL){
      return 'txt_g9_math';
    }
    if(slug == ASSESSMENT.OSSLT_OPERATIONAL){
      return 'lbl_osslt';
    }
  }

  isSampleSession(session: ISession) {
    return session.slug == ASSESSMENT.PRIMARY_SAMPLE || session.slug == ASSESSMENT.JUNIOR_SAMPLE || session.slug == ASSESSMENT.G9_SAMPLE || session.slug == ASSESSMENT.OSSLT_SAMPLE
  }

   processSession(session: ISession) {
    if(this.isPaymentModuleEnabled){
      this.getSessionStatus(session);
    }
    session.invigilators = this.getTeachers(session, true);
  }

  loadSessions() {
   if(this.g9DemoData.assessments){
    this.sessions = <any[]>this.g9DemoData.assessments.list;
   }
   if(this.isPrivateSchool){
    const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9DemoData.schoolData;
    if(payment_req_g9 || payment_req_osslt || payment_req_pj){
      this.isPaymentModuleEnabled = true;
    }
   }

   console.log(this.sessions);
   this.sessions.forEach(session => this.processSession(session));
   console.log(this.sessions);
   for(let i=0; i < this.sessions.length; i++) {
    if(this.sessions[i].slug === ASSESSMENT.G9_SAMPLE || this.sessions[i].slug === ASSESSMENT.G9_OPERATIONAL || this.sessions[i].slug === ASSESSMENT.OSSLT_SAMPLE || this.sessions[i].slug === ASSESSMENT.OSSLT_OPERATIONAL){
      // G9 && OSSLT Session A
      if(this.sessions[i].session_a && this.sessions[i].session_a.caption === "A") {
        this.sessions[i].session_a.slug = this.lang.tra(this.sessions[i].session_a.slug);
        this.sessions[i].session_a.datetime_start = this.getDate(this.sessions[i].session_a.datetime_start);
      }
      // G9 && OSSLT Session B
      if(this.sessions[i].session_b && this.sessions[i].session_b.caption === "B") {
        this.sessions[i].session_b.slug = this.lang.tra(this.sessions[i].session_b.slug);
        this.sessions[i].session_b.datetime_start = this.getDate(this.sessions[i].session_b.datetime_start);
      }
    }
    if(this.sessions[i].slug === ASSESSMENT.PRIMARY_SAMPLE || this.sessions[i].slug === ASSESSMENT.PRIMARY_OPERATIONAL || this.sessions[i].slug === ASSESSMENT.JUNIOR_SAMPLE|| this.sessions[i].slug === ASSESSMENT.JUNIOR_OPERATIONAL){
      // PJ Session Language
      if(this.sessions[i].session_a && this.sessions[i].session_a.caption === "A") {
        this.sessions[i].session_a.slug = this.lang.tra('pj_lang');
        this.sessions[i].session_a.datetime_start = this.getPJDate(this.sessions[i].session_a.datetime_start);
        this.sessions[i].session_a.datetime_end = this.getPJDate(this.sessions[i].session_a.datetime_end);
      }
      // PJ Session Math
      if(this.sessions[i].session_b && this.sessions[i].session_b.caption === "1") {
        this.sessions[i].session_b.slug = this.lang.tra('pj_math');
        this.sessions[i].session_b.datetime_start = this.getPJDate(this.sessions[i].session_b.datetime_start);
        this.sessions[i].session_b.datetime_end = this.getPJDate(this.sessions[i].session_b.datetime_end);
      }
    }
   }
    this.sessionsTable = new MemDataPaginated({
      data: this.sessions,
      pageSize: 15,
      filterSettings: {
        slug: (session: ISession, val: string) => {
          if (this.currentClassFilter) {
            if(this.currentClassFilter === ClassFilterId.Primary) {
              return session.slug.indexOf('PRIMARY_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.Junior) {
              return session.slug.indexOf('JUNIOR_') === 0
            }
            else if(this.currentClassFilter === ClassFilterId.G9) {
              return session.slug.indexOf('G9_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.OSSLT) {
              return session.slug.indexOf('OSSLT_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.TCLE) {
              return session.slug.indexOf('TCLE_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.TCN) {
              return session.slug.indexOf('TCN_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.SCIENCES8) {
              return session.slug.indexOf('SCIENCES8_') === 0
            }
            else if (this.currentClassFilter === ClassFilterId.MBED_SAMPLE) {
              return session.slug.indexOf('MBED_') === 0
            }
          } else {
            const slug = this.getSessionSlug(session.slug) || '';
            return slug.indexOf(val) === 0;
          }
        },
        type: (session: ISession, val: string) => session.slug.includes(val) ,
        tabType: (session: ISession) => this.filterSessionTabType(session),
        testWindowFilter:  (session: ISession) => this.filterSessionTestWindow(session),
        invigilators: (session: ISession, val: string) => _.includes(this.getTeachers(session, true).toLocaleLowerCase(), val.toLocaleLowerCase()),
        teacher: (session: ISession, val: string) => _.includes(this.getTeachers(session).toLocaleLowerCase(), val.toLocaleLowerCase()),
      },
      sortSettings: {
        // invigilators: (session: ISession) => _.orderBy(this.getTeachers(session, true).toLowerCase(), ['asc']),
        teacher: (session: ISession) => _.orderBy(this.getTeachers(session).toLowerCase(), ['asc']),
        type : (session: ISession) => session.slug.includes("SAMPLE"),
      }
    });
    this.isLoaded = true;
  }

  public sortNumericallyByStudents(a: Partial<ISession>)
  {
    return +a.students;
  }

  public sortNumericallyByAssessmentType(a: Partial<ISession>)
  {
    return +a.isFieldTest;
  }

  getSubmissionStatus(res, session) {
    const submissions = []
    const states = res[0].session_states.find(state => { return state.test_session_id === session.test_session_id }).states.studentStates;

    for (const [key, value] of Object.entries(states)) {
      for (const [innerKey, innerValue] of Object.entries(value)) {
        if (innerKey === 'is_submitted' && innerValue === 1) {
          submissions.push(key)
        }
      }
    }
    return submissions;
  }
  archiveSession(sessionId) {
    return this.auth.apiRemove(this.routes.SCHOOL_ADMIN_SESSION, sessionId, this.configureQueryParams())
  }

  getCurrentFilteredSessions(){
    const targetSessions = this.sessions.filter(s => this.filterSessionTestWindow(s));
    return targetSessions;
  }

  refreshSessionTabCounts(){
    for (let sessionTab of this.sessionTabs){
      if (this.currentTestWindow){
        const targetSessions = this.sessions.filter(s => this.filterSessionTestWindow(s)).filter(s => sessionTab.filterFunction(s));
        sessionTab.count = targetSessions.length;
      } 
      else {
        sessionTab.count = 0;
      }
    }
  }

  selectSessionTab(sessionType: SESSION_TYPE){
    this.setSelectAll(false);
    this.selectedSessionType = sessionType;
    this.setSessionTabFilter();
    this.sessionsTable.gotoEntryPage(1)
  }

  filterSessionTabType(session: ISession){
    if (!this.selectedSessionType) return false;
    const sessionTab = this.sessionTabs.find(t => t.id == this.selectedSessionType)
    return sessionTab.filterFunction(session)
  }

  allowCancelSessionCheck()
  {
    const completedSessions = this.sessions.filter(s => s.isclosed && !s.is_cancelled)
    const targets = completedSessions.concat(this.sessions).filter(entry => entry.__isSelected);
    const activeSession = targets.find(session => session.status === 'active');
    const submittedSession = targets.find(session => completedSessions.includes(session));
    this.allowCancelSession = activeSession === undefined && submittedSession === undefined;

    // console.log(targets, activeSession, submittedSession, this.isAnySelected);
  }

  allowABEDOperAssessmentCheck()
  {
    if (this.whitelabelService.isABED())
    {
      const completedSessions = this.sessions.filter(s => s.isclosed && !s.is_cancelled)
      const targets = completedSessions.concat(this.sessions).filter(entry => entry.__isSelected);
      console.log(targets);
      const nonFTABEDAssessments = targets.find(session => session.isFieldTest === 0);
      this.isABEDNonFTAssessmentSelected = !(nonFTABEDAssessments === undefined);
    }
  }

  shouldEditBtnBeDisabled()
  {
    return !this.isAnySelected
    || !this.currentClassFilter
    // || this.isShowingSubmitted
    || !this.isCurrentTestWindowActive()
    || this.isABEDNonFTAssessmentSelected;
  }

  cancelSelectedSessions() {
    let confirmationMsg;
    if (this.whitelabelService.isABED()) {
      confirmationMsg = this.lang.tra("abed_remove_session_confirm");
    } else {
      confirmationMsg = this.lang.tra("sa_remove_session");
    }
    let removeSessionPopUpShown: boolean = false;
    const targets = [].concat(this.sessions).filter(entry => entry.__isSelected);
    this.loginGuard.confirmationReqActivate({
      caption: confirmationMsg,
      confirm: () =>
      {
        targets.map(entry =>
        {
          this.archiveSession(entry.id)
            .then(res => {
              if(entry.status === 'active'){
                this.g9DemoData.activeSessionsCount--;
              }
              else{
                this.g9DemoData.scheduledSessionsCount--;
              }
              entry.is_cancelled = 1;
              entry.status = SessionStatus.CANCELLED;
              if(entry.paymentStatus == "lbl_required"){
                let classFilterSlug:string = null;
                classFilterSlug = this.convertClassFilterSlug(entry.slug);
                if(classFilterSlug !== null && classFilterSlug !== undefined){
                  if(classFilterSlug == 'lbl_primary' && this.unpaidPrimaryClassFilterCount == 1) {
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                  if(classFilterSlug == 'lbl_junior' && this.unpaidJuniorClassFilterCount == 1) {
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                  console.log(this.unpaidG9ClassFilterCount)
                  if(classFilterSlug == 'txt_g9_math' && this.unpaidG9ClassFilterCount == 1) {
                    console.log(this.unpaidG9ClassFilterCount)
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                  if(classFilterSlug == 'lbl_osslt' && this.unpaidOssltClassFilterCount == 1) {
                    this.unPaidStudentCurrentFilterCancelled.emit(classFilterSlug);
                  }
                }
                this.removeUnpaidClassFilterSlug(classFilterSlug);
              }
              this.sessionsTable.injestNewData(this.sessions);
              this.selectSessionTab(this.selectedSessionType);
              this.refreshSessionTabCounts();

              // only scheduled assessments can be cancelled
              this.g9DemoData.dynamicallyUpdateSessionCount(entry, "scheduled", true);

              if (!removeSessionPopUpShown && targets.length === 1)
              {
                alert(this.lang.tra('msg_session_cancelled'));
                removeSessionPopUpShown = true;
              }

              else if (!removeSessionPopUpShown && targets.length > 1)
              {
                alert(this.lang.tra('abed_msg_multiple_session_cancelled'));
                removeSessionPopUpShown = true;
              }
            })
            .catch(error => {
              if (error.message === 'NO_CANCEL_FOR_ACTIVE_SESSION'){
                alert("Can't cancel ongoing session");
              }
            })
        });


      }
    })
  }

  removeUnpaidClassFilterSlug(classFilterSlug:string){
    if(classFilterSlug == 'lbl_primary' && this.unpaidPrimaryClassFilterCount > 0) {
      this.unpaidPrimaryClassFilterCount--;
    }
    if(classFilterSlug == 'lbl_junior' && this.unpaidJuniorClassFilterCount > 0) {
      this.unpaidJuniorClassFilterCount--;
    }
    if(classFilterSlug == 'txt_g9_math' && this.unpaidG9ClassFilterCount > 0) {
      this.unpaidG9ClassFilterCount--;
    }
    if(classFilterSlug == 'lbl_osslt' && this.unpaidOssltClassFilterCount > 0) {
      this.unpaidOssltClassFilterCount--;
    }
  }

  configureQueryParams() {
    const schoolData: any = this.g9DemoData.schoolData
    if (schoolData) {
      return {
        query: {
          schl_group_id: schoolData.group_id,
        }
      }
    }
    return null;
  }
  getDate(dateTime) {
    const mStart = moment.tz(dateTime, moment.tz.guess());
    if(mStart.format(this.lang.tra('datefmt_sentence')) == 'Invalid date'){
      return dateTime
    }
    let timezone = mStart.zoneAbbr();
    timezone = timezone == 'EST' ? this.lang.tra('est_timezone_label') : this.lang.tra('edt_timezone_label')
    return `${mStart.format(this.lang.tra('datefmt_sentence'))} ${timezone}`
  }

  getPJDate(dateTime) {
    const mStart = moment.tz(dateTime, moment.tz.guess());
    if(mStart.format(this.lang.tra('pj_datefmt_sentence')) == 'Invalid date'){
      return dateTime
    }
    return `${mStart.format(this.lang.tra('pj_datefmt_sentence'))}`
  }

  returnSessionStatus(dateTime) {
    let currDate = new Date();
    let sessionDate = new Date(dateTime)
    if (currDate.getTime() > sessionDate.getTime()) {
      return true;
    }
    return false;
  }
  getStudentsRoute() {
    // return ['',
    //   this.lang.c(),
    //   AccountType.SCHOOL_ADMIN,
    //   'students'
    // ].join('/')
  }

  openInvigilation(session){
    this.loginGuard.quickPopup(this.lang.tra('msg_invigilate_myself_error'))
  }

  openStudentCountsModal(classroom_id) {
    this.getStudentList(classroom_id);
    const config = {};
    this.pageModal.newModal({
      type: SessionModal.STUDENTS_SESSION,
      config,
      finish: () => {}
    })
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() {return this.cModal().config; }

  // New Session
  newSessionModalStart(sessionConfig?: any) {
    const config = sessionConfig || {
      filter: this.currentClassFilter,
      assessment_def_id: this.currentAssessmentDefId,
      testWindow: this.currentTestWindow
    };

    // console.log(this.currentClassFilter, "filter");
    this.pageModal.newModal({
      type: SessionModal.NEW_SESSION,
      config,
      finish: this.newSessionModalFinish
    });
  }
  newSessionModalFinish = (config: { payload }) => {
    if(this.validateAssesment(config)){
      this.finalizeAssessmentCreation(config);
    }
  }
  verifyScheduleGracePeriod(sessionAEnd,dateToCompare){
    const gracePeriod = 1;
    const startDate = sessionAEnd + (gracePeriod*60*1000);
    console.log(startDate, dateToCompare)
    if(dateToCompare < startDate){
      return false;
    }
    return true;
  }
  isSessionScheduled(payload){
    // if(payload.slug === "G9_SAMPLE" || payload.slug === "OSSLT_SAMPLE"){
    //   return false;
    // }
    const session = this.sessions.find(session => parseInt(session.classroom_id) === parseInt(payload.school_class_id) && (session.slug === payload.slug));
    if(session){
      if(this.currentClassFilter === ClassFilterId.Primary) {
        switch (payload.slug){
          case ASSESSMENT.PRIMARY_SAMPLE:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_sample_primary'))
            break;
          case ASSESSMENT.PRIMARY_OPERATIONAL:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_primary'))
            break;
        }
      }
      if(this.currentClassFilter === ClassFilterId.Junior) {
        switch (payload.slug){
          case ASSESSMENT.JUNIOR_SAMPLE:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_sample_junior'))
            break;
          case ASSESSMENT.JUNIOR_OPERATIONAL:
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_junior'))
            break;
        }
      }
      if(this.currentClassFilter === ClassFilterId.G9) {
        switch (payload.slug){
          case "G9_SAMPLE":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_g9'))
            break;
          case "G9_OPERATIONAL":
            this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_assessment'))
            break;
        }
      }
      if (this.currentClassFilter === ClassFilterId.OSSLT) {
        switch (payload.slug){
          case "OSSLT_SAMPLE":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_osslt'))
            break;
          case "OSSLT_OPERATIONAL":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'))
            break;
        }
        //this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'))
      }

      if (ABED_ASSESSMENTS.includes(this.currentClassFilter)) {
        switch (payload.slug) {
          case "ABED_SAMPLE":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_abed'));
            break;
          case "ABED_OPERATIONAL":
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_abed_assessment'));
            break;
          default:
            this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_abed_assessment_default'));
            break;
        }
      }
     return true;
    }


    return false;
  }
  verifySchedulingconditions(sessionAStartDay,sessionAStartTime,sessionBStartDay,sessionBStartTime){

    if(sessionBStartDay.isBefore(sessionAStartDay)){
      this.schedulerErrors = 'msg_ses_b_before_ses_a';
      alert(this.lang.tra('msg_ses_b_before_ses_a'))
      throw new Error();
    }
    if(!this.verifyScheduleGracePeriod(sessionAStartTime,sessionBStartTime)){
      this.schedulerErrors = 'msg_time_gap';
      alert(this.lang.tra('msg_time_gap'))
      throw new Error();
    }
  }

  verifyPJSchedulingConditions(sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate){
    const validateDates = (endDate, startDate): void => {
      if ((endDate && !startDate) || (!endDate && startDate)) {
        this.schedulerErrors = 'sa_asses_time_invalid';
        alert(this.lang.tra('sa_asses_time_invalid'))
        throw new Error();
      }
      if(endDate && startDate && endDate.isBefore(startDate)){
        this.schedulerErrors = 'msg_pj_end_before_start';
        alert(this.lang.tra('msg_pj_end_before_start'))
        throw new Error();
      }
    };
    validateDates(sessionLangEndDate, sessionLangStartDate);
    validateDates(sessionMathEndDate, sessionMathStartDate);
  }

  checkVerifyPastDate = (payload, pjSchedulePayload) => {
    if(this.whitelabelService.isNBED() || this.whitelabelService.isMBED() || this.whitelabelService.isABED()) { return false } // only Now optional available
    if(this.isG9OrOsslt() || (this.isPrimaryOrJunior() && !payload.scheduled_time)) {
      return this.verifyPastDate(payload)
    }
    return this.verifyPastDate(pjSchedulePayload);
  }

  finalizeAssessmentCreation(config) {

    let sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate, sessionAStartDay, sessionAStartTime, sessionBStartDay, sessionBStartTime;
    const payload = this.getPayload(config);
    console.log('new payload', payload);
    let pjSchedulePayload;
    if(payload.scheduled_time)
    {
      if (ABED_ASSESSMENTS.includes(this.currentClassFilter))
      {
        sessionAStartDay = moment(payload.scheduled_time[0],'YYYY-MM-DD');
        sessionAStartTime = new Date(payload.scheduled_time[0]).getTime();
      }

      if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT) {
        sessionAStartDay = moment(payload.scheduled_time[0],'YYYY-MM-DD');
        sessionAStartTime = new Date(payload.scheduled_time[0]).getTime();
        sessionBStartDay = moment(payload.scheduled_time[1],'YYYY-MM-DD');
        sessionBStartTime = new Date(payload.scheduled_time[1]).getTime();
      }
      if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
        if (payload.isLangScheduled) {
          sessionLangStartDate = payload.scheduled_time[0] ? moment(payload.scheduled_time[0],'YYYY-MM-DD') : null;
          sessionLangEndDate = payload.scheduled_time[1] ? moment(payload.scheduled_time[1],'YYYY-MM-DD') : null;
        }
        if (payload.isMathScheduled) {
          sessionMathStartDate = payload.scheduled_time[2] ? moment(payload.scheduled_time[2],'YYYY-MM-DD') : null;
          sessionMathEndDate = payload.scheduled_time[3] ? moment(payload.scheduled_time[3],'YYYY-MM-DD') : null;
        }
      }
      const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == Number(config.payload.data.classId));
      const semester = this.g9DemoData.semesters.list.find(semester => semester.id == Number(classroom.semester));
      const testWindow = this.g9DemoData. testWindows.find(window => window.id == semester.testWindowId)

      if (payload.slug === ASSESSMENT.OSSLT_OPERATIONAL)
      {
        const windowStart = new Date(testWindow.date_start).toISOString().split('T');
        const windowEnd =  new Date(testWindow.date_end).toISOString().split('T');
        const alertSlug = 'msg_osslt_administration_window_warning';
        this.validateTestDatesWindow(payload.scheduled_time[0], payload.scheduled_time[1], windowStart, windowEnd, alertSlug)
      }

      else if(payload.slug === ASSESSMENT.G9_OPERATIONAL)
      {
        const windowStart = new Date(testWindow.date_start).toISOString().split('T');
        const windowEnd =  new Date(testWindow.date_end).toISOString().split('T');
        const alertSlug = 'msg_g9_administration_window_warning';
        this.validateTestDatesWindow(payload.scheduled_time[0], payload.scheduled_time[1], windowStart, windowEnd, alertSlug)
      }

      else if(payload.slug === ASSESSMENT.PRIMARY_OPERATIONAL || payload.slug === ASSESSMENT.JUNIOR_OPERATIONAL)
      {
        const windowStart = new Date(testWindow.date_start).toISOString().split('T')[0];
        const windowEndDate =  new Date(testWindow.date_end).toISOString().split('T');
        const windowTime = windowEndDate[1].split('.')[0];
        const windowEnd =`${windowEndDate[0]} ${windowTime}`;
        const alertSlug = 'msg_pj_administration_window_warning';
        this.validatePJTestDatesWindow(payload.scheduled_time[0], payload.scheduled_time[1], payload.scheduled_time[2], payload.scheduled_time[3], windowStart, windowEnd, alertSlug)
      }

      else if (ABED_ASSESSMENTS.includes(this.currentClassFilter))
      {
        const windowStart = new Date(testWindow.date_start).toISOString().split('T');
        const windowEnd =  new Date(testWindow.date_end).toISOString().split('T');
        const alertSlug = 'msg_abed_administration_window_warning';
        this.validateTestDatesWindow(payload.scheduled_time, null, windowStart, windowEnd, alertSlug);
      }


      if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT)
      {
        if(payload.scheduled_time[0] && payload.scheduled_time[1]){
          this.verifySchedulingconditions(sessionAStartDay, sessionAStartTime,sessionBStartDay,sessionBStartTime)
        }
      }

      if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
        const validSchedules: string[] = [];
        if (payload.isLangScheduled) {
          validSchedules.push(payload.scheduled_time[0], payload.scheduled_time[1]);
        }
        if (payload.isMathScheduled) {
          validSchedules.push(payload.scheduled_time[2], payload.scheduled_time[3]);
        }
        pjSchedulePayload = JSON.parse(JSON.stringify(payload));
        pjSchedulePayload.scheduled_time = validSchedules;
        this.verifyPJSchedulingConditions(sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate);
      }
    }

    if(this.isABED() && !payload.isScheduled){
      if (this.isDateTimeInTheFuture(payload.date_start)) {
        alert(this.lang.tra("abed_future_schedule_error"));
        throw new Error();
      }
    }
    let isVerifiedPastDate = this.checkVerifyPastDate(payload, pjSchedulePayload)
    if (this.isSessionScheduled(payload)) {
      throw new Error();
    }
    if (!isVerifiedPastDate && !this.isSessionScheduled(payload)){
      const is_fi_class = !this.isLanguageVisibleForFIClass(Number(payload.school_class_id));
      const newPayload = {...payload, is_fi_class};

      if (this.classHasNoTeacher(config.payload.data.classId)) {
        alert(this.lang.tra('msg_no_teacher'));
        this.pageModal.closeModal();
        return;
      }

      this.verifySessionCreationPayload(payload);
      // we do not care about checking for students when creating an assessment session for a class, in ABED
      if(this.checkForStudents(config) || this.whitelabelService.isABED()){
        return this.auth
          .apiCreate(this.routes.SCHOOL_ADMIN_SESSION, newPayload, this.configureQueryParams())
          .then((session: any) => {
            // console.log("after cretaion",session);
            this.logSessionBTime(session.test_session_id, config.payload.data)
            let session_A = null;
            let session_B = null;

            if(isAssessmentABED(session.slug))
            {
              if(session[0] || session[1]) {
                if(session[0] && session[0].caption === 'A') {
                  session_A = session[0];
                  if(session[1] && session[1].caption === 'B') {
                    session_B = session[1];
                  }
                } else if(session[1] && session[1].caption === 'A') {
                  session_A = session[1];
                  if(session[0] && session[0].caption === 'B') {
                    session_B = session[0];
                  }
                }
              }
            }
            else{
              this.logSessionBTime(session.test_session_id, config.payload.data)
              let session_A = null;
              let session_B = null;

              if(isAssessmentABED(session.slug))
              {
                if(session[0] || session[1]) {
                  if(session[0] && session[0].caption === 'A') {
                    session_A = session[0];
                    if(session[1] && session[1].caption === 'B') {
                      session_B = session[1];
                    }
                  } else if(session[1] && session[1].caption === 'A') {
                    session_A = session[1];
                    if(session[0] && session[0].caption === 'B') {
                      session_B = session[0];
                    }
                  }
                }
              }
            }
            if(session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.JUNIOR_SAMPLE) {
              if(session[0] || session[2]) {
                if(session[0] && session[0].caption === 'A') {
                  session_A = session[0];
                  if(session[2] && session[2].caption === '1') {
                    session_B = session[2];
                  }
                } else if(session[2] && session[2].caption === 'A') {
                  session_A = session[2];
                  if(session[0] && session[0].caption === '1') {
                    session_B = session[0];
                  }
                }
              }
            }
            if(session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
              if(session[0] || session[4]) {
                if(session[0] && session[0].caption === 'A') {
                  session_A = session[0];
                  if(session[4] && session[4].caption === '1') {
                    session_B = session[4];
                  }
                } else if(session[4] && session[4].caption === 'A') {
                  session_A = session[4];
                  if(session[0] && session[0].caption === '1') {
                    session_B = session[0];
                  }
                }
              }
            }
            let slug_a;
            let slug_b;
            if (isAssessmentABED(session.slug))
            {
              slug_a = session_A.slug;
              slug_b = session_B ? session_B.slug : null;
            }

            if(session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_SAMPLE || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
              slug_a = session_A.slug.includes('lang_') ? 'pj_lang' : 'pj_math';
              slug_b = session_B.slug.includes('math_') ? 'pj_math' : 'pj_lang';
            }
            let updatedStatus = payload.isScheduled ? 'pending' : 'active';
            if(session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_SAMPLE || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
              if(session_A && session_A.caption == 'A') {
                if (session_A.datetime_start) {
                  updatedStatus = this.detectSessionStatus(session_A.datetime_start, false);
                }
                if(session_B && session_B.caption == '1') {
                  if((updatedStatus === 'pending' || updatedStatus === null) && session_B.datetime_start){
                    updatedStatus = this.detectSessionStatus(session_B.datetime_start, false);
                  }
                }
              }
            }

            let teacher = this.getClassTeacher(config.payload.data.classId);
            if (teacher == null) {
              teacher = {
                invigilator: "",
                firstName: "",
                lastName: ""
              }
            }
            function getDataFromStudentIfExists(session: any, teacher: any) {
              const someStudent = session?.studentRecords?.length && session?.studentRecords[0];
              if (someStudent) {
                return {
                  invigilator: `${someStudent.first_name} ${someStudent.last_name}`,
                  firstName: someStudent.first_name,
                  lastName: someStudent.last_name,
                  students: someStudent.num_students
                }
              } else {
                return {
                  invigilator: teacher.invigilator,
                  firstName: teacher.firstName,
                  lastName: teacher.lastName,
                  students: 0
                }
              }
            }
            const dataFromStudent = getDataFromStudentIfExists(session, teacher);
            const { invigilator, firstName, lastName, students } = dataFromStudent;

            const newGeneratedSession = {
              id: session.test_session_id,
              school_name:null,
              invigilator,
              classroom_id: session.school_class_id,
              firstName,
              lastName,
              description: '',
              classCode: session.class_code,
              students,
              startTime: this.getFormattedDateTime(session.date_time_start),
              startTimeUTC: session.date_time_start,
              isFieldTest: session.isFieldTest,
              tw_slug: this.currentClassFilter,
              endTime: '',
              slug:session.slug,
              submissions:0,
              submissions_1:0,
              status: updatedStatus,
              isConfirmed: coinFlip(),
              isclosed: false,
              session_a:{
                slug: this.lang.tra(slug_a),
                datetime_start: this.getFormattedDateTime(session_A.datetime_start),
                datetime_end: session_A.datetime_end != null ? this.getPJDate(session_A.datetime_end) : null,
                caption: session_A.caption
              },
              session_b: session_B ? {
                slug: this.lang.tra(slug_b),
                datetime_start: this.getFormattedDateTime(session_B.datetime_start),
                datetime_end: session_B.datetime_end != null ? this.getPJDate(session_B.datetime_end) : null,
                caption: session_B.caption
              } : null
            };

            const sessionStatus = newGeneratedSession.status === "pending" ? "scheduled" : "active";
            this.g9DemoData.dynamicallyUpdateSessionCount(newGeneratedSession, sessionStatus);
            this.processSession(newGeneratedSession);
            this.sessionsTable.injestNewData(this.sessions);
            // this.selectSessionTab(this.selectedSessionType);
            this.g9DemoData.assessments.list.push(newGeneratedSession);
            this.g9DemoData.assessments.map[newGeneratedSession.id] = newGeneratedSession;
            alert(this.lang.tra('msg_new_session'));
            this.pageModal.closeModal();

          this.updateSessionsCount(payload);
          })
          .catch(error =>{
            if (error.message === 'TECH_READI_PENDING'){
              alert(this.lang.tra('msg_tech_read_incomplete'));
            }
            else if (error.message === 'ONGOING_SAMPLE_ASSESSMENT') {
              if (this.currentClassFilter === ClassFilterId.Primary) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_primary'))
              } else if (this.currentClassFilter === ClassFilterId.Junior) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_sample_junior'))
              } else if (this.currentClassFilter === ClassFilterId.G9) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_g9'))
              } else if (this.currentClassFilter === ClassFilterId.OSSLT) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_practice_osslt'));
              }
            }
            else if (error.message === 'ONGOING_LIVE_ASSESSMENT') {
              if (this.currentClassFilter === ClassFilterId.Primary) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_primary'))
              } else if (this.currentClassFilter === ClassFilterId.Junior) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_junior'))
              } else if (this.currentClassFilter === ClassFilterId.G9) {
                this.loginGuard.disabledPopup(this.lang.tra('sa_ongoing_operational_assessment'))
              } else if (this.currentClassFilter === ClassFilterId.OSSLT) {
                this.loginGuard.disabledPopup(this.lang.tra('msg_ongoing_osslt_assessment'));
              }
            }
          })
      }
      else{
        alert(this.lang.tra('msg_no_students'))
      }
    }
  }

  verifySessionCreationPayload = (payload) => {
    if (!payload.slug) {
      const caption = this.whitelabelService.isABED() ? 'abed_select_ass' : 'sa_asses_type_invalid'
      this.loginGuard.quickPopup(caption);
      throw new Error();
    }
  }

  isSaSessionCreationDisabled(){
    return this.whitelabelService.getSiteFlag('IS_SA_SESSION_DISABLED')
  }

  isDateTimeInTheFuture(UTCDateTime){
    const inputDTMoment = moment(UTCDateTime).utc(); // in UTC
    const nowDTMoment = moment().utc(); // in UTC
    return inputDTMoment.isAfter(nowDTMoment);
  }

  updateSessionsCount(payload){
    if(payload.isScheduled){
      this.g9DemoData.scheduledSessionsCount++;
    }
    else{
      this.g9DemoData.activeSessionsCount++;
    }
  }
  checkForStudents(config){
   return (this.g9DemoData.getStudentsByClassroomId(config.payload.data.classId).list.length > 0)
  }

  classHasNoTeacher(classId: number): boolean {
    const classroomIdx = this.g9DemoData.classrooms.findIndex(classroom => +classroom.id === +classId);
    if (classroomIdx === -1) {
      console.log("Cannot find class to create assessment session for, error");
      return true;
    }

    const teacherUid = this.g9DemoData.classrooms[classroomIdx].teacher_uid;
    return !(teacherUid != null && !isNaN(teacherUid) && +teacherUid > 0);
  }

  getClassTeacher(classId: number): any {
    const classroomIdx = this.g9DemoData.classrooms.findIndex(classroom => +classroom.id === +classId);
    if (classroomIdx === -1) {
      return null;
    }

    const teacherUid = this.g9DemoData.classrooms[classroomIdx].teacher_uid;
    if (teacherUid == null || isNaN(teacherUid) +teacherUid <= 0) {
      return null;
    }

    return this.g9DemoData.teachers.map[+teacherUid];

  }

  getStudentList(classroom_id) {
    this.studentList = this.g9DemoData.getStudentsByClassroomId(classroom_id).list;
  }
  isDateValid:boolean;
  setDateValidity($event){
    this.isDateValid = $event;
  }

  getFormattedDateTime(dateTime: string)
  {
    if (this.whitelabelService.isABED())
    {
      // this is because this time is already in ABED time, not UTC - so pass in the TZ of the input time as the 2nd parameter
      return this.auth.formatDateForWhitelabel(dateTime, this.whitelabelService.getTimeZone());
    }

    return this.isG9OrOsslt() ? this.getDate(dateTime) : this.getPJDate(dateTime)
  }
  // verifyPastDate(payload) {
  //   if(!payload.isScheduled){
  //     return false
  //   }

  //   let currDate = new Date();
  //   let selectedDate = new Date(payload.scheduled_time)
  //   if(!this.isDateValid){
  //     alert(this.lang.tra('Invalid date entered!'))
  //     return true;
  //   }
  //   if (Object.prototype.toString.call(selectedDate) === "[object Date]") {
  //     if (isNaN(selectedDate.getTime())) {  // d.valueOf() could also work
  //       alert(this.lang.tra('Invalid date entered!'))
  //       return true;
  //       console.log('date is not valid')
  //     }
  //   } else {
  //     alert(this.lang.tra('Invalid date entered!'))
  //     return true;
  //     console.log('date is invalid')
  //   }

  //   if (currDate.getTime() > selectedDate.getTime()) {
  //     alert(this.lang.tra('msg_date_time_passed'))
  //     return true;
  //   }
  //   return false;
  // }
  verifyPastDate(payload) {
    if(!payload.isScheduled){
      return false;
    }
    if(!this.isDateValid){
      alert(this.lang.tra('Invalid date entered!'))
      return true;
    }
    let isInvalid = false;
    let dateTime = payload.scheduled_time
    for(let i =0; i<dateTime.length; i++){
      if (dateTime[i]){
        let currDate = new Date();
        let selectedDate;
        if(this.currentClassFilter == ClassFilterId.G9 || this.currentClassFilter == ClassFilterId.OSSLT) {
          selectedDate = new Date(dateTime[i]);
        }
        if(this.currentClassFilter == ClassFilterId.Primary || this.currentClassFilter == ClassFilterId.Junior) {
          selectedDate = moment.tz(dateTime[i], this.STANDARD_TIMEZONE).toDate();
        }
        if (Object.prototype.toString.call(selectedDate) === "[object Date]") {
          if (isNaN(selectedDate.getTime())) {  // d.valueOf() could also work
            alert(this.lang.tra('Invalid date entered!'))
            isInvalid = true
            break;
          }
        } else {
          alert(this.lang.tra('Invalid date entered!'))
          isInvalid = true;
          break;
        }
        if(this.currentClassFilter == ClassFilterId.G9 || this.currentClassFilter == ClassFilterId.OSSLT) {
          if (currDate.getTime() > selectedDate.getTime()) {
            alert(this.lang.tra('msg_date_time_passed'))
            isInvalid = true
            break;
          }
        }
        if(this.currentClassFilter == ClassFilterId.Primary || this.currentClassFilter == ClassFilterId.Junior) {
          const curr_date = moment(currDate).format('YYYY-MM-DD');
          const selected_date = moment(selectedDate).format('YYYY-MM-DD');
          if (curr_date > selected_date) {
            alert(this.lang.tra('msg_date_time_passed'))
            isInvalid = true
            break;
          }
        }
      }
    }
    return isInvalid;
  }

  getPayload(config){
    const slug = config.slug ?? config.payload.data.slug;
    const slugType = config.payload.data.slugType;
    const sessionName = config.sessionName;
    if(config.payload.data.schedule === SCHEDULER.LATER)
    {
      if(slug === ASSESSMENT.G9_SAMPLE){
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [`${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`]
        }
      }

      else if (ABED_ASSESSMENTS.includes(this.currentClassFilter)
      && slugType !== AssessmentTypeOptions.OPERATIONAL)
      {
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: `${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`
        }
      }

      else if (ABED_ASSESSMENTS.includes(this.currentClassFilter)
      && slugType === AssessmentTypeOptions.OPERATIONAL)
      {
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: config.payload.data.sessionAStartDateTime
        }
      }

      else if(slug === ASSESSMENT.G9_OPERATIONAL || slug === ASSESSMENT.OSSLT_SAMPLE || slug === ASSESSMENT.OSSLT_OPERATIONAL){
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [
            `${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`,
            `${config.payload.data.sessionBStartDate}T${this.timeConvert(config.payload.data.sessionBStartTime)}`
          ]
        }
      }
      else if(slug === ASSESSMENT.PRIMARY_SAMPLE || slug === ASSESSMENT.PRIMARY_OPERATIONAL || slug === ASSESSMENT.JUNIOR_SAMPLE || slug === ASSESSMENT.JUNIOR_OPERATIONAL){
        let sessionLangStartDate =  config.payload.data.sessionLangStartDate ? `${config.payload.data.sessionLangStartDate}` : null;
        let sessionLangEndDate =  config.payload.data.sessionLangEndDate ? `${config.payload.data.sessionLangEndDate}` : null;
        let sessionMathStartDate =  config.payload.data.sessionMathStartDate ? `${config.payload.data.sessionMathStartDate}` : null;
        let sessionMathEndDate =  config.payload.data.sessionMathEndDate ? `${config.payload.data.sessionMathEndDate}` : null;
        return {
          school_class_id: config.payload.data.classId,
          slug: slug,
          isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
          scheduled_time: [
            sessionLangStartDate,
            sessionLangEndDate,
            sessionMathStartDate,
            sessionMathEndDate,
          ],
          isLangScheduled: config.payload.data.sessionLangStartDate || config.payload.data.sessionLangEndDate ? true : false,
          isMathScheduled: config.payload.data.sessionMathStartDate || config.payload.data.sessionMathEndDate ? true : false,
        }
      }
    }
    if(this.isSimplePracticeSetup()){
      return {
        school_class_id: config.payload.data.classId,
        slug: slug,
        isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
        date_start: config.testWindow.date_start,
        sessionName
      }
    }else{
      return {
        school_class_id: config.payload.data.classId,
        slug: slug,
        isScheduled: (config.payload.data.schedule === SCHEDULER.LATER) ? true : false,
        date_start: config.testWindow.date_start
      }
    }
  }
  timeConvert(tm) {
    let time;
    if (tm.includes('pm')){
      time = tm.replace('pm',' PM')
    }
    else{
      time = tm.replace('am', ' AM')
    }
    var hours = Number(time.match(/^(\d+)/)[1]);
    var minutes = Number(time.match(/:(\d+)/)[1]);
    var AMPM = time.match(/\s(.*)$/)[1];
    if (AMPM == "PM" && hours < 12) hours = hours + 12;
    if (AMPM == "AM" && hours == 12) hours = hours - 12;
    var sHours = hours.toString();
    var sMinutes = minutes.toString();
    if (hours < 10) sHours = "0" + sHours;
    if (minutes < 10) sMinutes = "0" + sMinutes;
    return (sHours + ":" + sMinutes);
  }
  // Edit Session
  editSessionModalStart() {
    this.isActive = false;
    this.isPartialActive = false;
    const sessions = this.getSelectedSessions(true);
    const config = {
      sessions,
      filter: this.currentClassFilter,
    };
    if (config.sessions.length === 1) {
      const currentSession = config.sessions[0];
      if (currentSession.status === 'active') {
        this.isActive = true;
      }
      if (this.isPrimaryOrJunior() && currentSession.session_a && currentSession.session_b) {
        const isFutureDate = (startDate: string): boolean => {
          if (!startDate) {
            return true;
          }
          const processedDate = moment(startDate, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
          const now = moment().format('YYYY-MM-DD');
          if (processedDate <= now) {
            return false;
          }
          return true;
        };
        if ((!currentSession.session_a.datetime_start && !currentSession.session_a.datetime_end) || isFutureDate(currentSession.session_a.datetime_start)) {
          this.isPartialActive = true;
        } else if ((!currentSession.session_b.datetime_start && !currentSession.session_b.datetime_end) || isFutureDate(currentSession.session_b.datetime_start)) {
          this.isPartialActive = true;
        }
      }
    }
    this.pageModal.newModal({
      type: SessionModal.EDIT_SESSION,
      config,
      finish: this.editSessionModalFinish,
    });
  }

  private validateTestDatesWindow(a_string, b_string, windowStart, windowEnd, alertSlug)
  {
    const timezone = this.whitelabelService.getTimeZone();
    const currDateTime = moment().tz(timezone); // with time
    const a: moment.Moment | null = a_string ? moment(a_string.split("T")[0]) : null; // no time
    const b: moment.Moment | null = b_string ? moment(b_string.split("T")[0]) : null; // no time
    const aWithTime: moment.Moment | null = a_string ? moment(a_string).tz(timezone) : null; // with time
    const bWithTime: moment.Moment | null = a_string ? moment(a_string).tz(timezone) : null; // with time
    const windowStartDate: moment.Moment = moment(windowStart[0]); // no time
    const windowEndDate: moment.Moment = moment(windowEnd[0]); // no time
    const windowStartFormatted: string = windowStartDate.format("YYYY-MM-DD");
    const windowEndFormatted: string = windowEndDate.format("YYYY-MM-DD");
    const pastDateWarningSlug = "msg_abed_administration_window_past_warning"; // reuseable for EQAO and NBED too

    if (!a && b)
    {
      if (bWithTime.diff(currDateTime) < 0)
      {
        alert(this.lang.tra(pastDateWarningSlug));
        throw new Error();
      }

      if (b.isBefore(windowStartDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }

      if (b.isAfter(windowEndDate)){
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }
    }

    else if(!b && a)
    {
      // console.log(aWithTime.diff(currDateTime));
      if (aWithTime.diff(currDateTime) < 0)
      {
        alert(this.lang.tra(pastDateWarningSlug));
        throw new Error();
      }

      if (a.isBefore(windowStartDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }

      if (a.isAfter(windowEndDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }
    }

    else if(a && b)
    {
      if (aWithTime.diff(currDateTime) < 0 || bWithTime.diff(currDateTime) < 0)
      {
        alert(this.lang.tra(pastDateWarningSlug));
        throw new Error();
      }

      if (a.isBefore(windowStartDate) || b.isBefore(windowStartDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }

      else if (a.isAfter(windowEndDate) || b.isAfter(windowEndDate))
      {
        alert(this.lang.tra(alertSlug, undefined,
        {
          start: windowStartFormatted,
          end: windowEndFormatted
        }));
        throw new Error();
      }
    }
  }

  showSessionPaymentModal() {
    const config = {};
    const sessions = this.getSelectedSessions(true);
    if(sessions.length > 0){
      const paymentNotRequiredAssessment = sessions.find(session => session.paymentStatus === "lbl_not_required");
      const alternativePaymentPendingAssessment = sessions.find(session => session.altPaymentPending);
      const approvedPaymentAssessment = sessions.find(session => session.paymentStatus === "lbl_approved");
      if(paymentNotRequiredAssessment || alternativePaymentPendingAssessment || approvedPaymentAssessment) {
        const errorMsg_paymentNotRequired = paymentNotRequiredAssessment ? this.lang.tra('caption_private_sample_payment_not_required') : "";
        const errorMsg_alternativePaymentPending = alternativePaymentPendingAssessment ? this.lang.tra('caption_private_alternative_payment_pending') : "";
        const errorMsg_approvedPaymentAssessment = approvedPaymentAssessment ? this.lang.tra('caption_private_approved_payment') : "";
        const confirmationMsg = this.lang.tra('caption_private_select_session_error') + errorMsg_paymentNotRequired + errorMsg_alternativePaymentPending + errorMsg_approvedPaymentAssessment;
        this.loginGuard.confirmationReqActivate({
          caption: confirmationMsg,
          btnCancelConfig: {
            hide: true
          },
          confirm: () => {}
        })
      }
      else {
        this.pageModal.newModal({
          type: SessionModal.SESSION_PAYMENT_OVERVIEW,
          config,
          finish: () => {}
        })
      }
    }
  }

  isPaymentAvailable() {
    this.checkSelection();
    if(this.isAnySelected) {
      return this.getSelectedSessions(true).every(session => session.paymentStatus === "lbl_required");
    }
    return false
  }

  async getPaymentPolicy() {
    await this.auth.apiFind('public/school-admin/payment-policy', {
      query: {
        schl_group_id: this.g9DemoData.schoolData.group_id
      }
    })
    .then(paymentPolicy => {
      this.paymentPolicy = paymentPolicy;
    })
    .catch(e => {
      console.error(e);
    });
  }

  onStripePayment(event) {
    console.log('event', event);
    if (event) {
      const config = {
        totalCost: event.totalCost
      };
      this.pageModal.newModal({
        type: SessionModal.SESSION_PAYMENT_STRIPE_OVERVIEW,
        config,
        finish: () => {}
      })
    }
  }

  onAlternativePayment(event) {
    if (event && event.totalCost && event.diffInDays) {
      this.alternative_days_before_session = this.paymentPolicy.find(policy => policy.method_type == 'Alternative').days_before_session;

      if (event.diffInDays > this.alternative_days_before_session) {
        const config = {
          totalCost: event.totalCost
        };
        this.pageModal.newModal({
          type: SessionModal.SESSION_PAYMENT_ALTERNATIVE_OVERVIEW,
          config,
          finish: () => {}
        })
      } else {
        const config = {};
        this.pageModal.newModal({
          type: SessionModal.SESSION_PAYMENT_ALTERNATIVE_WARNING,
          config,
          finish: () => {}
        })
      }
    }
  }

  onCompletePaymentAlt(event) {
    if(event) {
      this.getSelectedSessions(true).forEach(session => {
        session.paymentStatus = "lbl_pending";
      })
      this.setSelectAll(false);
      this.pageModal.closeModal();
    }
  }

  getTestWindowViewText(tw){
    if(tw){
      const startDate = formatDate(new Date(tw.date_start), 'MMM yyyy', 'en_US')
      const endDate = formatDate(new Date(tw.date_end), 'MMM yyyy', 'en_US')
      const isActive = (new Date(tw.date_end) > new Date ())? "lbl_active":"lbl_inactive"
      return startDate+" "+this.lang.tra("lbl_date_to") +" "+endDate+" ("+this.lang.tra(isActive)+")";
    }
  }

  private validatePJTestDatesWindow(langStart, langEnd, mathStart, mathEnd, windowStart, windowEnd, alertSlug){
    const langStartDate = langStart ? moment(langStart) : undefined;
    const langEndDate = langEnd ? moment(langEnd) : undefined;
    const mathStartDate = mathStart ? moment(mathStart) : undefined;
    const mathEndDate = mathEnd ? moment(mathEnd) : undefined;
    const windowEndStr = windowEnd.split(' ')[0];

    if(langStartDate && langEndDate){
      if(langStartDate.isBefore(windowStart) || langStartDate.isAfter(windowEnd) || langEndDate.isBefore(windowStart) || langEndDate.isAfter(windowEnd)){
        alert(this.lang.tra(alertSlug, undefined, {start:windowStart,end:windowEndStr}))
        throw new Error();
      }
    }
    if(mathStartDate && mathEndDate){
      if(mathStartDate.isBefore(windowStart) || mathStartDate.isAfter(windowEnd) || mathEndDate.isBefore(windowStart) || mathEndDate.isAfter(windowEnd)){
        alert(this.lang.tra(alertSlug, undefined, {start:windowStart,end:windowEndStr}))
        throw new Error();
      }
    }
  }

/*   private validateTimes(_a, _b, assessment){
    console.log(_a, _b)
    const a = _a ? moment(_a) : undefined
    const b = _b ? moment(_b) : undefined
    const windowStart = moment('2021-03-23');
    const windowEnd = moment('2021-06-05');
    if (assessment === ASSESSMENT.OSSLT_OPERATIONAL){
      if(!a && b){
        if (b.isBefore(windowStart)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
        if (b.isAfter(windowEnd)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
      }
      else if(!b && a){
        if (a.isBefore(windowStart)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
        if (a.isAfter(windowEnd)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
      }
      else if(a && b){
        if (a.isBefore(windowStart) || b.isBefore(windowStart)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
        else if (a.isAfter(windowEnd) || b.isAfter(windowEnd)){
          alert(this.lang.tra('msg_osslt_administration_window_warning'))
          throw new Error();
        }
      }
    }
  } */

  private logSessionBTime(test_session_id, data){
    console.log('logSessionBTime', test_session_id, data)
    this.auth.apiCreate(this.routes.LOG, {
      slug:'SA_SESSION_B_DATE_TEMP',
      data: {
        test_session_id,
        data
      }
    })
  }

  editSessionModalFinish = (config: { payload }) => {
    if(!this.validateAssesment(config, true))
    {
      return;
    }

    if(!this.isActive || this.isPartialActive){
      if(config.payload){
        const record = config.payload.record;
        const data = config.payload.data;
        const {slug} = record;
        let scheduled_time:any[] = [];
        if (ABED_ASSESSMENTS.includes(this.currentClassFilter))
        {
          if(data.sessionBStartDate && data.sessionBStartTime) {
            scheduled_time.push(`${data.sessionAStartDate}T${this.timeConvert(data.sessionAStartTime)}`);
            scheduled_time.push(`${data.sessionBStartDate}T${this.timeConvert(data.sessionBStartTime)}`);
          } else if(!data.sessionBStartDate && !data.sessionBStartTime) {
            scheduled_time.push(`${data.sessionAStartDate}T${this.timeConvert(data.sessionAStartTime)}`);
          }
        }
        if(slug === ASSESSMENT.G9_SAMPLE || slug === ASSESSMENT.G9_OPERATIONAL || slug === ASSESSMENT.OSSLT_SAMPLE || slug === ASSESSMENT.OSSLT_OPERATIONAL) {
          if(data.sessionBStartDate && data.sessionBStartTime) {
            scheduled_time.push(`${data.sessionAStartDate}T${this.timeConvert(data.sessionAStartTime)}`);
            scheduled_time.push(`${data.sessionBStartDate}T${this.timeConvert(data.sessionBStartTime)}`);
          } else if(!data.sessionBStartDate && !data.sessionBStartTime) {
            scheduled_time.push(`${data.sessionAStartDate}T${this.timeConvert(data.sessionAStartTime)}`);
          }
        }
        if(slug === ASSESSMENT.PRIMARY_SAMPLE || slug === ASSESSMENT.PRIMARY_OPERATIONAL || slug === ASSESSMENT.JUNIOR_SAMPLE || slug === ASSESSMENT.JUNIOR_OPERATIONAL) {
          if (record.session_a) {
            if (record.session_a.datetime_start && record.session_a.datetime_end) {
              if (data.sessionLangStartDate === undefined || data.sessionLangStartDate === null) {
                data.sessionLangStartDate = moment(record.session_a.datetime_start, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
              if (data.sessionLangEndDate === undefined || data.sessionLangEndDate === null) {
                data.sessionLangEndDate = moment(record.session_a.datetime_end, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
            }
          }
          if (record.session_b) {
            if (record.session_b.datetime_start && record.session_b.datetime_end) {
              if (data.sessionMathStartDate === undefined || data.sessionMathStartDate === null) {
                data.sessionMathStartDate = moment(record.session_b.datetime_start, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
              if (data.sessionMathEndDate === undefined || data.sessionMathEndDate === null) {
                data.sessionMathEndDate = moment(record.session_b.datetime_end, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
              }
            }
          }
          let sessionLangStartDate = null, sessionLangEndDate = null, sessionMathStartDate = null, sessionMathEndDate = null;
          // if Language has been scheduled (edit modal), w/o Math
          if(data.sessionLangStartDate && data.sessionLangEndDate) {
            sessionLangStartDate = data.sessionLangStartDate;
            sessionLangEndDate = data.sessionLangEndDate;
          }
          // if Math has been scheduled (edit modal), w/o Language
          if(data.sessionMathStartDate && data.sessionMathEndDate) {
            sessionMathStartDate = data.sessionMathStartDate;
            sessionMathEndDate = data.sessionMathEndDate;
          }
          scheduled_time.push(sessionLangStartDate);
          scheduled_time.push(sessionLangEndDate);
          scheduled_time.push(sessionMathStartDate);
          scheduled_time.push(sessionMathEndDate);
        }
        const payload = {
          scheduled_time,
          slug
        }
        const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == Number(config.payload.record.classroom_id));
        const semester = this.g9DemoData.semesters.list.find(semester => semester.id == Number(classroom.semester));
        const testWindow = this.g9DemoData. testWindows.find(window => window.id == semester.testWindowId)
        if (ABED_ASSESSMENTS.includes(this.currentClassFilter))
        {
          const windowStart = new Date(testWindow.date_start).toISOString().split('T');
          const windowEnd =  new Date(testWindow.date_end).toISOString().split('T');
          const alertSlug = 'msg_abed_administration_window_warning';
          this.validateTestDatesWindow(payload.scheduled_time[0], payload.scheduled_time[0], windowStart, windowEnd, alertSlug)
          this.validateTestDatesWindow(payload.scheduled_time[1], payload.scheduled_time[1], windowStart, windowEnd, alertSlug)
        }

        if (config.payload.record.slug === ASSESSMENT.OSSLT_OPERATIONAL){
          const windowStart = new Date(testWindow.date_start).toISOString().split('T');
          const windowEnd =  new Date(testWindow.date_end).toISOString().split('T');
          const alertSlug = 'msg_osslt_administration_window_warning';
          this.validateTestDatesWindow(payload.scheduled_time[0], payload.scheduled_time[0], windowStart, windowEnd, alertSlug)
          this.validateTestDatesWindow(payload.scheduled_time[1], payload.scheduled_time[1], windowStart, windowEnd, alertSlug)
        } else if(config.payload.record.slug === ASSESSMENT.G9_OPERATIONAL){
          const windowStart = new Date(testWindow.date_start).toISOString().split('T');
          const windowEnd =  new Date(testWindow.date_end).toISOString().split('T');
          const alertSlug = 'msg_g9_administration_window_warning';
          this.validateTestDatesWindow(payload.scheduled_time[0], payload.scheduled_time[0], windowStart, windowEnd, alertSlug)
          this.validateTestDatesWindow(payload.scheduled_time[1], payload.scheduled_time[1], windowStart, windowEnd, alertSlug)
        } else if (config.payload.record.slug === ASSESSMENT.PRIMARY_OPERATIONAL || config.payload.record.slug === ASSESSMENT.JUNIOR_OPERATIONAL){
          const windowStart = new Date(testWindow.date_start).toISOString().split('T')[0];
          const windowEndDate =  new Date(testWindow.date_end).toISOString().split('T');
          const windowTime = windowEndDate[1].split('.')[0];
          const windowEnd =`${windowEndDate[0]} ${windowTime}`;
          const alertSlug = 'msg_pj_administration_window_warning';
          this.validatePJTestDatesWindow(payload.scheduled_time[0], payload.scheduled_time[1], payload.scheduled_time[2], payload.scheduled_time[3], windowStart, windowEnd, alertSlug)
        }
        if (ABED_ASSESSMENTS.includes(this.currentClassFilter))
        {
          if(config.payload.data.sessionAStartDate && config.payload.data.sessionBStartDate && config.payload.data.sessionAStartTime && config.payload.data.sessionBStartTime){
            this.verifySchedulingconditions(moment(config.payload.data.sessionAStartDate), new Date(`${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`).getTime(), moment(config.payload.data.sessionBStartDate),  new Date(`${config.payload.data.sessionBStartDate}T${this.timeConvert(config.payload.data.sessionBStartTime)}`).getTime())
          }
        }

        if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT) {
          if(config.payload.data.sessionAStartDate && config.payload.data.sessionBStartDate && config.payload.data.sessionAStartTime && config.payload.data.sessionBStartTime){
            this.verifySchedulingconditions(moment(config.payload.data.sessionAStartDate), new Date(`${config.payload.data.sessionAStartDate}T${this.timeConvert(config.payload.data.sessionAStartTime)}`).getTime(), moment(config.payload.data.sessionBStartDate),  new Date(`${config.payload.data.sessionBStartDate}T${this.timeConvert(config.payload.data.sessionBStartTime)}`).getTime())
          }
        }
        if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
          if(data.sessionLangStartDate && data.sessionLangEndDate && data.sessionMathStartDate && data.sessionMathEndDate) {
            this.verifyPJSchedulingConditions(moment(data.sessionLangStartDate), moment(data.sessionLangEndDate), moment(data.sessionMathStartDate), moment(data.sessionMathEndDate));
          }
        }

        return this.auth
          .apiPatch(this.routes.SCHOOL_ADMIN_SESSION, record.id, payload, this.configureQueryParams())
          .then((session: any) => {
            record['startTime'] = this.getFormattedDateTime(payload.scheduled_time[0]);

            if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior)
            {
              record['startTime'] = this.getPJDate(payload.scheduled_time[0]) <= this.getPJDate(payload.scheduled_time[2]) ? this.getPJDate(payload.scheduled_time[0]) : this.getPJDate(payload.scheduled_time[2]);
            }

            if (ABED_ASSESSMENTS.includes(this.currentClassFilter))
            {
              if(record.session_a && record.session_a.caption == 'A') {
                record.session_a.datetime_start = this.getDate(payload.scheduled_time[0]);
                if(record.session_b && record.session_b.caption == 'B') {
                  record.session_b.datetime_start = this.getDate(payload.scheduled_time[1]);
                }
              }
            }
            if(this.currentClassFilter === ClassFilterId.G9 || this.currentClassFilter === ClassFilterId.OSSLT) {
              if(record.session_a && record.session_a.caption == 'A') {
                record.session_a.datetime_start = this.getDate(payload.scheduled_time[0]);
                if(record.session_b && record.session_b.caption == 'B') {
                  record.session_b.datetime_start = this.getDate(payload.scheduled_time[1]);
                }
              }
            }
            if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior) {
              if(record.session_a && record.session_a.caption == 'A') {
                record.session_a.datetime_start = this.getPJDate(payload.scheduled_time[0]);
                record.session_a.datetime_end = this.getPJDate(payload.scheduled_time[1]);
                let updatedStatus = record.status;
                if (record.session_a.datetime_start) {
                  updatedStatus = this.detectSessionStatus(record.session_a.datetime_start);
                }
                if(record.session_b && record.session_b.caption == '1') {
                  record.session_b.datetime_start = this.getPJDate(payload.scheduled_time[2]);
                  record.session_b.datetime_end = this.getPJDate(payload.scheduled_time[3]);
                  if((updatedStatus === 'pending' || updatedStatus === null) && record.session_b.datetime_start){
                    updatedStatus = this.detectSessionStatus(record.session_b.datetime_start);
                  }
                }
                if(record.status !== updatedStatus){
                  record.status = updatedStatus;
                }
              }
            }
            this.logSessionBTime(session.test_session_id, data)
            alert(this.lang.tra('msg_session_edit_success'))
            this.pageModal.closeModal();
          })
      }
      else{
        alert(this.lang.tra('msg_session_select'));
      }
    }
    else{
      this.pageModal.closeModal()
    }
    this.isActive = false;
  }

  private detectSessionStatus(startDate: string, extractDate: boolean = true): string {
    if (!startDate) {
      return null;
    }
    const processedDate = extractDate ? moment(startDate, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD') : moment(startDate).format('YYYY-MM-DD') ;
    const now = moment().format('YYYY-MM-DD');
    if (processedDate <= now) {
      return 'active';
    }
    return 'pending';
  }

  getSelectedSessions(throwNullSet: boolean = false) {
    const sessionSelector = this.visibleSessions();

    const sessions: ISession[] = sessionSelector.filter((session) => session.__isSelected);
    if (throwNullSet && sessions.length === 0) {
      alert(this.lang.tra('msg_session_select'));
      throw new Error(this.lang.tra('msg_account_select'));
    }
    return sessions;
  }

  visibleSessions() {
    return this.sessionsTable.getCurrentPageData();
  }

  setAssessmentDefId(asmtDefId: number) {
    this.currentAssessmentDefId = asmtDefId;
  }

  setClassFilter(filterId) {
    this.onSetClassFilter.emit(filterId);
    this.currentClassFilter = filterId;
    const {payment_req_g9, payment_req_osslt, payment_req_pj} = this.g9DemoData.schoolData;
    this.sessionsTable.activeFilters['slug'] = null;
    if (this.currentClassFilter) {
      if(this.currentClassFilter === ClassFilterId.G9) {
        this.sessionsTable.activeFilters['slug'] = { mode: FilterSettingMode.VALUE, config: { value: ClassFilterId.G9 } }
        this.isPrivateSchool && payment_req_g9 ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.OSSLT) {
        this.sessionsTable.activeFilters['slug'] = { mode: FilterSettingMode.VALUE, config: { value: ClassFilterId.OSSLT } }
        this.isPrivateSchool && payment_req_osslt ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.Primary) {
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
      else if (this.currentClassFilter === ClassFilterId.Junior) {
        this.isPrivateSchool && payment_req_pj ? this.isPaymentModuleEnabled = true : this.isPaymentModuleEnabled = false;
      }
    }
    this.loadScanProgress();
    this.sessionsTable.refreshFilters();
    this.refreshSessionTabCounts();
  }

  async setTestWindowFilter(tw){
    if (!tw) {
      return;
    }

    this.currentTestWindow = tw;
    const isNewData = await this.g9DemoData.loadSessionsByTestWindow(tw.id);
    if (isNewData){
      this.loadSessions()
    }

    if (this.sessions && this.sessions.length > 0) {
      this.sessionsTable.activeFilters['testWindowFilter'] = {mode: FilterSettingMode.VALUE, config: { value: this.currentTestWindow } }
      this.sessionsTable.refreshFilters();
      this.refreshSessionTabCounts();
      this.loadScanProgress();
      this.selectSessionTab(this.sessionTabs[0].id)
    }
  }

  scanProgress:ScanSessionInfo = {};
  async loadScanProgress(){
    const targetSessions = this.getCurrentFilteredSessions();
    const test_session_ids:number[] = [];
    const tsRef = new Map()
    for (let ts of targetSessions){
      if (!this.scanProgress[ts.id]){
        test_session_ids.push(ts.id);
        tsRef.set(ts.id, ts)
      }
    }
    if (test_session_ids.length){
      const scanProgressPayload = await this.auth.apiCreate('public/school-admin/session-scan-progress', {test_session_ids: test_session_ids});
      // console.log('loadScanProgress', scanProgressPayload)
      for (let ts_id of test_session_ids){
        const ts = tsRef.get(ts_id)
        const tsScanProgress = scanProgressPayload?.scanProgress[ts_id];
        this.scanProgress[ts_id] = tsScanProgress
        ts.is_scan_session = tsScanProgress?.is_scan_session
        ts.n_students = tsScanProgress?.n_students
        ts.n_scans_expected = tsScanProgress?.n_scans_expected
        ts.n_scans_received = tsScanProgress?.n_scans_received
      }
      this.sessionsTable.refreshFilters();
      this.refreshSessionTabCounts();
    }
  }

  isSessionUploadReadyToStart(session){
    return session.n_scans_expected>0
  }
  isSessionUploadComplete(session){
    return (session.n_scans_received >= session.n_scans_expected)
  }

  setSessionTabFilter(){
    this.sessionsTable.activeFilters['tabType'] = {mode: FilterSettingMode.VALUE, config: { value: this.selectedSessionType } }
    this.sessionsTable.refreshFilters();
  }

  filterSessionTestWindow(session: ISession): boolean {
    return this.g9DemoData.filterSessionTestWindow(session, this.currentTestWindow.id);
  }

  isSimplePracticeSetup(){
    return !!this.whitelabelService.getSiteFlag('IS_SESSION_PRACTICE_ONLY')
  }

  validateAssesment(config, fromEdit = false){
    if(this.isSimplePracticeSetup()) return true;
    let sessionLangStartDate, sessionLangEndDate, sessionMathStartDate, sessionMathEndDate, sessionAStartDate, sessionAStartTime, sessionBStartDate, sessionBStartTime;
    const assessment = config.payload.data;
    const classId = fromEdit ? config.payload.record.classroom_id : assessment.classId;
    let isLanguageVisible = true;

    if (fromEdit)
    {
      isLanguageVisible = this.isLanguageVisibleForFIClass(Number(classId));
    }

    const slug = fromEdit ? config.payload.record.slug : assessment.slug;
    const slugType = config.payload.data.slugType;

    let schedule = fromEdit ? "LATER" : assessment.schedule;
    if(this.currentClassFilter === ClassFilterId.Primary || this.currentClassFilter === ClassFilterId.Junior)
    {
      sessionLangStartDate = assessment.sessionLangStartDate;
      sessionLangEndDate = assessment.sessionLangEndDate;
      sessionMathStartDate = assessment.sessionMathStartDate;
      sessionMathEndDate = assessment.sessionMathEndDate;
    }

    else {
      sessionAStartDate = assessment.sessionAStartDate;
      sessionAStartTime = assessment.sessionAStartTime;
      sessionBStartDate = assessment.sessionBStartDate;
      sessionBStartTime = assessment.sessionBStartTime;
    }

    //const startTime = moment("12:15 AM", ["h:mm A"]).format("HH:mm");
    if(classId == undefined){
      alert(this.lang.tra('sa_asses_class_invalid'));
      return false;
    }
    if(slug == undefined){
      alert(this.lang.tra('sa_asses_type_invalid'));
      return false;
    }
    if(schedule == undefined && !(this.isActive && !this.isPartialActive))
    {
      alert(this.lang.tra('sa_asses_time_invalid'));
      return false;
    }

    const isInValid = (input): boolean => {
      if (input === null || input === undefined) {
        return true;
      }
      return false;
    }

    if(schedule == 'LATER')
    {
      if ((ABED_ASSESSMENTS.includes(this.currentClassFilter))
      && (slugType !== AssessmentTypeOptions.OPERATIONAL
      && ((isInValid(sessionAStartDate) || isInValid(sessionAStartTime)) && !(this.isActive && !this.isPartialActive))))
      {
        alert(this.lang.tra('abed_sa_asses_time_invalid'));
        return false;
      }

      if((slug == ASSESSMENT.G9_OPERATIONAL || slug == ASSESSMENT.OSSLT_SAMPLE || slug == ASSESSMENT.OSSLT_OPERATIONAL) &&
         (isInValid(sessionAStartDate) || isInValid(sessionAStartTime) || isInValid(sessionBStartDate) || isInValid(sessionBStartTime))) {
          alert(this.lang.tra('sa_asses_time_invalid'));
          return false;
      }
      if((slug == ASSESSMENT.PRIMARY_SAMPLE || slug == ASSESSMENT.PRIMARY_OPERATIONAL || slug == ASSESSMENT.JUNIOR_SAMPLE || slug == ASSESSMENT.JUNIOR_OPERATIONAL) &&
         (isInValid(sessionLangStartDate) && isInValid(sessionLangEndDate) && isInValid(sessionMathStartDate) && isInValid(sessionMathEndDate))) {
          if(!isLanguageVisible && this.isActive){
            return false;
          } else {
            alert(this.lang.tra('sa_asses_time_invalid'));
            return false;
          }
      }
    }
    return true;
  }

  getTotalStudent(session:any){
    const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == session.classroom_id)
    if(classroom != undefined){
      return classroom.students
    }
    return 0;
  }

  isCurrentTestWindowActive(){
    if(this.currentTestWindow){
      return new Date(this.currentTestWindow.date_end) > new Date ()
    }
  }

  onClickInvigilate(session, isBulkUpload: boolean = false, openInNewTab = true) {
    const class_id = session.classroom_id;
    const schl_group_id = this.g9DemoData.schoolData.group_id;
    this.auth.apiPatch(this.routes.SCHOOL_ADMIN_INVIGILATE_CLASS, 0, {class_id, schl_group_id}, this.configureQueryParams())
    .then(result =>{
      this.g9DemoData.setIsFromSchoolAdmin(true)
      const queryParams = { ...this.route.snapshot.queryParams, isSchlAdmin: "1"};
      let route = `/${this.lang.c()}/educator/assessment/${session.classroom_id}/${session.id}/`;
      if (isBulkUpload) route += 'bulk-scan/'
      const queryString = new URLSearchParams(queryParams).toString();
      const fullUrl = `${window.location.origin}/#${route}?${queryString}`;
      if (openInNewTab) {
        window.open(fullUrl, '_blank');
      } else {
        this.router.navigate([route], {
          relativeTo: this.route,
          queryParams
        });
      }
    });
  }

  isLanguageVisibleForFIClass(classroomId): boolean {
    const classroom = this.g9DemoData.classrooms.find(classroom => classroom.id == classroomId)
    const isFIClass = classroom.is_fi == 1 ? true : false;
    if(this.g9DemoData.schoolDist[0].fi_option === 'C' && isFIClass) {
      return false;
    }
    return true;
  }

  getAssessmentTitle(session){
    if (session.name_custom){
      return session.name_custom
    } else {
      return this.getAssessmentName(session.slug)
    }
  }

  getAssessmentName(type_slug): string
  {
    const assessmentType = G9_SLUG_TO_CAPTION[type_slug];
    if (assessmentType && this.lang.tra(assessmentType)) {
      return this.lang.tra(assessmentType)
    }

    return type_slug;
  }

  getAssessmentType(isFieldTest: null | 0 | 1): string
  {
    if (!this.whitelabelService.isABED())
    {
      return "";
    }

    return isFieldTest == null ? "" : isFieldTest == 1 ?
     this.lang.tra("abed_lbl_field_test") : this.lang.tra('lbl_abed_operational_test');
  }

  showRefundPolicy(){
    const config = {};
    this.pageModal.newModal({
      type: PurchaseModal.PURCHASE_REFUND_POLICY,
      config,
      finish: () => { }
    })
  }

  getSaAssessmentsHeaderSlug(){
    if (this.whitelabelService.isABED()){
      return 'sa_assessments_header_ABED';
    }
    return 'sa_assessments_header';
  }

  getColHeaderClassroomSlug(){
    if (this.whitelabelService.isABED()){
      return 'sa_classrooms_col_class_code_ABED'
    }
    return 'sa_classrooms_col_class_code'
  }

  getInProgressSessionSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_in_progress_session"
    }
    return "btn_progress_pend_sessions"
  }

  getSubmittedSessionSlug(){
    if(this.whitelabelService.isABED()){
      return "abed_submitted_session"
    }
    return "btn_submitted_sessions"
  }

  isABED() {
    return this.whitelabelService.isABED();
  }

  getTeachers(session, isInvigilators = false){
    let classID = session.classroom_id;
    var classroom;
    if(classID){
      classroom = this.g9DemoData.classrooms.find(theClass => theClass.id == classID);
      if(classroom){
        if(isInvigilators) {
          let invigilators = '';
          const class_group_id = this.g9DemoData.teacherClassrooms.map[classroom.id].group_id
          const classInvigilators = class_group_id?this.g9DemoData.invigilators.filter(invig => invig.group_id == class_group_id):[]
          classInvigilators.forEach(invig => {
            const invigName = invig.first_name + " " + invig.last_name;
            invigilators = invigilators == '' ? invigName : invigilators + ', '+ invigName
          })
          return invigilators;
        }
        if(classroom.teacher_uid != undefined){
          const teacher = this.g9DemoData.teachers.list.find(teacher => teacher.id == classroom.teacher_uid )
          if(teacher != undefined){
            return teacher.firstName+ ' '+teacher.lastName
          }
        }
      }
    }
    return '';
  }

  /** Return last date that bulk upload can be done for session (same logic as on the invigilation page) */
  calculateDateEndForUpload(session) {
    if(!session.is_scan_session || session.resp_sheet_config === null){
      return;
    }
    const {upload_until_days, exception_dates, is_exclude_weekends} = JSON.parse(session.resp_sheet_config);
    const isWeekendsExcluded = !!is_exclude_weekends;
    const uploadUntildateEnd = calculateDateEnd(session.startTimeUTC,+upload_until_days,true, exception_dates, isWeekendsExcluded);
    return uploadUntildateEnd
  }

  isDateEndForUploadPassed(session){
    const uploadUntildateEnd = this.calculateDateEndForUpload(session)
    if (!uploadUntildateEnd){
      return false
    }
    let now = new Date();
    const isDatePassed = now.getTime() > uploadUntildateEnd.getTime();
    return isDatePassed;
  }

  /** Check if accessing bulk upload is allowed (same logic as on the invigilation page) */
  isUploadSheetsDisabled(session){
    let now = new Date();
    let sessionDate = new Date(session.startTimeUTC);
    const isSessionOpened = now.getTime() > sessionDate.getTime();
    const uploadUntildateEnd = this.calculateDateEndForUpload(session)
    if((uploadUntildateEnd !== null && now >= uploadUntildateEnd ) || !isSessionOpened){
      return true
    }
    return false
  }
  
}
