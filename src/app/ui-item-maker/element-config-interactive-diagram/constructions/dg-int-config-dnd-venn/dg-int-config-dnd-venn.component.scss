@import '../../element-config-interactive-diagram.component.scss';

.target-container {
  margin-bottom: 1em;
}

.target-name-input-label {
  max-width: 10em;
}
.target-group-unused-label {
  max-width: 4em;
}

.target-option-textarea {
  display: inline-block;
}

.target-index {
  margin-right: 1em;
}

.target-label-container {
  display: flex;
  align-items: center;
}

.layout-config-container {
  display: grid;
  grid-template-columns: auto auto;
  gap: 0.5em;
  align-items: center;
}
