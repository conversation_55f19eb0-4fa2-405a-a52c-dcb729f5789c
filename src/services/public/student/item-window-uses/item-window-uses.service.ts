// Initializes the `public/student/item-window-uses` service on path `/public/student/item-window-uses`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ItemWindowUses } from './item-window-uses.class';
import hooks from './item-window-uses.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/item-window-uses': ItemWindowUses & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/item-window-uses', new ItemWindowUses(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/item-window-uses');

  service.hooks(hooks);
}
