
<div class="page-container" [class.is-abed]="isABED()">
  <div class="header-container" [class.is-abed]="isABED()">
    <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
  </div>
  <div class="panel-container" [class.is-abed]="isABED()">
    <div class="router-container is_wide">

      <div>
        <div class="DAP-title" *ngIf="isABED()">
          <tra slug="abed_DAP_title"></tra>
        </div>
        <div class="link-to-guides-container" *wlCtx="'IS_LANDING_GUIDE'">
          <tra [slug]="getGuideSlug()"></tra>&nbsp;<a routerLink="/{{lang.c()}}/user-guides"><tra [slug]="getClickText()" ></tra></a>
        </div>
        <div>
          <tra [slug]="getLoginAsSlug()"></tra>
        </div>
        <div *wlCtxNot="'IS_LANDING_MIN_FOR_CUSTOM_PAGE'">
          <a routerLink="/{{lang.c()}}/login-educator" class="button is-fullwidth" style="height: auto; word-wrap: break-word"><tra [slug]="getAdminLoginSlug()"></tra></a>
        </div>
        <div *wlCtxNot="'IS_LANDING_MIN_FOR_CUSTOM_PAGE'">
          <a routerLink="/{{lang.c()}}/{{isTestCenter() ? 'login-test-taker' : 'login-student'}}" class="button is-fullwidth"><tra [slug]="getStudentButtonSlug()"></tra></a>
        </div>
        <div *wlCtxNot="'IS_LANDING_MIN_FOR_CUSTOM_PAGE'">
          <a routerLink="/{{lang.c()}}/public-practice" class="button is-fullwidth"><tra [slug]="getPracticeTestTxt()"></tra></a>
        </div>
        <div *wlCtx="'IS_RESPONDUS'">
          <a class="button is-fullwidth" (click)="runRespondusDiagnostics()"> <tra [slug]="'Respondus Demo'"></tra></a>
        </div>
        <div class="link-to-guides-container" *ngIf="isABED()">
          <tra slug="abed_respondus_links_login_pg"></tra>&colon;&nbsp;
          <a [href]="lang.tra('ldb_download_chromebook')">Chromebook</a>&comma;&nbsp;
          <a [href]="lang.tra('ldb_download_windows')">Windows</a>&comma;&nbsp;
          <a [href]="lang.tra('ldb_download_mac')">Mac</a>&comma;&nbsp;
          <a [href]="lang.tra('ldb_download_ipad')">iPad</a>
        </div>
      </div>
    </div>
  </div>
</div>
