import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DropdownSelectButtonComponent } from './dropdown-select-button.component';

describe('DropdownSearchComponent', () => {
  let component: DropdownSelectButtonComponent;
  let fixture: ComponentFixture<DropdownSelectButtonComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ DropdownSelectButtonComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DropdownSelectButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
