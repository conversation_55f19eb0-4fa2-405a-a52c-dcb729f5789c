<div class="page-content">
  <div class="form-content">
    <div>
      <h2 class="strong"> <tra [slug]="getMainHeader()"></tra> </h2>
    </div>
    <h2 class="minor"> 
      <tra [slug]="checkIfTOTPLogin() ? 'title_mfa' : getReturningUsersSlug()"></tra> 
    </h2>

    <div *ngIf="checkIfTOTPLogin() then TOTPLoginView else normalLoginView">
    </div>

    <ng-template #TOTPLoginView>
      <multi-factor-authentication> </multi-factor-authentication>
    </ng-template>
    
    <ng-template #normalLoginView>
      <div class="form-instruction-major">
        <tra [slug]="getMainInstruction()"></tra>
      </div>

      <form [formGroup]="loginCtrl.formGroup" (ngSubmit)="submitForm()">

        <div class="field">
          <label class="label">
            <tra [slug]="getEmailSlug()"></tra>
          </label>
          <div *ngIf="isInputBlank(loginCtrl.formGroupEmailAddress)" class="help "> 
              <tra [slug]="getEnterEmail()"></tra>
          </div>
          <div *ngIf="isInputInvalid(loginCtrl.formGroupEmailAddress)" class="help is-danger"> 
            <tra [slug]="getInvalidEmailSlug()"></tra>
          </div>
          <div class="control">
            <input 
              type="text"
              id="username"
              [formControl]="loginCtrl.formGroupEmailAddress"
              class="input" 
              [class.is-warning]="isInputBlank(loginCtrl.formGroupEmailAddress)"
              [class.is-danger] ="isInputInvalid(loginCtrl.formGroupEmailAddress)"
            >
          </div>
        </div>

        <div class="field">
          <label class="label">
            <tra [slug]="getPasswordSlug()"></tra>
          </label>
          <div *ngIf="isInputBlank(loginCtrl.formGroupPassword)" class="help"> 
            <tra [slug]="getErrorPasswordSlug()"></tra>
          </div>
          <div *ngIf="isInputInvalid(loginCtrl.formGroupPassword)" class="help is-danger"> 
            <tra slug="error_min_length_password"></tra>
          </div>
          <div class="control">
            <input 
              [type]="isShowingPassword() ? 'text' : 'password'"
              id="password"
              [formControl]="loginCtrl.formGroupPassword"
              class="input" 
              [class.is-warning]="isInputBlank(loginCtrl.formGroupPassword)"
              [class.is-danger] ="isInputInvalid(loginCtrl.formGroupPassword)"
            >
            <i *ngIf="!isShowingPassword()" class="fas fa-eye-slash eye-icon" (click)="togglePasswordShow()"></i>
            <i *ngIf="isShowingPassword()" class="fas fa-eye eye-icon" (click)="togglePasswordShow()" ></i>
          </div>
        </div>

        <div class="notification is-warning login-warnings" *ngIf="loginCtrl.isFormFailed">
          <div [ngSwitch]="loginCtrl.formFailReason">
            <div *ngSwitchCase=FormFail.MAX_LOGIN_ATTEMPT>      	
              <tra slug="msg_signin_warn"> </tra>
                <button (click)="supportReqActivate()" class="footer-comment-link" type="button">
                  <tra slug="title_feedback"> </tra> 
                </button>
            </div>
            <div *ngSwitchCase="FormFail.NOT_FOUND">
              <tra slug="txt_alert_bad_login"></tra>
            </div>
            <div *ngSwitchCase="FormFail.PSW_RESET_REQ">
              <tra-md slug="txt_alert_login_pass_reset_req"></tra-md>
            </div>
            <div *ngSwitchCase="FormFail.NOT_VERIFIED">
              <tra slug="txt_alert_bad_login_unverified"></tra>
            </div>
            <div *ngSwitchCase="FormFail.UNKNOWN">
              <tra [slug]="getBadLoginSlug()"></tra>
            </div>
          </div>
        </div>

        <div>
          <input 
            type="submit"
            [value]="lang.tra('sign_in')"
            id="signIn"
            class="button is-large is-success is-fullwidth signin"
            [disabled]="isFormSent"
          />
        </div>
        
      </form>
      
      <div>
        <a [routerLink]="getForgotPasswordRoute()">
          <tra [slug]="getForgotPasswordSlug()"></tra>
        </a>
      </div>
      <div *ngIf="!whiteLabel.isABED()">
        <div class="or-horz"><hr> <tra slug="dividing_or"></tra> <hr></div>
        <div>
          <h2 class="minor"> 
            <tra slug="lbl_new_users"></tra>
          </h2>
          <a [routerLink]="getAccountCreationRoute()" class="button is-info is-large is-fullwidth">
            <tra slug="btn_create_account"></tra>
          </a>
        </div>
      </div>

      <div *ngIf="loginCtrl.loginErrorTroubleshootMsg" style="margin-top:4em;">
        <button class="button is-light is-small" (click)="loginCtrl.isLoginTroubleshooting = !loginCtrl.isLoginTroubleshooting">Troubleshoot Login</button>
        <div *ngIf="loginCtrl.isLoginTroubleshooting" style="margin-top:1em;">
          {{loginCtrl.loginErrorTroubleshootMsg}}
        </div>
      </div>
  
    </ng-template>
  </div>
  
</div>
