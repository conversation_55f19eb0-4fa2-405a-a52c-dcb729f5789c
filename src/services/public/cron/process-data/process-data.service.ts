// Initializes the `process-data` service on path `/process-data`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ProcessData } from './process-data.class';
import hooks from './process-data.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/cron/process-data': ProcessData & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/cron/process-data', new ProcessData(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/cron/process-data');

  service.hooks(hooks);
}
