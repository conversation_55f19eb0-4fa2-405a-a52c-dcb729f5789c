@import '../../../styles/partials/_modal.scss';

.sub-property-div {
    margin-top: 0.5em;
    margin-bottom: 1em;
    padding-left: 1em;
    border-left: 0.2em solid #e0e0e0;
}

.small-input {
  max-width: 5em;
}

.style-profile-container {
  margin-top: 1em;
  margin-bottom: 1em;
}
.style-profile-list-container {
  padding: 0.5em;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.5em;
}
.style-profile-capsule {
  background-color: #eee;
  border-radius: 0.5em;
  padding: 0.5em;
}
.style-profile-remove {
  display: inline-block;
  padding: 0.2em;
  margin-left: 0.5em;
  border-radius: 0.5em;
  background-color: white;
  cursor: pointer;
  &:hover {
    background-color: #ccc;
    color: white;
  }
}

.simple-image-container {
  width: fit-content;
  background-color: rgba(0,0,0,0.1);
  padding: 0.5em;
  margin-top: 0.5em;
  margin-bottom: 0.5em;
  border-radius: 0.5em;
}

.option-container {
  display: flex;
}
.capture-math-container {
  flex-grow: 1;
  font-size: 1.5em;
  text-align: center;
  background-color: white;
}
.option-image-container {
  padding: 10px;
  border: 1px solid #ccc;
  margin-bottom: 5px;
  background-color: #f8f9fa;
}

/* option ordering */

.list {
  /* width: 200px; */
  max-width: 100%;
  padding: 10px;
  box-shadow: none;
}

.list-item {
  padding: 10px;
  border: 1px solid #ccc;
  margin-bottom: 5px;
  background-color: #f8f9fa;
  cursor: move; /* Cursor indicates that this element is draggable */
}

.answer-set-block {
  margin-bottom: 1em;
  padding: 1em;
  border: 1px solid #ccc;
  background-color: #fafafa;
}

.cdk-drop-group {
  min-height: 5em;
  margin: 1em;
  padding: 1em;
  border: 1px solid #ccc;
  background-color: #fafafa;
}

.cdk-drop-element {
  padding: 10px;
  border: 1px solid #ccc;
  background-color: white;
  margin-bottom: 5px;
  border-radius: 0.5em;
  cursor: move;
}

.non-unique-option {
  background-color: #fca7ad;
}

.target-option-button {
  cursor: pointer;
  background-color:#ccc;
  color: #fff;
  text-align:center;
  vertical-align:middle;
  align-content: center;
  display: inline-block;
  padding: 0.5em;
  &:hover {
      background-color:rgb(170, 170, 170);
  }
}

.flex-container {
  display: flex;
  /* align-items: center; */
}

.error-bg-color {
  background-color: #fca7ad !important;
}
.error-border {
  outline: 0.5em solid #fca7ad;
  outline-offset: -0.5em;
}
.twiddle-container {
  border-radius: 0.5em;
}

.target-answer-select {
  max-width: 10em;
  text-overflow: ellipsis;
}

.note {
}
.note>.code {
  background-color: #f1f1f1;
  padding: 0.1em;
  border-radius: 0.25em;
  font-family: monospace;
}

.is-disabled {
  pointer-events: none;
  opacity: 0.5;
}

.target-name-input-label {
  max-width: 10em;
}


:host ::ng-deep .mat-form-field-infix {
  padding: 0 !important;
  padding-bottom: 0.5em !important;
}
:host ::ng-deep .mat-form-field-wrapper {
  margin: 0 !important;
  padding: 0 !important;
}

.warning-container {
  background-color: #ffd966;
  padding: 1em;
  border-radius: 10px;
}
.warning-title {
  font-weight: bold;
}
.warning-text {
}