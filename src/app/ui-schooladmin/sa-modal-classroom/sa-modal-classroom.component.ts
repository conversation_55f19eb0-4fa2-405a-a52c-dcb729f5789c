import { Component, OnInit, Input, ViewEncapsulation, Output } from '@angular/core';
import { IClassroom } from '../data/types';
import { ISaFormEntry, SaFormType, initFormEntries } from '../sa-widget-form-entry/sa-widget-form-entry.component';
import { STUDENT_MATH_CLASS_WHEN, 
         STUDENT_EQAO_GRADECOURSE_TYPE, 
         STUDENT_NBED_COURSE_TYPE, 
         STUDENT_MBED_COURSE_TYPE,
         STUDENT_ABED_COURSE_TYPE, 
         EQAO_ASSESSMENTS } from '../data/constants';
import { G9DemoDataService } from '../g9-demo-data.service';
import { DEMO_TC_SETTINGS } from '../data/sample-data/test-controller-settings';
import { filterArr, isInArr } from '../data/util';
import { LangService } from "../../core/lang.service";
import { ClassFilterId } from '../my-school.service';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { ClassroomModal } from '../sa-classrooms/sa-classrooms.component';
import { EventEmitter } from '@angular/core';

@Component({
  selector: 'sa-modal-classroom',
  templateUrl: './sa-modal-classroom.component.html',
  styleUrls: ['./sa-modal-classroom.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class SaModalClassroomComponent implements OnInit {

  @Input() classrooms:IClassroom[];
  @Input() savePayload:{[key:string]:Partial<IClassroom>};
  @Input() saveProp:string;
  @Input() isEditing:boolean;
  @Input() isGroup:boolean;
  @Input() currentClassFilter : ClassFilterId;
  @Output() isClassroomSelected: EventEmitter<boolean> = new EventEmitter<boolean>();

  showIsFrImm = false;

  constructor(
    private g9DemoData: G9DemoDataService,
    public lang: LangService,
    public whiteLabelService:  WhitelabelService,
    private auth: AuthService,
    private routes: RoutesService,
    private pageModalService: PageModalService,
  ) { }
  pageModal: PageModalController;
  formEntries:ISaFormEntry[];
  teachers:any[];
  semesters:any[];
  classroomType:string;
  selectedClassroom:IClassroom;
  isSelectingClassroom:boolean;
  invigilators:any[] = [];
  ClassroomModal = ClassroomModal;
  availableTeachers = []; 
  selectedClassroomGroupId: Number;

  /*
  semesterValues = {
    "First semester": "sdc_student_math_class_when_1", "Second semester": "sdc_student_math_class_when_2",
    "First quadmester": "sdc_student_math_class_when_A", "Second quadmester": "sdc_student_math_class_when_B", "Third quadmester": "sdc_student_math_class_when_C", "Fourth quadmester": "sdc_student_math_class_when_D",
    "First octomester": "sdc_student_math_class_when_E", "Second octomester": "sdc_student_math_class_when_F", "Third octomester": "sdc_student_math_class_when_G", "Fourth octomester": "sdc_student_math_class_when_H",
    "Fifth octomester": "sdc_student_math_class_when_I", "Sixth octomester": "sdc_student_math_class_when_J", "Seventh octomester": "sdc_student_math_class_when_K", "Eighth octomester": "sdc_student_math_class_when_L",
    "First hexamester": "sdc_student_math_class_when_M", "Second hexamester": "sdc_student_math_class_when_N", "Third hexamester": "sdc_student_math_class_when_O", "Fourth hexamester": "sdc_student_math_class_when_P",
    "Fifth hexamester": "sdc_student_math_class_when_Q", "Sixth hexamester": "sdc_student_math_class_when_R", "Full year": "lbl_full_year",
  };
  */
  semesterValues = {
    "Full year": "lbl_full_year", "Semester":"sdc_student_math_class_semester", "Quadmester":"sdc_student_math_class_quadmester", "Octomester":"sdc_student_math_class_octomester"
  }


  ngOnInit(): void {
    this.initFormEntries({});
    this.initContextInfo();  
    this.initSelectedClassroom();    
    this.pageModal = this.pageModalService.defineNewPageModal();
  }
  initSelectedClassroom(){
    if(this.currentClassFilter === ClassFilterId.Primary && this.lang.c() == 'en'){
      this.showIsFrImm =true;
    }
    console.log(this.classrooms);
    if (this.classrooms){
      if (this.classrooms.length === 1)
      {
        return this.selectClassroom(this.classrooms[0]);
      }
      if (this.classrooms.length > 1)
      {
        this.isClassroomSelected.emit(false);
        return this.isSelectingClassroom = true;
      }
    }
  }
  initContextInfo(){
    this.getSemesters();
    this.teachers = this.g9DemoData.teachers.list.map(teacher => {
      return { 
        id: teacher.id,
        label: teacher.invigilator,
      }
    });
    this.teachers = this.teachers.sort((a, b) => a.label.localeCompare(b.label));
  }
  getSemesters(){
    const test_window_type =  this.currentClassFilter === ClassFilterId.G9?"EQAO_G9M":"EQAO_G10L"
    const validTestwindows = this.g9DemoData.testWindows.filter( tw => tw.type_slug === test_window_type && new Date(tw.date_end) > new Date ())
    this.semesters = this.g9DemoData.semesters.list.filter(semester => {
      const theTestWindow = validTestwindows.find(tw => tw.id === semester.testWindowId)
      if(theTestWindow){
        if(this.currentClassFilter === ClassFilterId.G9 && (semester.foreign_scope_id === null||semester.foreign_scope_id === undefined)){
          return semester
        }
        if(this.currentClassFilter === ClassFilterId.OSSLT){
          return semester
        }
      }
    }) 
  }
  getSemesterLabel(semesterLabel){
    let semesterslug = this.semesterValues[semesterLabel];
    return this.lang.tra(semesterslug);
  }

  initFormEntries(classroom:Partial<IClassroom>){
    const list = filterArr(STUDENT_MATH_CLASS_WHEN.list, entry => {
      return isInArr( DEMO_TC_SETTINGS.ALLOWED_SEMESTERS, entry.id);
    })

    this.initDefaults(classroom);
    
    this.formEntries = this.getFormEntries(classroom);

    const payload =  {
      id: classroom.id,
      data: {}
    };
    this.savePayload[this.saveProp] = payload;
    this.formEntries.forEach(entry => {
      const prop = entry.valProp;
      const val = classroom[prop]
      payload.data[prop] = val;
      entry.formControlRef.setValue(val);
      entry.formControlRef.valueChanges.subscribe(val => {
        payload.data[entry.valProp] = val;
      })
    });

  }

  //isGroupingType;
  showTermFormat;

  initDefaults(classroom){
    if (classroom['is_grouping'] === undefined || classroom['is_grouping'] === null){
      classroom['is_grouping'] = this.isGroup ? 1 : 0;
    }
    classroom['course_type'] = this.getCourseType()    
  }

  isEQAOAssessment() {
    return EQAO_ASSESSMENTS.includes(this.currentClassFilter);
  }

  getAdditionalInvigilatorSlug() {
    if(this.whiteLabelService.isABED()) {
      return 'abed_add_remove_additional_teachers';
    } else {
      return 'ta_class_invigilators_label';
    }
  }

  getCourseType = ():ClassFilterId => {
    if (this.isEQAOAssessment()) {
      switch(this.currentClassFilter){
        case ClassFilterId.Primary:
          this.showTermFormat = false;
          return <ClassFilterId>STUDENT_EQAO_GRADECOURSE_TYPE.list[0].id
        case ClassFilterId.Junior:
          this.showTermFormat = false;
          return <ClassFilterId>STUDENT_EQAO_GRADECOURSE_TYPE.list[1].id
        case ClassFilterId.G9:
          this.showTermFormat = true;
          return <ClassFilterId>STUDENT_EQAO_GRADECOURSE_TYPE.list[2].id
        case ClassFilterId.OSSLT:
          this.showTermFormat = false;
          return <ClassFilterId>STUDENT_EQAO_GRADECOURSE_TYPE.list[3].id
        default:
          return this.currentClassFilter
      }
    }

    this.showTermFormat = false; // can make this dependent on whitelabel

    return this.currentClassFilter

    // todo:DB_DATA_MODEL mapping needs to be aligned before merging into other whitelabel branches

    // if (STUDENT_NBED_COURSE_TYPE.map[this.currentClassFilter]) {
    //   this.showTermFormat = false;
    //   return STUDENT_NBED_COURSE_TYPE.map[this.currentClassFilter].id;
    // }

    // if (STUDENT_MBED_COURSE_TYPE.map[this.currentClassFilter]) {
    //   this.showTermFormat = false;
    //   return STUDENT_MBED_COURSE_TYPE.map[this.currentClassFilter].id;
    // }


  }

  getFormEntriesOptions(){
    if(this.isEQAOAssessment()) return STUDENT_EQAO_GRADECOURSE_TYPE.list;
    // todo:DB_DATA_MODEL
    if (this.whiteLabelService.isABED()) return STUDENT_ABED_COURSE_TYPE.list;
    if (this.getCourseType().includes("NBED_")) return STUDENT_NBED_COURSE_TYPE.list;
    if (this.getCourseType().includes("MBED_")) return STUDENT_MBED_COURSE_TYPE.list;
  }

  getFormEntries(classroom){
    const fields = [];
    const isClassCodeLocked = classroom.is_placeholder ? true : false;
    let defaultSelection = this.currentClassFilter;
    if (this.isEQAOAssessment()) {
      defaultSelection = this.getCourseType();
    }
    fields.push({
      // Course
      type: SaFormType.SELECT,
      valProp: "course_type",
      label: this.whiteLabelService.isNBED() ? this.lang.tra("sa_class_course_nbed") : this.whiteLabelService.isABED() ? this.lang.tra("abed_course") : this.lang.tra("sa_class_course"),
      options: this.getFormEntriesOptions(),
      isLocked: false,
      defaultSelection: defaultSelection
    });
    fields.push({
      // Class Code / Grouping
      type: SaFormType.TEXT,
      valProp: "class_code",
      label: this.whiteLabelService.isNBED() ? "sa_class_course_nbed" : this.whiteLabelService.isABED() ? "sa_classrooms_col_class_code_ABED" : "sa_class_course",
      required: true,
      isLocked: isClassCodeLocked
    });

    if (this.showIsFrImm){
      fields.push(
        {
        // French Immersion
        type: SaFormType.RADIOC,
        valProp: 'is_fi',
        label: this.lang.tra("sa_class_fr_imm_lbl"),
        options: [{ id: "1", label: "sa_class_is_fr_imm", tag: "" }],
        greyOut: this.isEditing
      });
    }

    fields.push(
      {
      // Class Code
      type: SaFormType.TEXT,
      valProp:'is_grouping',
      label:'',
      isHidden:true
    }
    )
    //if (!this.isGroupingType){
    if (this.showTermFormat){
      //fields.push({type: SaFormType.SELECT,  valProp:'semester_id',       label: this.lang.tra('sa_class_semester'), options: this.semesters}) // Semester
      fields.push(
        {
        // Semester
        type: SaFormType.SELECT,
        valProp: "semester",
        label: this.lang.tra("sa_class_term_format"),
        options: this.semesters,
        required: true
      });
    }
    fields.push({
      // Teacher / Invigilator
      type: SaFormType.SELECT,
      valProp: "teacher_uid",
      label: this.whiteLabelService.isABED() ? this.lang.tra("abed_exam_supervisor_assign_later") : this.lang.tra("sa_class_educator") + " " + this.lang.tra("txt_can_assign_later"),

      options: this.teachers
    });
    return initFormEntries(classroom, fields);

    //   if (this.isEditing){
    //   return initFormEntries(classroom, [
    //     //{type: SaFormType.SELECT,  valProp:'course',         label: this.lang.tra('sa_class_course'), options:STUDENT_G9_COURSES_SIMPLE.list, }, // Course
    //     //{type: SaFormType.RADIOC,  valProp:'remote_courses', label: this.lang.tra('sa_class_remote_course'), options:STUDENT_IN_PERSON_REMOTE.list },   // Remote Courses
    //     {type: SaFormType.TEXT,    valProp:'class_code',     label: this.lang.tra('sa_class_code') }, // Class Code
    //     {type: SaFormType.SELECT,  valProp:'semester',       label: this.lang.tra('sa_class_semester'), options: this.semesters}, // Semester
    //     {type: SaFormType.SELECT,  valProp:'teacher_uid',       label: this.lang.tra('sa_class_educator'), options: this.teachers}, // Teacher
    //     //{type: SaFormType.RADIO,   valProp:'isFrench',       label: this.lang.tra('sa_class_french'), options:STUDENT_YES_NO.list, }, // French Immersion or Extended French
    //   ]);
    // }
  }

  selectClassroom(classroom:IClassroom){
    // return  && !this.selectedClassroom)
    this.isSelectingClassroom = false;
    this.selectedClassroom = classroom;
    // console.log(this.selectedClassroom)
    this.initFormEntries(this.selectedClassroom);
    this.loadInvigilators();
    this.isClassroomSelected.emit(true);
  }

  loadInvigilators(){
    if(this.isEditing){
      const class_group_id = this.g9DemoData.teacherClassrooms.map[this.selectedClassroom.id].group_id
      if(class_group_id){
        this.invigilators = this.g9DemoData.invigilators.filter(invig => invig.group_id == class_group_id )
      }
    }  
  }

  loadAllInvigilators(){
    if(this.isEditing){
      if(!this.selectedClassroom || !this.g9DemoData.invigilators){
        return;
      }
      this.availableTeachers = [];
      let invigilatorsList = [];
      const currentClassroom = this.g9DemoData.getClassroomById(String(this.selectedClassroom.id)); 
      if(currentClassroom) {
        this.selectedClassroomGroupId = currentClassroom.group_id;
      }
      const class_teacher_uid = this.selectedClassroom.teacher_uid; 
      this.g9DemoData.invigilators.forEach(g9invig => {
        if(+g9invig.uid === +class_teacher_uid) {
          return;
        }
        const in_invigilator = invigilatorsList.find(invig => invig.contact_email === g9invig.contact_email);
        if(!in_invigilator){
          let theInvig = this.g9DemoData.invigilators.find( invig => invig.contact_email === g9invig.contact_email && +invig.group_id == this.selectedClassroomGroupId);
          if(theInvig){
            invigilatorsList.push(theInvig);
          }else{
            invigilatorsList.push(g9invig);
          }
        }
      })
      invigilatorsList.forEach(invig => {
        const avaiableInvig ={
          uid:invig.uid,
          name: invig.first_name+" "+invig.last_name,
          email: invig.contact_email,
          selected: +invig.group_id == this.selectedClassroomGroupId
        };
        this.availableTeachers.push(avaiableInvig);
      });
      this.availableTeachers.sort((a, b) => a.name.localeCompare(b.name));
      console.log("availableTeachers: ", this.availableTeachers);
    }
  }

  toggleAddRemoveInvigilator(invigilator){
    const theTeacher = this.g9DemoData.invigilators.find(invig => invig.contact_email == invigilator.email);
    if(invigilator.selected){ //add teacher as invigilator
      this.auth.apiCreate( this.routes.SCHOOL_ADMIN_INVIGILATORS,
        {
          invig_uid: invigilator.uid,
          school_class_group_id: this.selectedClassroomGroupId
        },
        this.configureQueryParams()
      ).then( res =>{
        if(theTeacher.group_id == null){
          theTeacher.group_id = res.group_id;
        } else {
          this.g9DemoData.invigilators.push({
            uid: theTeacher.uid, 
            contact_email: theTeacher.contact_email, 
            first_name: theTeacher.first_name,
            last_name: theTeacher.last_name,
            group_id: res.group_id
          })
        }  
        this.invigilators.push(theTeacher);
      })
    } else { // remove teacher as invigilator
      this.removeInvigilator(invigilator);
      theTeacher.group_id = null;
    }
  }

  removeInvigilator(invigilator){
    const school_class_group_id = this.g9DemoData.teacherClassrooms.map[this.selectedClassroom.id].group_id
    this.auth.apiRemove( this.routes.SCHOOL_ADMIN_INVIGILATORS,-1,this.configureQueryParams({ 
      data: { 
        invig_uid:invigilator.uid,
        school_class_group_id
      }
    }))
      .then( res => {
        // update local invigilators
        invigilator.group_id = null;
        const index = this.invigilators.findIndex(invig => invig.uid === invigilator.uid)
        this.invigilators.splice(index, 1)

        // Update g9DemoData invigilators
        const currentInvigRecords = this.g9DemoData.invigilators.filter(invig => invig.uid === invigilator.uid);
        if(currentInvigRecords.length == 1) {
          currentInvigRecords[0].group_id = null;
        } else if(currentInvigRecords.length > 1) {
          const g9Index = this.g9DemoData.invigilators.findIndex(invig => invig.uid === invigilator.uid && invig.group_id === school_class_group_id)
          this.g9DemoData.invigilators.splice(g9Index, 1);
        }
      })
  }

  configureQueryParams(query?:any){
    const schoolData: any = this.g9DemoData.schoolData
    if(query){
      return {
        query:{
          schl_group_id: schoolData.group_id,
          ...query
        }  
      }  
    }
    return {
      query:{
        schl_group_id: schoolData.group_id,
      }  
    }
  }

  cModal() {
    return this.pageModal.getCurrentModal();
  }

  addInvigilatorsModalStart() {
    this.loadAllInvigilators();
    const config: any = {};
    this.pageModal.newModal({
      type: ClassroomModal.ADD_INVIGILATOR,
      config,
      finish: this.addInvigilatorsModalFinish
    });
  }

  addInvigilatorsModalFinish() {
    this.pageModal.closeModal();
  }

  getRequiredFieldSlug() {
    if (this.whiteLabelService.isABED()) {
      return "abed_required_field_2";
    }
    return "lbl_required_field";
  }
}

