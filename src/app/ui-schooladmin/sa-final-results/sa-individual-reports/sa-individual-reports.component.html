<div class="sa-individual-reports fit-print-to-page">
  <bc-paginated-table 
    *ngIf="mode == Mode.TABLE" 
    class="dont-print" 
    [columnHeadings]="columnHeadings" 
    [pagination]="pagination"
    [getRows]="getRows" 
    [getDisplay]="getDisplay" 
    [getTag]="getTag" 
    [defaultFilterCondition]="FilterCondition.LIKE"
    [columnWidths]="tableColumnWidths" 
    [withExport]="true" 
    (export)="export($event)" 
    (link)="onLinkClicked($event)">
  </bc-paginated-table>

  <div class="single-student" *ngIf="mode == Mode.SINGLE && singleModeData && !isLoading">

    <div class="single-student-header">
      <bc-paginator class="dont-print" [paginator]="singleModeData.singlePagination" [showCount]="true" [getCountDisplay]="getCountDisplay"
        (refresh)="onSinglePaginationChange()">
      </bc-paginator>
      <a class="dont-print" (click)="backToListOfStudents()"><tra slug="sa_fr_issr_backToStudent"></tra></a>
    </div>
    
    <div class="dont-print">
      <button (click)="printReport()" class="button is-large">Print Report</button>
    </div>
    
    <div class="single-student-header-name isr-header-rows-container">
      <div class="isr-header-rows">
        <div>Ministry of Education</div>
        <div>Foundation Skills Assessment October/November 2021</div>
        <div>Student Report </div>
        <img *ngIf="!isFr()" class="report-logo" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/bed-isr-logo/1629259627047/bed-isr-logo.png">
        <img *ngIf="isFr()" class="report-logo" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/bed-isr-logo-fr/1629263063643/bed-isr-logo-fr.png">
      </div>
      <div class="space-between">
        <div>
          <div> <strong>Student Name:</strong> {{singleModeData.singleModeName.first_name}} {{singleModeData.singleModeName.last_name}} </div>
          <div> <strong>School Name:</strong> Sample School </div>
        </div>
        <div>
          <div> <strong>Personal Education Number:</strong> ({{singleModeData.singleModePens[singleModeData.singlePagination.skip].pen}}) </div>
          <div> <strong>Grade:</strong> 4 </div>
        </div>
      </div>
      
      
    </div>

    <div class="report-section">
      <!--            <p>Student <span *ngIf="hasRemarked">Remarked</span> Proficiency Level: <span class="student-proficiency">3-Proficient</span></p> <button class="show-proficiency">Show Proficiency Level Descriptions</button>-->
      <!--            <p>Student Name: <span class="student-name">Alejandro Lopez - Castro</span></p>-->

      <!--            <p>Student <span *ngIf="hasRemarked">Remarked</span> Raw Scores:</p> <button (click)="showResponsesModal=true;">View Student Responses</button> <button (click)="showRemarkRequestModal=true;">Remark Student Responses</button>-->
      <div class="report-intro">
        <p>
          <tra-md [isCondensed]="true" slug="sa_fr_issr_report_intro_p1"></tra-md>
        </p>
        <br>
        <p>
          <tra-md [isCondensed]="true" slug="sa_fr_issr_report_intro_p2"></tra-md>
        </p>
        <br>
        <p><strong><tra slug="sa_fr_issr_report_intro_p3"></tra><u><tra slug="sa_literacy"></tra></u>&nbsp;<tra slug="sa_fr_issr_is"></tra>
            {{getProficiencyLevel('literacy', singleModeData.literacyScore)}}</strong>
        <p>
      </div>

      <table>
        <thead class="literacy_level_thead">
          <tr>
            <th class="emergingThLiteracy"
              [class.active]="getProficiencyLevel('literacy', singleModeData.literacyScore) == SchoolLevelDataReportProficiencyLevel.EMERGING">
              <tra slug="sa_sr_emerging"></tra></th>
            <th class="onTrackThLiteracy"
              [class.active]="getProficiencyLevel('literacy', singleModeData.literacyScore) == SchoolLevelDataReportProficiencyLevel.ON_TRACK">
                <tra slug="sa_sr_ontrack"></tra>
              <div class="shade-container">
                <img class="isr-shading" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/shading/1629259754737/shading.png">
              </div>
            </th>
            <th class="extendingThLiteracy"
              [class.active]="getProficiencyLevel('literacy', singleModeData.literacyScore) == SchoolLevelDataReportProficiencyLevel.EXTENDING">
                <tra slug="sa_sr_extend"></tra>
            </th>
          </tr>
        </thead>
        <tr>
          <td class="emergingTdLiteracy"
            [class.active]="getProficiencyLevel('literacy', singleModeData.literacyScore) == SchoolLevelDataReportProficiencyLevel.EMERGING">
            <p><em><tra slug="sa_fr_issr_emergingLit_p1"></tra><strong><tra slug="sa_fr_issr_emergingLit_p2"></tra></strong><tra slug="sa_fr_issr_emergingLit_p3"></tra></em></p>
            <ul>
              <li><tra slug="sa_fr_issr_emergingLit_l1"></tra></li>
              <li><tra slug="sa_fr_issr_emergingLit_l2"></tra></li>
              <li><tra slug="sa_fr_issr_emergingLit_l3"></tra></li>
              <li><tra slug="sa_fr_issr_emergingLit_l4"></tra></li>
              <li><tra slug="sa_fr_issr_emergingLit_l5"></tra></li>
            </ul>
          </td>
          <td class="onTrackTdLiteracy"
          [class.active]="getProficiencyLevel('literacy', singleModeData.literacyScore) == SchoolLevelDataReportProficiencyLevel.ON_TRACK">
              <p><em><tra slug="sa_fr_issr_ontrackLit_p1"></tra><strong><tra slug="sa_fr_issr_ontrackLit_p2"></tra></strong><tra slug="sa_fr_issr_ontrackLit_p3"></tra></em></p>
              <ul>
                <li><tra slug="sa_fr_issr_ontrackLit_l1"></tra></li>
                <li><tra slug="sa_fr_issr_ontrackLit_l2"></tra></li>
                <li><tra slug="sa_fr_issr_ontrackLit_l3"></tra></li>
                <li><tra slug="sa_fr_issr_ontrackLit_l4"></tra></li>
                <li><tra slug="sa_fr_issr_ontrackLit_l5"></tra></li>
                <li><tra slug="sa_fr_issr_ontrackLit_l6"></tra></li>
                <li><tra slug="sa_fr_issr_ontrackLit_l7"></tra></li>
              </ul>
            <div class="shade-container">
              <img class="isr-shading" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/shading/1629259754737/shading.png">
            </div>
          </td>
          <td class="extendingTdLiteracy"
            [class.active]="getProficiencyLevel('literacy', singleModeData.literacyScore) == SchoolLevelDataReportProficiencyLevel.EXTENDING">
              <p><em><tra slug="sa_fr_issr_extendLit_p1"></tra><strong><tra slug="sa_fr_issr_extendLit_p2"></tra></strong><tra slug="sa_fr_issr_extendLit_p3"></tra></em></p>
              <ul>
                <li><tra slug="sa_fr_issr_extendLit_l1"></tra></li>
                <li><tra slug="sa_fr_issr_extendLit_l2"></tra></li>
                <li><tra slug="sa_fr_issr_extendLit_l3"></tra></li>
                <li><tra slug="sa_fr_issr_extendLit_l4"></tra></li>
                <li><tra slug="sa_fr_issr_extendLit_l5"></tra></li>
                <li><tra slug="sa_fr_issr_extendLit_l6"></tra></li>
                <li><tra slug="sa_fr_issr_extendLit_l7"></tra></li>
                <li><tra slug="sa_fr_issr_extendLit_l8"></tra></li>
              </ul>
          </td>
        </tr>
      </table>

      <br>
      <p><strong>
        <tra slug="sa_fr_issr_LitNumSep"></tra><u><tra slug="sa_numeracy"></tra></u><tra slug="sa_fr_issr_is"></tra> 
        <!-- {{getProficiencyLevel('numeracy', singleModeData.numeracyScore)}} -->
        Extending
      </strong>
      </p>
      <br>
      <table>
        <thead class="literacy_level_thead">
          <tr>
            <th class="emergingThNumeracy"
              [ngClass]="getProficiencyLevel('numeracy', singleModeData.numeracyScore) == SchoolLevelDataReportProficiencyLevel.EXTENDING">
              <tra slug="sa_sr_emerging"></tra></th>
            <th class="onTrackThNumeracy"
              [ngClass]="getProficiencyLevel('numeracy', singleModeData.numeracyScore) == SchoolLevelDataReportProficiencyLevel.ON_TRACK">
              <tra slug="sa_sr_ontrack"></tra>
            </th>
            <th class="extendingThNumeracy"
              [ngClass]="getProficiencyLevel('numeracy', singleModeData.numeracyScore) == SchoolLevelDataReportProficiencyLevel.EXTENDING">
                <tra slug="sa_sr_extend"></tra>
            <div class="shade-container">
              <img class="isr-shading" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/shading/1629259754737/shading.png">
              </div>
            </th>
          </tr>
        </thead>
        <tr>
          <td class="emergingTdNumeracy"
            [ngClass]="getProficiencyLevel('numeracy', singleModeData.numeracyScore) == SchoolLevelDataReportProficiencyLevel.EXTENDING">
            <p><em><tra slug="sa_fr_issr_emergingNum_p1"></tra><strong><tra slug="sa_fr_issr_emergingNum_p2"></tra></strong><tra slug="sa_fr_issr_emergingNum_p3"></tra></em></p>
            <ul>
              <li><tra slug="sa_fr_issr_emergingNum_l1"></tra></li>
              <li><tra slug="sa_fr_issr_emergingNum_l2"></tra></li>
              <li><tra slug="sa_fr_issr_emergingNum_l3"></tra></li>
            </ul>
          </td>
          <td class="onTrackTdNumeracy"
            [ngClass]="getProficiencyLevel('numeracy', singleModeData.numeracyScore) == SchoolLevelDataReportProficiencyLevel.ON_TRACK">
            <p><em><tra slug="sa_fr_issr_ontrackNum_p1"></tra><strong><tra slug="sa_fr_issr_ontrackNum_p2"></tra></strong><tra slug="sa_fr_issr_ontrackNum_p3"></tra></em></p>
            <ul>
              <li><tra slug="sa_fr_issr_ontrackNum_l1"></tra></li>
              <li><tra slug="sa_fr_issr_ontrackNum_l2"></tra></li>
              <li><tra slug="sa_fr_issr_ontrackNum_l3"></tra></li>
            </ul>
          </td>
          <td class="extendingTdNumeracy"
            [ngClass]="getProficiencyLevel('numeracy', singleModeData.numeracyScore) == SchoolLevelDataReportProficiencyLevel.EXTENDING">
              <p><tra slug="sa_fr_issr_extendLit_p1"></tra><strong><tra slug="sa_fr_issr_extendLit_p2"></tra></strong><tra slug="sa_fr_issr_extendLit_p3"></tra></p>
              <ul>
                <li><tra slug="sa_fr_issr_extendNum_l1"></tra></li>
                <li><tra slug="sa_fr_issr_extendNum_l2"></tra></li>
                <li><tra slug="sa_fr_issr_extendNum_l3"></tra></li>
              </ul>
            <div class="shade-container">
              <img class="isr-shading" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/shading/1629259754737/shading.png">
            </div>
          </td>
        </tr>
      </table>
      <br>
      <div class="report-footer isr-footer-rows">
        <p><strong><tra slug="sa_fr_issr_footer_p1"></tra></strong></p>
        <p><a
            href="https://www2.gov.bc.ca/assets/gov/education/administration/kindergarten-to-grade-12/assessment/en-fsa-parent-brochure.pdf">https://www2.gov.bc.ca/assets/gov/education/administration/kindergarten-to-grade-12/assessment/en-fsa-parent-brochure.pdf</a>
        </p>
        <br>
        <p><strong><tra slug="sa_fr_issr_footer_p2"></tra></strong></p>
        <p><a
            href=" https://www2.gov.bc.ca/gov/content/education-training/k-12/administration/program-management/assessment/foundation-skills-assessment">
            https://www2.gov.bc.ca/gov/content/education-training/k-12/administration/program-management/assessment/foundation-skills-assessment</a>
        </p>
        <br>
        <p><tra slug="sa_fr_issr_footer_p3"></tra></p>
      </div>
    </div>

  </div>
</div>