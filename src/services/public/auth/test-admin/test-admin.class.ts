import { Id, NullableId, Paginated, Para<PERSON>, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { dbDateNow } from '../../../../util/db-dates';
import * as Errors from '@feathersjs/errors';
import { IUInvites } from '../../../db/schemas/u_invites.schema';
import { IAuth } from '../../../db/schemas/auths.schema';
import { IUser } from '../../../db/schemas/users.schema';
import { defaultDomain, defaultLangCode } from '../../../../constants/mail-constants';
import { DB_MAX_EMAIL_LEN } from '../../../../constants/db-limits';
import { normalizeDomain } from '../../../../util/domain-whitelist';
import { currentUid } from "../../../../util/uid";
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}
interface ICreate extends ICreateFromInvite {}
interface IPatch {
  invitationCode: string,
  email: string
}
export interface ICreateFromInvite {
  invitationCode: string,
  firstName: string,
  lastName: string,
  phoneNumber: string,
  email: string,
  password: string,
  //
  langCode? :string
  domain? :string,
  whiteLabelFlag?: string
}

interface ServiceOptions {}

export class TestAdmin implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: ICreate, params?: Params): Promise<Data> {
    const whitelabel = (params && params.headers) ? params.headers.host : undefined;
    return this.createAdminFromInvite(data, whitelabel);
  }

  
  
  async createAdminFromInvite (data: ICreate, whitelabel?: string): Promise<Data> {

    const langCode = data.langCode || defaultLangCode;
    const domain = normalizeDomain(data.domain || defaultDomain);

    // databases
    const dbInvitationsRead = this.app.service('db/read/u-invites'); // to validate the secret key
    const dbInvitationsWrite = this.app.service('db/write/u-invites');
    const dbUserWrite = this.app.service('db/write/users');
    const dbAuthWrite = this.app.service('db/write/auths');

    // confirm the invitation record exists
    const ERR_INVALID_CODE = 'ERR_INVALID_CODE';
    const ERR_INVALID_CODE_TEACHER = 'ERR_INVALID_CODE_TEACHER';
    const invitationCodeSplit = data.invitationCode.toLowerCase().split('x');
    const invitationId = parseInt(invitationCodeSplit[0], 10);
    const secret_key = invitationCodeSplit[1];
    if (!secret_key || secret_key =='undefined'){
      throw new Errors.BadRequest(ERR_INVALID_CODE);
    }

    if (!invitationId || isNaN(invitationId)){
      throw new Errors.BadRequest(ERR_INVALID_CODE);
    }

    let userId; 
    let userRoleType;
    const inviteRecord = await dbInvitationsRead.db().where('id', invitationId);
    userId = inviteRecord[0].uid;

    const userRoleRecord = <any[]>await this.app
      .service('db/read/user-roles')
      .find({
        query: { uid: userId },
        paginate: false
      });
    
    if(userRoleRecord.length > 0){
      userRoleType =  userRoleRecord[0].role_type;
    }
    
    const invitationRecords = <IUInvites[]> await dbInvitationsRead
      .db()
      .where('id', invitationId)
      .where('secret_key', secret_key)
      .whereNot('is_revoked', 1)
      .whereNull('revoked_on')
      .whereNull('used_on')
      .where('expire_on', '>', dbDateNow(this.app))
      .limit(1)

    if (invitationRecords.length === 0){
      if(userRoleType === 'schl_teacher') {
        throw new Errors.BadRequest(ERR_INVALID_CODE_TEACHER);
      } else {
        throw new Errors.BadRequest(ERR_INVALID_CODE);
      }
    }
    const invitationRecord = invitationRecords[0];
    const uid = invitationRecord.uid;

    // max email length
    if (data.email.length > DB_MAX_EMAIL_LEN){
      throw new Errors.Forbidden('EMAIL_TOO_LONG');
    }


    // validate the password
    const password = await this.app
      .service('auth/password-validation')
      .sanitizeAndValidateNewPassword(data.password)


    const userRecord = await this.app.service('db/read/users').get(uid);
    let isEmailConfirmed = (userRecord.contact_email === data.email)
    
    // create an auth record assigned to the user
    /* send verification email -> happening as part of a hook */
    const authInsertFields:IAuth = {
      email : data.email,
      password,
      password_updated_on : dbDateNow(this.app),
      uid,
      enabled: isEmailConfirmed ? 1 : 0
    }
    const whiteLabelFlag = data.whiteLabelFlag;
    const newAuthRecord:IAuth = <any> await dbAuthWrite.create({
      ... authInsertFields
    }, {isRecordLookup:true, langCode, domain, whitelabel, whiteLabelFlag});
    const authId = newAuthRecord.id;



    // attach to user
    const userUpdateFields:Partial<IUser> = {
      first_name   : data.firstName,
      last_name    : data.lastName,
      contact_phone : data.phoneNumber,
      contact_email: data.email,
      is_claimed   : 1
    }
    await dbUserWrite.patch(
      uid,
      userUpdateFields
    )

    // close invitation
    const invitePatchFields: Partial<IUInvites> = {
      used_on: dbDateNow(this.app),
      used_by_uid: uid,
    }
    await dbInvitationsWrite.patch(
      invitationId,
      invitePatchFields
    )

    // return isEmailConfirmed to determine which 
    // acc creation page will appear on client side
    return {isEmailConfirmed};
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    //throw new Errors.MethodNotAllowed();
    if (!params || !params.query) {
      throw new Errors.BadRequest();
    }
    const ERR_INVALID_CODE = 'ERR_INVALID_CODE';
    const { email, invitationCode, emailLinkDomain, renewInviteMode } = <any> data;
    const invitationCodeSplit = invitationCode.toLowerCase().split('x');
    const invitationId = parseInt(invitationCodeSplit[0], 10);
    const secret_key = invitationCodeSplit[1];

    if (!secret_key || secret_key =='undefined'){
      throw new Errors.BadRequest(ERR_INVALID_CODE);
    }
    if (!invitationId || isNaN(invitationId)){
      throw new Errors.BadRequest(ERR_INVALID_CODE);
    }
    const invitationRecords = <IUInvites[]> await this.app.service('db/read/u-invites')
      .db()
      .where('id', invitationId)
      .where('secret_key', secret_key)
      .whereNot('is_revoked', 1)
      .whereNull('revoked_on')
      .whereNull('used_on')
      .where('expire_on', '>', dbDateNow(this.app))
      .limit(1)

    if (invitationRecords.length === 0){
      throw new Errors.BadRequest('ERR_INVALID_CODE');
    }

    const currentUID = invitationRecords[0].uid
    
    const authRecords = await dbRawRead(this.app, [email], `
      SELECT * FROM auths where email = ?
    ;`);
    if(authRecords.length === 0){
      throw new Errors.BadRequest("ACCOUNT_NOT_EXIST")
    }
    const targetUID = authRecords[0].uid

     //get all current user roles
     const currentUIDUserRoles = await dbRawRead(this.app, [currentUID], `
     SELECT * FROM user_roles where uid = ? and is_revoked = 0
   ;`);

    //get all current user roles that are not included in the target user_roles
    const newUserRoles = await dbRawRead(this.app, [targetUID, currentUID], `
      SELECT ur.* 
        FROM user_roles ur
   left join user_roles ur2 on ur2.uid = ? and ur2.group_id = ur.group_id and ur2.role_type = ur.role_type and ur2.is_revoked = 0
       where ur.uid = ? 
         and ur.is_revoked = 0
         and ur2.id is null
    ;`);

    //add the newUserRoles user_roles to the targetUID user_roless
    await Promise.all(
      newUserRoles.map(async (record: any) => {
        const newRecord = {
          ...record
        }
        newRecord.id = null
        newRecord.uid = targetUID
        await this.app.service('db/write/user-roles')
          .create(newRecord)
      })
    )

    //revoke the currentUID urser_roles
    await Promise.all(
      currentUIDUserRoles.map(async (record: any) => {
        await this.app
          .service('db/write/user-roles')
          .patch(record.id, {
            is_revoked: 1,
            revoked_on: dbDateNow(this.app),
            revoked_by_uid:currentUID,
          })
      })
    )
    return []
  }

  async patch (id: NullableId, data: IPatch, params?: Params): Promise<Data> {
    const dbInvitationsRead = this.app.service('db/read/u-invites');
    const dbInvitationsWrite = this.app.service('db/write/u-invites');
    const dbAuthWrite = this.app.service('db/write/auths');

    const ERR_INVALID_CODE = 'ERR_INVALID_CODE';
    const invitationCodeSplit = data.invitationCode.toLowerCase().split('x');
    const invitationId = parseInt(invitationCodeSplit[0], 10);
    const secret_key = invitationCodeSplit[1];

    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const uid = <number> userInfo.uid;

    if (!secret_key){
      throw new Errors.BadRequest(ERR_INVALID_CODE);
    }
    const invitationRecords = <IUInvites[]> await dbInvitationsRead
      .db()
      .where('id', invitationId)
      .where('secret_key', secret_key)
      .whereNot('is_revoked', 1)
      .whereNull('revoked_on')
      .whereNull('used_on')
      .where('expire_on', '>', dbDateNow(this.app))
      .limit(1)

    if (invitationRecords.length === 0){
      throw new Errors.BadRequest(ERR_INVALID_CODE);
    }

    const invitationRecord = invitationRecords[0];
    const invitation_uid = invitationRecord.uid;

    if(invitation_uid !== uid){
      //deactive sudo account
      await dbAuthWrite
      .db()
      .where('uid', invitation_uid)
      .update({
        enabled: 0
      })
    }

    // close invitation
    const invitePatchFields: Partial<IUInvites> = {
      used_on: dbDateNow(this.app),
      used_by_uid: invitation_uid,
      uid: uid
    }
    await dbInvitationsWrite.patch(
      invitationId,
      invitePatchFields
    )

    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
