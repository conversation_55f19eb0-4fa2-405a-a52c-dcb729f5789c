import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';
import { currentUid } from '../../../../util/uid';
import { PatchOption } from '../../test-auth/batch-alloc-policies/batch-alloc-policies.class';

interface Data {}

interface ServiceOptions {}

export class ScoreProfileGroups implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.NotImplemented();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    if (data && params) {
      const {
        group_name,
        order,
        assessment_slug,
        batch_allocation_policy_id,
        item_slug,
        item_nbr
      } = <any> data;
  
      const currentUID = await currentUid(this.app, params);
  
      const profileGroupCreateFields = {
        group_name: group_name == '' ? null : group_name,
        assessment_slug: assessment_slug == '' ? null : assessment_slug,
        order,
        item_nbr: item_nbr == ''? null: item_nbr
      }
      
      const score_profile_group = await this.app
        .service('db/write/marking-score-profile-groups')
        .create(profileGroupCreateFields);

      if(batch_allocation_policy_id){
        await this.app.service("public/test-auth/batch-alloc-policies").patch(batch_allocation_policy_id, 
          {
            option: PatchOption.ADD_PROFILE_GROUP,
            score_profile_group_id: score_profile_group.id,
            item_slug
          },
          params
        )
      }
      return score_profile_group;
    }
    return new Errors.BadRequest('NO_PARAMS');
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if(!params) {
      throw new Errors.BadRequest('MISSING_PARAMS');
    }

    const {
      group_name,
      order,
      assessment_slug,
      item_nbr
    } = <any> data;

    const score_profile_group = await this.app.service('db/write/marking-score-profile-groups').patch(id, {
      group_name: group_name == '' ? null : group_name,
      assessment_slug: assessment_slug == '' ? null : assessment_slug,
      order,
      item_nbr: item_nbr == ''? null: item_nbr
    })
    
    return score_profile_group;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    if (id && params) {
      const { batch_allocation_policy_id } = params.query!;
      const currentUID = await currentUid(this.app, params);
      
      const score_profile = await this.app
      .service('db/write/marking-score-profile-groups')
      .patch(id, {
        is_revoked: 1,
        revoked_on: dbDateNow(this.app)
      });

      if(batch_allocation_policy_id){
        await this.app.service("public/test-auth/batch-alloc-policies").patch(batch_allocation_policy_id, 
          {
            option: PatchOption.REMOVE_PROFILE_GROUP,
            score_profile_group_id: id,
          },
          params
        )
      }

      return score_profile; 
    }
    throw new Errors.BadRequest('MISSING_SCORE_PROFILE_ID');
  }
}
