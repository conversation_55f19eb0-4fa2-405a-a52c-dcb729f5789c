<div class="custom-modal" *ngIf="isTemplateSelectorModalActive" style="z-index: 100;">
  <div class="modal-contents is-max-size">
    <!-- TODO:COMPONENT make this it's own component -->
    <strong>Select a Template</strong>
    <div class="space-between">
      <div style="display: flex; gap: 1em; align-items: center;">
        <div>Filter: <input type="text" [(ngModel)]="templateNameFilter" [disabled]="isListView" /></div>
        <button [disabled]="isListView"*ngIf="!authRestrictions.isCurrentUserRestricted" class="button is-small" [class.is-info]="hasSimplified" (click)="toggleSimplifiedFilter()" >Simplified&nbsp;<i class="fas fa-bolt"></i></button>
        <button [disabled]="isListView"*ngIf="!hideArchived" class="button is-small" [class.is-info]="hasArchived" (click)="toggleArchivedFilter()" >Archived&nbsp;<i class="fas fa-archive"></i></button>
        <button [disabled]="isListView"*ngFor="let group of templateAuthGroups" class="button is-small" [class.is-info]="currentGroupId == group.group_id" (click)="toggleGroupFilter(group.group_id)">{{group.description}}</button>
        <button class="button is-small" [disabled]="isListView" (click)="exportTemplates()">Export Template List&nbsp;<i class="fa fa-download" aria-hidden="true"></i></button>
        <!-- <button (click)="getGroups()" class="button is-small" >Test</button> -->
      </div>
      <button class="button is-small is-danger" (click)="closeTemplateModal()">Cancel Template Selection</button>
    </div>
    <div style="display: flex;flex-direction: column; gap: 1em;">
      <div *ngIf="!getTemplateList(false) || !getTemplateList(false).length">
        No templates found.
      </div>
      <div *ngIf="getTemplateList(false)  && getTemplateList(false).length" style="display: flex; gap: 0.5em; align-items: center;">
        <a (click)="isListView=false" style="color: black;" [class.deselected]="!isListView">
          <i class="fas fa-regular fa-image"></i>
        </a>
        |
        <a (click)="setListView(true)" [class.deselected]="isListView" style="color: black;">
          <i class="fas fa-solid fa-table"></i> 
        </a>
      </div>
      <div *ngIf="isListView" style="display: flex; flex-direction: column; gap: 1em;">
        <ag-grid-angular
          style="width: auto; min-width:36em; min-height: 100%;"
          class="ag-theme-alpine ag-grid-fullpage"
          [rowData]="listData"
          [gridOptions]="gridOptions"
          [columnDefs]="columnDefs"
          [defaultColDef]="defaultColDef"
          (gridReady)="onGridReady($event)"
          (rowSelected)="selectTemplateAdvancedList($event.data)"
        >
        </ag-grid-angular>
        <div>
          <button class="button" (click)="exportCSV()"><tra slug="tc_dash_export_csv"></tra></button>
        </div>
      </div>
      <div *ngIf="!isListView" style="display: flex; flex-flow: row wrap;">
        <button *ngFor="let template of getTemplateList(false)" class="template-selector" (click)="selectTemplate(template)">
          <div style="display: flex; justify-content: flex-end;gap:0.5em;">
            <i *ngIf="template.is_archived" class="fas fa-archive"></i>
            <i *ngIf="template.is_simplified" class="fas fa-bolt"></i>
            <i *ngIf="!template.is_simplified && !template.is_archived" class="fas fa-bolt invisible"></i>
          </div>
          <img [src]="template.icon_url" style="max-height: 9em;">
          <div>ID: {{ template.id }}</div>
          <div>{{ template.template_name }}</div>
        </button>
      </div>
    </div>
  </div>
</div>