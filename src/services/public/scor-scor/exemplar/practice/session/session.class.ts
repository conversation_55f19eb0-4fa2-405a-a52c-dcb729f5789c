import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../../declarations';
import { currentUid } from '../../../../../../util/uid';
import { dbRawRead, dbRaw<PERSON>eadSingle } from '../../../../../../util/db-raw';
import { SQL_ITEM_COMPONENT_RESPONSE_SET, SQL_ITEM_COMPONENT_SET_RESPONSES, SQL_ITEM_SCORE_OPTIONS, SQL_BATCH_RESP_SCORES, SQL_ITEM_SCORE_FLAGS, SQL_SCORER_SCORING_TASK, SQL_ITEM_FAILED_QT, SQL_ITEM_BATCH_POLICIES } from '../../../../../../sql/scoring';
import { dbDateNow } from '../../../../../../util/db-dates';
import { Errors } from '../../../../../../errors/general';
import { AssignedItemStatus } from '../../../summary/types/assigned-tasks';

interface Data {}

interface ServiceOptions {}

export class Session implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    // return list of response ids in order
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    if (params && params.query){
      const window_item_id = id;
      const component_type = 'PRACTICE';
      const uid = await currentUid(this.app, params);
      return this.app
        .service('public/scor-scor/exemplar/practice/session')
        .getTrainingBatch(uid, window_item_id, component_type)
    }
    throw new Errors.BadGateway();
  }

  public async getTrainingBatch(uid:number, window_item_id:Id, component_type:string){
    let currentBatch;
    let responses;

    const scoringTask = await dbRawReadSingle(this.app, [uid, window_item_id, component_type], SQL_SCORER_SCORING_TASK);
    if (scoringTask.status === AssignedItemStatus.FAILED && scoringTask.allow_revisit == 0){
      throw new Errors.Forbidden('CANNOT_RESUME_FAILED_TASK')
    }

    // identify existing batch that has not already been closed
    const potentialBatches = <any[]> await this.app.service('db/read/marking-claimed-batches').find({
      query: {
        $select: ['id','is_marked', 'marking_window_item_id', 'num_prev'],
        marking_window_item_id: window_item_id,
        component_type,
        uid,
        is_training: 1,
        is_marked: 0,
        is_revoked: 0,
      },
      paginate: false,
    });

    if (potentialBatches.length > 0){
      // load existing batch
      currentBatch = potentialBatches[0];
      responses = await this.app.service('db/read/marking-claimed-batch-responses').find({query: {
        $select: ['id','taqr_id','is_marked', 'marked_on', 'is_training_graded', 'is_training_correct'],
        claimed_batch_id: currentBatch.id,
        $sort: { id: 1 }
      }, paginate: false})
    }
    else{
      const responseSets = await dbRawRead(this.app, [window_item_id, component_type], SQL_ITEM_COMPONENT_RESPONSE_SET); 
      if (responseSets.length === 0){
        throw new Errors.GeneralError('NO_PRACTICE_SET_AVAIL')
      }
      const {responseSet, numPreviousSets} = await this.selectNextResponseSet(responseSets, window_item_id, component_type, uid); 
      const response_set_id = responseSet.id;
      const responseSelections = await dbRawRead(this.app, [response_set_id], SQL_ITEM_COMPONENT_SET_RESPONSES); 
      // create new batch
      currentBatch  = {
        id: undefined,
        uid,
        marking_window_item_id: window_item_id,
        component_type,
        response_set_id,
        is_training: 1,
        num_prev: numPreviousSets, // easier to retrieve again later
        is_marked: 0,
      }
      const newBatchRecord = await this.app
        .service('db/read/marking-claimed-batches')
        .create(currentBatch);
      currentBatch.id = newBatchRecord.id;
      const claimed_batch_id = currentBatch.id;
      responses = await Promise.all(
        responseSelections.map(response => {
          const {taqr_id, selection_id} = response;
          return this.app
            .service('db/write/marking-claimed-batch-responses')
            .create({
              claimed_batch_id, 
              window_item_id: window_item_id,
              taqr_id,
              response_selection_id: selection_id,
            })
        })
      )
    }
    const claimed_batch_id = currentBatch.id;
    const scores = await dbRawRead(this.app, [claimed_batch_id], SQL_BATCH_RESP_SCORES);
    const scoreOptions = await dbRawRead(this.app, [window_item_id], SQL_ITEM_SCORE_OPTIONS);
    const flagOptions = await dbRawRead(this.app, [window_item_id], SQL_ITEM_SCORE_FLAGS(false));
    const batchPolicy = await dbRawRead(this.app, [window_item_id], SQL_ITEM_BATCH_POLICIES);
    const cutScore = batchPolicy[0].qt_cut_score;
    const prevFailedQT = await dbRawRead(this.app, [window_item_id, uid, cutScore], SQL_ITEM_FAILED_QT);
    return {
      task_id: scoringTask.id,
      currentBatch,
      scoreOptions,
      flagOptions,
      responses,
      scores,
      prevFailedQT,
      batchPolicy
    };
  }

  async selectNextResponseSet(responseSets:any[], window_item_id:Id, component_type:string, uid:number){
    let selectedSet = responseSets[0];
    const prevResponseSets = await dbRawRead(this.app, [window_item_id, component_type, uid], `
      select mcb.created_on
           , mrs.set_type_variant_num 
      from marking_claimed_batches mcb
      join  marking_response_set mrs
        on mrs.id  = mcb.response_set_id 
      where mcb.marking_window_item_id = ?
        and mcb.component_type = ?
        and mcb.uid = ?
      order by mcb.created_on desc
    ;`);
    const numPreviousSets = prevResponseSets.length
    if (numPreviousSets > 0){
      const lastResponseSet = prevResponseSets[0];
      const lastNum = lastResponseSet.set_type_variant_num;
      for (let responseSet of responseSets){
        if (responseSet.set_type_variant_num > lastNum){
          selectedSet = responseSet;
          break;
        }
      }
    }
    return {
      responseSet: selectedSet,
      numPreviousSets,
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
