<fieldset [disabled]="isReadOnly()">
  <div style="margin-bottom: 1em">
    <button (click)="setListView()" class="button is-small has-icon" [class.is-info]="element.config.isUseArrayEditor">
      <span class="icon"><i class="fas fa-list" aria-hidden="true"></i></span>
      <span>List Editor View</span>
    </button>
    <button (click)="setInlineView()" class="button is-small has-icon" [class.is-info]="!element.config.isUseArrayEditor">
      <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
      <span>Inline Editor View</span>
    </button>
  </div>
</fieldset>

<div *ngIf="!element.config.isUseArrayEditor">
  <div style="padding-bottom: 0.5em;">
    <textarea
      [formControl]="textContentForm"
      class="textarea"
      cdkTextareaAutosize
      [cdkAutosizeMinRows]="3"
      (focus)="onFocus()"
      (blur)="onBlur()"
      (click)="updateCursorPosition($event)"
      (keyup)="updateCursorPosition($event)"
      (select)="updateCursorPosition($event)"
    ></textarea>
  </div>
  
  <div>
    <button 
      (click)="newTargetText()" 
      (mouseenter)="setNewTargetButtonHoverState(true)"
      (mouseleave)="setNewTargetButtonHoverState(false)"
      class="button is-small has-icon"
      [disabled]="!isFocus"
    >
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>Add New Target</span>
    </button>
  </div>
  
</div>

<div *ngIf="element.config.isUseArrayEditor">
  
  <div class="list" cdkDropList (cdkDropListDropped)="dropContentArray($event)" [class.no-pointer-events]="isReadOnly()">
    <div class="list-item" *ngFor="let content of element.contentArray; let i = index;" cdkDrag>
      
      <div class="space-between" style="align-items: flex-start">
        
        <div [ngSwitch]="content.type">
          <div *ngSwitchCase="DND_INLINE_CONTENT_TYPE.TEXT">
            <textarea 
              (mousedown)="$event.stopPropagation()"
              [(ngModel)]="content.value" 
              (ngModelChange)="validateConfig()"
              class="textarea"
              cdkTextareaAutosize
              [cdkAutosizeMinRows]="3"
            ></textarea>
          </div>
          <div *ngSwitchCase="DND_INLINE_CONTENT_TYPE.TARGET">
            <div>
              <span>target label:</span>
              <input 
                (mousedown)="$event.stopPropagation()"
                [(ngModel)]="content.value" 
                (ngModelChange)="validateConfig()"
                type="text"/>
            </div>
          </div>
          <div *ngSwitchCase="DND_INLINE_CONTENT_TYPE.MATH">
            <div class="capture-math-container" style="min-width: 10em" (mousedown)="$event.stopPropagation()">
              <capture-math 
                [obj]="content" 
                (onChange)="validateConfig()"
                [class.is-disabled]="isReadOnly()"
                prop="value" [isManualKeyboard]="true">
              </capture-math>
            </div>
          </div>
        </div>
        
        <div (click)="elementEditDeleteContentArray(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
          <i class="fas fa-trash" aria-hidden="true"></i>
        </div>
      </div>
      
    </div>
  </div>
  
  <div class="field has-addons">
    <div class="control select is-fullwidth">
      <select [formControl]="contentArrayTypeForm">
        <option *ngFor="let option of contentArrayTypeOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </div>
    <div class="control">
      <button class="button is-primary" (click)="elementEditAddContentArray()">Add</button>
    </div>
  </div>
  
</div>

<hr />

<fieldset [disabled]="isReadOnly()">
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm" (change)="rerender()"/>
    Show Answer
  </label>
</fieldset>


<twiddle caption="Target Options" [state]="twiddleTargets"></twiddle>
<div *ngIf="twiddleTargets.value">
  <fieldset [disabled]="isReadOnly()">
    
    <div style="margin-bottom: 1em;">
      <label class="checkbox">
        <input type="checkbox" [formControl]="isUsingAnswerGroupForm" (change)="validateConfig()"/>
        Use Answer Group
      </label>
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="validateConfig()"/>
        Accept Multiple Answers
      </label>
    </div >
    
    
    <div >
      <div *ngFor="let target of dndTargetOptionsForm; let i = index; trackBy: trackByFn" class="target-container">
        
        <div *ngIf="!isUnusedTarget(target); else unusedTargetLabelHeader">
          <span class="target-label">target: </span>
          <input 
            type="text" disabled 
            value="{{target.label}}" 
            class="target-name-input-label">
          <div *ngIf="element.config.isUsingAnswerGroup">
            <label for="dropdown">group:</label>
            <select [formControl]="target.group">
              <option *ngFor="let option of groupOptions" [value]="option.id">
                {{option.caption}}
              </option>
            </select>
          </div>
          <div>
            <span>option:</span>
          </div>
        </div>
        <ng-template #unusedTargetLabelHeader>
          <span>extra options:</span>
          <div *ngIf="isUsingAnswerGroup()" style="display: inline;">
            <span>(group</span>
            <input 
              type="text" disabled 
              value="{{target.group.value}}" 
              class="target-group-unused-label">
            <span>)</span>
          </div>
        </ng-template>
        
        
        <div class="sub-property-div" style="max-width: 20em;">
          <div *ngFor="let optionForm of target.content; let j = index; trackBy: trackByFn" class="option-container">
            <div [ngSwitch]="getOptionTextMode(i,j)" style="flex-grow: 1;">
              <div *ngSwitchCase="DND_OPTION_TEXT_MODE.TEXT">
                <textarea
                  [formControl]="optionForm"
                  style="min-width: 10em; max-width: 20em;"
                  class="textarea is-small target-option-textarea"
                  [class.error-bg-color]="isNonUniqueOption(optionForm.value)"
                  cdkTextareaAutosize
                  [cdkTextareaAutosize]="true"
                  [cdkAutosizeMinRows]="2"
                ></textarea>
              </div>
              <div *ngSwitchCase="DND_OPTION_TEXT_MODE.MATH">
                <div 
                  class="capture-math-container"
                  [class.error-border]="isNonUniqueOption(optionForm.value)"
                >
                  <capture-math 
                    [obj]="getOptionObject(i,j)" 
                    (onChange)="validateConfig()"
                    [class.is-disabled]="isReadOnly()"
                    prop="value" [isManualKeyboard]="true">
                  </capture-math>
                </div>
              </div>
            </div>
            
            <div *ngIf="getOptionTextMode(i,j) === DND_OPTION_TEXT_MODE.TEXT"
              (click)="elementEditSetOptionTextMode(i,j,DND_OPTION_TEXT_MODE.MATH)" class="target-option-button"
              [class.no-pointer-events]="isReadOnly()">
              <i class="fas fa-square-root-alt" aria-hidden="true"></i>
            </div>
            <div *ngIf="getOptionTextMode(i,j) === DND_OPTION_TEXT_MODE.MATH"
              (click)="elementEditSetOptionTextMode(i,j,DND_OPTION_TEXT_MODE.TEXT)" class="target-option-button"
              [class.no-pointer-events]="isReadOnly()">
              <i class="fas fa-font" aria-hidden="true"></i>
            </div>
            
            <div *ngIf="isAllowDeleteOption(i,j)" (click)="elementEditDeleteOption(i,j)" class="target-option-button"
              [class.no-pointer-events]="isReadOnly()"
            >
              <i class="fas fa-trash" aria-hidden="true"></i>
            </div>
          </div>
          <button 
            *ngIf="isAllowAddOption(i)"
            (click)="elementEditAddOption(i)" 
            class="button is-small has-icon"
          >
            <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
            <span>Add Option</span>
          </button>
        </div>
        
      </div>
    </div>
  </fieldset>
</div>

<div class="twiddle-container" [class.error-bg-color]="isAnswersContainsError()">
  <twiddle *ngIf="element.config.isAllowMultipleAnswer" caption="Answers" [state]="twiddleAnswers"></twiddle >
</div>
<div *ngIf="twiddleAnswers.value && element.config.isAllowMultipleAnswer && element.altAnswers">
  <fieldset [disabled]="isReadOnly()" (change)="rerender()">
    
    <div class="flex-container" style="align-items: flex-start;">
      <div class="answer-set-block">
        <div *ngFor="let targetAnswer of defaultAnswerSet; let j = index; trackBy: trackByFn">
          <input 
            type="text" disabled 
            value="{{idToLabelMap.get(targetAnswer.targetId)}}" 
            class="target-name-input-label">
          <!-- targets in tableDnD can only have one answer -->
          <select [(ngModel)]="targetAnswer.optionIds[0]" disabled>
            <option *ngFor="let id of sortedOptionIdList; let k = index" [value]="id">({{k+1}}) {{idToLabelMap.get(id)}}</option>
          </select>
        </div>
      </div >
    </div>
    
    <div *ngFor="let answerSet of element.altAnswers; let i = index; trackBy: trackByFn" 
      class="flex-container" style="align-items: flex-start;">
      
      <div class="answer-set-block">
        <div *ngFor="let targetAnswer of answerSet; let j = index; trackBy: trackByFn">
          
          <input 
            type="text" disabled 
            value="{{idToLabelMap.get(targetAnswer.targetId)}}" 
            class="target-name-input-label">
          <!-- targets in tableDnD can only have one answer -->
          <select [(ngModel)]="targetAnswer.optionIds[0]" [class.error-bg-color]="isInvalidOptionId(targetAnswer.optionIds[0])">
            <option *ngFor="let id of sortedOptionIdList; let k = index" [value]="id">({{k+1}}) {{idToLabelMap.get(id)}}</option>
          </select>
          
        </div>
      </div >
      
      <div (click)="elementEditDeleteAnswerSet(i)" class="target-option-button"
        [class.no-pointer-events]="isReadOnly()"
      >
        <i class="fas fa-trash" aria-hidden="true"></i>
      </div>
      
    </div>
      
    
    
    <button (click)="elementEditAddAnswerSet()" class="button is-small has-icon">
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>New Answer Set</span>
    </button>
    
  </fieldset>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <fieldset [disabled]="isReadOnly()" (change)="rerender()">
    
    <div style="margin-bottom: 1em;">
      <span>Layout : </span>
      <div class="select">
        <select [formControl]="homeLayoutForm">
          <option *ngFor="let option of homeLayoutOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
        </select>
      </div>
    </div>
    
    Home Position:
    <select [formControl]="homePositionForm">
      <option *ngFor="let option of homePositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
    </select>
    
    <br><label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.homeConfig.isAddHomeLabel"/>
      Add Label
    </label>
    
    <div *ngIf="element.homeConfig.isAddHomeLabel">
      <textarea
        [formControl]="homeLabelForm"
        style="min-width: 10em; max-width: 20em;"
        class="textarea is-small target-option-textarea"
        cdkTextareaAutosize
        [cdkTextareaAutosize]="true"
        [cdkAutosizeMinRows]="2"
      ></textarea>
      <br>Label Position:
      <select [formControl]="homeLabelPositionForm">
        <option *ngFor="let option of homeLabelPositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </div>
    
    <br><label class="checkbox">
      <input type="checkbox" [formControl]="isGroupHomeForm"/>
      Group Home
    </label>
    
    <div *ngIf="element.homeConfig.isGroupHome">
      <label class="checkbox">
        <input type="checkbox" [formControl]="isSyncGroupForm"/>
        Group home based on Answer Group
      </label>
    </div>
    
    <div cdkDropListGroup [class.no-pointer-events]="isReadOnly()">
      <div *ngFor="let group of element.homeConfig.element; let i = index" 
           [cdkDropListData]="group" 
           cdkDropList 
           [id]="'group' + i"
           class="cdk-drop-group" 
           (cdkDropListDropped)="dropHomeLayout($event)">
        <div *ngFor="let optionId of group; let j = index" cdkDrag class="cdk-drop-element">
          {{getOptionLabelFromId(optionId)}}
        </div>
        
        <div *ngIf="isEmptyHomeGroup(i) && element.homeConfig.isGroupHome">
          <div (click)="elementEditDeleteHomeGroup(i)" class="target-option-button"
            style="width: 100%"
            [class.no-pointer-events]="isReadOnly()"
          >
            <i class="fas fa-trash" aria-hidden="true"></i>
          </div>
        </div>
        
      </div>
      
      <div *ngIf="element.homeConfig.isGroupHome">
        <button (click)="elementEditAddHomeGroup()" class="button is-small has-icon">
          <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
          <span>New Group</span>
        </button>
      </div >
      
      
    </div>
    
  </fieldset>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value">
  <div style="margin-bottom: 0.5em;">
    <span>Content Justify: </span>
    <select [(ngModel)]="element.config.contentJustify" (change)="rerender()">
      <option *ngFor="let option of contentJustifyOptions" [value]="option.id">{{option.caption}}</option>
    </select>
  </div>
  
  <dg-int-dnd-color-style
    [styleConfig]="element.styleConfig"
    [constructionElement]="element"
    [dgIntElement]="parentElement"
    [idToLabelMap]="idToLabelMap"
    [targetIdList]="targetIdList"
    [homeIdList]="homeIdList"
    [sortedOptionIdList]="sortedOptionIdList"
    [homeConfig]="element.homeConfig"
    [isSimplified]="hiddenConfigs.simpleStyleMode"
    (onChange)="validateConfig()"
  ></dg-int-dnd-color-style>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="rerender()">
    <div class="sub-property-div control">
      <br>Target Background Colour:
      <input type="color" [(ngModel)]="element.config.targetBgColor">
      <br>Draggable Background Colour:
      <input type="color" [(ngModel)]="element.config.draggableBgColor">
      <br>
        
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isUniformLineSpacing"/>
        Uniform line spacing
      </label>
      <br>
      
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
        
      <br>
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.maxHomeWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
    </div>
  </fieldset>
</div>
