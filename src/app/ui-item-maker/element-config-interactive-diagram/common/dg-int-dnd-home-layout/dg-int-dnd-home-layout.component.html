<fieldset [disabled]="isReadOnly()" (change)="emitChange()">
  
  
  <div *ngIf="isValidLayout()">
    
    <div style="margin-bottom: 1em;">
      <span>Layout : </span>
      <div class="select">
        <select [(ngModel)]="element.layout" (change)="onLayoutChange()">
          <option *ngFor="let option of homeLayoutOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
        </select>
      </div>
    </div>
    
    <div *ngIf="this.element.layout == 'custom_advanced'">
      <button (click)="openHomeLayoutEditorModal()" class="button is-small has-icon">
        <span class="icon"><i class="fas fa-edit" aria-hidden="true"></i></span>
        <span>Edit Layout</span>
      </button>
      <button (click)="openHomeLayoutJSONModal()" class="button is-small has-icon">
        <span class="icon"><i class="fas fa-upload" aria-hidden="true"></i></span>
        <span>Import/Export</span>
      </button>
    </div>
    
    <div *ngIf="this.element.layout == 'freeform'">
      <label class="label">Background Image</label>
      <div class="background-config-container">
        <capture-image [element]="element.freeformLayoutConfig.image" (change)="emitChange()"></capture-image>
        <asset-library-link [element]="element.freeformLayoutConfig.image"></asset-library-link>     
      </div>
    </div>
    
  </div>
  <div *ngIf="!isValidLayout()">
    <config-preview-container headerText="Layout">
      
      <div style="margin-bottom: 1em;">
        <span>Layout : </span>
        <div class="select">
          <select [(ngModel)]="element.layout">
            <option *ngFor="let option of homeLayoutOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
          </select>
        </div>
      </div>
      
    </config-preview-container>
  </div>
  
  
  
  <ng-container *ngIf="!isHomeConfigLayoutFixedPosition(element.layout)">
  <span>Home Position:</span>
  <select [formControl]="homePositionForm">
    <option *ngFor="let option of homePositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
  </select>
  </ng-container>
  
  <ng-container *ngIf="!isHomeConfigLayoutFixedPosition(element.layout)">
    <br><label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.isAddHomeLabel"/>
      Add Label
    </label>
  </ng-container>
  
  <div *ngIf="element.isAddHomeLabel && !isHomeConfigLayoutFixedPosition(element.layout)">
    <ng-container
      [ngTemplateOutlet]="textOrMathInput"
      [ngTemplateOutletContext]="{obj: element, valueProp: 'label', isMathProp: 'isLabelMath'}"
    ></ng-container>
    <ng-container *ngIf="element.label">
      <br>Label Position:
      <select [(ngModel)]="element.labelPosition">
        <option *ngFor="let option of homeLabelPositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </ng-container>
      
  </div>
  
  <ng-container *ngIf="element.isAddHomeLabel && isHomeConfigLayoutAllowGroupLabel(element.layout)">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.isAddGroupHomeLabels"/>
      Add Group Labels
    </label>
  </ng-container>
  
  <br><label class="checkbox">
    <input type="checkbox" [(ngModel)]="element.isAddHomeNote"/>
    Add Note
  </label>
  <div *ngIf="element.isAddHomeNote">
    <ng-container
      [ngTemplateOutlet]="textOrMathInput"
      [ngTemplateOutletContext]="{obj: element, valueProp: 'note', isMathProp: 'isNoteMath'}"
    ></ng-container>
    <br>
  </div>
  
  <div cdkDropListGroup [class.no-pointer-events]="isReadOnly()">
    <div *ngFor="let group of element.element; let i = index; trackBy: trackByFn"
          [cdkDropListData]="group" 
          cdkDropList 
          [id]="'group' + i"
          class="cdk-drop-group" 
          (cdkDropListDropped)="dropHomeLayout($event)"
    >
            
      <div *ngIf="element.isAddGroupHomeLabels && element.isAddHomeLabel && isHomeConfigLayoutAllowGroupLabel(element.layout)" >
        <label>Label: </label>
        <div>
          <ng-container
            *ngIf="element.groupHomeLabels && element.groupHomeLabels[i]"
            [ngTemplateOutlet]="textOrMathInput"
            [ngTemplateOutletContext]="{obj: element.groupHomeLabels[i], valueProp: 'label', isMathProp: 'isLabelMath', isSmaller: true}"
          ></ng-container>
        </div>
        <hr style="margin: 1em; background-color: #ccc; height: 1px;">
      </div> 
            
      <div *ngFor="let optionId of group; let j = index" cdkDrag class="cdk-drop-element">
        
        <div *ngIf="getOptionImageURLFromId(optionId)">
          <div class="simple-image-container">
            <img [src]="getOptionImageURLFromId(optionId)" style="max-width: 10em; max-height: 10em;">
          </div>
        </div>
        <ng-container *ngIf="!idIsMathSet.has(optionId); else mathLabel">
          {{getOptionLabelFromId(optionId)}}
        </ng-container>
        <ng-template #mathLabel>
          <div style="width: fit-content;">
            <katex-display [expression]="getOptionLabelFromId(optionId)" [displayMode]="true"></katex-display>
          </div>
        </ng-template>
        
        <div *ngIf="element.layout == 'freeform'">
          
          <div>
            <button 
              (click)="requestMoveFreeformHome($event, j)" 
              class="button is-small has-icon"
            >
              <span class="icon"><i class="fa fa-map-marker-alt" aria-hidden="true"></i></span>
              <span>Move Home Position</span>
            </button>
            <span *ngIf="movingHomeIndex == j">Right Click to cancel</span>
          </div>
          
          <div style="display: flex; flex-direction: row; align-items: center; gap: 0.5em" *ngIf="parentElement.isShowAdvancedOptions">
            <div>X: </div>
            <input 
                type="number" 
                class="input is-small" 
                style="width:8em; text-align:center" 
                [(ngModel)]="getFreeformPositionObject(optionId).x"
                (mousedown)="$event.stopPropagation()"
                step="0.01"
            >
            <div>Y: </div>
            <input 
                type="number" 
                class="input is-small" 
                style="width:8em; text-align:center" 
                [(ngModel)]="getFreeformPositionObject(optionId).y"
                (mousedown)="$event.stopPropagation()"
                step="0.01"
            >
          </div>
        </div>
        
      </div>
      
      <div *ngIf="isEmptyHomeGroup(i) && i != 0 && !isFixedGroup()">
        <div (click)="elementEditDeleteHomeGroup(i)" class="target-option-button"
          style="width: 100%"
          [class.no-pointer-events]="isReadOnly()"
        >
          <i class="fas fa-trash" aria-hidden="true"></i>
        </div>
      </div>
      
    </div>
    
    <div *ngIf="!isFixedGroup()">
      <button (click)="elementEditAddHomeGroup()" class="button is-small has-icon">
        <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
        <span>New Group</span>
      </button>
    </div >
    
    <div>
      <button (click)="shuffleElements()" class="button is-small has-icon">
        <span class="icon"><i class="fas fa-random" aria-hidden="true"></i></span>
        <span>Shuffle</span>
      </button>
    </div >
    
    <div *ngIf="element.layout == 'freeform'">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.isShowAdvancedOptions"/>
        Show Advanced Options
      </label>
    </div>
    
    
  </div>

</fieldset>

<ng-template #textOrMathInput let-obj="obj" let-valueProp="valueProp" let-isMathProp="isMathProp" let-isSmaller="isSmaller">
  <textarea
    *ngIf="!obj[isMathProp]"
    textInputTransform
    [(ngModel)]="obj[valueProp]"
    (input)="emitChange()"
    style="min-width: 10em; display: inline-block"
    [style.max-width]="isSmaller ? '19em' : '20em'"
    class="textarea is-small"
    cdkTextareaAutosize
    [cdkTextareaAutosize]="true"
    [cdkAutosizeMinRows]="2"
  ></textarea>
  <div *ngIf="obj[isMathProp]" class="capture-math-container" 
    style="display: inline-block; vertical-align: top;"
    [style.width]="isSmaller ? '11em' : '12em'"
  >
    <capture-math 
      [obj]="obj" 
      (onChange)="emitChange()"
      [class.is-disabled]="isReadOnly()"
      [class.is-disabled]="isReadOnly()"
      [prop]="valueProp" [isManualKeyboard]="true">
    </capture-math>
  </div>
  <div *ngIf="!obj[isMathProp]"
    (click)="obj[isMathProp] = true; emitChange()" 
    class="target-option-button"
    [class.no-pointer-events]="isReadOnly()">
    <i class="fas fa-square-root-alt" aria-hidden="true"></i>
  </div>
  <div *ngIf="obj[isMathProp]"
    (click)="obj[isMathProp] = false; emitChange()"
    class="target-option-button"
    [class.no-pointer-events]="isReadOnly()">
    <i class="fas fa-font" aria-hidden="true"></i>
  </div>
</ng-template>


<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="Modal.ADVANCED_HOME_LAYOUT_EDITOR" style="width: 35em; ">
        <dg-int-dnd-advanced-home-layout-editor 
          [homeConfig]="element"
        ></dg-int-dnd-advanced-home-layout-editor>
        <modal-footer [pageModal]="pageModal" [confirmButton]="false" closeMessage="btn_ok"></modal-footer>
      </div>
      <div *ngSwitchCase="Modal.ADVANCED_HOME_LAYOUT_JSON" style="width: 35em; ">
        <textarea
          [(ngModel)]="advancedHomeLayoutJSON"
          style="height: 34em"
          class="textarea "
        ></textarea>
        <modal-footer [pageModal]="pageModal"></modal-footer>
      </div>
    </div>
  </div>
</div>
