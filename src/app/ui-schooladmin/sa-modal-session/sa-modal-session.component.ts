import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import * as moment from 'moment-timezone';
import { ISession, IStudentAccount, SessionStatus } from "../data/types";
import { GENERAL_SINGLE_CHECK } from "../data/constants";
import { ISaFormEntry, SaFormType, initFormEntries } from "../sa-widget-form-entry/sa-widget-form-entry.component";
import { randId } from "../../core/util/random";
import { G9DemoDataService } from '../g9-demo-data.service';
import { FormControl, FormGroup } from '@angular/forms';
import { LangService } from 'src/app/core/lang.service';
import { ClassFilterId, MySchoolService } from '../my-school.service';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { ASSESSMENT, SCHEDULER, isAssessmentABED } from 'src/app/ui-teacher/data/types';
import { WhitelabelService } from '../../domain/whitelabel.service';
@Component({
  selector: 'sa-modal-session',
  templateUrl: './sa-modal-session.component.html',
  styleUrls: ['./sa-modal-session.component.scss']
})
export class SaModalSessionComponent implements OnInit {

  @Input() sessions: ISession[];
  @Input() savePayload: { [key: string]: any };
  @Input() saveProp: string;
  @Input() isActive:boolean = false;
  @Input() isPartialActive:boolean = false;
  @Input() isEditing:boolean = false;
  @Output() isDateValid = new EventEmitter();
  classes: any[];
  ASSESSMENT = ASSESSMENT;
  SCHEDULER = SCHEDULER;
  assessments = [
    ASSESSMENT.PRIMARY_SAMPLE,
    ASSESSMENT.PRIMARY_OPERATIONAL,
    ASSESSMENT.JUNIOR_SAMPLE,
    ASSESSMENT.JUNIOR_OPERATIONAL,
    ASSESSMENT.G9_OPERATIONAL, 
    ASSESSMENT.G9_SAMPLE,
    ASSESSMENT.OSSLT_OPERATIONAL, 
    ASSESSMENT.OSSLT_SAMPLE,
    ASSESSMENT.TCLE_OPERATIONAL,
    ASSESSMENT.TCLE_SAMPLE,
    ASSESSMENT.TCN_OPERATIONAL,
    ASSESSMENT.TCN_SAMPLE,
    ASSESSMENT.SCIENCES8_OPERATIONAL,
    ASSESSMENT.SCIENCES8_SAMPLE,
    ASSESSMENT.MBED_SAMPLE
  ];
  schedules = [SCHEDULER.LATER, SCHEDULER.NOW]
  assessmentTypes: any[];
  scheduleOptions: any[]
  showDatePicker: boolean = false;
  assessmentToBeScheduled: ASSESSMENT;
  removesessionB: boolean;
  constructor(
    private g9DemoData: G9DemoDataService,
    public lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,
    public whitelabelService: WhitelabelService,
    private schoolService: MySchoolService
  ) { }

  formEntries: { [key: string]: ISaFormEntry};
  timeEntries:{ [key: string]: ISaFormEntry};
  formGroup = new FormGroup({
    date: new FormControl(),
    time: new FormControl()
  })
  formEntriesList: ISaFormEntry[] = [];
  selectedSession: Partial<ISession>;
  isSelectingSession: boolean;
  isSessionCompleted: boolean[];
  isValidAssessmentType: boolean;
  isPrimary:boolean;
  isJunior:boolean;
  isG9:boolean;
  isOsslt:boolean;
  isFIClass:boolean = false;  

  ngOnInit(): void {
    this.isValidAssessmentType = Object.values(ClassFilterId).includes(this.savePayload.filter);
    this.isPrimary = (this.savePayload.filter === ClassFilterId.Primary);
    this.isJunior = (this.savePayload.filter === ClassFilterId.Junior);
    this.isG9 = (this.savePayload.filter === ClassFilterId.G9);
    this.isOsslt = (this.savePayload.filter === ClassFilterId.OSSLT);
    this.initContextInfo();
    this.initSelectedSession();
    if(this.isEditing) {
      this.updateSessionsCompleted()
    }
  }

  initSelectedSession() {
    if (this.sessions) {
      if (this.sessions.length === 1) {
        return this.selectSession(this.sessions[0]);
      }
      if (this.sessions.length > 1) {
        return this.isSelectingSession = true;
      }
    }
    return this.selectSession({});
  }

  selectSession(session: Partial<ISession>) {
    this.isSelectingSession = false;
    this.selectedSession = session;
    this.initFormEntries(this.selectedSession);
    this.initPayloadSubscription(this.selectedSession);
  }
  initContextInfo() {
    this.initializeScheduleOptions();
    this.initializeAssessments()
    this.initializeClasses();
  }
  initializeClasses() {
    this.classes = [];
    this.g9DemoData.classrooms.forEach(classroom => {
      const isClassPrimary = (classroom.course_type === "EQAO_G3");
      let course_type = classroom.course_type;
      if (classroom.course_type === "EQAO_G10") {
        course_type = "OSSLT"; // need to do this because OSSLT and EQAO_G10 are not consistently named
      }
      const courseTypeMatchesAssessmentType = course_type.includes(this.savePayload.filter);
      const semester = this.g9DemoData.semesters.map[classroom.semester]
      const testWindow = this.g9DemoData.testWindows.find(tw => tw.id == semester.testWindowId)
      const isActive = new Date(testWindow.date_end) > new Date ()
      const isPlaceholder = classroom.is_placeholder
      if (this.isValidAssessmentType && courseTypeMatchesAssessmentType && isActive && !isPlaceholder) {
        if (isClassPrimary) {
          this.classes.push({
            id: classroom.id,
            label: classroom.class_code,
            is_fi: classroom.is_fi,
          })
        } else {
          this.classes.push({
            id: classroom.id,
            label: classroom.class_code,
          })
        }
      }
    })
  }
  initializeScheduleOptions() {
    if(this.assessmentToBeScheduled === ASSESSMENT.PRIMARY_OPERATIONAL || this.assessmentToBeScheduled === ASSESSMENT.JUNIOR_OPERATIONAL || this.assessmentToBeScheduled === ASSESSMENT.G9_OPERATIONAL || this.assessmentToBeScheduled === ASSESSMENT.OSSLT_OPERATIONAL){
      this.scheduleOptions = [{
        id:SCHEDULER.LATER,
        label:this.renderTitle(SCHEDULER.LATER)
      }]
    }
    else if(this.whitelabelService.isNBED() || this.whitelabelService.isMBED()) {
      this.scheduleOptions = [{
        id:SCHEDULER.NOW,
        label:this.renderTitle(SCHEDULER.NOW)
      }]
    }
    else{
      this.scheduleOptions = this.schedules.map(schedule => {
        return {
          id: schedule,
          label: this.renderTitle(schedule),
        }
      })
    }
  }

  initializeAssessments() {
    this.assessmentTypes = this.assessments.map(assessment => {
      return {
        id: assessment,
        label: this.renderTitle(assessment),
      }
    })
  }
  initFormEntries(session: Partial<ISession>) {
    if(this.isEditing){
      this.showDatePicker = true;
      if(session.slug && (session.slug === ASSESSMENT.PRIMARY_SAMPLE || session.slug === ASSESSMENT.PRIMARY_OPERATIONAL || session.slug === ASSESSMENT.JUNIOR_SAMPLE || session.slug === ASSESSMENT.JUNIOR_OPERATIONAL)) {
        this.formEntriesList = initFormEntries(session, [
          { valProp: 'sessionLangStartDate', label: 'lbl_date' },
          { valProp: 'sessionLangEndDate', label: 'lbl_date' }, 
          { valProp: 'sessionMathStartDate', label: 'lbl_date' },
          { valProp: 'sessionMathEndDate', label: 'lbl_date' }, 
        ]);
      }
      if(session.slug && session.slug === ASSESSMENT.G9_SAMPLE) {
        this.removesessionB = true;
        this.formEntriesList = initFormEntries(session, [
          { valProp: 'sessionAStartDate', label: 'lbl_date' },
          { valProp: 'sessionAStartTime', label: 'lbl_time' },  // Start Time
        ]);
      } 
      if(session.slug && (session.slug === ASSESSMENT.G9_OPERATIONAL || session.slug === ASSESSMENT.OSSLT_SAMPLE || session.slug === ASSESSMENT.OSSLT_OPERATIONAL)) {
        this.formEntriesList = initFormEntries(session, [
          { valProp: 'sessionAStartDate', label: 'lbl_date' },
          { valProp: 'sessionAStartTime', label: 'lbl_time' },  // Start Time
          { valProp: 'sessionBStartDate', label: 'lbl_date' },
          { valProp: 'sessionBStartTime', label: 'lbl_time' },
        ]);
      }
      if(session.slug && isAssessmentABED(session.slug)) 
      {
        this.formEntriesList = initFormEntries(session, [
          { valProp: 'sessionAStartDate', label: 'lbl_date' },
          { valProp: 'sessionAStartTime', label: 'lbl_time' },  // Start Time
        ]);
      }
    }
    else
    { 
      const assessments = this.getAssessmentsForNewSession();
      console.log('session: ' + session);
      this.formEntriesList = initFormEntries(session, [
        { 
          // Grouping
          type: SaFormType.SELECT, 
          valProp: 'classId', 
          label: this.whitelabelService.isABED() ? 'sa_classrooms_col_class_code_ABED':'sa_class_code', 
          options: this.classes 
        },  
        { 
          // Description
          type: SaFormType.SELECT, 
          valProp: 'slug', 
          label: 'lbl_assessment', 
          options: assessments 
        }, 
        { 
          type: SaFormType.SELECT, 
          valProp: 'schedule', 
          label: 'lbl_start', 
          options: this.scheduleOptions 
        },
        { valProp: 'sessionAStartDate', label: 'lbl_date' },
        { valProp: 'sessionAStartTime', label: 'lbl_time' },  // Start Time
        { valProp: 'sessionBStartDate', label: 'lbl_date' },
        { valProp: 'sessionBStartTime', label: 'lbl_time' },
        { valProp: 'sessionLangStartDate', label: 'lbl_date' },
        { valProp: 'sessionLangEndDate', label: 'lbl_date' }, 
        { valProp: 'sessionMathStartDate', label: 'lbl_date' },
        { valProp: 'sessionMathEndDate', label: 'lbl_date' }, 
      ]);
    }

    this.extractFormEntryRef();
  }

  filterDuplicateObjects = (array) => {
    return array.filter((value, index, array) => array.findIndex(v2 => ((v2.id === value.id)) && (v2.asmt_slug === value.asmt_slug)) === index);
  }

  getAssessmentsForNewSession = () => {
    const assessmentData = this.savePayload.testWindow.assessments.map(asmt => {
      return ({
        id: asmt?.asmt_slug,
        label: this.renderTitle(asmt?.asmt_slug)
      })
    });

    return assessmentData;
  }

  updateSessionsCompleted() 
  {
    if (this.whitelabelService.isABED())
    {
      this.removesessionB = true;
    }

    let classId = null;
    let asmtSlug = null;
    if(this.isEditing) {
      if(this.sessions && this.sessions.length == 1){
        classId = this.sessions[0].classroom_id;
        asmtSlug = this.sessions[0].slug;
      } else {
        this.isSessionCompleted = [false, false]
        return
      }
    } else {
      classId = this.formEntries['classId'].formControlRef.value;
      asmtSlug = this.formEntries['slug'].formControlRef.value;
    }
    
    this.isSessionCompleted = undefined;
    if(!classId || !asmtSlug) {
      return;
    }

    this.auth.apiFind(this.routes.SCHOOL_ADMIN_SESSION_COMPLETE, {query: {asmt_slug: asmtSlug, schl_class_id: classId, schl_group_id: this.schoolService.getCurrentSchoolGroupId()}}).then((res) => {
      this.isSessionCompleted = res;
    })
  }

  isSessionComplete(session_i: number) {
    return !this.isSessionCompleted || this.isSessionCompleted[session_i];
  }
  
  checkFIClass(classId: string): boolean {
    let currentClass = this.classes.find(c => c.id == classId && c?.is_fi === 1);
    return currentClass ? true : false;
  }

  checkSelection(entry, value) {
    if(entry.valProp === 'classId') {
      this.updateSessionsCompleted();
      this.isFIClass = this.checkFIClass(value);       
    } else if(entry.valProp === 'slug') {
      this.updateSessionsCompleted();
      switch(value){
        case(ASSESSMENT.PRIMARY_SAMPLE):
        case(ASSESSMENT.JUNIOR_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
        break;
        case(ASSESSMENT.PRIMARY_OPERATIONAL):
        case(ASSESSMENT.JUNIOR_OPERATIONAL):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
        break;
        case(ASSESSMENT.OSSLT_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
          this.removesessionB = false;
          //this.initSelectedSession()
        break;
        case(ASSESSMENT.G9_OPERATIONAL):
        case(ASSESSMENT.OSSLT_OPERATIONAL):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
          this.removesessionB = false;
          //this.initSelectedSession()
          break;
        case(ASSESSMENT.G9_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.removesessionB = true;
          this.formEntriesList[2].options = this.scheduleOptions;
          break;
        case(ASSESSMENT.OSSLT_SAMPLE):
          this.assessmentToBeScheduled = value;
          this.initializeScheduleOptions();
          this.formEntriesList[2].options = this.scheduleOptions;
          this.removesessionB = false;
          //this.initSelectedSession()
          break;
        case(SCHEDULER.LATER):
          this.showDatePicker = true
          break;
        case(SCHEDULER.NOW):
          this.showDatePicker = false
          break;
      }
    } else if(entry.valProp === 'schedule') {
      switch(value){
        case(SCHEDULER.LATER):
          this.showDatePicker = true;
          break;
        case(SCHEDULER.NOW):
          this.showDatePicker = false;
          break;
      }
    }
  }
  extractFormEntryRef() {
    this.formEntries = {};
    this.formEntriesList.forEach(entry => {
      this.formEntries[entry.valProp] = entry;
    })
  }

  saWidgetFormEntries() {
    return this.formEntriesList.filter(entry => !!entry.type)
  }
  renderTitle(title: string) {
    switch (title) {
      case ASSESSMENT.PRIMARY_SAMPLE: return this.lang.tra("primary_sample_test");
      case ASSESSMENT.PRIMARY_OPERATIONAL: return this.lang.tra("primary_operational_test");
      
      case ASSESSMENT.JUNIOR_SAMPLE: return this.lang.tra("junior_sample_test");
      case ASSESSMENT.JUNIOR_OPERATIONAL: return this.lang.tra("junior_operational_test");
     
      case ASSESSMENT.G9_OPERATIONAL: return this.lang.tra("g9_operational_test");
      //case ASSESSMENT.G9_SAMPLE: return 'Sample Assessment (G9 Math)'
      case ASSESSMENT.G9_SAMPLE: return this.lang.tra('lbl_sample_g9');
      
      case ASSESSMENT.OSSLT_OPERATIONAL: return this.lang.tra('lbl_osslt_test');
      //case ASSESSMENT.OSSLT_SAMPLE: return this.lang.tra('lbl_sample_assessment');
      case ASSESSMENT.OSSLT_SAMPLE: return this.lang.tra('lbl_sample_test_osslt');

      case ASSESSMENT.TCLE_OPERATIONAL : return 'TCLE OPERATIONAL' //cretae label 
      case ASSESSMENT.TCLE_SAMPLE : return 'TCLE SAMPLE' // create label

      case ASSESSMENT.TCN_OPERATIONAL : return 'TCN OPERATIONAL'
      case ASSESSMENT.TCN_SAMPLE : return 'TCN SAMPLE'

      case ASSESSMENT.SCIENCES8_OPERATIONAL : return 'SCIENCES8 OPERATIONAL'
      case ASSESSMENT.SCIENCES8_SAMPLE : return 'SCIENCES8 SAMPLE'

      case ASSESSMENT.MBED_SAMPLE : return 'MBED SAMPLE';
      
      case ASSESSMENT.ABED_SAMPLE : return this.lang.tra('assessment_abed_2');
      case ASSESSMENT.ABED_OPERATIONAL: return this.lang.tra('assessment_abed_1');

      case ASSESSMENT.SMCS_EN_G7 : return 'SMCS_EN_G7'
      
      case SCHEDULER.NOW: return this.lang.tra('lbl_now');
      case SCHEDULER.LATER: return this.lang.tra('lbl_later');
    }
  }

  setDateValidity($event){
    this.isDateValid.emit($event)
  }

  initPayloadSubscription(sessionRecord: Partial<ISession>) {
    const payload = {
      record: sessionRecord,
      data: this.savePayload?.payload?.data || {}
    };
    this.savePayload[this.saveProp] = payload;
    this.formEntriesList.forEach(entry => {
      if (Object.keys(payload.data).length > 0) {
        const prop = entry.valProp;
        const val = payload.data[prop];
        if (typeof val !== 'undefined') {
          payload.data[prop] = val;
          entry.formControlRef.setValue(val);
        }
      }

      if (sessionRecord || payload.data) {
        const prop = entry.valProp;
        const val = sessionRecord[prop];
        if (typeof val !== 'undefined') {
          payload.data[prop] = val;
        }
      }
      entry.formControlRef.valueChanges.subscribe(val => {
        payload.data[entry.valProp] = val;
      })
    })
  }

  showSessionA() {
    return !this.isSessionComplete(0);
  }

  showSessionB() {
    return !this.removesessionB && !this.isSessionComplete(1);
  }

  getCompletedSlug() {
    return this.isOsslt ? 'msg_all_students_comp_sa_osslt' : 'msg_all_students_comp_sa'
  }

  isLanguageVisibleForFIClass(): boolean {
    if(this.schoolDistFIOption === 'C' && this.isFIClass) {
      return false;
    }
    return true;
  }

  getSessionHeaderSlug(): string {
    if (this.whitelabelService.isABED()) {
      return "abed_edit_assessment_session";
    }

    return "g9_sample_session";
  }

  canEditDatetimePicker(type: DatePickerType): boolean {
    if (!this.isEditing && !this.sessions) {
      return true;
    }
    const currentSession = this.sessions ? this.sessions[0] : null;
    this.isFIClass = currentSession ? this.checkFIClass(currentSession.classroom_id) : false;
    if (!this.isActive) {
      return true;
    }
    // const currentSession = this.sessions[0];
    const isFutureDate = (startDate: string): boolean => {
      if (!startDate) {
        return true;
      }
      const processedDate = moment(startDate, this.lang.tra('pj_datefmt_sentence')).format('YYYY-MM-DD');
      const now = moment().format('YYYY-MM-DD');
      if (processedDate <= now) {
        return false;
      }
      return true;
      };
    switch(type) {
      case this.datePickerType.Language:
        if ((!currentSession.session_a.datetime_start && !currentSession.session_a.datetime_end) || isFutureDate(currentSession.session_a.datetime_start)) {
          return true;
        }
        break;
      case this.datePickerType.Math:
        if ((!currentSession.session_b.datetime_start && !currentSession.session_b.datetime_end) || isFutureDate(currentSession.session_b.datetime_start)) {
          return true;
        }
        break;
    }
    return false;
  }

  get datePickerType() {
    return DatePickerType;
  }

  get schoolDistFIOption():string {
    return this.g9DemoData.schoolDist[0].fi_option; 
  }
}
enum DatePickerType {
  Language = 0,
  Math = 1
}