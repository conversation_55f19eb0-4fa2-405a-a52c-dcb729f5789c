import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    test_window_ids: number[],
    days_back: number,
}

export const SQL_03B_AUTH_MONIT_RECENTLY_ALLOCATED: IExportQueryDef = {
  // If you want a time-based filter, you can make days_back optional
  requiredInputs: ['test_window_ids'],
  optionalInputs: [
    "days_back", 
    "auth_group_name",
  ],
  queryGen: (config: any) => `
    SELECT
      twtar.test_window_id
    , JSON_EXTRACT(tw.title, '$.en') AS title_en
    , twtar.type_slug
    , twtar.long_name
    , twtar.td_assigned_on AS allocated_on
    , a.email AS allocated_by
    , ts.signoff_slug
    , ts.comment
    FROM test_windows tw
    JOIN test_window_td_alloc_rules twtar
      ON twtar.test_window_id = tw.id
      AND twtar.type_slug NOT LIKE '%DO_NOT_USE%'
      AND twtar.type_slug NOT LIKE '%ABED_DEMO%'
    JOIN auths a
      ON a.uid = twtar.td_assigned_by_uid
    LEFT JOIN twtdar_signoffs ts
      ON ts.twtar_id = twtar.id
      AND ts.signoff_slug = 'form_design_keys_sys'
      AND ts.is_revoked = 0
    WHERE DATE(twtar.td_assigned_on) >= DATE(DATE_ADD(NOW(), INTERVAL -${config.days_back || 7} DAY))
        AND twtar.test_window_id IN (:test_window_ids)
    GROUP BY twtar.id
    ORDER BY twtar.td_assigned_on DESC
  `
};
