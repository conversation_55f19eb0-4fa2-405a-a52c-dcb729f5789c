import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiDistAdminRoutingModule } from './ui-dist-admin-routing.module';
import { UiTestadminModule } from '../ui-testadmin/ui-testadmin.module';
import { SbBoardComponent } from './sb-board/sb-board.component';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { SbTechReadinessComponent } from './sb-tech-readiness/sb-tech-readiness.component';
import { SbItContactComponent } from './sb-it-contact/sb-it-contact.component';
import { ViewTrackingComponent } from './view-tracking/view-tracking.component';
import { SbSessionsComponent } from './sb-sessions/sb-sessions.component';
import { SbMonitoringComponent } from './sb-monitoring/sb-monitoring.component';
import { ViewDistrictadminDashboardComponent } from './view-districtadmin-dashboard/view-districtadmin-dashboard.component';
import { UiMinistryadminModule } from '../ui-ministryadmin/ui-ministryadmin.module';
import { DaAccountsStudentsComponent } from './da-accounts-students/da-accounts-students.component';
import { MatRadioModule } from '@angular/material/radio';
import { UiTestctrlModule } from '../ui-testctrl/ui-testctrl.module'; // Import the module instead
import { UiTeacherModule } from '../ui-teacher/ui-teacher.module';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { DaAccountsComponent } from './da-accounts/da-accounts.component';
import { DaStudentDetailsComponent } from './da-student-details/da-student-details.component';
import { DaResultsTabComponent } from './da-student-details/da-results-tab/da-results-tab.component';
import { DaSpecialFormatsTabComponent } from './da-student-details/da-special-formats-tab/da-special-formats-tab.component';
import { DaStudentInfoTabComponent } from './da-student-details/da-student-info-tab/da-student-info-tab.component';
import { DaAssessmentsComponent } from './da-assessments/da-assessments.component';
import { DaCurrentAssessmentsComponent } from './da-current-assessments/da-current-assessments.component';
import { DaFinalResultsComponent } from './da-final-results/da-final-results.component';
import { DaIndividualReportsComponent } from './da-final-results/da-individual-reports/da-individual-reports.component';
import { DaAdminSessionComponent } from './da-admin-session/da-admin-session.component';
import { UiSchooladminModule } from '../ui-schooladmin/ui-schooladmin.module';
import { DaReportsComponent } from './da-reports/da-reports.component';
import { DaTestWindowFilterComponent } from './da-test-window-filter/da-test-window-filter.component';
import { AgGridModule } from 'ag-grid-angular';
import { UiChartsModule } from '../ui-charts/ui-charts.module';
import { MatFormFieldModule } from '@angular/material/form-field'
import { MatSelectModule } from '@angular/material/select';
import { PanelSbSTwStatementComponent } from './panel-sb-s-tw-statement/panel-sb-s-tw-statement.component';
import { PanelSbSAccountsComponent } from './panel-sb-s-accounts/panel-sb-s-accounts.component';
import { PanelSbSdAccountsComponent } from './panel-sb-sd-accounts/panel-sb-sd-accounts.component';
import { SbModalAdminAccountsComponent } from './sb-modal-admin-accounts/sb-modal-admin-accounts.component';
import { SbWidgetFormEntryComponent } from './sb-widget-form-entry/sb-widget-form-entry.component';
import { SbWidgetDateEntryComponent } from './sb-widget-date-entry/sb-widget-date-entry.component'

@NgModule({
  declarations: [
    DaAdminSessionComponent,
    DaCurrentAssessmentsComponent,
    DaAssessmentsComponent,
    SbBoardComponent,
    SbTechReadinessComponent,
    SbItContactComponent,
    ViewTrackingComponent,
    SbSessionsComponent,
    SbMonitoringComponent,
    ViewDistrictadminDashboardComponent,
    DaAccountsStudentsComponent,
    DaAccountsComponent,
    DaStudentDetailsComponent,
    DaResultsTabComponent,
    DaSpecialFormatsTabComponent,
    DaStudentInfoTabComponent,
    DaFinalResultsComponent,
    DaIndividualReportsComponent,
    DaReportsComponent,
    DaTestWindowFilterComponent,
    PanelSbSTwStatementComponent,
    PanelSbSAccountsComponent,
    PanelSbSdAccountsComponent,
    SbModalAdminAccountsComponent,
    SbWidgetFormEntryComponent,
    SbWidgetDateEntryComponent
  ],
  imports: [
    UiPartialModule,
    ReactiveFormsModule,
    FormsModule,
    UiTeacherModule,
    MatAutocompleteModule,
    MatRadioModule,
    CommonModule,
    UiPartialModule,
    UiDistAdminRoutingModule,
    UiTestadminModule,
    UiTestctrlModule,
    FormsModule,
    ReactiveFormsModule,
    UiMinistryadminModule,
    MatRadioModule,
    UiSchooladminModule,
    AgGridModule,
    UiChartsModule,
    MatFormFieldModule,
    MatSelectModule,
  ],
  exports: [
    SbSessionsComponent,
  ]
})
export class UiDistAdminModule { }
