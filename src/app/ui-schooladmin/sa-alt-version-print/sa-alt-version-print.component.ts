import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment-timezone';
import { LangService } from 'src/app/core/lang.service';
import { IStudentAccount } from '../data/types';
import { MySchoolService, ClassFilterId } from '../my-school.service';
import * as _ from 'lodash';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AuthService, getFrontendDomain } from '../../api/auth.service';
import { FormControl } from '@angular/forms';
import { G9DemoDataService } from '../g9-demo-data.service';
import { downloadFromExportData, downloadCSVFromExportData, IExportColumn } from 'src/app/ui-testctrl/tc-table-common/tc-table-common.component';
import { formatDate } from '@angular/common';
import { RoutesService } from 'src/app/api/routes.service';
import { OSSLTResultColumns } from '../../ui-dist-admin/sb-board/sb-board.component';
import { DomSanitizer } from '@angular/platform-browser';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import { LoginGuardService } from '../../api/login-guard.service';
import { allowBypassDomains } from '../../domain/qa-bypass'
import { ColumnApi, GridApi } from 'ag-grid-community';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { ALT_VERSION_REQUEST_STATUS, REQUEST_STATUS_SLUGS } from 'src/app/ui-alt-version-ctrl/alt-version-ctrl-req-table/types';
import {renderYesNo} from './../../core/util/render'
@Component({
  selector: 'sa-alt-version-print',
  templateUrl: './sa-alt-version-print.component.html',
  styleUrls: ['./sa-alt-version-print.component.scss']
})

export class SaAltVersionPrintComponent implements OnInit {

  @Input() set config(cnf: {rowData: any}) {
    this.rowData = cnf.rowData;
  }

  currentClassFilter;

  constructor(
    public lang: LangService,
    public mySchool: MySchoolService,
    public whiteLabel: WhitelabelService
  ) {}

  rowData: any;
  isSASNSchool = this.mySchool.isSASNLogin();

  ngOnInit(): void {
  }

  printRequest() {
    const printBody: string[] = [];
    const css = `
    @media print {
      body {
        padding: 0.5rem;
      }
      div {
        width: 100%;
      }
      table {
          border-collapse: collapse;
          border-spacing: 0;
          width: 100%;
        }
        th {
          border: 1px solid #000000 !important;
          background-color: #d3d3d3 !important;
          color: #000000  !important;
          text-align: center;
          -webkit-print-color-adjust: exact; 
          padding: 0.5em 0.75em;
          vertical-align: top;
        }
        th.limit-width {
          text-align: left;
          max-width: 5rem;
        }
        td {
            border: 1px solid #000000 !important;
            -webkit-print-color-adjust: exact; 
            padding: 0.5em 0.75em;
            vertical-align: top;
        }
        .no-padding {
            padding: 0;
        }
        .students-container-flex {
          display: flex;
        }
        .students-container-flex:last-child .dynamic-col {
          border-bottom: none;
        }
        .student-header {
            font-weight: bold;
            text-align: left;
        }
        .dynamic-col {
            padding: 0.5rem;
            flex: 1;
            text-align: left;
            border-bottom: 1px solid black;
            -webkit-print-color-adjust: exact; 
        }
      
        .dynamic-col:first-child {
          border-right: 1px solid #000000;
          -webkit-print-color-adjust: exact; 
        }
        .dynamic-col:first-child {
          border-right: 1px solid #000000;
          -webkit-print-color-adjust: exact; 
        }
        .pagebreak {
          clear: both;
          page-break-after: always;
        }
    }
      `;
    const invoiceTitle = document.getElementById('invoice-title');
    const id = `print-container-1`;
    const printContent = document.getElementById(id);
    if (printContent === null)
      throw `${id} not found!`;
    printBody.push(`<div><br/></div>${invoiceTitle.innerHTML}<div><br/></div>${printContent.innerHTML}`);

    const WindowPrt = window.open('', '', 'left=0,top=0,width=900,height=900,toolbar=0,scrollbars=0,status=0');
    WindowPrt.document.write('<html><head>');
    WindowPrt.document.write(`<style>${css} .style-title{margin-left:2.75rem;} .style-margin{margin-left:1.25rem;} .style-response{margin-left:3.5rem;}</style>`);
    WindowPrt.document.write('</head><body>');
    WindowPrt.document.write(printBody.join('<div class="pagebreak"> </div>'));
    WindowPrt.document.write('</body></html>');
    WindowPrt.document.close();
    WindowPrt.focus();
    WindowPrt.print();
    WindowPrt.close();
  }

  isEnglish(){
    return (this.lang.c() === 'en')
  }

  getAssessmentName() {
    const assessmentName = JSON.parse(this.rowData.assessment_name)
    return this.isEnglish() ? assessmentName.en : assessmentName.fr
  }

  getLanguage() {
    return this.rowData.lang === 'en' ? 'English' : 'French'
  }

  parseDate(date: string) {
    if (!date) {
      return null;
    }
    
    const parsedDate = new Date(Date.parse(date));
    return parsedDate.toString();
  }

  getRequestSlug(status): string {
    let slug = "";
    switch(status) {
      case ALT_VERSION_REQUEST_STATUS.Pending:
        slug = REQUEST_STATUS_SLUGS.Pending
        break;
      case ALT_VERSION_REQUEST_STATUS.Approved: 
        slug = REQUEST_STATUS_SLUGS.Approved
        break;
      case ALT_VERSION_REQUEST_STATUS.Canceled: 
        slug = REQUEST_STATUS_SLUGS.Canceled
        break;
      case ALT_VERSION_REQUEST_STATUS.Rejected: 
        slug = REQUEST_STATUS_SLUGS.Rejected
        break;
      case ALT_VERSION_REQUEST_STATUS.Shipment_Send: 
        slug = REQUEST_STATUS_SLUGS.Shipment_Send
        break;
      case ALT_VERSION_REQUEST_STATUS.Operational_Link_Send: 
        slug = REQUEST_STATUS_SLUGS.Operational_Link_Send
        break;
    }

    return this.lang.tra(slug);
  }

  renderRequestedFormats(requested_formats:string, lang:string){
    return requested_formats.split(',').map(slug => this.lang.tra(slug, lang)).join(', ')
  }

  isNBED() {
    return this.whiteLabel.getSiteFlag('IS_NBED');
  }
  isABED() {
    return this.whiteLabel.getSiteFlag('IS_ABED');
  }
  isMBED() {
    return this.whiteLabel.getSiteFlag('IS_MBED');
  }
  isEQAO() {
    return this.whiteLabel.getSiteFlag('IS_EQAO');
  }
  


  getStudentIdSlug(){
    if (this.isABED()){
      return 'lbl_student_asn'
    }
    return 'oen_alt_version';
  }

  getSchlForeignIdSlug(){
    if (this.isABED()){
      return 'sa_sr_school_code'
    }
    return 'sch_mident_alt_version';
  }

  renderYesNo(input){
    renderYesNo(this.lang, input)
  }
}
