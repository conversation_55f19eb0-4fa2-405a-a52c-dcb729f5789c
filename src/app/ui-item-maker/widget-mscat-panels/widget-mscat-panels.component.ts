import { Component, OnInit, Input } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';

import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';


@Component({
  selector: 'widget-mscat-panels',
  templateUrl: './widget-mscat-panels.component.html',
  styleUrls: ['./widget-mscat-panels.component.scss']
})
export class WidgetMscatPanelsComponent implements OnInit {


  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl

  public util = new ItemBankUtilCtrl();
  inspectedPanel:any;

  constructor(
    private editingDisabled: EditingDisabledService,

    ) { }

  inspectPanel(panel:any){
    if (panel === this.inspectedPanel){
      this.inspectedPanel = null;
    }
    else {
      this.inspectedPanel = panel
    }
  }

  itemStats = [];
  _isItemExposureActive;
  async toggleItemExposure(){
    if (this.isItemExposureActive()){
      this._isItemExposureActive = null;
      return;
    }
    const items = [];
    const itemStatRef = new Map();
    let numPanels = 0;
    const panels = this.frameworkCtrl.asmtFmrk.panels || [];
    const moduleIdToStageWeight = this.computeModuleWeights()
    panels.forEach(panel => {
      if (panel.isDisabled){
        return
      }
      numPanels ++
      panel.modules.forEach(module => {
        const moduleWeight = moduleIdToStageWeight[''+module.moduleId]
        const questionIds = this.getModuleItemIdArr(module);
        questionIds.forEach(item_id => {
          let item = itemStatRef.get(item_id);
          if (!item){
            const question = this.frameworkCtrl.itemBankCtrl.getQuestionById(item_id)
            item = {
              item_id, 
              label: question.label,
              panels:[], 
              panelModules:[],
              panelPresence: 0,
              estimatedExposure: 0,
            }
            itemStatRef.set(item_id, item);
            items.push(item)
          }
          if (!item.panels.includes(panel.id)){
            item.panels.push(panel.id)
          }
          item.panelModules.push(`${panel.id}:M${module.moduleId}`);
          item.panelPresence += moduleWeight
        })
      })
    });
    items.forEach(item => {
      item.estimatedExposure = item.panelPresence / numPanels
    })
    this.itemStats = items
    this._isItemExposureActive = true
  }

  
  formatPerc(num:number){
    return ((num || 0)*100).toFixed(2)+'%'
  }

  isItemExposureActive(){
    return this._isItemExposureActive
  };

  downloadItemExposureCSV(){

  }


  isSplitView(){
    return !!this.inspectedPanel;
  }

  moduleExpandMap = new Map()
  toggleModuleExpand(module){
    let isExpanded = this.isModuleExpanded(module)
    this.moduleExpandMap.set(module, !isExpanded);
  }
  isModuleExpanded(module){
    return !!this.moduleExpandMap.get(module);
  }

  computeModuleStages(){
    const moduleIdToStage = {};
    this.frameworkCtrl.asmtFmrk.panelAssembly.allModules.forEach(module => {
      moduleIdToStage[''+module.id] = module.stageNumber
    });
    return moduleIdToStage;
  }

  computeModuleWeights(){
    const stageSplits = {};
    const moduleIdToStageWeight = {};
    this.frameworkCtrl.asmtFmrk.panelAssembly.allModules.forEach(module => {
      const stage = ''+module.stageNumber
      stageSplits[stage] = (stageSplits[stage] || 0) + 1
    });
    this.frameworkCtrl.asmtFmrk.panelAssembly.allModules.forEach(module => {
      moduleIdToStageWeight[''+module.id] = 1/stageSplits[''+module.stageNumber]
    });
    return moduleIdToStageWeight;
  }

  promptItemFromLabel(panel?:any, module?:any){
    const itemLabel = prompt('Replace with which item? (paste in the label)')
    const item = this.frameworkCtrl.itemBankCtrl.getQuestionByLabel(itemLabel)
    if (!item){
      alert('The label you provided does not exist within this item bank.')
      throw new Error();
    }
    if (panel && module){
      const moduleToStages = this.computeModuleStages();
      for (let _module of panel.modules){
        const isActiveModule = (_module.moduleId === module.moduleId)
        const isActiveStage = (moduleToStages[''+_module.moduleId] === moduleToStages[''+module.moduleId])
        if (!isActiveStage || isActiveModule){
          const itemIds = this.getModuleItemIdArr(_module);
          if (itemIds.includes(item.id)){
            if (isActiveModule){
              alert('The item you selected is already used in this module.')
            }
            else{
              alert('The item you selected is already used in this panel (in a different stage).')
            }
            throw new Error();
          }
        }
      }
    }
    // check for presence of item
    return {itemLabel, item}
  }

  swapModuleItem(panel, module, index){
    const {itemLabel, item} = this.promptItemFromLabel(panel, module)
    module.itemLabels[index] = itemLabel;
    module.__cached_itemIds[index] = item.id;
  }

  removeModuleItem(module, index){
    module.itemLabels.splice(index, 1);
    module.__cached_itemIds.splice(index, 1);
  }

  saveUpdatedPanel(data){
    console.log('saveUpdatedPanel', data);
  }

  addItemToModule(panel, module){
    const {itemLabel, item} = this.promptItemFromLabel(panel, module)
    module.itemLabels.push(itemLabel);
    module.__cached_itemIds.push(item.id);
  }

  getModuleItemIdArr(module){
    if (!module.__cached_itemIds){
      const __cached_itemIds = [];
      for (let itemLabel of module.itemLabels){
        const item = this.frameworkCtrl.itemBankCtrl.getQuestionByLabel(itemLabel)
        let itemID = -1
        if (item){
          itemID = item.id
        }
        else { 
          module.isMissingItemIds = true
          console.error('No item for label', itemLabel)
        }
        __cached_itemIds.push(itemID);
      }
      module.__cached_itemIds = __cached_itemIds
    }
    return module.__cached_itemIds
  }

  getModuleItemId(module, index){
    const itemIds = this.getModuleItemIdArr(module);
    return itemIds[index]
  }

  duplicateInspectedPanel(){
    const panelConfig = JSON.stringify(this.inspectedPanel)
    const panelId = prompt('Please provide a panel ID', 'mscat-');
    if (panelId){
      const newPanel = JSON.parse(panelConfig)
      newPanel.id = panelId
      this.frameworkCtrl.asmtFmrk.panels.push(newPanel)
      this.inspectPanel(newPanel)
    }
  }
  
  isItemExclRouting(module, index){
    const itemId = this.getModuleItemId(module, index);
    if (module.routingExclusions){
      return !!module.routingExclusions[+itemId];
    }
    return false;
  }

  toggleItemExclRouting(module, index){
    const itemId = this.getModuleItemId(module, index);
    if (!module.routingExclusions){
      module.routingExclusions = {};
    }
    module.routingExclusions[+itemId] = !module.routingExclusions[+itemId]
  }

  ngOnInit(): void {
  }
  isReadOnly = () => this.editingDisabled.isReadOnly(true);

}
