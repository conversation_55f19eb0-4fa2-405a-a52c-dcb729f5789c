.text-area-middle * {
    vertical-align: middle;
}

.flex-gap {
    display: inline-flex;
    gap: 1em;
    margin-top: 1em;
}

.hasBorder {
    border-style: solid;
    border-width: 0.1em;
}

.content-block {
  margin-bottom: 20px;
}

.content-label {
  font-weight: bold;
  margin-bottom: 1em;
}

.size-minimize {
  font-size: 70%;

  .panel-title {
    font-size: 13px;
  }
  .panel-subtitle {
    font-size: 12px;
    color: gray;
  }
}

.red-diff {
  color: #f71b1b;
}

.green-diff {
  color: #32a632;
}

.diff-listing:not(:last-child) {
  margin-bottom: 1em;
}

.category-label {
  margin-left: 1em;
}
