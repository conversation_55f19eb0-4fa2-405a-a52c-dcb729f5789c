<div class="simple-form-header" *ngIf="!isSimplified || !hiddenConfigs?.hideStyleProfile">
  Interactive Diagram
</div>

<div class="control">
  <div class="select" *ngIf="!isSimplified">
    <select [formControl]="constructionTypeForm">
      <option *ngFor="let option of constructionTypes" [value]="option.id"><tra [slug]="option.caption"></tra></option>
    </select>
  </div>
  <div class="style-profile-container" *ngIf="!hiddenConfigs?.hideStyleProfile">
    Add style:
    <dropdown-search [list]="styleProfileList" (onSelect)="addStyleProfile($event)"></dropdown-search>
    <div class="style-profile-list-container">
      <div *ngFor="let styleId of element.styleProfiles" class="style-profile-capsule">
        <span class="style-profile-caption">{{ getStyleProfileCaption(styleId) }}</span>
        <div class="style-profile-remove" (click)="removeStyleProfile(styleId)">×</div>
      </div>
    </div>
  </div>
</div>

<hr *ngIf="!hiddenConfigs?.hideTypeDropdown || !hiddenConfigs?.hideStyleProfile">

<div [ngSwitch]="element.constructionType">
  <div *ngSwitchCase="DgIntConstructionType.DND_TABLE"><dg-int-config-dnd-table [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-table></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_INLINE_2"><dg-int-config-dnd-inline-2 [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-inline-2></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_CLOZE_MATH"><dg-int-config-dnd-cloze-math [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-cloze-math></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_GROUP"><dg-int-config-dnd-group [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-group></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_SORTING"><dg-int-config-dnd-sorting [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-sorting></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_TALLY"><dg-int-config-dnd-tally [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-tally></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_NUMBERLINE"><dg-int-config-dnd-numberline [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-numberline></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_FREEFORM"><dg-int-config-dnd-freeform [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-freeform></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_VENN"><dg-int-config-dnd-venn [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-venn></div>
  <div *ngSwitchCase="DgIntConstructionType.MCQ_HOTSPOT"><dg-int-config-mcq-hotspot [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-mcq-hotspot></div>
  <div *ngSwitchCase="DgIntConstructionType.DND_INLINE"><dg-int-config-dnd-inline [parentElement]="element" [element]="element.constructionElement" [hiddenConfigs]="hiddenConfigs"></dg-int-config-dnd-inline></div>
</div>
