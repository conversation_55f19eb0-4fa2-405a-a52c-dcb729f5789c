export const parseCsv = (csvText:string) => {
    const rows = csvText
        .trim()
        .split("\n")
        .map(row => 
            row.split(",").map(cell => 
                cell.trim().replace(/^"(.*)"$/, "$1") // Remove external quotes
            )
        );
    const data = []
    let header;
    for (let row of rows){
        if (!header){
            header = rows[0]
        }
        else {
            const record = {}
            for (let i=0; i<header.length; i++){
                const prop = header[i]
                record[prop] = row[i]
            }
            data.push(record)
            
        }
    }
    return data
}