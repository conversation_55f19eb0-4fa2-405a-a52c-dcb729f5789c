import { Component, Input, Output, EventEmitter, OnInit, AfterViewInit, ViewChild, ElementRef } from '@angular/core';
import { FormControl } from '@angular/forms';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { 
  DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE as ELEMENT_TYPE, generateAdvancedHomeLayoutDefaultElement, renderAdvancedHomeDiagramLayout,
} from 'src/app/ui-testrunner/element-render-interactive-diagram/advanced-home-layout';
import { DgLib, DiagramaticsVersion, loadDiagramaticsLibrary } from 'src/assets/lib/diagramatics/config';
import { IDgIntHomeConfig } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { advancedHomeLayoutPreviewRendererVersion } from 'src/app/ui-testrunner/element-render-interactive-diagram/renderer';

@Component({
  selector: 'dg-int-dnd-advanced-home-layout-editor',
  templateUrl: './dg-int-dnd-advanced-home-layout-editor.component.html',
  styleUrls: ['./dg-int-dnd-advanced-home-layout-editor.component.scss']
})
export class DgIntDndAdvancedHomeLayoutEditorComponent implements OnInit, AfterViewInit {
  @ViewChild('svgElement') svgElementRef: ElementRef;
  @Input() homeConfig: IDgIntHomeConfig;
  elementTypes: ELEMENT_TYPE; 
  
  elementTypeCaption = {
    [ELEMENT_TYPE.VERTICAL_CONTAINER]: 'Vertical Container',
    [ELEMENT_TYPE.GRID_CONTAINER]: 'Grid Container',
    [ELEMENT_TYPE.TARGET_ROW]: 'Target Row',
    [ELEMENT_TYPE.TEXT_ROW]: 'Text Row',
    [ELEMENT_TYPE.LEFT_LABEL]: 'Left Label',
  }
  gridContainerElementList = [
    ELEMENT_TYPE.TEXT_ROW,
    ELEMENT_TYPE.LEFT_LABEL,
    ELEMENT_TYPE.TARGET_ROW,
    ].map(id => {
      return {id, caption: this.elementTypeCaption[id]}
    })
  
  constructor(
  ) { }
  
  ngOnInit() {
  }
  ngAfterViewInit(): void {
    this.renderConstruction();
  }
  
  rerender(){
    this.renderConstruction();
  }
  renderConstruction( ) : void {
    if (!this.homeConfig?.advancedLayoutConfig || !this.svgElementRef) return;
    const svgElement = this.svgElementRef.nativeElement;
    if (!svgElement) return;
    svgElement.innerHTML = '';
    
    loadDiagramaticsLibrary(advancedHomeLayoutPreviewRendererVersion).then(dg => {
      renderAdvancedHomeDiagramLayout(dg as DgLib, this.homeConfig.advancedLayoutConfig, svgElement);
    });
  }
  
  trackByFn(index: number, item: any) {
      return index;
  }
  
  addElementFromType(type: ELEMENT_TYPE, list: any[]) {
    const element = generateAdvancedHomeLayoutDefaultElement(type);
    this.addElement(element, list);
  }
  addEmptyString(list: any[]) {
    list.push('');
    this.rerender();
  }
  addElement(element: any, list: any[]) {
    list.push(element);
    this.rerender();
  }
  removeElement(element: any, list: any[]) {
    const index = list.indexOf(element);
    if (index < 0) return;
    const confirm = window.confirm('Are you sure you want to delete this element?');
    if (!confirm) return;
    list.splice(index, 1);
    this.rerender();
  }
  
  drop(event: CdkDragDrop<any[]>, contents: any[]) {
    moveItemInArray(contents, event.previousIndex, event.currentIndex);
    this.rerender()
  }
  
}
