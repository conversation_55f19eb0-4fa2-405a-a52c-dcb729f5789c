@import '../../element-config-interactive-diagram.component.scss';

.target-name-input-label {
  max-width: 10em;
}
.target-group-unused-label {
  max-width: 4em;
}

.target-option-textarea {
  display: inline-block;
}

.target-index {
  margin-top: 0.5em;
  margin-right: 1em;
}

.target-form-container {
  display: flex;
  align-items: flex-start;
}

.target-form-inner-container {
  flex-grow: 1;
}

.layout-config-container {
  display: grid;
  grid-template-columns: auto auto;
  gap: 0.5em;
  align-items: center;
}
