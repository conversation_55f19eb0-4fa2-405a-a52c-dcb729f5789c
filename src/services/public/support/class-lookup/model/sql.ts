import { dbEscapeString } from "../../../../../util/db-raw"

export const SQL_SELECT_CLASS_LOOKUP = async (config:{schoolCode?:string, classNamePartial?:string, classAccessCodePartial?:string}) => ` /* SQL_SELECT_CLASS_LOOKUP */
select s.foreign_id s_code
     , s.name s_name
     , sc.name sc_name
     , sc.access_code sc_access_code
	 , sc.is_active sc_is_active
	 , sc.created_on sc_created_on
	 , u_create.contact_email sc_created_by
	 , sc.is_archived 
	 , sc.deactivated_on sc_deactivated_on
	 , u_deactiv.contact_email sc_deactivated_by
	 , sc.is_placeholder 
	 , scts.test_session_id 
     , ts.name_custom ts_name
	 , ts.created_on ts_created_on
	 , ts.is_closed  ts_is_closed
	 , ts.is_cancelled ts_is_cancelled
	 , ts.closed_on ts_closed_on
	 , count(distinct ta.id) n_attempts
	 , ts.date_time_start ts_date_time_start
	 , group_concat(distinct twtar.test_date_start) twtar_date_time_start
	 , twtar.type_slug 
	 , group_concat(distinct twtar.form_code) twtar_form_code
	 , group_concat(distinct twtar.is_active) twtar_is_active
	 , twtar.is_sample  twtar_is_sample 
from school_classes sc 
join schools s 
	on s.group_id = sc.schl_group_id 
join school_semesters ss 
	on ss.id = sc.semester_id 
left join users u_create
	on u_create.id = sc.created_by_uid 
left join users u_deactiv
	on u_deactiv.id = sc.deactivated_by_uid
left join school_class_test_sessions scts 
	on scts.school_class_id = sc.id
left join test_sessions ts 
	on ts.id = scts.test_session_id 
left join test_window_td_alloc_rules twtar 
	on twtar.type_slug = scts.slug 
	and twtar.test_window_id = ss.test_window_id 
left join test_attempts ta 
	on ta.test_session_id = scts.test_session_id 
	and ta.started_on is not null
where 1=1
  ${!!config.classAccessCodePartial ? ` and sc.access_code like ${await dbEscapeString(config.classAccessCodePartial, true)}` : '' }
  ${!!config.classNamePartial       ? ` and sc.name like ${await dbEscapeString(config.classNamePartial, true)}` : '' }
  ${!!config.schoolCode             ? ` and s.foreign_id = :schoolCode` : '' }
group by sc.id
      , scts.test_session_id 
      , twtar.type_slug 
order by s.foreign_id
       , sc.id desc 
       , scts.test_session_id desc 
`



