export interface MaterialLink {
    name: string,
    link: string,
}
export interface MaterialList {
    title: string,
    subtitle?: string,
    expanded: boolean,
    rows: string[],
    columns: string[],
    columnAvailable: boolean[],
    data: MaterialLink[][][],
}

export const getGeneralFsaSecureMaterialList = (): MaterialList => ({
    title: 'sa_am_header_1',
    expanded: false,
    rows: ['sa_am_material_en', 'sa_am_material_fr'],
    columns: ['sa_am_general', 'sa_am_score', 'sa_am_secure_admin_info'],
    columnAvailable: [true, true, true],
    data: [
        [
            [{
                name: 'Administration Guide',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/2021-22 FSA Admin Manual Online/1629302065239/2021-22 FSA Admin Manual Online.pdf',

            }, {
                name: 'Information for Parents Brochure',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/en-fsa-parent-brochure(downloaded Aug2021)/1629212121184/en-fsa-parent-brochure(downloaded Aug2021).pdf',

            }, {
                name: 'Student Learning Assessment Order',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/m60_94__2019_/1629294155321/m60_94__2019_.pdf',

            }, {
                name: 'Foundation Skills Assessment (FSA) Samples',
                link: '/#/en/bced-landing/fsa/sample',
            }],

            [{
                name: 'Scoring Guide',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/FSA-scoring-guide-en/1629209120100/FSA-scoring-guide-en.pdf',

            }, {
                name: 'Score Entry, System Guide',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/2021-22_FSA_Score_Entry_System_ Guide/1629311149255/2021-22_FSA_Score_Entry_System_ Guide.pdf',

            }, {
                name: 'Information for Organizing Scoring',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/info-for-organizing-scoring (2) (1)/1629302561916/info-for-organizing-scoring (2) (1).pdf',

            },],

            [{
                name: "Password Information Sheet",
                link: "/#/en/school-admin/bc-fsa/assessment_setup1/administration_session",
            }, {
                name: "List of Enrolled Students",
                link: "/#/en/school-admin/bc-fsa/students/enrolled"
            }],
        ],

        [
            [{
                name: 'Guide d’Organisation',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/2021-22 FSA Admin Manual Online/1629302065239/2021-22 FSA Admin Manual Online.pdf',

            }, {
                name: 'Information aux parents Brochure',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/fr-fsa-parent-brochure(downloaded Aug2021)/1629212992678/fr-fsa-parent-brochure(downloaded Aug2021).pdf',

            }, {
                name: 'Évaluations types: Évaluation des habiletés de base (ÉHB)',
                link: '/#/fr/bced-landing/fsa/sample',
            }],

            [{
                name: 'Guide de notation',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/fsa-scoring-guide-fr/1629209152913/fsa-scoring-guide-fr.pdf',

            }, {
                name: 'Entrée de résultat, guide du système',
                // need FR
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/2021-22_FSA_Score_Entry_System_ Guide/1629311149255/2021-22_FSA_Score_Entry_System_ Guide.pdf',

            }],

            [{
                name: "Fiche d'information sur le mot de passe",
                link: "/#/fr/school-admin/bc-fsa/assessment_setup1/administration_session",
            }, {
                name: "Liste d'élèves inscrits",
                link: "/#/en/school-admin/bc-fsa/students/enrolled"
            }],
        ]
    ],
})

export const getG4SecureMaterialList = (): MaterialList => ({
    title: 'sa_am_header_2',
    subtitle: 'sa_am_header_2_sub',
    expanded: false,
    rows: ['sa_am_material_en', 'sa_am_material_fr'],
    columns: ['sa_am_secure_a_links', 'sa_am_scoring_links', 'sa_am_secure_admin_info', 'sa_am_item'],
    columnAvailable: [true, true, true, true],
    data: [
        [
            [
                {
                    name: 'Collaboration Activity',
                    link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/FSA--grade-4-collaboration-activities-sample/1629214008557/FSA--grade-4-collaboration-activities-sample.pdf',
                }, 
                {
                    name: 'Numeracy SR: Online Link',
                    link: '/#/en/test-auth/public-test-runner/504?bcedCopyrightNotice=1',

                }, 
                {
                    name: 'Literacy SR: Online Link',
                    link: '/#/en/test-auth/public-test-runner/501?bcedCopyrightNotice=1',
                },
                {
                    name: 'FSA Secure Login for Students',
                    link: '/#/en/bced-landing/fsa/secure',
                },
        ],

            [{
                name: 'Grade 4 FSA specification',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/g4-2019-fsa_description_and_specs/1629215976369/g4-2019-fsa_description_and_specs.pdf',
            }, 
        ],

            [{
                name: "Password Information Sheet",
                link: "/#/en/school-admin/bc-fsa/assessment_setup1/administration_session",
            }, {
                name: "List of Enrolled Students",
                link: "/#/en/school-admin/bc-fsa/students/enrolled"
            }],

            [{
                name: 'Grade 4 Item Descriptors ',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/FSA 2021_22 G4 Item Descriptors_FINAL-(v2)/1629216386760/FSA 2021_22 G4 Item Descriptors_FINAL-(v2).pdf',
            }],
        ],

        [
            [{
                name: 'Activité de collaboration',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/grade-4-collaboration-activities-sample-fr/1629214178791/grade-4-collaboration-activities-sample-fr.pdf',
            }, {
                name: 'ÉHB - session sécurisée - accès élève',
                link: '/#/fr/bced-landing/ehb/secure',
            },],

            [{
                name: 'G4 Évaluation des habiletés de base, description et spécifications (PDF)',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/g4-2019-fr-fsa-description-specification/1629215995786/g4-2019-fr-fsa-description-specification.pdf',
            }],

            [{
                name: "Fiche d'information sur le mot de passe",
                link: "/#/fr/school-admin/bc-fsa/assessment_setup1/administration_session",
            }, {
                name: "Liste d'élèves inscrits",
                link: "/#/en/school-admin/bc-fsa/students/enrolled"
            }],

            [{
                name: "Descripteurs d'items de 4e année",
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/FSA 2021_22 G4 Item FRENCH Descriptors_FINAL July 21 (v2)/1629216412537/FSA 2021_22 G4 Item FRENCH Descriptors_FINAL July 21 (v2).pdf',
            }],
        ]
    ]
});

export const getG7SecureMaterialList = (): MaterialList => ({
    title: 'sa_am_header_3',
    subtitle: 'sa_am_header_3_sub',
    expanded: false,
    rows: ['sa_am_material_en', 'sa_am_material_fr'],
    columns: ['sa_am_secure_a_links', 'sa_am_scoring_links', 'sa_am_secure_admin_info', 'sa_am_item'],
    columnAvailable: [true, true, true, true],
    data: [
        [
            [{
                name: 'Collaboration Activity',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/grade-7-collaboration-activities-sample/1629216838316/grade-7-collaboration-activities-sample.pdf',

            }, {
                name: 'Numeracy SR: Online Link',
                link: '/#/en/test-auth/public-test-runner/505?bcedCopyrightNotice=1',

            }, {
                name: 'Literacy SR: Online Link',
                link: '/#/en/test-auth/public-test-runner/503?bcedCopyrightNotice=1',
            },
            {
                name: 'FSA Secure Login for Students',
                link: '/#/en/bced-landing/fsa/secure',
            },
        ],

            [{
                name: 'Grade 7 FSA specification',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/g7-2019-fsa_description_and_specs/1629218141170/g7-2019-fsa_description_and_specs.pdf',
            }
        ],

            [{
                name: "Password Information Sheet",
                link: "/#/en/school-admin/bc-fsa/assessment_setup1/administration_session",
            }, {
                name: "List of Enrolled Students",
                link: "/#/en/school-admin/bc-fsa/students/enrolled"
            }],

            [{
                name: 'Grade 7 Item Descriptors',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/FSA 2021_22 G7 Item Descriptors_FINAL (v2)/1629217876688/FSA 2021_22 G7 Item Descriptors_FINAL (v2).pdf',
            }],
        ],

        [
            [{
                name: 'Activité de collaboration',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/grade-7-collaboration-activities-sample-fr/1629216866797/grade-7-collaboration-activities-sample-fr.pdf',
            },
            {
                name: 'ÉHB - session sécurisée - accès élève',
                link: '/#/fr/bced-landing/ehb/secure',
            },
        ],

            [{
                name: 'G7 Évaluation des habiletés de base, description et spécifications (PDF)',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/g7-2019-fr-fsa_description_and_specs/1629218159762/g7-2019-fr-fsa_description_and_specs.pdf',
            }],

            [{
                name: "Fiche d'information sur le mot de passe",
                link: "/#/fr/school-admin/bc-fsa/assessment_setup1/administration_session",
            }, {
                name: "Liste d'élèves inscrits",
                link: "/#/en/school-admin/bc-fsa/students/enrolled"
            }],

            [{
                name: "Descripteurs d'items de 7e année",
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/515714/authoring/ÉHB 2021_22 G7 Lit Item Descriptors_FINAL_July_2021 (v2)/1629217920565/ÉHB 2021_22 G7 Lit Item Descriptors_FINAL_July_2021 (v2).pdf',
            }],
        ]
    ]
})

export const getG4SampleMaterialList = (): MaterialList => ({
    title: 'Grade 4 Materials',
    expanded: false,
    columns: ['Sample Assessment Links', 'Scoring Links', 'Policies & Guides'],
    rows: ['Materials in English', 'Materials in French'],
    columnAvailable: [true, true, true],
    data: [
        [
            [{
                name: 'Sample Assessment A Online',
                link: '',
            }, {
                name: 'Sample A- Print (PDF)',
                link: '',
            }, {
                name: 'Sample A- Print Answer Key (PDF)',
                link: '',
            }, {
                name: 'Sample B- Print (PDF)',
                link: '',
            }, {
                name: 'Sample B- Print Answer Key (PDF)',
                link: '',
            }, {
                name: 'Assessment Specifications (PDF)',
                link: '',
            }],

            [{
                name: 'Sample Scoringing Rubric (PDF)',
                link: '',
            }, {
                name: 'Sample Scoring Guide & Exemplars (PDF)',
                link: '',
            }, {
                name: 'Sample Comments from Markers',
                link: '',
            }],

            [{
                name: 'Administration Guide',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/2021-22 FSA Admin Manual Online/1629302065239/2021-22 FSA Admin Manual Online.pdf',
            }, {
                name: 'Calculator Policy',
                link: '',
            }, {
                name: 'Reference Pages',
                link: '',
            }, {
                name: 'Technical Readiness Guides',
                link: '',
            }],
        ],

        [
            [],
            [],
            [],
        ]
    ]
})

export const getG7SampleMaterialList = (): MaterialList => ({
    title: 'Grade 7 Materials',
    expanded: false,
    columns: ['Sample Assessment Links', 'Scoring Links', 'Policies & Guides'],
    rows: ['Materials in English', 'Materials in French'],
    columnAvailable: [true, true, true],
    data: [
        [
            [{
                name: 'Sample Assessment A Online',
                link: '',
            }, {
                name: 'Sample A- Print (PDF)',
                link: '',
            }, {
                name: 'Sample A- Print Answer Key (PDF)',
                link: '',
            }, {
                name: 'Sample B- Print (PDF)',
                link: '',
            }, {
                name: 'Sample B- Print Answer Key (PDF)',
                link: '',
            }, {
                name: 'Assessment Specifications (PDF)',
                link: '',
            }],

            [{
                name: 'Sample Scoringing Rubric (PDF)',
                link: '',
            }, {
                name: 'Sample Scoring Guide & Exemplars (PDF)',
                link: '',
            }, {
                name: 'Sample Comments from Markers',
                link: '',
            }],

            [{
                name: 'Administration Guide',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/2021-22 FSA Admin Manual Online/1629302065239/2021-22 FSA Admin Manual Online.pdf',
            }, {
                name: 'Calculator Policy',
                link: '',
            }, {
                name: 'Reference Pages',
                link: '',
            }, {
                name: 'Technical Readiness Guides',
                link: '',
            }],
        ],

        [
            [],
            [],
            [],
        ]
    ]
})

export const getAssessmentKeys = (): MaterialList => ({
    title: 'Grade XXX Materials',
    expanded: false,
    columns: ['Sample Assessment Links', 'Scoring Links', 'Policies & Guides'],
    rows: ['Materials in English', 'Materials in French'],
    columnAvailable: [true, true, true],
    data: [
        [
            [{
                name: 'Sample Assessment A XXXXXXXXXXXXOnline',
                link: '',
            }, {
                name: 'Sample A- Print XXXXXXXXXXXXXXXx(PDF)',
                link: '',
            }, {
                name: 'Sample A- Print Answer Key (PDF)',
                link: '',
            }, {
                name: 'Sample B- Print (PDF)',
                link: '',
            }, {
                name: 'Sample B- Print Answer Key (PDF)',
                link: '',
            }, {
                name: 'Assessment Specifications (PDF)',
                link: '',
            }],

            [{
                name: 'Sample Scoringing Rubric (PDF)',
                link: '',
            }, {
                name: 'Sample Scoring Guide & Exemplars (PDF)',
                link: '',
            }, {
                name: 'Sample Comments from Markers',
                link: '',
            }],

            [{
                name: 'Administration Guide',
                link: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/230/authoring/2021-22 FSA Admin Manual Online/1629302065239/2021-22 FSA Admin Manual Online.pdf',
            }, {
                name: 'Calculator Policy',
                link: '',
            }, {
                name: 'Reference Pages',
                link: '',
            }, {
                name: 'Technical Readiness Guides',
                link: '',
            }],
        ],

        [
            [],
            [],
            [],
        ]
    ]
})