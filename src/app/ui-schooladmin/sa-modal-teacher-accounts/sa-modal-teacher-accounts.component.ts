import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { ISession } from '../data/types';
import { ISaFormEntry, SaFormType, initFormEntries } from '../sa-widget-form-entry/sa-widget-form-entry.component';
import { FormControl } from '@angular/forms';
import { STUDENT_YES_NO, STUDENT_G9_COURSES, STUDENT_MATH_CLASS_WHEN, STUDENT_IN_PERSON_REMOTE } from '../data/constants';
import { generateEntries } from '../../ui-testadmin/demo-data.service';
import { randomName } from '../../constants/fakenames';
import { G9DemoDataService } from '../g9-demo-data.service';
import { DEMO_TC_SETTINGS } from '../data/sample-data/test-controller-settings';
import { filterArr, isInArr } from '../data/util';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { initMappedList } from '../data/util';

export enum ROLES {
  TEACHER = "TEACHER",
  INVIGILATOR = "INVIGILATOR",
}
@Component({
    selector: 'sa-modal-teacher-accounts',
    templateUrl: './sa-modal-teacher-accounts.component.html',
    styleUrls: ['./sa-modal-teacher-accounts.component.scss']
  })

  export class SaModalTeacherAccountsComponent implements OnInit {

  @Input() teachers:ISession[];
  @Input() savePayload:{[key:string]:Partial<ISession>};
  @Input() saveProp:string;
  @Input() isEdit:boolean = false;
  @Output() isTeacherSelected = new EventEmitter();

  constructor(
    private g9DemoData: G9DemoDataService,
    private whitelabel: WhitelabelService
  ) { }

  formEntries:ISaFormEntry[];
 classes:any[];
  isConfirmed: any[]
  selectedTeacher:ISession;
  isSelectingTeacher:boolean;
  teacherRoles = initMappedList([    
    { id: ROLES.TEACHER, label: 'sa_class_educator'},
    { id: ROLES.INVIGILATOR, label: 'abed_exsup'},
  ]);

  ngOnInit(): void {
    this.initContextInfo();  
    this.initSelectedTeacher();
  }
  initSelectedTeacher(){
    if (this.teachers){
      if (this.teachers.length === 1){
        return this.selectTeacher(this.teachers[0]);
      }
      if (this.teachers.length > 1){
        this.isTeacherSelected.emit({selected:true,classCode:null,id:null})
        return this.isSelectingTeacher = true;
      }
    }
    this.initFormEntries({});
  }
  initContextInfo(){
    this.classes = this.g9DemoData.classrooms.map(classroom => {
      return { 
        id: classroom.id,
        label: classroom.class_code,
      }
    })
    this.isConfirmed = [
      {
        id: true,
        label: 'sa_session_yes' // 'Yes'
      },
      {
        id: false,
        label: 'sa_session_no' // 'No'
      }
    ]
  }
  initFormEntries(teacher:Partial<ISession>){
    const list = filterArr(STUDENT_MATH_CLASS_WHEN.list, entry => {
      return isInArr( DEMO_TC_SETTINGS.ALLOWED_SEMESTERS, entry.id);
    })

    // const editedCoursesList = filterArr(STUDENT_G9_COURSES.list, entry => {
    //   return isInArr(DEMO_TC_SETTINGS.ALLOWED_COURSES, entry.id)
    // })
    
    this.formEntries = initFormEntries(teacher, this.getFormEntries());
    
    const payload =  {};
    this.savePayload[this.saveProp] = payload;
    this.formEntries.forEach(entry => {
      payload[entry.valProp] = teacher[entry.valProp];
      entry.formControlRef.valueChanges.subscribe(val => {
        payload[entry.valProp] = val;
      })
    })
  }

  getFormEntries(){
    if(!this.isEdit && this.whitelabel.isABED()){
      return [{type: SaFormType.TEXT,  valProp:'firstName', label: 'lbl_first_name', required: true},
      {type: SaFormType.TEXT,  valProp:'lastName', label: 'lbl_last_name', required: true}, 
      {type: SaFormType.TEXT,  valProp:'email', label: 'lbl_email', required: true},
      {type: SaFormType.RADIO, valProp: "role", label: "sa_abed_role", options: this.teacherRoles.list, required: true},
      {type: SaFormType.TEXT,  valProp:'grouping', label: 'abed_new_grouping', required: false},
    ]
    } else if(!this.isEdit) {
      return [{type: SaFormType.TEXT,  valProp:'firstName', label: 'lbl_first_name', required: true},
      {type: SaFormType.TEXT,  valProp:'lastName', label: 'lbl_last_name', required: true}, 
      {type: SaFormType.TEXT,  valProp:'email', label: 'lbl_email', required: true},
      {type: SaFormType.TEXT,  valProp:'grouping', label: 'New Grouping', required: false},
    ]
    }
    return [{type: SaFormType.TEXT,  valProp:'firstName', label: 'lbl_first_name', required: true},
      {type: SaFormType.TEXT,  valProp:'lastName', label: 'lbl_last_name', required: true}, 
    ]
  }
  selectTeacher(teacher:ISession){
    this.isTeacherSelected.emit({selected:false,classCode:teacher.classCode,id:teacher.id})
    this.isSelectingTeacher = false;
    this.selectedTeacher = teacher;
    this.initFormEntries(this.selectedTeacher);
  }

  getNoClassForTeachersSlug(){
    if (this.whitelabel.isNBED()){
      return 'txt_no_class_for_teachers_warn_nbed';
    }

    else if (this.whitelabel.isABED() && this.isEdit)
    {
      return 'txt_no_class_for_teachers_warn_abed';
    }
    
    else if (!this.whitelabel.isABED()) return 'txt_no_class_for_teachers_warn';
  }
}