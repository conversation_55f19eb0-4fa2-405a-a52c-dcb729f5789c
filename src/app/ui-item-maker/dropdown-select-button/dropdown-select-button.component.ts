import { Component, OnInit, Input, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { EditingDisabledService } from '../editing-disabled.service';

@Component({
  selector: 'dropdown-select-button',
  templateUrl: './dropdown-select-button.component.html',
  styleUrls: ['./dropdown-select-button.component.scss']
})
export class DropdownSelectButtonComponent implements OnInit {

  @Input() list: Array<{id:string, caption:string}>;
  @Input() buttonCaption: string = "Select";
  @Output() onClick = new EventEmitter<string>();
  selected: string;
  
  constructor(private editingDisabled: EditingDisabledService) { }

  ngOnInit() {
  }

  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  emitSelected(){
    console.log(this.selected);
    this.onClick.emit(this.selected);
  }

}
