import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    tw_ids: number,
}

export const SQL_05A_ATTEMPTS_TESTCENTRES:IExportQueryDef = {
    requiredInputs: [
        'tw_ids',
    ],
    optionalInputs: [
    ],
    queryGen: (config:IQueryConfig) => `
        select /*+ MAX_EXECUTION_TIME(300000) */  
              tw.id admin_window_id
            , JSON_EXTRACT(tw.title, '$.en') admin_window_name
            , cb.foreign_id TestingJID
            , cb.id jurisdiction_id
            , JSON_EXTRACT(cb.title, '$.en') jurisdiction_name
            , i.id test_center_id
            , i.name test_center_name
            , ts.id test_session_id
            , ta.id SrlNbr
            , ta.uid CAECUID
            , u.first_name
            , twtar.lang
            , twtar.type_slug asmt_code
            , twtar.form_code
            , twtar.is_print
            , ta.is_invalid is_attempt_invalidated
            , ts.is_cancelled is_session_cancelled
            , ts.is_closed is_session_closed
            , ta.started_on is_started
            , (CASE WHEN ta.started_on IS NULL THEN 0 ELSE 1 END) AS is_started
            , ta.started_on 
            , tr.created_on results_computed_on
            , tr.results_released_on 
        from certification_bodies cb 
        join institutions i
            on i.jurisdiction_id = cb.id 
        join test_sessions ts 
            on ts.instit_group_id = i.group_id 
        join test_attempts ta 
            on ta.test_session_id = ts.id 
        join users u 
            on u.id = ta.uid
        join test_window_td_alloc_rules twtar 
            on twtar.id = ta.twtdar_id 
        join test_windows tw 
            on tw.id = twtar.test_window_id 
        left join test_reports tr 
            on tr.test_attempt_id = ta.id 
            and tr.is_invalid = 0
        where cb.is_sample = 0
            and ta.started_on is not null
            and twtar.test_window_id in (:tw_ids) 
    `
}

