<div>
  <div *ngIf="!isPrivateSchool" style = "display:table">
    <div style = "display: table-cell;">
      <filter-toggles 
        [state]="mySchool.getClassFilterToggles()"
        (id)="setClassFilter($event)"
      ></filter-toggles>
    </div>
    <div style = "display: table-cell;">
      <sa-test-window-filter
        [currentClassFilter] = "currentClassFilter"
        (setTestWindowEvent) = "setTestWindowFilter($event)"
      ></sa-test-window-filter>
    </div>
  </div>
  <div *ngIf="isPrivateSchool" style = "display:table">
    <div style = "display: table-cell;">
      <filter-toggles 
        [state]="mySchool.getPrivateSchoolClassFilterToggles()"
        (id)="setClassFilter($event)"
      ></filter-toggles>
    </div>
    <div style = "display: table-cell;">
      <sa-test-window-filter
      [currentClassFilter] = "currentClassFilter"
      (setTestWindowEvent) = "setTestWindowFilter($event)"
    ></sa-test-window-filter>
    </div>
  </div>

  <p *ngIf="currentClassFilter" style="margin-bottom:1em;">
    <tra-md [slug]="getClassroomDataInfoSlug()"></tra-md>
    <tra-md *ngIf="!currentClassFilter" slug="select_a_grade_to_create_classgroups"></tra-md>
  </p>

  <div *ngIf="!currentClassFilter">
    <tra-md [slug]="getClassesReqFilterSlug()"></tra-md>
  </div>

  <sa-semester-filter 
    *ngIf="currentClassFilter && isLoaded && isShowingSemesterFilter" 
    (filter)="processFilterConditions($event)" 
    (changeCategory)="changeCategory($event)" 
    [baseFilter]="baseFilter"
  ></sa-semester-filter>

  <ng-container *ngIf="currentClassFilter">
    <div class="pre-table-strip">
      <div>
        <!-- todo:WHITELABEL newClassroomModalStart(true) vs. newClassroomModalStart(false)-->
        <!-- *ngIf="isCurrentFilterGroupings(currentClassFilter)" -->
        <button   
          *wlCtx="'IS_SA_SC_CREATE'"
          class="button is-small has-icon is-success" 
          (click)="newClassroomModalStart(true)"  
          [disabled]="!isCurrentTestWindowActive()">
            <!-- newClassroomModalStart() -->
            <span class="icon"><i class="fas fa-plus-square"></i></span>
            <span><tra [slug]="getCreateNewSlug()"></tra><!--New Classroom--></span>
        </button>
        <button 
          *ngIf="currentClassFilter" 
          class="button is-small has-icon" 
          (click)="editClassroomModalStart()" 
          [disabled]="shouldEditOrArchiveBtnsBeDisabled()">
          <!-- editClassroomModalStart() -->
            <span class="icon"><i class="fas fa-edit"></i></span>
            <span><tra [slug]="getEditSlug()"></tra><!--Edit Selected--></span>
        </button>
        <button 
          *ngIf="currentClassFilter" 
          class="button is-small has-icon" 
          (click)="archiveSelectedClassrooms()" 
          [disabled]="shouldEditOrArchiveBtnsBeDisabled()">
          <!-- archiveSelectedClassrooms() -->
            <span class="icon"><i class="fas fa-archive"></i></span>
            <span><tra [slug]="getDeleteSlug()"></tra><!--Delete Selected--></span>
        </button>
        <button 
          *ngIf="currentClassFilter && false" 
          class="button is-small has-icon" 
          (click)="assignClassrooms()" 
          [disabled] ="!classroomSelections.isAnySelected || noOtherTestWindow() || isCurrentTestWindowActive()">
            <span><tra [slug]="getAssignToTestWindow()"></tra></span>
        </button>
        <button 
          (click)="handleUnassignTeacherPopup()" 
          class="button is-small has-icon is-warning"
          [disabled] ="disableUnassignTeachersBtn()">
          <tra slug="sa_classroom_unassign_header"></tra>
        </button>
      </div>

      <div>
        <!-- Import Button -->
        <button 
          *wlCtx="'IS_EQAO'"
          class="button is-small has-icon" 
          (click)="importClassroomModalStart()" 
          [disabled]="!isCurrentTestWindowActive()">
            <span class="icon"><i class="fas fa-table"></i></span>
            <span><tra slug="sa_classrooms_import"></tra></span>
        </button>
        
        <!-- Export Button -->
        <button 
          class="button is-small has-icon" 
          (click)="exportClassroomModalStart()">
            <span class="icon"><i class="fas fa-table"></i></span>
            <span><tra slug="sa_classrooms_export"></tra></span>
        </button>
      </div>
    </div>
    <paginator 
      [model]="classroomsTable.getPaginatorCtrl()" 
      [page]="classroomsTable.getPage()" 
      [numEntries]="classroomsTable.numEntries()"
      (pageChange)="pageChanged()"
    ></paginator>
    
    <div class="classrooms-table-container">
      <table class="table is-hoverable">
        <tr class="header-row">
          <th> 
            <table-row-selector 
                [entry]="classroomSelections" 
                prop="isAllSelected" 
                (toggle)="classroomSelections.toggleSelectAll()">>
              </table-row-selector> 
          </th>
          <th 
            *ngIf="!currentClassFilter" 
            class="flush"> 
              <table-header 
                id = "group_type"      
                caption = "lbl_filter"  
                [ctrl] = "classroomsTable" 
                [isSortEnabled]="false" 
                [disableFilter]="true">
              </table-header><!--Class Code--> 
          </th>
          <th 
            class="flush"> 
              <table-header 
                id = "class_code"      
                [caption] = "getTableHeaderClassCode()"  
                [ctrl] = "classroomsTable" 
                [isSortEnabled]="true">
              </table-header><!--Class Code--> 
          </th>
          <th 
            *ngIf="currentClassFilter === ClassFilterId.G9" 
            class="flush"> 
              <div class="space-between">
                <table-header 
                  id = "semester"        
                  caption = "sa_classrooms_col_term_format"    
                  [ctrl] = "classroomsTable" 
                  [isSortEnabled]="true" 
                  [disableFilter]="true">
                </table-header>  
              <!-- <button  class="button is-small is-light btn-filter" (click)="toggleSemesterFilter()"></button>
                <i class="fa fa-filter" aria-hidden="true"></i>
              </button> --> <!--Semester-->
            </div>
          </th>
          <th class="flush" *ngIf="whiteLabelService.isABED()"> 
            <!--Educator(s)--> 
              <table-header 
                  id = "educator"        
                  [caption] = "getTeacherSlug()"
                  [ctrl] = "classroomsTable" 
                  [isSortEnabled]="true">
              </table-header>
          </th>
          <th class="flush"> 
            <!--Educator(s)--> 
              <table-header 
                  id = "invigilators"        
                  caption = "{{whiteLabelService.isABED() ? 'lbl_exam_supervisors' : 'lbl_table_invig'}}"    
                  [ctrl] = "classroomsTable" 
                  [isSortEnabled]="true">
              </table-header>
          </th>
          <!-- <th *ngIf="currentClassFilter !== ClassFilterId.OSSLT" class="flush">d
            <table-header id = "course" caption = "sa_classrooms_col_learning_format" [ctrl] = "classroomsTable" [isSortEnabled]="true" [disableFilter]="true"></table-header>
          </th>--><!--Description-->
          <th class="flush"> 
            <!--Students--> 
            <table-header 
              id = "students"        
              caption = "sa_classrooms_col_students"    
              [ctrl] = "classroomsTable" 
              [isSortEnabled]="true"
              [customSortFunction]="sortNumerically">
            </table-header>
          </th>
          <th *ngIf="IS_ONBOARDING_IMPLEMENTED" class="flush"> 
            <!--Onboarding--> 
            <table-header 
              id = "onboarding"      
              caption = "sa_classrooms_col_onboard"     
              [ctrl] = "classroomsTable" 
              [isSortEnabled]="true" 
              [disableFilter]="true">
            </table-header>
          </th>
          <th *ngIf="IS_ASMT_COUNT_IMPLEMENTED" class="flush"> 
            <!--Assessment--> 
            <table-header 
              id = "assessment"      
              caption = "sa_classrooms_col_assess"      
              [ctrl] = "classroomsTable" 
              [isSortEnabled]="true" 
              [disableFilter]="true">
            </table-header>
          </th>
        </tr>
        <tr *ngFor="let classroom of classroomsTable.getCurrentPageData()">
          <td>
            <table-row-selector 
              [entry]="classroom" 
              prop="__isSelected" 
              (toggle)="classroomSelections.checkSelection()">
            </table-row-selector>
          </td>
          <td *ngIf="!currentClassFilter"> 
            {{renderCourseType(classroom.course_type)}} 
          </td>
          <td style="white-space: pre-wrap;">
            <a 
              class="modal-link" 
              (click)="selectClassGroup(classroom)">
                {{classroom.class_code}}
                <span 
                  *ngIf="classroom.is_fi == 1" 
                  class="tag fr-imm">
                    <tra slug="sa_lbl_french_immi"></tra>
                </span> 
            </a>
          </td>
          <!-- <td style="white-space: pre-wrap;"> {{classroom.class_code}}
            <span *ngIf="classroom.is_fi == 1" class="tag fr-imm"><tra slug="sa_lbl_french_immi"></tra></span> 
          </td> -->
          <td  *ngIf="currentClassFilter === ClassFilterId.G9" > {{classroom.semester_label}} </td>
          <td *ngIf="whiteLabelService.isABED()">
            {{classroom.educator}}
          </td>
          <td> {{getInvigilators(classroom)}} </td>
          <!--
          <td  *ngIf="currentClassFilter !== ClassFilterId.OSSLT" >
            {{renderLearningFormat(classroom.learningFormats)}}
          </td>
          -->
          <td> 
            <span *ngIf="classroom.students">{{classroom.students}}</span><span *ngIf="!classroom.students">0</span> 
          </td>
          <td *ngIf="IS_ONBOARDING_IMPLEMENTED">
            <div> 
              <progress 
                *ngIf="classroom.onboarding" 
                class="progress is-small" 
                [value]="classroom.onboarding" 
                max="100">
              </progress>
              <progress 
                *ngIf="!classroom.onboarding" 
                class="progress is-small" 
                value="0" 
                max="100">
              </progress>
            </div>
          </td>
          <td *ngIf="IS_ASMT_COUNT_IMPLEMENTED">
            <div> 
              <progress class="progress is-small" value="0" max="20"></progress> 
            </div>
          </td>
        </tr>
      </table>  
    </div>
  </ng-container>
  
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
      <div [ngSwitch]="cModal().type">
        <div *ngSwitchCase="ClassroomModal.NEW" style="width: 30em; ">

          <!-- New Grouping popup-->
          <tra-md 
            style="font-size:1.3rem; font-weight: 700; text-transform: uppercase;" 
            [slug]="getNewClassSectionSlug()">
          </tra-md>
          <sa-modal-classroom 
            [classrooms]="cModal().config.classrooms" 
            [isGroup]="cModal().config.isGroup"  
            [savePayload]="cModal().config" 
            [currentClassFilter]="currentClassFilter" 
            saveProp="payload">
          </sa-modal-classroom>
        </div>
        <div *ngSwitchCase="ClassroomModal.EDIT" style="width: 30em; ">
          <tra-md 
            [slug]="getEditGroupingSlug()"
            [class.edit-grouping]="whiteLabelService.isABED()">
          </tra-md>
          <sa-modal-classroom 
            [isEditing]="true" 
            (isClassroomSelected)="setIsClassroomSelected($event)"
            [classrooms]="cModal().config.classrooms" 
            [savePayload]="cModal().config" 
            [currentClassFilter]="currentClassFilter" 
            saveProp="payload">
          </sa-modal-classroom>
        </div>
        <div *ngSwitchCase="ClassroomModal.IMPORT" >
            <!-- <sa-modal-classroom-import   [saveImportData]="cModal().config"  saveProp="payload"></sa-modal-classroom-import> -->
            <!-- <tc-table-common-import
            [saveImportData]="cmc()"
            [hideTable]="cmc().hideTable"
            [columns]="cmc().columns"
            templateUrlSlug="sa_class_template_url"
            altTemplateUrlSlug="sa_osslt_class_template_url"
            [useAltUrl]="currentClassFilter === ClassFilterId.OSSLT"
            ></tc-table-common-import> -->
            <tc-table-common-import
                [saveImportData]="cmc()"
                [hideTable]="cmc().hideTable"
                [columns]="cmc().columns"
                [templateUrlSlug] ="getClassTemplate()">
            </tc-table-common-import>
        </div>
        <div *ngSwitchCase="ClassroomModal.ASSIGN_TESTWINDOW" class="simple-content-bounds">
          <p><tra slug="sa_class_assigntw_title"></tra></p>
          <div style="margin-bottom:2em;">
              <table>
                  <tr>
                      <th><tra slug="sa_class_assigntw_id"></tra><!--ID--></th>
                      <th><tra slug="sa_class_assigntw_name"></tra><!--Class Name--></th>
                      <th><tra slug="sa_class_assigntw_semester"></tra><!--semester--></th>
                      <th><tra slug="sa_class_assigntw_teacher"></tra><!--teacher--></th>
                  </tr>
                  <tr *ngFor="let classroom of cmc().classrooms">
                      <td>{{classroom.id}}</td>
                      <td>{{classroom.class_code}}</td>
                      <td>{{classroom.semester_label}}</td>
                      <td>{{classroom.educator}}</td>
                  </tr>
              </table>
          </div>
          <p><tra slug="sa_class_assigntw_title_2"></tra></p>
          <div>
              <table>
                  <tr>
                      <th style="width:2em;"></th>
                      <th><tra slug="sa_class_assigntw_id"></tra></th>
                      <th><tra slug="sa_class_assigntw_tw"></tra></th>
                  </tr>
                  <tr *ngFor="let testWindow of testWindowsToAssign">
                      <td><table-row-selector [entry]="cmc().testWindowSelected" [prop]="testWindow.id" [singleSelect]="true"></table-row-selector> </td>
                      <td>{{testWindow.id}}</td>
                      <td>{{getTestWindowViewText(testWindow)}}</td>
                  </tr>
              </table>
          </div>
        </div>
      </div>

      <div *ngIf="isClassroomSelectedInModal || cModal().type !== ClassroomModal.EDIT">
        <modal-footer [pageModal]="pageModal" [isConfirmAlert]="true" ></modal-footer>
      </div>
  </div>
</div>