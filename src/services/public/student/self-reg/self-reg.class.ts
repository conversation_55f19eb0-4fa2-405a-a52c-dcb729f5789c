import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { LoginABEDKeyStrategy, RequestContext } from '../../../../login-abed-key.strategy';
import { INewStudent } from '../../dist-admin/student/student.class';

interface Data {}

export interface SelfRegPayload {
  first_name: string,
  last_name: string,
  dob: string,
  accessCode: string,
  studentNumber: string,
  isSasnLogin: string,
}

interface ServiceOptions {}

export class SelfReg implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  checkForExistingStudent(sutdent_number:string, sc_access_code:string){

  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.NotImplemented();
  }

  async addSelfRegStudent(data:SelfRegPayload, ctx:RequestContext){
    const {
      first_name,
      last_name,
      dob,
      accessCode,
      studentNumber,
    } = <any> data;
    // re-confirm the class (based on the access code)
    const { sc_id, sc_group_id, school_group_id, user_meta_keys} = await LoginABEDKeyStrategy.getClassesFromAccessCode(this.app, accessCode); 
    // re-confirm that the student does not exist
    LoginABEDKeyStrategy.validateStudentNumber(studentNumber, ctx); // throws error if invalid
    const uids = await LoginABEDKeyStrategy.getUidsFromStunum(this.app, ctx, school_group_id, user_meta_keys, studentNumber, true);
    if(uids.length > 0) {
      throw new Errors.GeneralError('EXISTING_REGISTRATION')
    }
    const stuNumKey = user_meta_keys[0];
    const studentAccountDef:INewStudent = {
      account: {
        first_name,
        last_name,
      },
      isSelfReg: true,
      roles: [],
      meta: {
        [stuNumKey]: studentNumber
      },
      meta_clean: [
        {
          key : "DateofBirth",
          key_namespace : "abed_course",
          value : dob,
        }
      ]
    }
    const newStudent = await this.app
      .service('public/dist-admin/student')
      .createStudentRecordForClassroom(
        studentAccountDef,
        sc_id,
        -1,
      )
    return newStudent;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
