import { Component, OnInit, Input, EventEmitter, Output} from '@angular/core';
import { ISaFormEntry, SaFormType, initFormEntries } from 'src/app/ui-schooladmin/sa-widget-form-entry/sa-widget-form-entry.component';

@Component({
  selector: 'sb-modal-admin-accounts',
  templateUrl: './sb-modal-admin-accounts.component.html',
  styleUrls: ['./sb-modal-admin-accounts.component.scss']
})
export class SbModalAdminAccountsComponent implements OnInit {
  @Input() admins:any[];
  @Input() savePayload:{[key:string]:Partial<any>};
  @Input() saveProp:string;
  @Input() isEdit:boolean = false;
  @Output() setSelectAdmin = new EventEmitter();

  constructor() { }

  formEntries:ISaFormEntry[];
  selectedAdmin:any;
  isSelectingAdmin:boolean;

  ngOnInit(): void {
    this.initSelectedAdmin();
  }

  initSelectedAdmin(){
    if (this.admins){
      if (this.admins.length === 1){
        return this.selectAdmin(this.admins[0]);
      }
      if (this.admins.length > 1){
        //this.setSelectAdmin.emit({selected:true, id:null})
        return this.isSelectingAdmin = true;
      }
    }
    this.initFormEntries({});
  }

  selectAdmin(admin){
    this.setSelectAdmin.emit(admin)
    this.isSelectingAdmin = false;
    this.selectedAdmin = admin;
    this.initFormEntries(this.selectedAdmin);
  }

  initFormEntries(admin){
    this.formEntries = initFormEntries(admin, this.getFormEntries());
    
    const payload =  {};
    this.savePayload[this.saveProp] = payload;
    this.formEntries.forEach(entry => {
      payload[entry.valProp] = admin[entry.valProp];
      entry.formControlRef.valueChanges.subscribe(val => {
        payload[entry.valProp] = val;
      })
    })
  }

  getFormEntries(){
    if(!this.isEdit){ // create new
      return [
        {type: SaFormType.TEXT,  valProp:'firstName', label: 'lbl_first_name', required: true},
        {type: SaFormType.TEXT,  valProp:'lastName', label: 'lbl_last_name', required: true}, 
        {type: SaFormType.TEXT,  valProp:'email', label: 'lbl_email', required: true}
      ]
    }else{ // Edit
      return [
        {type: SaFormType.TEXT,  valProp:'firstName', label: 'lbl_first_name', required: true},
        {type: SaFormType.TEXT,  valProp:'lastName', label: 'lbl_last_name', required: true}, 
      ]
    }
  }

}
