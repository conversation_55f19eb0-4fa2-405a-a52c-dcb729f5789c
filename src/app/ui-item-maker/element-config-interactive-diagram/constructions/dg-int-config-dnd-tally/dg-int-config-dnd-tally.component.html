<fieldset [disabled]="isReadOnly()">
  <label class="label">Targets</label>
  <div class="list" cdkDropList (cdkDropListDropped)="dropTargetOrdering($event)" [class.no-pointer-events]="isReadOnly()">
    <div class="list-item" *ngFor="let targetForm of targetForms; let i = index; trackBy: trackByFn" cdkDrag>
      <div class="target-form-container">
        <div class="target-index">{{ i + 1 }}</div>
        <div class="target-form-inner-container">
          <div class="flex-container">
            <textarea
              textInputTransform
              #targets
              (mousedown)="$event.stopPropagation()"
              [formControl]="targetForm.label"
              style="min-width: 10em; max-width: 20em;"
              class="textarea is-small target-option-textarea"
              (input)="rerender()"
              cdkTextareaAutosize
              [cdkTextareaAutosize]="true"
              [cdkAutosizeMinRows]="2"
              (focus)="setTextFocus(targetForm.label, 'target_' + i)"
            ></textarea>
            <div (click)="elementEditDeleteTarget(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
              <i class="fas fa-trash" aria-hidden="true"></i>
            </div>
          </div>
          <div class="flex-container">
            <span style="white-space: nowrap;">Target value : &nbsp;</span>
            <input type="number" 
              step="any"
              (mousedown)="$event.stopPropagation()"
              (input)="rerender()"
              [formControl]="targetForm.targetValue" class="input is-small target-option-input" />
          </div>
        </div>
      </div>
    </div>
  </div> 
</fieldset>
  
<button 
  (click)="elementEditAddTarget()" 
  class="button is-small has-icon"
>
  <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
  <span>Add Target</span>
</button>

<hr/>
  
<fieldset [disabled]="isReadOnly()">
  <label class="label">Options</label>
  
  <button (click)="setUseImage(true)" class="button is-small has-icon" [class.is-info]="isUseImage()">
    <span class="icon"><i class="fa fa-image" aria-hidden="true"></i></span>
    <span>Image</span>
  </button>
  <button (click)="setUseImage(false)" class="button is-small has-icon" [class.is-info]="!isUseImage()">
    <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
    <span>Text</span>
  </button>
  
  <div class="list" cdkDropList (cdkDropListDropped)="dropOptionOrdering($event)" [class.no-pointer-events]="isReadOnly()">
    <div class="list-item" *ngFor="let optionForm of optionForms; let i = index" cdkDrag>
      <div class="target-form-container">
        <div class="target-index">{{ i + 1 }}</div>
        <div *ngIf="isUseImage(); then optionWithImage; else optionNoImage"></div>
        
        <ng-template #optionNoImage>
          <div class="target-form-inner-container">
            <div class="flex-container">
              <textarea
                textInputTransform
                #options
                (mousedown)="$event.stopPropagation()"
                [formControl]="optionForm.label"
                (input)="rerender()"
                style="min-width: 10em; max-width: 20em;"
                class="textarea is-small target-option-textarea"
                cdkTextareaAutosize
                [cdkTextareaAutosize]="true"
                [cdkAutosizeMinRows]="2"
                (focus)="setTextFocus(optionForm.label, 'option_' + i)"
              ></textarea>
              <div (click)="elementEditDeleteOption(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
                <i class="fas fa-trash" aria-hidden="true"></i>
              </div>
            </div>
            <div class="flex-container">
              <span style="white-space: nowrap;">Value : &nbsp;</span>
              <input type="number" 
                step="any"
                (mousedown)="$event.stopPropagation()"
                (input)="rerender()"
                [formControl]="optionForm.value" class="input is-small target-option-input" />
            </div>
          </div>
        </ng-template>
        
        <ng-template #optionWithImage>
          <div class="target-form-inner-container">
            <div class="flex-container">
              <span style="white-space: nowrap;">Value : &nbsp;</span>
              <input type="number" 
                step="any"
                (input)="rerender()"
                (mousedown)="$event.stopPropagation()"
                [formControl]="optionForm.value" class="input is-small target-option-input" />
              <div (click)="elementEditDeleteOption(i)" class="target-option-button" [class.no-pointer-events]="isReadOnly()">
                <i class="fas fa-trash" aria-hidden="true"></i>
              </div>
            </div>
            <twiddle caption="Image" [state]="optionsImageTwiddleConfig[i]"></twiddle>
            <div *ngIf="optionsImageTwiddleConfig[i].value">
              <capture-image [element]="getImageElement(i)" (change)="rerender()"></capture-image>
              <asset-library-link [element]="getImageElement(i)"></asset-library-link>     
            </div>
          </div>
        </ng-template>
        
      </div>
    </div>
  </div> 
</fieldset>

<button 
  (click)="elementEditAddOption()" 
  class="button is-small has-icon"
>
  <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
  <span>Add Option</span>
</button>

<hr/>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="rerender()">
    <div class="sub-property-div control">
      Home Position:
      <select [formControl]="homePositionForm">
        <option *ngFor="let option of homePositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
      <br>Target Background Colour:
      <input type="color" [(ngModel)]="element.config.targetBgColor">
      <br>Draggable Background Colour:
      <input type="color" [(ngModel)]="element.config.draggableBgColor">
      <br>
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br><label class="checkbox">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      <br>
      <br> Minimum Target Width:
      <input type="number" [(ngModel)]="element.config.boxMinSize" class="small-input">
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
    </div>
  </fieldset>
</div>
