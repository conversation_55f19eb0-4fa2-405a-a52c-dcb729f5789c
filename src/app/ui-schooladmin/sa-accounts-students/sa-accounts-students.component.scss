@import "../../../styles/partials/modal";
@import "../../../styles/page-types/standard.scss";

.page-body {
  @extend %page-body;
  background-color: #f1f1f1;
  header {
    flex-grow: 0 !important;
  }
}

.sa-accounts-students {
  background-color: white;
  padding: 30px;

  .view-header {
    .students {
      display: flex;
      align-items: center;
      margin-bottom: 3px;
      span {
        font-family: Roboto;
        font-style: normal;
        font-weight: bold;
        font-size: 24px;
        line-height: 28px;

        color: #000000;
      }
      img {
        margin-left: 9px;
        width: 38px;
        height: 38px;
      }
    }
  }

  > hr {
    background-color: #d3d3d3;
  }

  .invitation-section {
    margin-top: 14px;
    background-color: #f5f5f5;
    padding: 9px;
    margin-bottom: 24px;

    button {
      font-family: Roboto;
      font-style: normal;
      font-weight: 500;
      font-size: 18px;
      line-height: 21px;
      /* identical to box height */
      border-radius: 5px;
      text-align: center;
      color: #ffffff;
      border: none;
      outline: none;
      cursor: pointer;
      &.new-invite-button {
        background: #32bf00;
        margin-right: 14px;
        width: auto;
        height: auto;
      }

      &.revoke-access-button {
        background: #c5c5c5;
        width: auto;
        height: auto;
      }
    }
  }

  .grade-selector {
    margin-top: 22px;
  }

  .table-section {
    margin-top: 19px;
    table {
      tr {
        &:first-child {
          background: #f3f3f3;
          th {
            font-family: Roboto;
            font-style: normal;
            font-weight: bold;
            font-size: 17px;
            line-height: 20px;
            /* identical to box height */

            color: #000000;
          }
        }

        td {
          vertical-align: middle;

          &:nth-child(1) {
            width: 370px;
          }

          a {
            text-decoration: underline;
          }

          button {
            font-family: Roboto;
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 21px;

            text-align: center;

            color: #ffffff;

            border-radius: 5px;
            border: none;
            outline: none;
            cursor: pointer;
            width: auto;
            height: auto;

            &.enroll-new-student-button {
              background: #32bf00;
            }

            &.exempt-student-button,
            &.unenroll-student-button {
              background: #9c241c;
            }
            &.export {
              cursor: pointer;
              background: #f5f5f5;
              border: 1px solid #c4c4c4;
              border-radius: 4px;
              height: 25px;
              radius: 4px;
              .title {
                font-style: normal;
                font-weight: 500;
                font-size: 13px;
                line-height: 15px;
                text-align: center;
                color: #3e3e3e;
              }
              //width: 75px;
              .icon {
                width: 12px;
                height: 13px;
                position: relative;
                top: 2px;
                margin-right: 6px;
                background-image: url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6388/authoring/fsa_accounts_export_button/*************/fsa_accounts_export_button.svg");
              }
            }
          }
        }
      }

      th,
      td {
        border: solid 1px black;
      }
    }
  }

  .list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;

    a {
      text-decoration: underline;
    }
  }

  .grade-selector,
  .school-selector,
  .assessment-session-selector,
  .list-selector {
    display: flex;
    align-items: center;
    margin-top: 7px;
    label {
      font-family: Roboto;
      font-style: normal;
      font-weight: normal;
      font-size: 17px;
      line-height: 20px;
      /* identical to box height */
      margin-right: 7px;
      color: #000000;
    }
    select {
      min-width: 200px;
      font-style: normal;
      font-weight: 500;
      font-size: 22px;
      line-height: 26px;
      color: #000000;
      cursor: pointer;
      padding: 0 0 0 5px;
      border: none;
      border-bottom: 1px solid black;
      option {
        font-size: 14px;
      }
    }
  }
}
