<div class="assessment-sessions-view">
  <div *ngIf="!isPrivateSchool" style = "display:table">
    <div style = "display: table-cell;">
      <filter-toggles 
          [state]="mySchool.getClassFilterToggles()"
          (id)="setClassFilter($event)"
      ></filter-toggles>
    </div> 
    <div style = "display: table-cell;">
      <sa-test-window-filter
        [currentClassFilter] = "currentClassFilter"
        (setTestWindowEvent) = "setTestWindowFilter($event)"
      ></sa-test-window-filter>
    </div> 
  </div>
  <div *ngIf="isPrivateSchool">
    <div style = "display: table-cell;">
      <filter-toggles 
          [state]="mySchool.getPrivateSchoolClassFilterToggles()"
          (id)="setClassFilter($event)"
      ></filter-toggles>
    </div> 
    <div style = "display: table-cell;">
      <sa-test-window-filter
      [currentClassFilter] = "currentClassFilter"
      (setTestWindowEvent) = "setTestWindowFilter($event)"
      ></sa-test-window-filter>
    </div>
  </div>
  <div><tra-md [slug]="getEducatorsHeaderSlug()"></tra-md></div>
    <sa-semester-filter 
      *ngIf="isLoaded && isShowingSemesterFilter" 
      (filter)="processFilterConditions($event)" 
      (changeCategory)="changeCategory($event)" 
      [baseFilter]="baseFilter"
    ></sa-semester-filter>
    <div class="pre-table-strip">
        <div>
          <!-- openModal(OVERLAYS.NEW_TEACHER) -->
            <button class="button is-small has-icon is-success"  [disabled]="isCurrentTestWindowExistAndInactive() || !this.currentClassFilter" (click)="newTeacherModalStart()">
              <!-- newSessionModalStart() -->
                <span class="icon"><i class="fas fa-plus-square"></i></span>
                <span><tra slug="btn_creat_a_new_account"></tra><!--New Session--></span>
            </button>
            <button class="button is-small has-icon" [disabled]="!isAnySelected || !isCurrentTestWindowActive()" (click)="editTeacherModalStart()">
              <!-- editTeacherModalStart() -->
                <span class="icon"><i class="fas fa-edit"></i></span>
                <span><tra [slug]="getEditSelectedSlug()"></tra><!--Edit Selected--></span>
            </button>
            <button class="button is-small has-icon" [disabled]="!isAnySelected" (click)="revokeSelectedTeachers()">
              <!-- revokeSelectedTeachers() -->
                <span class="icon"><i class="fas fa-archive"></i></span>
                <span><tra [slug]="getCancelSelectedSlug()"></tra><!--Cancel Selected--></span>
            </button>
        </div>
        <div>
            <!-- Import Button -->
            <button 
                *wlCtx="'IS_SA_IMPORT_TEACHER'" 
                class="button is-small has-icon" 
                (click)="importTeachersModal()" 
                [disabled]="!isCurrentTestWindowActive() || whitelabel.isABED()">
                  <span class="icon"><i class="fas fa-table"></i></span>
                  <span><tra slug="g9_import"></tra></span>
            </button>

            <!-- Export Button -->
            <button 
                class="button is-small has-icon" 
                (click)="exportTeachersModal()">
                  <span class="icon"><i class="fas fa-table"></i></span>
                  <span><tra slug="g9_export"></tra></span>
            </button>
        </div>
    </div>

    <paginator 
        [model]="teachersTable.getPaginatorCtrl()" 
        [page]="teachersTable.getPage()" 
        [numEntries]="teachersTable.numEntries()"
        (pageChange)="pageChanged()">
    </paginator>
    <div class="classrooms-table-container">
    <table style="overflow-x: scroll;" class="sessions-table table is-hoverable">
        <tr>
            <th style="width:3em;"> 
              <table-row-selector 
                [entry]="this" 
                prop="isAllSelected" 
                (toggle)="toggleSelectAll()">
              </table-row-selector> 
            </th>
            <th class="flush"> 
              <!--Teachers/Invigilators--> 
              <table-header 
                id = "invigilator"  
                [caption] = "columnLabels.invigilators" 
                [ctrl] = "teachersTable" 
                [isSortEnabled]="true">
              </table-header>
            </th>
            <th class="flush"> 
              <!--Students--> 
              <table-header 
                id = "students"     
                [caption] = "columnLabels.students"     
                [ctrl] = "teachersTable" 
                [isSortEnabled]="true">
              </table-header>
            </th>
            <th class="flush"> 
              <!--Class Code / Grouping--> 
              <table-header 
                id = "classCode"    
                [caption]="getColHeaderClassroomSlug()"    
                [ctrl] = "teachersTable" 
                [isSortEnabled]="true">
              </table-header>
            </th>
            <!-- <th class="flush"> 
              <div class="space-between">
                <table-header id = "semester"        caption = "sa_classrooms_col_semester"    [ctrl] = "teachersTable" [isSortEnabled]="true" [disableFilter]="true"></table-header>  
                <button  class="button is-small is-light btn-filter" (click)="toggleSemesterFilter()">
                  <i class="fa fa-filter" aria-hidden="true"></i>
                </button>
              </div>
            </th> --> <!-- Semester -->
            <th class="flush"> <table-header id = "hasAccess"    [caption] = "columnLabels.hasAccess"    [ctrl] = "teachersTable" [isSortEnabled]="false" [disableFilter]="true"></table-header><!--Has Access?--> </th>
            <th class="flush"> <table-header id = "startTime"        [caption] = "columnLabels.startTime"        [ctrl] = "teachersTable" [isSortEnabled]="true" [disableFilter]="true"></table-header><!--Invitation Sent On--> </th>
        </tr>
        <tr *ngFor="let teacher of teachersTable.getCurrentPageData();">
            <td>
                <table-row-selector [entry]="teacher" prop="__isSelected" (toggle)="checkSelection()"></table-row-selector>
            </td>
            <!-- <td> {{teacher.id}} </td> -->
            <td>
                {{teacher.invigilator}}
                <div *ngIf="teacher.isConfirmed" class="is-green" style="font-size:0.8em;">
                  {{teacher.email}}
                </div>
                <div *ngIf="!teacher.isConfirmed" class="is-red" style="font-size:0.8em;">
                  {{teacher.invit_email}}
                </div>
            </td>
            <td> {{teacher.students}} </td>
            <td> {{teacher.classCode}} </td>
            <!-- <td> {{teacher.semester_label}}</td> -->
            <td>
                <span *ngIf="teacher.isConfirmed">
                    <i  class="fa fa-check" style="margin-right:0.5em;"></i>
                    <tra slug="lbl_yes"></tra>
                </span>
                <span *ngIf="!teacher.isConfirmed">
                    <i  class="fa fa-times" style="color: #f00; margin-right:0.5em;"></i>
                    <tra slug="lbl_no"></tra>
                    <button
                        class="button is-small  has-icon" 
                        style="margin-left: 0.5em;"
                        (click)="logInvitation(teacher)" 
                        *ngIf="!teacher.invit_email"
                    >
                      <span><tra slug="sa_educators_connect_email"></tra></span>
                    </button>
                    <button 
                        class="button is-small  has-icon" 
                        style="margin-left: 0.5em;" 
                        ngxClipboard 
                        [cbContent]="renderTeacherInvitationToClipboard(teacher)"
                        (cbOnSuccess)="onTeacherInviteLinkCopy(teacher)"
                        *ngIf="teacher.invit_email"
                    >
                        <span class="icon">
                          <i class="fa fa-copy"></i>
                        </span>
                        <span><tra slug="invite_link"></tra></span>
                    </button>
                </span>
            </td>
            <td> 
                <div *ngIf="teacher.email || teacher.invit_email" style="display:flex; flex-direction: row; justify-content: space-between;">
                    <div>
                        {{teacher.startTime || teacher.created_on}} 
                        <div *ngIf="!teacher.isConfirmed" style="font-size:0.8em;">
                          <tra slug="abed_expires"></tra>: {{teacher.endTime || teacher.expire_on}} 
                        </div>
                    </div>
                    <div  *ngIf="!teacher.isConfirmed && invitationExpired(teacher.expireOn)" style="display:flex; flex-direction: center; align-items: center;">
                    <!-- <div  *ngIf="!teacher.isConfirmed"> -->
                        <button class="button is-small extend" (click)="extendInvitation(teacher)"><tra slug="g9_osslt_extend"></tra></button>
                        <!-- <button class="button is-small"><tra slug="g9_extend"></tra></button> -->
                    </div>
                </div>
            </td>
        </tr>
    </table>
    </div>
</div>

<div class="dashboard-overlays" *ngIf="activeOverlay">
    <div class="dashboard-overlay-screen" (click)="closeActiveOverlay()"></div>
    <div class="dashboard-overlay-window">
      <div class="dashboard-overlay-header">
        <div class="overlay-title">{{renderOverlayName(activeOverlay)}}</div>
        <div class="overlay-close" (click)="closeActiveOverlay()">
          <i class="fa fa-times" aria-hidden="true"></i>
        </div>
      </div>
      <div class="dashboard-overlay-content">
        <div [ngSwitch]="activeOverlay">
          <div *ngSwitchCase="OVERLAYS.NEW_TEACHER" class="dashboard-overlay-account">
            <div class="field is-horizontal">
                <div class="field-label is-normal">
                  <label class="label"><tra slug="lbl_name"></tra></label>
                </div>
                <div class="field-body">
                  <div class="field">
                    <input class="input" [formControl]="newInvigilatorName" type="text">
                  </div>
                </div>
              </div>
              <div class="field is-horizontal">
                <div class="field-label is-normal">
                  <label class="label">Email</label>
                </div>
                <div class="field-body">
                  <div class="field">
                    <input class="input" [formControl]="newInvigilatorEmail" type="text">
                  </div>
                </div>
              </div>
              <div class="field-body" style="margin-bottom:0.75rem">
                <div class="field-label is-normal">
                  <label class="label">classCode</label>
                </div>
                <div class="field-body select">
                  <select (change)="getClassCodes($event.target.value)">
                    <option *ngFor="let opt of classCodes" >{{opt}}</option>
                  </select>
                </div>
              </div>
              
              <div class="field is-horizontal">
                <div class="field-label is-normal">
                  <label class="label">Students</label>
                </div>
                <div class="field-body">
                  <div class="field">
                    <input class="input" [formControl]="newInvigilatorStudents" type="text">
                  </div>
                </div>
              </div>
              <div>
                <button [disabled]="!newInvigilatorName.value" class="button is-success is-fullwidth" (click)="createNewAccount()">Create Account</button>
              </div>
          </div>
          <div *ngSwitchCase="OVERLAYS.EDIT_TEACHER" class="dashboard-overlay-account">
            <div class="field is-horizontal">
                <div class="field-label is-normal">
                  <label class="label"><tra slug="lbl_name"></tra></label>
                </div>
                <div class="field-body">
                  <div class="field">
                    <input class="input" type="text">
                  </div>
                </div>
              </div>
              <div>
                <button [disabled]="true" class="button is-success is-fullwidth ">Edit Account</button>
              </div>
          </div>
        </div>
      </div>
    </div>
    
</div>
  
<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <div [ngSwitch]="cModal().type">
          <div *ngSwitchCase="TeacherAccountsModal.NEW" style="width: 30em; ">

            <!-- New Teacher / New Invigilator popup-->
            <tra-md 
              style="font-size:1.3rem; font-weight: 700; text-transform: uppercase;" 
              [slug]="getSANewTeacherSlug()">
            </tra-md>
            <sa-modal-teacher-accounts 
              [savePayload]="cModal().config" 
              [teachers]="cModal().config.teachers"
              saveProp="payload">
            </sa-modal-teacher-accounts>

            <table>
              <tr>
                  <th style="width:2em;"></th>
                  <th><tra slug="ab_lbl_classes"></tra></th>
              </tr>
              <tr *ngFor="let classroom of availableClassrooms">
                  <td><table-row-selector [entry]="cmc().classroomSelected" [prop]="classroom.id"></table-row-selector> </td>
                  <td>{{classroom.class_code}}</td>
              </tr>
            </table>
            <!-- <div [ngSwitch]="availableClassrooms && availableClassrooms.length">
              <div *ngSwitchCase="false">
                <tra-md slug="txt_no_class_for_teachers_warn"></tra-md>
              </div>
              <div  *ngSwitchCase="true">
                <tra slug="sa_new_teacher_class"></tra>
                <table>
                  <tr>
                      <th style="width:2em;"></th>
                      <th><tra slug="ab_lbl_classes"></tra></th>
                  </tr>
                  <tr *ngFor="let classroom of availableClassrooms">
                      <td><table-row-selector [entry]="cmc().classroomSelected" [prop]="classroom.id"></table-row-selector> </td>
                      <td>{{classroom.class_code}}</td>
                  </tr>
                </table>
              </div>
            </div> -->
          </div>
          <div *ngSwitchCase="TeacherAccountsModal.EDIT" style="width: 30em; ">
              <h3>
                <tra-md 
                  [slug]="getEditTeacherHeader()"
                  [class.edit-teacher]="whitelabel.isABED()">
                </tra-md>
              </h3>
              <sa-modal-teacher-accounts [teachers]="cModal().config.teachers" [savePayload]="cModal().config" saveProp="payload" (isTeacherSelected)="setTeacher($event)" [isEdit]="true"></sa-modal-teacher-accounts>
              <table *ngIf="selectedId">
                <tr>
                    <th style="width:2em;"></th>
                    <th><tra slug="ab_lbl_classes"></tra></th>
                </tr>
                <tr *ngFor="let classroom of availableClassrooms">
                    <td><table-row-selector [entry]="selectedClassrooms" [prop]="classroom.id"></table-row-selector> </td>
                    <td>{{classroom.class_code}}</td>
                </tr>
            </table>
              <!-- <div [ngSwitch]="availableClassrooms && availableClassrooms.length">
                <div *ngSwitchCase="false">
                  <tra-md slug="txt_no_class_for_teachers_warn"></tra-md>
                </div>
                <div *ngSwitchCase="true">
                  <table *ngIf="selectedId">
                    <tr>
                        <th style="width:2em;"></th>
                        <th><tra slug="ab_lbl_classes"></tra></th>
                    </tr>
                    <tr *ngFor="let classroom of availableClassrooms">
                        <td><table-row-selector [entry]="selectedClassrooms" [prop]="classroom.id"></table-row-selector> </td>
                        <td>{{classroom.class_code}}</td>
                    </tr>
                </table>
              </div>
            </div> -->
            </div>
              <div *ngSwitchCase="TeacherAccountsModal.EXPORT">
                <tc-table-common 
                    [name]="cmc().name"
                    [data]="cmc().data"
                    [hideTable]="cmc().hideTable"
                    [columns]="cmc().columns">
                </tc-table-common>
            </div>
            <div *ngSwitchCase="TeacherAccountsModal.IMPORT">
                <tc-table-common-import
                    [saveImportData]="cmc()"
                    [hideTable]="cmc().hideTable"
                    [columns]="cmc().columns"
                    templateUrlSlug="sa_teacher_template_url">
                </tc-table-common-import>
            </div>
          </div>
        <modal-footer [pageModal]="pageModal" ></modal-footer>
    </div>
</div>
