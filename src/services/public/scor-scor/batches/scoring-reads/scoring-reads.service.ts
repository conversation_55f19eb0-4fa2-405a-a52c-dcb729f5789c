// Initializes the `public/scor-scor/batches/claim` service on path `/public/scor-scor/batches/claim`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { ScoringReads } from './scoring-reads';
import hooks from './scoring-reads.hooks';

// Add this service to the service type index
declare module '../../../../../declarations' {
  interface ServiceTypes {
    'public/scor-scor/batches/scoring-reads': ScoringReads & ServiceAddons<any>;
  }
}

export default function (app: Application) {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/scor-scor/batches/scoring-reads', new ScoringReads(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/scor-scor/batches/scoring-reads');

  service.hooks(hooks);
}
