import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';

interface Data {}

interface ServiceOptions {}

export class CoverPage implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }


  
  async get (id: Id, params?: Params): Promise<Data> {
      
    const db:Knex = this.app.get('knexClientRead');
    const getData = async (props:any[], query:string) => {
      const res = await db.raw(query, props);
      return <any[]> res[0];
    }


    if (params && params.query) {

      const assessmentTitleDate = await getData([],`
          SELECT * 
          from test_windows 
          where type_slug = "${params.query.sch_class_group_type}"  AND title LIKE '%DIP %' 
        ;`)
        
      const coverPageInfo = await getData([],`
        SELECT *
        from test_window_td_types twtt
        left join assessment_courses
          on twtt.course_code = assessment_courses.course_code 
        where twtt.course_code = '${params.query.student_course_code}'
          and twtt.test_window_id is null
          and twtt.is_revoked = 0
      ;`)

      return {
        info: {
          window_date_human: assessmentTitleDate[0].window_date_human,
          title_persistent: assessmentTitleDate[0].title_persistent,
          course_name_full: coverPageInfo[0].course_name_full,
          part_code: coverPageInfo[0].part_code,
          part_description_short: coverPageInfo[0].part_description_short,
          cover_color: coverPageInfo[0]?.cover_color,
        }
      }
    }
    
    throw new Errors.BadRequest();
  }


  
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

}
