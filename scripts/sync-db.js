const fs = require('fs');
const USE_DB = 'mpt_dev';

const synchronize = async (from, to, options) => {

  console.log('sync from', from, 'to', to);

  options = options || {}

  const config = {
    from,
    to,
    isPushFixEnabled: true,
    // isPullFixEnabled: false,
    tables: [
      {
        table: 'data_targets',
        keyCols: ['target_path',],
        stripCols: ['id', 'created_on']
      },
      {
        table: 'u_role_types',
        keyCols: ['role_type'],
        stripCols: ['id']
      },
      {
        table: 'u_role_actions',
        keyCols: ['role_type', 'data_method', 'data_target'],
        stripCols: ['id', 'created_on']
      },
    ],
    // delim: '\t',
    delim: ';;~;;',
  }

  const getConfig = (slug) => JSON.parse(fs.readFileSync(`./config/${slug}.json`));
  const knexFrom = require('knex')(getConfig(config.from).mysql_write);
  const knexTo = require('knex')(getConfig(config.to).mysql_write);
  const composeKey = (entry, cols) => cols.map(prop => (''+entry[prop]).trim()).join(config.delim);

  console.log('START OF SCHEMA SYNC')
  console.log('::::::::::::::')

  const newTables = [];
  const newTableColumns = [];
  const newViews = [];
  let updatedViews = [];
  const getTablesAndViews = async (knex) => {
    const tablesAndViews = await knex.raw('SELECT * FROM information_schema.tables');
    const tables = [];
    const views = [];
    const tableRef = new Map();
    const viewRef = new Map();
    const tableTypes = new Map();
    tablesAndViews[0].forEach(tableInfo => {
      tableTypes.set(tableInfo.TABLE_TYPE, true)
      if (tableInfo.TABLE_SCHEMA === USE_DB){
        const tableName = tableInfo.TABLE_NAME
        switch (tableInfo.TABLE_TYPE){
          case 'BASE TABLE': tables.push(tableName); tableRef.set(tableName, true); break;
          case 'VIEW': views.push(tableName); viewRef.set(tableName, true); break;
        }
      }
    });
    return {tables, views, tableRef, viewRef}
  }
  const sourceTablesAndViews = await getTablesAndViews(knexFrom);
  const targetTablesAndViews = await getTablesAndViews(knexTo);

  console.log(':::::: TABLES (diffs)::::::');
  const str_contains = (str, s) => str && s && (str.indexOf(s) !== -1);
  const appendColumnDetails = async (knex, tableName, columns) => {
    // await knexFrom(tableName).columnInfo();
    const columnRes = await knex.raw('SHOW COLUMNS FROM ' + tableName);
    columnRes[0].forEach(column => {
      const columnName = column['Field'];
      const columnSettings = columns[columnName];
      if (str_contains(column['Extra'], 'auto_increment')){
        columnSettings.isAutoIncrement = true;
      }
      if (str_contains(column['Key'], 'PRI')){
        columnSettings.isPrimaryKey = true;
      }
      if (str_contains(column['Key'], 'UNI')){
        columnSettings.isUnique = true;
      }
      columnSettings.isNullable = ! str_contains(column['Null'], 'NO') ;
      columnSettings.default = column['Default'];
      columnSettings.type = column['Type'];
      columnSettings.name = columnName;
    });
  }
  await Promise.all(sourceTablesAndViews.tables.map( async (tableName) => {
    const sourceColumns = await knexFrom(tableName).columnInfo();
    await appendColumnDetails(knexFrom, tableName, sourceColumns)
    // get columns
    if (!targetTablesAndViews.tableRef.get(tableName)){
      newTables.push( {tableName, sourceColumns} ) // to do
    }
    else{
      const targetColumns = await knexTo(tableName).columnInfo();
      Object.keys(sourceColumns).forEach(columnName => {
        if (!targetColumns[columnName]){
          newTableColumns.push({tableName, config: sourceColumns[columnName]});
        }
      })
    }
  }));

  console.log(':::::: VIEWS (diffs)::::::');
  const getViewDef = async (knex, viewName) => {
    // console.log('show create view ' + viewName)
    const res = await knex.raw('SHOW CREATE VIEW '+viewName);
    const defLongStr = res[0][0]['Create View'];
    if (!defLongStr){
      throw new Error('View not defined');
    }
    return defLongStr.split('DEFINER VIEW')[1]
  }
  await Promise.all(sourceTablesAndViews.views.map( async (viewName) => {
    const sourceViewDef = await getViewDef(knexFrom, viewName);
    if (!targetTablesAndViews.viewRef.get(viewName)){
      newViews.push( {viewName, sourceViewDef} ) // to do
    }
    else {
      const targetViewDef = await getViewDef(knexTo, viewName);
      if (targetViewDef !== sourceViewDef){
        updatedViews.push( {viewName, sourceViewDef, targetViewDef} );
      }
    }
  }));

  if (updatedViews.length > 0 && !options.allowViewUpdates){
    console.error(updatedViews.length + ' unexpected view updates, please either handle those manually or allow override from source to target. for all view listed. \n\n' + updatedViews.map(view => view.viewName).join('\n'));
    updatedViews = [];
    // throw new Error();
  }
  console.log('Schema updates: ', {
    newTables: newTables.length,
    newTableColumns: newTableColumns.length,
    newViews: newViews.length,
    updatedViews: updatedViews.length,
  })

  fs.writeFileSync(`./scripts/sync-db-logs/run-${+(new Date())}.json`, JSON.stringify({
    newTables,
    newTableColumns,
  }, null, 2))

  if (options.isDryRun){
    return;
  }

  console.log(':::::: TABLES (merges)::::::')
  const CURRENT_TIMESTAMP = 'CURRENT_TIMESTAMP';
  const defineColumn = (column) => {
    let defaultValue = column.default;
    if (defaultValue && defaultValue !== CURRENT_TIMESTAMP){
      defaultValue = `'${defaultValue}'`
    }
    return `\`${column.name}\` ${column.type} ${!column.isNullable ? 'NOT NULL' : ''} ${defaultValue ? 'DEFAULT '+defaultValue : '' } ${column.isAutoIncrement ? 'AUTO_INCREMENT':''} `
  }
  for (let table of newTables){
    let pk;
    let query = 'CREATE TABLE `'+table.tableName+'` ( \n';
    query += Object.keys(table.sourceColumns).map(columnName => {
      const column = table.sourceColumns[columnName];
      if (column.isPrimaryKey){
        pk = columnName;
      }
      return defineColumn(column);
    }).join(',\n');
    if (pk){
      query += '\n, PRIMARY KEY (`'+pk+'`)'
    };
    query += ') ENGINE=InnoDB DEFAULT CHARSET=utf8'
    console.log(query);
    await knexTo.raw(query);
  }
  for (let newColumn of newTableColumns){
    let pk;
    let query = `ALTER TABLE \`${newColumn.tableName}\` ADD COLUMN ${defineColumn(newColumn.config)} `;
    console.log(query);
    await knexTo.raw(query);
  }
  //  await knexTo.schema.alterTable('user_roles'); // add new columns
  console.log(':::::: VIEWS (merges)::::::')
  // console.log('tableTypes', sourceViews.length, sourceTables.length);
  const viewsToCreateReplace = newViews.concat(updatedViews);
  await Promise.all(viewsToCreateReplace.map(async view => {
    await knexTo.raw('CREATE OR REPLACE VIEW' + view.sourceViewDef)
  }));

  console.log('::::::::::::::')
  console.log('END OF SCHEMA SYNC');

  console.log('START OF DATA SYNC')
  console.log('::::::::::::::');
  console.log('Table [ Not In Source, Not in Target, # Records in Source, # Records in Target ]');
  console.log(' .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  .  . ');

  await Promise.all(
    config.tables.map( async (tableConfig) => {

      const existingRecordsRef = new Map();
      const targetRecordsRef = new Map();
      const summary = {
        notInSource:[],
        notInTarget:[],
        recordsSource:null,
        recordsTarget:null,
      }

      const recordsSource = await knexFrom(tableConfig.table);
      summary.recordsSource = recordsSource;
      recordsSource.forEach(entry => {
        existingRecordsRef.set(composeKey(entry, tableConfig.keyCols), true);
      })

      const recordsTarget = await knexTo(tableConfig.table);
      summary.recordsTarget = recordsTarget;
      recordsTarget.forEach(entry => {
        const key = composeKey(entry, tableConfig.keyCols);
        targetRecordsRef.set(key, true);
        if (!existingRecordsRef.has(key)){
          summary.notInSource.push(entry);
        }
      });
      recordsSource.forEach(entry => {
        const key = composeKey(entry, tableConfig.keyCols);
        if (!targetRecordsRef.has(key)){
          summary.notInTarget.push(entry);
        }
      });

      console.log(tableConfig.table, [
        summary.notInSource.length,
        summary.notInTarget.length,
        summary.recordsSource.length,
        summary.recordsTarget.length,
      ])
      // console.log(tableConfig.table, 'notInTarget', summary.notInTarget.map(entry => composeKey(entry, tableConfig.keyCols)).join('\n') )
      // console.log(tableConfig.table, 'recordsTarget', summary.recordsTarget.map(entry => composeKey(entry, tableConfig.keyCols)).join('\n') )
      if (config.isPushFixEnabled){
        return Promise.all(
          summary.notInTarget.map((entry, i) => {
            tableConfig.stripCols.forEach( prop => delete entry[prop] );
            return knexTo(tableConfig.table).insert(entry).then(
              console.log('insert', i)
            )
          })
        )
      }
      console.log('----------');

    })
  );

  console.log('::::::::::::::')
  console.log('END OF SYNC')
  process.exit(0);

  // check eqao-mpt (to do)

}

synchronize(
  'abed', 
  'multiplex', 
  {
    allowViewUpdates: false,
    isDryRun: true,
  }
); // only activate allowViewUpdates when necessary
