<div *ngIf="currentClassFilter" style="margin-left:2em;">
    <tra [slug]="getTestWindowLabelSlug()"></tra> 
    <select 
      (change)="onTestWindowChanged($event.target.value)" 
      class="test-window-select" >
        <option 
          *ngFor="let tw of testWindows; let i = index" 
          [value]="i" 
          [selected]="i===currentTestWindowIndex()">
            {{getTestWindowViewText(tw)}}
        </option>
    </select>
<div>

  