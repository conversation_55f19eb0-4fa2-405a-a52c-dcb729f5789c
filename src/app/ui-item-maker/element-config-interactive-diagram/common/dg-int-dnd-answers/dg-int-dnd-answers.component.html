<fieldset [disabled]="isReadOnly()" (change)="emitChange()">
  
  <!-- Default Answer Set: -->
  <ng-container *ngIf="isShowDefaultAnswerSet && defaultAnswerSet">
    <div class="flex-container" style="align-items: flex-start;">
      <div class="answer-set-block">
        <div *ngFor="let targetAnswer of defaultAnswerSet; let j = index; trackBy: trackByFn">
          <div style="display: flex;">
            <div>
              <!-- left -->
              <input 
                type="text" disabled 
                value="{{idToLabelMap.get(targetAnswer.targetId)}}" 
                class="target-name-input-label">
            </div>
            <div>
              <!-- right -->
              <div *ngIf="!isAllowGrouping" class="is-disabled">
                <ng-container *ngTemplateOutlet="optionDropdown; context: { obj: targetAnswer.optionIds, prop: 0 }"></ng-container>
              </div>
              <div *ngIf="isAllowGrouping" class="is-disabled">
                <div *ngFor="let optionId of targetAnswer.optionIds; let k = index">
                  <div style="display: flex;">
                    <ng-container *ngTemplateOutlet="optionDropdown; context: { obj: targetAnswer.optionIds, prop: k }"></ng-container>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div >
    </div>
  </ng-container>
  
  <div *ngFor="let answerSet of altAnswers; let i = index; trackBy: trackByFn" 
    class="flex-container" style="align-items: flex-start;">
    
    <div class="answer-set-block">
      <div *ngFor="let targetAnswer of answerSet; let j = index; trackBy: trackByFn">
        
        <div style="display: flex;">
          <div>
            <!-- left -->
            <input 
              type="text" disabled 
              value="{{idToLabelMap.get(targetAnswer.targetId)}}" 
              class="target-name-input-label">
          </div>
          <div>
            <!-- right -->
            <div *ngIf="!isAllowGrouping">
              <ng-container *ngTemplateOutlet="optionDropdown; context: { obj: targetAnswer.optionIds, prop: 0 }"></ng-container>
            </div>
            <div *ngIf="isAllowGrouping">
              <div *ngFor="let optionId of targetAnswer.optionIds; let k = index">
                <div style="display: flex;">
                  <ng-container *ngTemplateOutlet="optionDropdown; context: { obj: targetAnswer.optionIds, prop: k }"></ng-container>
                  <div 
                    (click)="elementEditDeleteOptionFromAnswerSet(i,j,k)" class="target-option-button"
                    style="padding: 0;"
                    [class.no-pointer-events]="isReadOnly()"
                  >
                    <i class="fas fa-trash" aria-hidden="true" style="font-size: smaller; padding: 0.4em;"></i>
                  </div>
                </div>
              </div>
            </div>
            
            <button 
              *ngIf="isAllowGrouping"
              (click)="elementEditAddOptionToAnswerSet(i,j)" 
              class="button is-small has-icon"
            >
              <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
              <span>Add Option</span>
            </button>
          </div>
        </div>
        
        
      </div>
    </div >
    
    <ng-container *ngIf="i > 0 || isAllowMultipleAnswer">
      <div (click)="elementEditDeleteAnswerSet(i)" class="target-option-button"
        [class.no-pointer-events]="isReadOnly()"
      >
        <i class="fas fa-trash" aria-hidden="true"></i>
      </div>
    </ng-container>
    
  </div>
  
  <ng-container *ngIf="isAllowMultipleAnswer">
    <button (click)="elementEditAddAnswerSet()" class="button is-small has-icon">
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>New Answer Set</span>
    </button>
  </ng-container>
  
  <div *ngIf="isAllowNonScoringTarget">
    <label style="font-weight: bold;">
      Non Scoring Targets:
    </label>
    <div *ngFor="let target of targets; let i = index">
      <div *ngIf="!isUnusedId(idToLabelMap.get(target.id))">
        <input type="checkbox"
          [(ngModel)]="target.isNonScoring"
          (change)="emitChange()"
        >
        <input 
          type="text" disabled 
          value="{{idToLabelMap.get(target.id)}}" 
          class="target-name-input-label">
      </div>
    </div>
  </div>
  
</fieldset>

<ng-template #optionContent let-index="index" let-id="id">
  <div *ngIf="index == 0">
    <div class="option-content-container">
      <span class="option-index">&nbsp;</span>
      <span class="option-text-content"><b>(EMPTY)</b></span>
    </div>
  </div>
  <div [ngSwitch]="categorizeOptionType(id)" *ngIf="index != 0">
    <div *ngSwitchCase="'text'" class="option-content-container">
      <span class="option-index">({{index}})</span>
      <span class="option-text-content">{{idToLabelMap.get(id)}}</span>
    </div>
    <div *ngSwitchCase="'math'" class="option-content-container">
      <span class="option-index">({{index}})</span>
      <katex-display [expression]="idToLabelMap.get(id)" [displayMode]="true"></katex-display>
    </div>
    <div *ngSwitchCase="'image'" class="option-content-container">
      <span class="option-index">({{index}})</span>
      <img [src]="idToUrlMap.get(id)" style="max-width: 6em; max-height: 6em;">
    </div>
  </div>
</ng-template>

<ng-template #optionDropdown let-obj="obj" let-prop="prop">
  <button 
    [class.error-bg-color]="isInvalidOptionId(obj[prop])"
    (click)="openOptionDropdown($event, obj, prop)"
    class="custom-dropdown-select-button"
  >
    <div style="display: flex; align-items: center; flex-direction: row; justify-content: space-between;">
      <div>
        <ng-container *ngTemplateOutlet="optionContent; context: { index: optionIdList.indexOf(obj[prop])+1, id: obj[prop] }"></ng-container>
      </div>
      <i style="margin-left: 0.5em;" class="fas fa-chevron-down" name="dropdown-icon-down"></i>
    </div>
  </button>
</ng-template>

<ng-template #optionDropdownOverlay let-obj="obj" let-prop="prop">
  <div class="custom-dropdown-container">
    <div class="custom-dropdown-option" *ngIf="isAllowEmptyTarget && !isAllowGrouping">
      <div (click)="selectOptionEmpty(obj, prop)">
        <ng-container *ngTemplateOutlet="optionContent; context: { index: 0, id: 0 }"></ng-container>
        <hr>
      </div>
    </div>
    <div class="custom-dropdown-option" *ngFor="let id of optionIdList; let i = index">
      <div (click)="selectOption(obj, prop, id)">
        <ng-container *ngTemplateOutlet="optionContent; context: { index: i+1, id: id }"></ng-container>
        <hr>
      </div>
    </div>
  </div>
</ng-template>