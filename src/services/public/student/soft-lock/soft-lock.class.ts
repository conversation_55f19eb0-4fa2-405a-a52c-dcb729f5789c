import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawReadSingle } from '../../../../util/db-raw';
import { isRecentlyUnpaused } from '../../../../util/time-control'
import { ITestAttempt } from '../../../db/schemas/test_attempts.schema'

interface Data {}

interface ServiceOptions {}

export class SoftLock implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {

    // const { test_attempt_ids } = <any>params?.query
    // if(test_attempt_ids){
    //   const softLockStatus = <any[]>await this.app
    //   .service('db/read/test-attempts')
    //   .db()
    //   .select('uid', 'section_index', 'question_index', 'is_submitted', 'is_paused', 'is_soft_lock_disabled')
    //   .where('id','in', test_attempt_ids)
  
    //   return softLockStatus;
    // }
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    const slStatus = await dbRawReadSingle(this.app, {id}, `
      select ta.is_paused
           , ta.is_soft_lock_disabled
           , ta.test_session_id
           , ts.is_soft_lock_enabled 
           , ts.is_soft_lock_disabled 
      from test_attempts ta
      join test_sessions ts 
        on ts.id = ta.test_session_id
      where ta.id = :id
    `)

    slStatus.is_test_session_softlock_disabled = 0; 
    if (slStatus.is_soft_lock_disabled == 1 || slStatus.is_soft_lock_enabled == 0){
      slStatus.is_test_session_softlock_disabled = 1 // does not seem to be used...
    }
    
    return slStatus
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    const test_attempt_id = id;
    const attemptRecord:ITestAttempt = await dbRawReadSingle(this.app, {test_attempt_id}, `
      select unpaused_on
      from test_attempts
      where id = :test_attempt_id
    `)

    if (!isRecentlyUnpaused(attemptRecord.unpaused_on)) {
      const payload: any = {
        is_paused : 1
      };
      
      const testAttempt = <any[]>await this.app
      .service('db/read/test-attempts')
      .patch(test_attempt_id, payload);
    }

    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}
