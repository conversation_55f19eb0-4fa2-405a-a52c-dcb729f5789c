export enum SchoolBoardView {
  TECH_READI      = 'tech_readiness',
  LOCKDOWN_INFO   = 'lockdown_info',
  SECURITY        = 'security',
  MONITORING      = 'monitoring',
  SESSIONS        = 'sessions',
  REPORTS         = "reports",
  SD_ACCOUNTS     = "sd_accounts",
  SCHOOLS         = "schools",
  PRINCIPAL_KITS  = "principal_kits",
  //CONTACT = 'contact',
}

export const wlViewEnablePrefix = 'SD_VIEW_ENABLE__';

export const SCHOOL_BOARD_VIEWS = [
  {
    id:SchoolBoardView.SD_ACCOUNTS, 
    caption: ('School Authority Accounts'), //'Technical Readiness',
    imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/clipboard/*************/clipboard.png',
    description: '', //'',
    hasIndicator: true,
  },
  {
    id:SchoolBoardView.TECH_READI, 
    caption: ('lbl_network_readiness'), //'Technical Readiness',
    imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/clipboard/*************/clipboard.png',
    description: ('txt_tech_readi'), //'',
    hasIndicator: true,
  },
  // {
  //   id:SchoolBoardView.CONTACT, 
  //   caption: ('lbl_contact_info'),
  //   imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/school/*************/school.png',
  //   description: ('txt_sa_classrooms_info_1'), //'You can review and manage the classes as they progress with their introductory materials and administration of the assessment.',
  // },
  {
    id:SchoolBoardView.SECURITY, 
    caption:('lbl_security_info'),
    imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
    description: ('txt_teachers'),
  },
  {
    id:SchoolBoardView.LOCKDOWN_INFO, 
    caption:('lbl_lockdown_info'), 
    imgUrl: '',
    description: (''),
  },
  {
    id:SchoolBoardView.MONITORING, 
    caption:('lbl_monitoring'),
    imgUrl: '',   // Keeping it empty for now.
    description: (''),  // Keeping it empty for now.
  },
  {
    id:SchoolBoardView.SESSIONS, 
    // We either want to change the label name of this or remove Sessions fully.
    caption:('lbl_monitoring'), //'Teachers',
    imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
    description: ('txt_teachers'), // '0 accounts registered. Use this view to send email invitations to teachers so that they can access their account.',
  },
  {
    id:SchoolBoardView.SCHOOLS, 
    // We either want to change the label name of this or remove Sessions fully.
    caption:('School Accounts'), //'Teachers',
    imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
    description: ('list of schools in your district'), // '0 accounts registered. Use this view to send email invitations to teachers so that they can access their account.',
  },
  {
    id:SchoolBoardView.PRINCIPAL_KITS, 
    // We either want to change the label name of this or remove Sessions fully.
    caption:('Principal Kits'), //'Teachers',
    imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
    description: ('principal kits completed by schools, by administration window'), // '0 accounts registered. Use this view to send email invitations to teachers so that they can access their account.',
  },
  {
    id:SchoolBoardView.REPORTS, 
    caption:('lbl_reports'), 
    imgUrl: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/work-from-home/*************/work-from-home.png',
    description: ('txt_teachers'), // '0 accounts registered. Use this view to send email invitations to teachers so that they can access their account.',
  }
]