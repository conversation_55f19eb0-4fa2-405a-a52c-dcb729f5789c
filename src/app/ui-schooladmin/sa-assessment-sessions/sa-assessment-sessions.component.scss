@import '../../../styles/pseudo-objects/pre-table-strip.scss';
@import '../../../styles/partials/_colors.scss';
.assessment-sessions-view {
  .sessions-table {
    td {
      &.studentId {
        a {
          text-decoration: underline;
          color: unset;
          font-weight: unset;
        }
      }
      &.status {
        text-transform: capitalize;
        .content {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          .fa-circle {
            font-size: 9px;
            margin-right: 7px;
            &.active {
              color: green;
            }
            &.pending {
              color: $red;
            }
            &.cancelled {
              color: lightgrey;
            }
          }
        }
      }
    }
  }
}

@import '../../../styles/partials/_modal.scss';
.custom-modal { 
  @extend %custom-modal;
  /* .modal-contents {
    width: 42em;
  } */
}

.new-assessment-session{
  font-size:1.3rem; 
  font-weight: 700; 
  text-transform: uppercase;
  margin-bottom: 1.5rem !important;
}