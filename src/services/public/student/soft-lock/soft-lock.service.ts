// Initializes the `public/student/soft-lock` service on path `/public/student/soft-lock`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { SoftLock } from './soft-lock.class';
import hooks from './soft-lock.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/student/soft-lock': SoftLock & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/student/soft-lock', new SoftLock(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/student/soft-lock');

  service.hooks(hooks);
}
