import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../declarations';
import * as Errors from '@feathersjs/errors';
import { AUTH_ERROR } from '../../../errors/auth';
import { IAuth } from '../../db/schemas/auths.schema';
import { getSysConstNumeric } from '../../../util/sys-const-numeric';

// const MAX_LOGIN_ATTEMPTS = 5;

interface Data {}
interface ICreateData{
  email: string
}
interface ServiceOptions {}

export class FailedLogin implements ServiceMethods<Data> 
{
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    return [];
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: ICreateData, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }
    const dbAuthTable = this.app.service('db/write/auths');   
    const authFindFieldQuery:Partial<IAuth> = { 
      email: data.email
    }; 
    const matchingAuthRecord = <any> await dbAuthTable.find({
      query: { 
        ... authFindFieldQuery
      },  
    });
    if (matchingAuthRecord.total === 0){
      return {};

    }

    if (matchingAuthRecord.total > 1){
      throw new Errors.GeneralError(AUTH_ERROR.MULTIPLE_AUTHS)
    }
    const retrieveAuthRecord = matchingAuthRecord.data[0];   
    if(retrieveAuthRecord.enabled === 0){
      throw new Errors.Forbidden(AUTH_ERROR.NOT_VERIFIED);
    }

    let MAX_LOGIN_ATTEMPTS = await getSysConstNumeric(this.app, "MAX_LOGIN_ATTEMPTS", true);
    if(!MAX_LOGIN_ATTEMPTS) MAX_LOGIN_ATTEMPTS = 5;
    
    if(retrieveAuthRecord.failed_login_attempts >= MAX_LOGIN_ATTEMPTS){
      throw new Errors.Forbidden(AUTH_ERROR.MAX_LOGIN_ATTEMPT);
    }

    const authPatchFieldQuery:Partial<IAuth> = { };

    //increment failed_login_attempts regardless of TOTP user or not
    authPatchFieldQuery.failed_login_attempts = retrieveAuthRecord.failed_login_attempts+1;
    
    await dbAuthTable.patch(retrieveAuthRecord.id, {
      ... authPatchFieldQuery
    });

    return retrieveAuthRecord;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}
