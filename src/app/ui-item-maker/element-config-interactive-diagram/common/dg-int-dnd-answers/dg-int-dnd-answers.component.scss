@import '../../element-config-interactive-diagram.component.scss';

.custom-dropdown-select-button {
  background: none;
  cursor: pointer;
  width: 100%;
  height: 100%;
  font-size: inherit;
  font-family: inherit;
  
  min-width: 10em;
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 0.5em;
}
.custom-dropdown-container {
  background-color: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  min-width: 10em;
  padding: 0.5em;
  max-height: 80vh;
  overflow-y: auto;
}
.custom-dropdown-option {
  cursor: pointer;
  background-color: white;
  &:hover {
    background-color: #f0f0f0f0;
  }
}

hr {
 margin: 0.5em 0;
}

.option-content-container {
  display: flex;
  align-items: flex-start;
}
.option-index {
  font-size: 0.8em;
  color: #aaa;
  margin-right: 1em;
}