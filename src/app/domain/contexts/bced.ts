import { API_ADDRESS_LOCAL } from "../../api/api-addr/l";
import { IContextData } from "./common";

export const BCED: IContextData = {
  id: 'BCED',
  // homepageRoute: '/en/general/login/false',
  homepageRoute: '/en/bced-landing/admin',
  brandName: 'BC Education eAssessment',
  logo: '',
  footer: null,
  apiAddress: (hostname: string) => {
    switch (hostname) {
      case 'localhost2':
      case 'localhost':
      case '26dc5ff374e3.ngrok.io':
        return API_ADDRESS_LOCAL;
        //return 'https://api-eassessment.vretta.com';
        // return 'https://eqao-api.vretta.com';
        return "https://bced-api.vretta.com"
      case 'bced.vretta.com': // field test
      case 'bced-qc.vretta.com': // field test
      case 'bced-qc-asmt-preview.vretta.com':
      case 'bced-asmt-preview.vretta.com':
        return "https://bced-qc-api.vretta.com"
      case 'eqao-to-bced.vretta.com':
      case 'd1909dsjz8m5d1.cloudfront.net':
        return 'https://eqao-to-bced-api.vretta.com'
      case 'bced-feat11.vretta.com':
      case 'bced-feat1.vretta.com':
      case 'bced.vretta.com': // field test
      case 'bced-feat2.vretta.com':
      case 'bced-feat1.vretta.com':
      default:
        return "https://bced-api.vretta.com"
      // return 'https://api-eassessment.vretta.com';
    }
  },
  siteFlags: {
    'INST_MNG_TT': true,
    'IS_BCED': true,
    'IS_CKEDITOR': true,
    'IS_TTS_SOMETIMES': true,
    'TEST_CTRL_REPORT_EXPLR': true,
    'TEST_CTRL_DATA_QUERY': true,
    'TEST_RUNNER_WIDTH_CONSTRAINT': false,
    'IS_BC_FIELD_TEST_2021': false,
    'QUESTION_DEBUG_LOGS': false,
    'IS_SUPPORT_SD_LOOKUP': true,
    'IS_TC_SUMM': false,
    'IS_TC_DATA': false,
    'IS_TWTAR_CRED': true,
    'IS_TWTAR_PIPELINE_EXCLUSION': true,
    'IS_TWTAR_USER_METAS': true,
    'IS_STU_SLUG_ROUTING': false,
    'IS_STU_SHOW_RESULTS': true,
    'IS_RESULTS_PAGE_AVAILABLE': true
  },
  siteText: {
    copyright: '', //  © Vretta Inc. 2021
    supportEmail: '<EMAIL>',
    supportPhone: '**************',
    login_admins: 'lbl_administrators',
    my_inst: 'My School',
    my_inst_admin: 'You are the **administrator** of this school.',
    my_inst_manage_acct: 'Manage Teachers+',
    lbl_sd: 'School District',
    // asmt_logo_en: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/bc-logo/*************/bc-logo.png',
    // asmt_logo_fr: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/bc-logo/*************/bc-logo.png',
    header_bg_color: '#003366',
    header_logo_en: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/21/authoring/bc-logo/*************/bc-logo.png',
    header_logo_fr: 'https://d3azfb2wuqle4e.cloudfront.net/user_uploads/38543/authoring/fr-BC_V_pos-v2/*************/fr-BC_V_pos-v2.png',
    student_ident: 'PEN',
    exemplar: 'exemplar',
    exemplars: 'exemplars',
    mark: 'mark',
    marked: 'marked',
    marker: 'marker',
    markers: 'markers',
    marking_chat: 'Scorings Chat',
    marking: 'marking',
    marks: 'marks',
    reliability: 'reliability',
    tech_support: 'tech_support_bc',
    TnR: 'T&R',
    txt_support_popup: 'tech_support_bc',
  },
  sdRoleTypes: [
    'schl_dist_it_admin',
    // 'school_district_curr',
    // 'schl_disct_curr_ele',
    // 'schl_disct_curr_sec'
  ],
  hosts: [
    '46734d1c3012.ngrok.io',
    // 'eassessment.vretta.com',
    'bced.vretta.com',
    'bced-qc.vretta.com',
    'bced-feat1.vretta.com',
    'bced-feat2.vretta.com',
    'eqao-to-bced.vretta.com',
    'localhost:4200',
    'localhost2:4200',
    '26dc5ff374e3.ngrok.io'
  ],
  defaultTimezone: 'America/Vancouver',
  defaultTimezoneAbbr: 'PST',
}
