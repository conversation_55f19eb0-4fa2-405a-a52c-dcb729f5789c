import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { ISelectionData } from 'src/app/ui-testrunner/element-render-template/model';

@Component({
  selector: 'template-selection',
  templateUrl: './template-selection.component.html',
  styleUrls: ['./template-selection.component.scss']
})
export class TemplateSelectionComponent implements OnInit {
  @Input() value: any;
  @Input() data: ISelectionData;
  @Output() onChange = new EventEmitter<any>();

  constructor(
    private editingDisabled: EditingDisabledService,
  ) { }

  ngOnInit(): void {
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  setValue(value: any) {
    this.onChange.emit(value);
  }

}
