<div class="page-container" [class.is-abed]="isABED()">
  <div class="header-container" [class.is-abed]="isABED()">
    <header [breadcrumbPath]="breadcrumb" [isLoginPage]="true"></header>
  </div>
  <div class="panel-container" [class.is-abed]="isABED()">
    <div>

      <div class="user-guide-title">
        <div class="user-guide-title-big">
          <tra slug="title_help_abed"></tra>
        </div>
        <div>
          <tra [slug]="getUserGuideSubtitleSlug()"></tra>
        </div>
        <!-- <div class="expand-button">
          <div class="button is-small" (click)="collapseAll()">
            <tra slug="Expand/Collapse All"></tra>
          </div>
        </div> -->
      </div>
      <div class="user-guide-links" *ngFor="let section of guideSections">
        <details [(open)]="sectionModels[section]">
          <summary>
            <h2 class="subtitle" style="display: inline-block">{{section || 'Other'}}</h2>
          </summary>

          <div class="columns">
            <div class="column" *ngIf="userGuidesBySection(section, AssetTypes.LINK).length > 0">
              <h3 class="subtitle">
                <tra slug="Resources"></tra>
              </h3>
              <ul>
                <li *ngFor="let guide of userGuidesBySection(section, AssetTypes.LINK)">
                  <a [href]="guide.file_url" target="_blank">
                    <tra [slug]="guide.caption"></tra>
                  </a>
                </li>
              </ul>
            </div>

            <div class="column" *ngIf="userGuidesBySection(section, AssetTypes.VIDEO).length > 0">
              <h3 class="subtitle">
                <tra slug="Videos"></tra>
              </h3>
              <ul>
                <li *ngFor="let guide of userGuidesBySection(section, AssetTypes.VIDEO)">
                  <a (click)="introVideoModalStart(guide.file_url)" target="_blank">
                    <tra [slug]="guide.caption"></tra>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </details>
      </div>
    </div>
  </div>
  <div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
      <div [ngSwitch]="cModal().type">
        <video width="840" height="460" controls autoplay>
          <source [src]="cmc().file_url" type="video/mp4">
        </video>
      </div>
      <modal-footer [pageModal]="pageModal"></modal-footer>
    </div>
  </div>
</div>
