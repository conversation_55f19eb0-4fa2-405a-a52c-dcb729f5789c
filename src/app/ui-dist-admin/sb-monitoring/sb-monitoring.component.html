<div>
    <da-test-window-filter (setTestWindowEvent)="setTestWindowFilter($event)">
    </da-test-window-filter>

    <!-- Trigger component reload by toggling `isWindowSet` -->
    <div *ngIf="isWindowSet">
      <menu-bar [menuTabs]="views" [tabIdInit]="selectedView" (change)="selectView($event)"></menu-bar>
      
      <div [ngSwitch]="selectedView">
        <div *ngSwitchCase="MonitoringView.PRINCIPAL_KIT">
          
        </div>
        <div *ngSwitchCase="MonitoringView.SCHOOL_LIST">
          <!-- TODO -->
        </div>
      </div>
    </div>
</div>
