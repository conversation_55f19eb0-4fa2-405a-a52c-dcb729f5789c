<div class="assessment-sessions-view">
    <div class="pre-table-strip">
      <div>
        <button class="button is-small has-icon is-success" (click)="newAdminModalStart()">
            <span class="icon"><i class="fas fa-plus-square"></i></span>
            <span><tra slug="btn_creat_a_new_account"></tra></span>
        </button>
        <button *ngIf="false" class="button is-small has-icon" [disabled]="!isAnySelected" (click)="editAdminModalStart()">
            <span class="icon"><i class="fas fa-edit"></i></span>
            <span><tra slug="Edit_Account_Access"></tra></span>
        </button>
        <button class="button is-small has-icon" [disabled]="!isAnySelected" (click)="revokeSelectedAdmins()">
            <span class="icon"><i class="fas fa-archive"></i></span>
            <span><tra slug="Revoke_Account_Access"></tra></span>
        </button>
      </div>
      <div>
        <button class="button is-small is-danger" (click)="notScholAdmin()">
            <span>Remove yourself from {{getSchoolDistrictRoleLabel()}}</span>
        </button>
    </div>
    </div>
    <paginator 
      [model]="schoolAdminsTable.getPaginatorCtrl()" 
      [page]="schoolAdminsTable.getPage()" 
      [numEntries]="schoolAdminsTable.numEntries()"
      (pageChange)="pageChanged()">
    </paginator>
    <div class="classrooms-table-container">
      <table style="overflow-x: scroll;" class="sessions-table table is-hoverable">
        <tr>
            <th style="width:3em;"> <table-row-selector [entry]="this" prop="isAllSelected" (toggle)="toggleSelectAll()"></table-row-selector> </th>
            <th class="flush"> <table-header id = "schoolAdmin"  [caption] = "getSchoolDistrictRoleLabel()" [ctrl] = "schoolAdminsTable" [isSortEnabled]="true"></table-header></th>
            <th class="flush"> <table-header id = "hasAccess"    caption = "sa_access_col_has_access"    [ctrl] = "schoolAdminsTable" [isSortEnabled]="true" [disableFilter]="true"></table-header></th>
            <th class="flush"> <table-header id = "startTime"    caption = "sa_access_col_times"    [ctrl] = "schoolAdminsTable" [isSortEnabled]="true" [disableFilter]="true"></table-header></th>
        </tr>
        <tr *ngFor="let admin of schoolAdminsTable.getCurrentPageData();">
            <td>
                <table-row-selector [entry]="admin" prop="__isSelected" (toggle)="checkSelection()"></table-row-selector>
            </td>
            <td>
                {{admin.name}}
                <div *ngIf="admin.isConfirmed" class="is-green" style="font-size:0.8em;">
                  {{admin.email}}
                </div>
                <div *ngIf="!admin.isConfirmed" class="is-red" style="font-size:0.8em;">
                  {{admin.invit_email}}
                </div>
            </td>
            <td>
                <span *ngIf="admin.isConfirmed">
                    <i  class="fa fa-check" style="margin-right:0.5em;"></i>
                    <tra slug="lbl_yes"></tra>
                </span>
                <span *ngIf="!admin.isConfirmed">
                    <i  class="fa fa-times" style="color: #f00; margin-right:0.5em;"></i>
                    <tra slug="lbl_no"></tra>
                    <!-- <button
                        class="button is-small  has-icon" 
                        style="margin-left: 0.5em;"
                        (click)="logInvitation(admin)" 
                        *ngIf="!admin.invit_email"
                    >
                      <span><tra slug="sa_admin_connect_email"></tra></span>
                    </button> -->
                    <button 
                        class="button is-small  has-icon" 
                        style="margin-left: 0.5em;" 
                        (click)="showInvitationLink(admin)"
                        *ngIf="false"
                    >
                        <!-- ngxClipboard 
                        [cbContent]="renderAdminInvitationToClipboard(admin)"
                        (cbOnSuccess)="onAdminInviteLinkCopy(admin)"
                        *ngIf="admin.invit_email" -->
                        <span class="icon">
                          <i class="fa fa-copy"></i>
                        </span>
                        <span><tra slug="invite_link"></tra></span>
                    </button>
                </span>
            </td>
            <td> 
                <div *ngIf="admin.email || admin.invit_email" style="display:flex; flex-direction: row; justify-content: space-between;">
                    <div>
                        {{admin.startTime_formatted || admin.created_on_formatted}} 
                        <div *ngIf="!admin.isConfirmed" style="font-size:0.8em;">
                            <tra slug="abed_expires"></tra>: {{admin.endTime_formatted || admin.expire_on_formatted}} 
                        </div>
                    </div>
                    <div  *ngIf="!admin.isConfirmed && invitationExpired(admin.expire_on)" style="display:flex; flex-direction: center; align-items: center;">
                        <button class="button is-small extend" (click)="extendInvitation(admin)"><tra slug="sa_school_admin_invite_extend"></tra></button>
                    </div>
                </div>
            </td>
        </tr>
      </table>
    </div>
  </div>
  
  <div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
        <div [ngSwitch]="cModal().type">
          <div *ngSwitchCase="AdminAccountsModal.NEW" style="width: 30em; ">
            <h3>New {{getSchoolDistrictRoleLabel()}} Account</h3>
            <sb-modal-admin-accounts [savePayload]="cModal().config" saveProp="payload"></sb-modal-admin-accounts>
          </div>
          <div *ngSwitchCase="AdminAccountsModal.EDIT" style="width: 30em; ">
            <h3><tra-md slug="sa_session_edit_admin"></tra-md></h3>
            <sb-modal-admin-accounts [admins]="cModal().config.admins" [savePayload]="cModal().config" saveProp="payload" [isEdit]="true" (setSelectAdmin)="setSelectAdmin($event)"></sb-modal-admin-accounts>
          </div>
        </div>
        <modal-footer [pageModal]="pageModal" ></modal-footer>
    </div>
  </div>