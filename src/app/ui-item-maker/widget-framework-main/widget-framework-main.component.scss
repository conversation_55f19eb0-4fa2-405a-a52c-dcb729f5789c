.black-text{
    color: black;
}
.tab-container {
    width: 300px;
    .tab {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px;
      background-color: #f1f1f1;
      border: 1px solid #ddd;
      cursor: pointer;
      transition: background-color 0.3s;
  
      &:hover {
        background-color: #e1e1e1;
      }
  
      i {
        margin-right: 10px;
      }
    }  
    .content {
      padding: 1px;
      border-left: 1px solid #ddd;
      border-right: 1px solid #ddd;
      &:last-child{
        border-bottom: 1px solid #ddd;
      }
      ul {
        li {
          margin-bottom: 5px;
        }
      }
    }
}
.disabled {
    pointer-events: none;
    opacity: 0.5;
} 