import { Component, Input, Output, EventEmitter, OnInit, Renderer2 } from '@angular/core';
import { FormControl } from '@angular/forms';
import { CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { IDgIntHomeConfig, homeLabelPositionOptions, HOME_LABEL_POSITION, LAYOUT_HOME_POSITION, HOME_CONFIG_LAYOUT, homeConfigLayoutFixedCapacity, isHomeConfigLayoutFixedPosition, isHomeConfigLayoutAllowGroupLabel } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common';
import { bindFormControls } from 'src/app/ui-item-maker/services/data-bind';
import { EditingDisabledService } from 'src/app/ui-item-maker/editing-disabled.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from 'src/app/ui-testrunner/element-render-interactive-diagram/model';
import { ElementType } from 'src/app/ui-testrunner/models';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { clientPos_to_svgPos } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-freeform';
import { IDgIntConstructionElement } from 'src/app/ui-testrunner/element-render-interactive-diagram/common';
import { shuffle } from 'cypress/types/lodash/ts3.1';

enum Modal {
  ADVANCED_HOME_LAYOUT_EDITOR = 'advanced_home_layout_editor',
  ADVANCED_HOME_LAYOUT_JSON = 'advanced_home_layout_json'
}

@Component({
  selector: 'dg-int-dnd-home-layout',
  templateUrl: './dg-int-dnd-home-layout.component.html',
  styleUrls: ['./dg-int-dnd-home-layout.component.scss']
})
export class DgIntDndHomeLayoutComponent implements OnInit {
  @Input() element: IDgIntHomeConfig;
  @Input() parentElement: any;
  @Input() idToLabelMap: Map<string, string>;
  @Input() idToUrlMap: Map<string, string> | undefined;
  @Input() idIsMathSet: Set<string> = new Set();
  @Output() onChange = new EventEmitter();
  @Output() onResetState = new EventEmitter();
  @Output() onOpenAdvancedEditor = new EventEmitter();
  
  Modal = Modal;
  pageModal: PageModalController;
  isHomeConfigLayoutFixedPosition = isHomeConfigLayoutFixedPosition;
  isHomeConfigLayoutAllowGroupLabel = isHomeConfigLayoutAllowGroupLabel;
  advancedHomeLayoutJSON: string = "";
  
  homeLabelPositionOptions = homeLabelPositionOptions;
  homePositionForm = new FormControl(LAYOUT_HOME_POSITION.BOTTOM)
  homePositionOptions = [
    {id: LAYOUT_HOME_POSITION.LEFT, caption:'Left'},
    {id: LAYOUT_HOME_POSITION.RIGHT, caption:'Right'},
    {id: LAYOUT_HOME_POSITION.TOP, caption:'Top'},
    {id: LAYOUT_HOME_POSITION.BOTTOM, caption:'Bottom'},
    {id: LAYOUT_HOME_POSITION.LEFT_TOP, caption:'Left (top aligned)'},
    {id: LAYOUT_HOME_POSITION.RIGHT_TOP, caption:'Right (top aligned)'},
    {id: LAYOUT_HOME_POSITION.TOP_LEFT, caption:'Top (left aligned)'},
    {id: LAYOUT_HOME_POSITION.BOTTOM_LEFT, caption:'Bottom (left aligned)'},
  ]
  homeLayoutOptions = [
    {id: HOME_CONFIG_LAYOUT.DEFAULT, caption:'Default'},
    {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT, caption:'Default (Individual)'},
    {id: HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN, caption:'Column'},
    {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN, caption:'Column (Individual)'},
    {id: HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM, caption:'Top and Bottom'},
    {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_TOP_AND_BOTTOM, caption:'Top and Bottom (Individual)'},
    {id: HOME_CONFIG_LAYOUT.FREEFORM, caption:'Freeform'},
    {id: HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED, caption:'Advanced'},
    // {id: HOME_CONFIG_LAYOUT.ROW, caption:'[TBD] Row'},
    // {id: HOME_CONFIG_LAYOUT.INDIVIDUAL_ROW, caption:'[TBD] Row (Individual)'},
  ]
  
  constructor(
    private editingDisabled: EditingDisabledService,
    private pageModalService: PageModalService,
    private sharedObjectMap: SharedObjectMapService,
    private renderer: Renderer2,
  ) { }
  
  cModal() {
    return this.pageModal.getCurrentModal();
  }
  cmc() { return this.cModal().config}
  
  ngOnInit() {
    this.pageModal = this.pageModalService.defineNewPageModal();
    bindFormControls(this.parentElement.config, [
      {f: this.homePositionForm, p:'homePosition'},
    ]);
  }
  
  onLayoutChange() {
    const layout = this.element.layout;
    if (layout != HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED) delete this.element.advancedLayoutConfig;
    if (layout != HOME_CONFIG_LAYOUT.FREEFORM) delete this.element.freeformLayoutConfig;
    if (layout == HOME_CONFIG_LAYOUT.FREEFORM) {
      this.ensureFreeformConfig();
    }
    this.emitChange();
  }
  
  isFixedGroup(): boolean {
    const capacity = homeConfigLayoutFixedCapacity(this.element.layout);
    return capacity != undefined;
  }
  
  isValidLayout() : boolean {
    return this.element.layout == HOME_CONFIG_LAYOUT.DEFAULT 
      || this.element.layout == HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN
      || this.element.layout == HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT
      || this.element.layout == HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN
      || this.element.layout == HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM
      || this.element.layout == HOME_CONFIG_LAYOUT.INDIVIDUAL_TOP_AND_BOTTOM
      || this.element.layout == HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED
      || this.element.layout == HOME_CONFIG_LAYOUT.FREEFORM
  }
  
  emitChange() {
    this.onChange.emit();
  }
  
  trackByFn(index: number, item: any) {
    return index;
  }
  
  dropHomeLayout(event: CdkDragDrop<number[]>){
    this.onResetState.emit();
    if (event.previousContainer === event.container){
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(event.previousContainer.data, event.container.data, event.previousIndex, event.currentIndex);
    }
    this.emitChange();
  }
  
  elementEditAddHomeGroup(){
    this.element.element.push([]);
    this.onChange.emit();
  }
  elementEditDeleteHomeGroup(index: number) {
    const confirm = window.confirm("Are you sure you want to delete this group?");
    if (!confirm) return;
    this.element.element.splice(index, 1);
    if (this.element.groupHomeLabels.length > index) this.element.groupHomeLabels.splice(index, 1);
    this.onChange.emit();
  }
  
  getOptionLabelFromId(id: string) : string {
    return this.idToLabelMap.get(id);
  }
  getOptionImageURLFromId(id: string): string {
    if (this.idToUrlMap == undefined) return undefined;
    return this.idToUrlMap.get(id);
  }
  
  isEmptyHomeGroup(index: number) : boolean {
    const target = this.element.element[index];
    return target.length == 0;
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  openHomeLayoutEditorModal() {
    // prevent the page svg to be rerendered while the modal is open
    this.pageModal.newModal({
      type: Modal.ADVANCED_HOME_LAYOUT_EDITOR, 
      config: {}, 
      finish: this.closeHomeLayoutEditorModal, 
      cancel: this.closeHomeLayoutEditorModal,
    });
  }
  closeHomeLayoutEditorModal = async (_config : any) =>  {
    this.emitChange();
  }
  openHomeLayoutJSONModal() {
    this.advancedHomeLayoutJSON = JSON.stringify(this.element.advancedLayoutConfig);
    this.pageModal.newModal({
      type: Modal.ADVANCED_HOME_LAYOUT_JSON, 
      confirmationCaption: "Import",
      config: {}, 
      finish: this.closeHomeLayoutJSONModal, 
    });
  }
  closeHomeLayoutJSONModal = async (_config : any) =>  {
    try {
      const parsedElement = JSON.parse(this.advancedHomeLayoutJSON);
      this.element.advancedLayoutConfig = parsedElement;
    } catch(e) {
      console.error(e);
    }
  }
  
  shuffleElements() {
    for (let group of this.element.element) {
      this.fisherYatesShuffleInPlace(group);
    }
    this.emitChange();
  }
  fisherYatesShuffleInPlace<T>(array: T[]): void {
      for (let i = array.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [array[i], array[j]] = [array[j], array[i]];
      }
  }
  
  // -------------------- freeform --------------------
  
  private listener: Function;
  private removeGuidebox: Function;
  movingHomeIndex = undefined;
  positionPrecision = 2;
  ensureFreeformConfig() {
    if (this.element.freeformLayoutConfig == undefined) 
      this.element.freeformLayoutConfig = {};
    if (this.element.freeformLayoutConfig.positionMap == undefined) 
      this.element.freeformLayoutConfig.positionMap = {};
    if (this.element.freeformLayoutConfig.image == undefined) 
      this.element.freeformLayoutConfig.image = createDefaultElement(ElementType.IMAGE);
  }
  getFreeformPositionObject(id: string): {x: number, y: number} {
    const pos = this.element.freeformLayoutConfig.positionMap[id];
    if (pos == undefined) {
      this.element.freeformLayoutConfig.positionMap[id] = {x: 0, y: 0};
    }
    return pos;
  }
  requestMoveFreeformHome(ev: MouseEvent, optionIndex: number) {
    const buttonElement = ev.target;
    if (this.listener) this.listener();
    
    const optionSize = this.getFreeformOptionSize(optionIndex);
    this.createGuideBox(optionSize[0], optionSize[1]);
    this.movingHomeIndex = optionIndex;
    
    let cancelListener: CallableFunction;
    let cleanup = () => {
      cancelListener();
      if (this.removeGuidebox) this.removeGuidebox();
      if (this.listener) this.listener();
      this.movingHomeIndex = undefined;
    }
    
    cancelListener  = this.renderer.listen('document', 'contextmenu', (event) => {
      event.preventDefault();
      cleanup();
    });
    this.listener = this.renderer.listen('document', 'click', (event) => {
      if (event.target == buttonElement) return;
      cleanup();
      this.elementEditMoveFreeformPosition(event, optionIndex);
    });
  }
  getFreeformOptionSize(optionIndex: number) : [number,number] {
    const dgSharedObject = this.sharedObjectMap.get(this.parentElement) as IDgIntSharedObject;
    const svgElement = dgSharedObject?.svgElement;
    if (!svgElement) return [0,0];
    
    const svgDnD = svgElement.querySelector("svg[meta=dnd_svg]");
    if (!svgDnD) return [0,0];
    
    let containerSvg = svgDnD.querySelectorAll(".diagramatics-dnd-draggable");
    if (containerSvg.length == 0) return [0,0];
    const rect = containerSvg[optionIndex].getBoundingClientRect();
    return [rect.width, rect.height];
  }
  createGuideBox(width: number, height: number) {
    if (this.removeGuidebox) this.removeGuidebox();
    
    let guideBox: HTMLDivElement = this.renderer.createElement('div');
    guideBox.style.width = width + 'px';
    guideBox.style.height = height + 'px';
    guideBox.style.backgroundColor = 'rgba(0, 0, 0, 0)';
    guideBox.style.left = '0';
    guideBox.style.top = '0';
    guideBox.style.transform = 'translate(-50%, -50%)';
    guideBox.style.visibility = 'hidden';
    guideBox.style.position = 'absolute';
    guideBox.style.zIndex = '2000';
    guideBox.style.pointerEvents = 'none';
    guideBox.style.border = '1px dashed black';
    document.body.appendChild(guideBox);
    
    // guideBox will follow the mouse
    let listener = this.renderer.listen('document', 'mousemove', (event) => {
      requestAnimationFrame(() => {
        guideBox.style.visibility = 'visible';
        guideBox.style.left = event.clientX + 'px';
        guideBox.style.top = event.clientY + 'px';
      });
    });
    let removeGuidebox = () => {
      guideBox.remove();
      listener();
    };
    this.removeGuidebox = removeGuidebox;
  }
  elementEditMoveFreeformPosition(ev: MouseEvent, optionIndex: number) {
    const dgSharedObject = this.sharedObjectMap.get(this.parentElement) as IDgIntSharedObject;
    const svgElement = dgSharedObject?.svgElement?.children?.[0];
    if (!svgElement) return;
    
    const clientPos = {
      x : ev.clientX,
      y : ev.clientY
    };
    const svgPos = clientPos_to_svgPos(clientPos, svgElement as SVGSVGElement);
    const homePositionOffset = this.element.freeformLayoutConfig._homePositionOffset;
    
    const optionId = this.element.element[0][optionIndex];
    const positionObject = this.getFreeformPositionObject(optionId);
    const roundedX = this.roundTo(svgPos.x - homePositionOffset.x, this.positionPrecision);
    const roundedY = this.roundTo(svgPos.y - homePositionOffset.y, this.positionPrecision);
    positionObject.x = roundedX;
    positionObject.y = roundedY;
    this.emitChange();
  }
  roundTo(value: number, precision: number) {
    return Math.round(value * Math.pow(10, precision)) / Math.pow(10, precision);
  }
  
}
