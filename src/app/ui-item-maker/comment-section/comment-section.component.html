<twiddle [state]="itemBankCtrl.showComments" [caption]="lang.tra('auth_show_comments') + ' ('+fromHereCommentsCount+')'"></twiddle>
<div style="padding-left:1em;" *ngIf="itemBankCtrl.showComments.value">
    <!-- <div class="section-header"><tra slug="ie_comments"></tra></div> -->

    <twiddle icon="fa-thumbtack" [state]="pinnedCommentsTwiddle" [caption]="lang.tra('ie_comment_pinned')+' ('+pinnedCommentsCount+')'"></twiddle>
    <div [style.display]="pinnedCommentsTwiddle.value ? 'initial' : 'none'">
      <element-notes (refreshCount)="pinnedCommentsHereCount($event)" [refreshNotesTrigger]="refreshPinnedTrigger" [newCommentDisabled]="true" [itemId]="itemBankCtrl.currentQuestion.id" [groupId]="itemBankCtrl.groupId" [singleGroupId]="itemBankCtrl.single_groupId" [getPinned]="true" (assignHandler)="assignButtonHandler($event)" (refreshPinned)="refreshPinned()"></element-notes>
    </div>
    <ng-container  *ngIf="itemBankCtrl.getParentId(itemBankCtrl.currentQuestion)" >
      <twiddle [icon]="ItemTypeDefs['sequence'].icon" [state]="sequenceCommentsTwiddle" [caption]="lang.tra('ie_comment_from_seq') + ' ('+seqCommentsCount+')'"></twiddle>
      <div [style.display]="sequenceCommentsTwiddle.value ? 'initial' : 'none'">
        <element-notes (refreshCount)="updateFromSeqCount($event)" [refreshNotesTrigger]="refreshPinnedTrigger" (refreshPinned)="refreshPinned()" [itemId]="itemBankCtrl.getParentId(itemBankCtrl.currentQuestion)" [groupId]="itemBankCtrl.groupId" [singleGroupId]="itemBankCtrl.single_groupId" [newCommentDisabled]="true" (assignHandler)="assignButtonHandler($event)"></element-notes>
      </div>
    </ng-container>
    <twiddle [icon]="ItemTypeDefs['item'].icon" [state]="commentsTwiddle" [caption]="lang.tra('auth_from_here')+' ('+fromHereCommentsCount+')'"></twiddle>
    <element-notes (refreshCount)="updateFromHereCount($event)" [refreshNotesTrigger]="refreshPinnedTrigger" (refreshPinned)="refreshPinned()" *ngIf="commentsTwiddle.value" [itemId]="itemBankCtrl.currentQuestion.id"  [groupId]="itemBankCtrl.groupId" [singleGroupId]="itemBankCtrl.single_groupId" [itemBankCtrl]="itemBankCtrl" (assignHandler)="assignButtonHandler($event)"></element-notes>
  </div>