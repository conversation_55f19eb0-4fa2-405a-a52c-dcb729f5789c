import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';

var QRCode = require('qrcode');

@Component({
  selector: 'mfa-setup',
  templateUrl: './mfa-setup.component.html',
  styleUrls: ['./mfa-setup.component.scss']
})

export class MfaSetupComponent implements OnInit {

  constructor(    
    private auth: AuthService,
    private routes : RoutesService,
    public lang:LangService,
    private router:Router,
    ) 
  {

  }

  //variable list
  public readyToAuthenticateTxt = this.lang.tra("mfa_authenticate_btn");
  public QRCodeImageURL: string = "";
  public QRCodeImageIsDisplaying: boolean = false;

  ngOnInit(): void 
  {
    this.registerUserForMFA();
  }

  public async registerUserForMFA()
  {    
    //results.otpauthURL will always contain the URL with the TOTP secret needed for QR code generation,
    //regardless of whether the user is an existing TOTP user or not
    let results = await this.auth.apiPatch
    (
      this.routes.AUTH_REGISTER_MFA,
      null,
      {
        email: this.auth.getCredentials()[0]
      }
    )

    //console.log(results);
    try 
    {
      this.QRCodeImageURL = await QRCode.toDataURL(results.otpauthURL);
      // console.log(results.otpauthURL);
      this.QRCodeImageIsDisplaying = true;
      results = null; //ensure private user information is nulled instantly after used (no leakage)
    } 
    catch (err) 
    {
      console.error(err);
    }
  }

  public rerouteUserToAuthenticate()
  {
    const authenticateRouterLink:string = `/${this.lang.c()}/general/authenticate`;
    this.router.navigateByUrl(authenticateRouterLink);
  }
}