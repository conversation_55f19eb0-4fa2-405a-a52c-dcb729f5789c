import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadSingle } from '../../../../util/db-raw';
import { SQL_STU_COVER_PAGE } from '../session/model/sql';
import { IAsmtCoverPage } from './model/types';



interface Data {}

interface ServiceOptions {}

export class AsmtCoverPage implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }
    
  async get (id: Id, params?: Params): Promise<Data> {
    if (params && params.query) {
      const {school_class_id, uid} = params.query;
      const asmtCoverPage:IAsmtCoverPage = await dbRawReadSingle(this.app, {school_class_id, uid}, SQL_STU_COVER_PAGE)
      if (!asmtCoverPage) {
        return {}
      }
      return asmtCoverPage;
    }
    throw new Errors.BadRequest();
  }
  
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

}
