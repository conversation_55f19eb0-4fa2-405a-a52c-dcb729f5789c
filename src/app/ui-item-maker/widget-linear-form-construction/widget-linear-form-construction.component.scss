.section-question-navigation {
  flex-grow: 0;
  width: 30em;
  padding: 1em;
  border-top: 1px solid #ddd;
  border-right: 1px solid #ddd;
  max-height: 90vh;
  overflow: auto;

  .config-container {
    .flex-col {
      display: flex;
      flex-direction: column;
    }

    .flex-row {
      display: flex;
      flex-direction: row;

      &.center {
        align-items: center;
        justify-content: space-between;
        padding-bottom: .5em;
      }
    }

    .control {
      border-bottom: 1px solid #ddd;
      padding: .5em 0;
    }

    .input, .select {
      margin: 0;

      &.ng-invalid {
        border-color: #f00;
      }
    }

    .item-label {
      margin-bottom: .5em;
      > button {
        margin-left: .5em;
      }
    }
  }
}

.is-info {
  &:hover {
    background-color: #4388b7;
  }
}