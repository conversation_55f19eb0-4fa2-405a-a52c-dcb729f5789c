import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';

@Component({
  selector: 'sa-modal-add-invigilators',
  templateUrl: './sa-modal-add-invigilators.component.html',
  styleUrls: ['./sa-modal-add-invigilators.component.scss']
})
export class SaModalAddInvigilatorsComponent implements OnInit {

  @Input() availableTeachers: [];
  @Output() teacherChange = new EventEmitter();
  @Output() closeInvigilatorModal = new EventEmitter();

  constructor(
    public whiteLabelService: WhitelabelService,
  ) { }

  ngOnInit(): void {
  }

  toggleInvigilator(invigilator){
    invigilator.selected = !invigilator.selected;
    this.teacherChange.emit(invigilator);
  }

  closeModal(){
    this.closeInvigilatorModal.emit();
  }

  isABED(){
    return this.whiteLabelService.isABED();
  }
}
