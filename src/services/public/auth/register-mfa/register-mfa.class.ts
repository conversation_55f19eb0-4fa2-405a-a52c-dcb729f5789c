import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";
import {dbRawRead, dbRawWrite} from '../../../../util/db-raw';
import { IAuth } from '../../../db/schemas/auths.schema';
import { AUTH_ERROR, AUTH_REG_ERROR } from '../../../../errors/auth';

const speakeasy = require('speakeasy');

interface Data
{
  email: string;
}

interface MFAData
{
  [key: string]: boolean;
}

interface ServiceOptions {}

export class RegisterMfa implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<any | Paginated<any>>
  // use find function in this class solely to determine if a user requires registration of MFA or not
  {
    if (!params || !params.query)
    {
      throw new Errors.BadRequest('REQ_PARAMS_MISS');
    }

    const dbAuthTableRead = this.app.service('db/read/auths');

    const authFindFieldQuery:Partial<IAuth> =
    {
      email: params.query.email,
    }
    const matchingAuthRecord = <any> await dbAuthTableRead.find({
      query:
      {
        ... authFindFieldQuery
      }
    });

    if (matchingAuthRecord.total === 0)
    {
      throw new Errors.NotFound(AUTH_ERROR.NOT_FOUND);
    }

    else if (matchingAuthRecord.total > 1)
    {
      throw new Errors.GeneralError(AUTH_ERROR.MULTIPLE_AUTHS);
    }

    const retrievedAuthRecord = matchingAuthRecord.data[0];

    const numRolesRequiringMFA = await dbRawRead(this.app, [retrievedAuthRecord.uid], `
    SELECT count(mfa_requirement.rm) as num_roles_requiring_mfa
    FROM ( SELECT distinct urt.role_type, urt.requires_mfa as rm
          FROM mpt_dev.u_role_types as urt, mpt_dev.user_roles as ur
          WHERE urt.role_type = ur.role_type and ur.uid = ?
         ) as mfa_requirement
    WHERE mfa_requirement.rm = true;
      `);

    //numRolesRequiringMFA[0].num_roles_requiring_mfa // typeof is number
    let response: MFAData = {isFirstLoginWithTotp: retrievedAuthRecord.is_first_login_with_totp};
    (numRolesRequiringMFA[0].num_roles_requiring_mfa >= 1) ? response.userRequiresMFA = true : response.userRequiresMFA = false;
    return response;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data>
  {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data>
  {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data>
  {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<any>
  {
    const dbAuthTableRead = this.app.service('db/read/auths');
    //const dbAuthTableWrite = this.app.service('db/write/auths');
    const authFindFieldQuery:Partial<IAuth> =
    {
      email: data.email,
    }
    const matchingAuthRecord = <any> await dbAuthTableRead.find({
      query:
      {
        ... authFindFieldQuery
      }
    });

    if (matchingAuthRecord.total === 0)
    {
      throw new Errors.NotFound(AUTH_ERROR.NOT_FOUND);
    }

    else if (matchingAuthRecord.total > 1)
    {
      throw new Errors.GeneralError(AUTH_ERROR.MULTIPLE_AUTHS);
    }

    // throw error for already existing MFA user

    const retrievedAuthRecord = matchingAuthRecord.data[0];

    //retrievedUserRecord guranteed to have exactly 1 record found because of the above error checking
    const retrievedUserRecord = await dbRawRead(this.app, [retrievedAuthRecord.uid], `
    SELECT *
    FROM mpt_dev.users as u
    WHERE u.id = ?
    ;`);

    if (retrievedUserRecord[0].is_totp_user == 1)
    {
      // the user is already a TOTP user; just form their otpauth_url from their existing secret and return it
      let existingOtpauthURL = "otpauth://totp/SecretKey?secret=" + retrievedAuthRecord.totp_secret;
      return {otpauthURL: existingOtpauthURL};

      // throw new Errors.GeneralError(AUTH_REG_ERROR.ALREADY_TOTP_USER);
    }

    // generates object with keys "ascii", "hex", "base32", and "otpauth_url"
    // use base32 for authenticator applications
    const userSecret = speakeasy.generateSecret();

    // update auths table
    await dbRawWrite(this.app, [userSecret.base32, retrievedAuthRecord.id], `
    UPDATE mpt_dev.auths as a
    SET
      a.totp_secret=?,
      a.is_first_login_with_totp=true
    WHERE a.id = ?
    ;`);

    //update user table
    await dbRawWrite(this.app, [retrievedAuthRecord.uid], `
    UPDATE mpt_dev.users as u
    SET u.is_totp_user=true
    WHERE u.id = ?
    ;`);

    return {otpauthURL: userSecret.otpauth_url};
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data>
  {
    throw new Errors.MethodNotAllowed();
  }
}
