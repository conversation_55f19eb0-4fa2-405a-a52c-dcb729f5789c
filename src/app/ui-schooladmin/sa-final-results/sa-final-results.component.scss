@import "../../../styles/page-types/standard.scss";
@import "../../../styles/page-types/landing.scss";
@import "../../../styles/partials/_media.scss";
@import "../../../styles/partials/_colors.scss";
@import "../../../styles/pseudo-objects/pre-table-strip.scss";

.page-body {
  @extend %page-body;
  background-color: #f1f1f1;
  .test-window-tag {
    font-family: Roboto;
    font-style: normal;
    font-weight: normal;
    font-size: 14px;
    line-height: 16px;
    color: #000000;
    span {
      color: red;
    }
  }
}

.sa-final-results {
  background-color: white;
  padding: 30px;

  .sa-final-results-intro {
    font-family: Roboto;
    font-style: normal;
    font-weight: normal;
    font-size: 17px;
    line-height: 20px;

    color: #000000;
  }

  table {
    margin-top: 22px;
    td {
      vertical-align: middle;
      border: 1px solid #000000;

      &:first-child {
        width: 346px;
      }

      &:nth-child(2) {
        width: 504px;
      }
      &:nth-child(3) {
        width: 163px;
      }
    }
    tr {
      &.report-row {
        td {
          font-family: Roboto;
          font-style: normal;
          font-weight: normal;
          font-size: 17px;
          line-height: 20px;

          color: #000000;

          a {
            font-family: Roboto;
            font-style: normal;
            font-weight: bold;
            font-size: 17px;
            line-height: 20px;
            /* identical to box height */

            text-decoration-line: underline;

            color: #176ff3;
          }
        }
      }
      &.header-row {
        td {
          font-family: Roboto;
          font-style: normal;
          font-weight: bold;
          font-size: 17px;
          line-height: 20px;
          /* identical to box height */

          color: #000000;
        }
      }
    }
  }

  .export-button {
    cursor: pointer;
    background: #f5f5f5;
    border: 1px solid #c4c4c4;
    border-radius: 4px;
    height: 25px;
    radius: 4px;
    .title {
      font-style: normal;
      font-weight: 500;
      font-size: 13px;
      line-height: 15px;
      text-align: center;
      color: #3e3e3e;
    }
    .icon {
      width: 12px;
      height: 13px;
      position: relative;
      top: 2px;
      margin-right: 6px;
      background-image: url("https://d3azfb2wuqle4e.cloudfront.net/user_uploads/6388/authoring/fsa_accounts_export_button/*************/fsa_accounts_export_button.svg");
    }
  }
}

// .final-results {
//   .school-year-selection {
//     display: flex;
//     background-color: white;
//     padding: 29px 14px;
//   }
//   .final-results-icon {
//     margin-right: 20px;
//     img {
//       width: 3em;
//       flex-grow: 0;
//     }
//   }

//   .final-results-content {
//     > div {
//       &:first-of-type {
//         font-family: Roboto;
//         font-style: normal;
//         font-weight: 500;
//         font-size: 18px;
//         line-height: 21px;
//         /* identical to box height */
//         margin-bottom: 6px;
//         color: #000000;
//       }
//       &:nth-of-type(2) {
//         font-family: Roboto;
//         font-style: normal;
//         font-weight: normal;
//         font-size: 14px;
//         line-height: 16px;
//         margin-bottom: 6px;
//         color: #000000;
//       }
//     }

//     select {
//       height: 27px;
//       font-family: Roboto;
//       font-style: normal;
//       font-weight: normal;
//       font-size: 14px;
//       line-height: 16px;
//       min-width: 220px;
//       color: #000000;
//     }

//     .test-window-link {
//       font-family: Roboto;
//       font-style: normal;
//       font-weight: normal;
//       font-size: 14px;
//       line-height: 16px;
//       text-decoration-line: underline;
//       margin-top: 21px;
//       color: #0085ff;
//       cursor: pointer;

//       &:first-of-type {
//         margin-top: 35px;
//       }

//       &.disabled {
//         color: gray;
//         cursor: default;
//       }
//     }
//   }
// }

// .final-results-header {
//   .final-results-title {
//     display: flex;
//     align-items: center;

//     .final-results-icon {
//       img {
//         width: 75px;
//         height: 75px;
//       }
//     }

//     > div:nth-of-type(2) {
//       font-family: Roboto;
//       font-style: normal;
//       font-weight: 500;
//       font-size: 18px;
//       line-height: 21px;
//       margin-left: 10px;
//       color: #000000;
//     }
//   }
//   .final-results-intro {
//     max-width: 800px;
//     font-family: Roboto;
//     font-style: normal;
//     font-weight: normal;
//     font-size: 14px;
//     line-height: 16px;
//     margin-top: 14px;
//     color: #000000;
//   }
// }

// .final-results-tabs {
//   margin-top: 25px;
//   border-bottom: 1px solid #a9a9a9;
//   display: flex;
//   margin-bottom: 19px;
//   width: 1000px;

//   .final-results-tab {
//     // height: 28px;
//     margin-right: 25px;
//     font-family: Roboto;
//     font-style: normal;
//     font-weight: 500;
//     font-size: 14px;
//     line-height: 21px;
//     cursor: pointer;

//     text-align: left;

//     color: #4b4b4b;

//     &.selected {
//       border-bottom: 3px solid #0085ff;
//     }

//     &:first-of-type {
//       width: 180px;
//     }
//     &:nth-of-type(2) {
//       width: 134px;
//     }
//     &:nth-of-type(3) {
//       width: 122px;
//     }
//     &:nth-of-type(4) {
//       width: 130px;
//     }
//     &:nth-of-type(5) {
//       align-self: flex-end;
//     }
//   }
// }

// .bcg-loader {
//   top: 0;
//   bottom: 0;
//   left: 0;
//   right: 0;
//   position: absolute;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   margin: -5px 0;

//   div {
//     border: 7px solid lightgray;
//     border-radius: 50%;
//     border-top: 7px solid darkgray;
//     width: 30px;
//     height: 30px;
//     -webkit-animation: spin 1s linear infinite; /* Safari */
//     animation: spin 1s linear infinite;
//   }
// }

// .final-results-view {
//   margin-top: 25px;
//   background-color: white;
//   padding: 15px;
//   min-height: 400px;
// }

// /* Safari */
// @-webkit-keyframes spin {
//   0% {
//     -webkit-transform: rotate(0deg);
//   }
//   100% {
//     -webkit-transform: rotate(360deg);
//   }
// }

// @keyframes spin {
//   0% {
//     transform: rotate(0deg);
//   }
//   100% {
//     transform: rotate(360deg);
//   }
// }

// @media only print {
//   .results-subsection {
//     margin-top: 0px;
//     padding-top: 0px;
//   }
//   .final-results-tabs {
//     border-bottom: 0.5px solid white;
//     padding-top: 0em;
//     margin-bottom: 0px;
//     margin-top: 0px;
//   }
//   .page-content {
//     background-color: white;
//     padding: 0;
//   }
//   .hide-on-paper {
//     display:none;
//   }
//   .page-body[_ngcontent-serverApp-c325] .page-content[_ngcontent-serverApp-c325] {
//     padding: 0;
//   }
//   .results-section[_ngcontent-serverApp-c324] .results-subsection[_ngcontent-serverApp-c324] {
//     margin-top: 0;
//   }
// }

// * {
//   -webkit-print-color-adjust: exact !important;
//   color-adjust: exact !important;
// }
