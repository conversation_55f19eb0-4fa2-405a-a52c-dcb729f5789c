export const applyMask = (input, mask) => {
    input = ''+input
    mask = ''+(mask || '')
    let output = '';
    let index = 0;
    for (var i = 0; i < mask.length; i++) {
        if (mask[i] === 'X') {
            output += input[index];
            index++;
        } else {
            output += mask[i];
        }
    }
    for (let i = index; i < input.length; i++){
        output += input[i];
    }
    return output;
}