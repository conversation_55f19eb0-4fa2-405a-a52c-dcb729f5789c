import { Component, OnInit, Renderer2 } from '@angular/core';
import { BreadcrumbsService } from "../../core/breadcrumbs.service";
import { LangService } from "../../core/lang.service";
import { Router, ActivatedRoute } from "@angular/router";
import { LDBLoginStrategy, LockdownService } from 'src/app/ui-student/lockdown.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';

@Component({
  selector: 'ldb-launch-page',
  templateUrl: './ldb-launch-page.component.html',
  styleUrls: ['./ldb-launch-page.component.scss']
})
export class LdbLaunchPageComponent implements OnInit {

  constructor(
    private breadcrumbsService: BreadcrumbsService,
    public lang: LangService,
    private router: Router,
    public lockdown: LockdownService,
    private route: ActivatedRoute,
    public whitelabel: WhitelabelService
  ) { }

  breadcrumb = [];

  ngOnInit(): void {
    this.breadcrumb = [this.breadcrumbsService._CURRENT(this.lang.tra("lbl_login"), `/${this.lang.c()}/login-router-st`), this.breadcrumbsService._CURRENT(this.lang.tra("lbl_students"), this.router.url)];
    this.route.params.subscribe(params => {
      const studentNumber = params["studentNumber"];
      const accessCode = params["accessCode"];
      const isSasnLogin = params["isSasnLogin"];
      const dob = params["dob"];
      
      this.lockdown.getLDBEncrptedLaunchLink({
        strategy: LDBLoginStrategy.ACCESS_CODE,
        loginInfo: {
          accessCode, studentNumber, isSasnLogin, dob
        }
      })
    })
  }
}
