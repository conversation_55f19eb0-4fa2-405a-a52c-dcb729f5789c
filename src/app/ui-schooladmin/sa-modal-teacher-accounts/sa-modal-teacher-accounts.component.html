<div *ngIf="isSelectingTeacher">
  <div><tra slug="select_teacher_edit"></tra></div>
  <ul>
    <li *ngFor="let teacher of teachers">
      <button class="button" (click)="selectTeacher(teacher)">
        {{teacher.invigilator}}
      </button>
    </li>
  </ul>
</div>
<div *ngIf="!isSelectingTeacher && formEntries">
  <div 
    class="required-field-container" 
    style="position: unset; margin:0.5rem 0 1rem 0">
      <b><tra slug="lbl_required_field"></tra></b>
      <i class="fa fa-asterisk" aria-hidden="true" style="margin-left: 0.5em;"></i>
  </div>
  <sa-widget-form-entry 
    *ngFor="let entry of formEntries" 
      [formEntry]="entry"
      [formEntryLabelContainerStyle]="{minWidth: '3rem'}"
  ></sa-widget-form-entry>
</div>
<div>
<tra-md [slug]="getNoClassForTeachersSlug()"></tra-md>
</div>