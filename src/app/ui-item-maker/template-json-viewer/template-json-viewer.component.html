<div class="json-viewer">
    <ng-container *ngIf="isObject(data) || isArray(data)">
        <span *ngIf="isArray(data)"class="square-bracket">[</span>
        <span *ngIf="!isArray(data)"class="bracket">&#123;</span>
        <div *ngFor="let key of getKeys(data); let i = index" class="json-item">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <div class="json-key" (click)="isExpandable(data[key]) ? toggleExpand(key) : null">
                    <span *ngIf="isExpandable(data[key])" [class.expanded]="expandedKeys[key]">
                        {{ expandedKeys[key] ? '-' : '+' }}
                    </span>
                    <span *ngIf="!isArray(data)">"{{ key }}"</span>
                    <span *ngIf="isArray(data)">{{ i }}</span>:
                    <span *ngIf="!isExpandable(data[key])" class="primitive">{{ formatPrimitive(data[key]) }}</span>
                </div>
                <a class="copy-icon" (click)="getPath(currentPath + (isArray(data) ? i : key))">
                    <i class="fas fa-solid fa-copy"></i>
                </a>
            </div>
            <div *ngIf="expandedKeys[key] && isExpandable(data[key])" class="json-value">
                <template-json-viewer [data]="data[key]" [currentPath]="getCurrentPath(i, key)" class="nested"></template-json-viewer>
            </div>
            <span *ngIf="!isLastKey(key) && (expandedKeys[key])">,</span>
        </div>
        <span *ngIf="isArray(data)"class="square-bracket">]</span>
        <span *ngIf="!isArray(data)"class="bracket">&#125;</span>
    </ng-container>
    <ng-container *ngIf="!isObject(data) && !isArray(data)">{{ formatPrimitive(data) }}</ng-container>
</div>
  