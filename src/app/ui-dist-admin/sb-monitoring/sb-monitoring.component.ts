import { Component, Input, OnInit } from '@angular/core';
import { LangService } from "../../core/lang.service";
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';
import { LoginGuardService } from '../../api/login-guard.service';
import { AuthService } from '../../api/auth.service';
import { MyBoardService } from '../my-board.service';
import { RoutesService } from '../../api/routes.service';
import { MyCtrlOrgService } from '../../ui-testctrl/my-ctrl-org.service';
import { Subscription } from 'rxjs';
import { MonitoringView, MONITORING_VIEWS } from './data/views';

interface IView<T> extends IMenuTabConfig<T> {
  imgUrl: string,
  description: string,
  hasIndicator?: boolean,
}

@Component({
  selector: 'sb-monitoring',
  templateUrl: './sb-monitoring.component.html',
  styleUrls: ['./sb-monitoring.component.scss']
})
export class SbMonitoringComponent implements OnInit {
  @Input() schoolDistrictId: number;

  constructor(
    public lang: LangService,
    public loginGuard: LoginGuardService,
    public myBoard: MyBoardService,
    private myCtrlOrg:MyCtrlOrgService,
    private routes:RoutesService
  ) { }

  // Track whether to show components after window selection
  isWindowSet: boolean = false;


  MonitoringView = MonitoringView
  selectedView: MonitoringView;
  views: IView<MonitoringView>[] = [];

  ngOnInit(): void {
    this.views = [];
    MONITORING_VIEWS.forEach(view => {
        this.views.push(Object({
          ...view,
          caption: view.caption
          //caption: this.lang.tra(view.caption),
          //description: this.lang.tra(view.description)
        }));
    });
    if (this.views.length > 0) {
      this.selectedView = this.views[0].id;
    }
  }

  selectView(id: MonitoringView) {
    this.selectedView = id;
  }

  getTestWindowId(){
    return this.myCtrlOrg.selectedWindow?.id
  }

  setTestWindowFilter(tw){
    this.myCtrlOrg.selectedWindow = {
      id: tw.id,
      test_ctrl_group_id: tw.test_ctrl_group_id,
      created_on: tw.created_on,
      dateEnd: tw.date_end,
      date_end: tw.date_end,
      dateStart: tw.date_start,
      date_start: tw.date_start,
      is_active: tw.is_active,
      is_qa: tw.is_qa,
      is_bg: tw.is_bg,
      title: tw.title,
      type_slug: tw.type_slug,
    };

    console.log("sb-monitor: \n")
    console.log(this.myCtrlOrg.selectedWindow)

    // Toggle to force reload
    this.isWindowSet = false;
    setTimeout(() => (this.isWindowSet = true), 0); // Allow Angular to trigger re-render
  }
}