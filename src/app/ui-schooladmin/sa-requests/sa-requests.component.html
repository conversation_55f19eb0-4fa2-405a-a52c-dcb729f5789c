<div>
  <div *ngIf="haveRequests()" style = "display:table">
    <div style = "display: table-cell;">
        <filter-toggles
            [state]="mySchool.getClassFilterToggles({isExcludeClassroomAssessment: true})"
            (id)="setClassFilter($event)"
        ></filter-toggles>
    </div>
    <div style = "display: table-cell;">
        <sa-test-window-filter
            [currentClassFilter] = "currentClassFilter"
            (setTestWindowEvent) = "setTestWindowFilter($event)"
            [restrictToSaSignoff]="true"
        ></sa-test-window-filter>
    </div>
  </div>
  <div>
      <div>
          <h2><u><tra-md slug="title_alt_version"></tra-md></u></h2>
      </div>
      <div *ngIf="altRequestsLoaded && altRequestsIsEmpty()" class="notification is-dark">
          <tra slug="no_req_alt_version"></tra>
      </div>
      <div *ngIf="altRequestsLoading" class="notification notification is-info">
          <tra slug="loading_req_alt_version"></tra>
      </div>
      <div *ngIf="altRequestsLoadFailed" class="notification notification is-danger">
          <tra slug="load_error_req_alt_version"></tra>
      </div>
      <div *ngIf="altRequestsLoaded && !altRequestsIsEmpty()">
          <div>
            <tra-md slug="instr_sa_alt_version_req"></tra-md>
          </div>
          <div style="display: flex; justify-content: flex-end; gap: 2px; margin-bottom: 10px;">
              <button class="button is-small has-icon" [disabled]="checkAltSelected()" (click)="viewAltRequestModalStart()">
                  <span class="icon" style="margin-right: 0.3em"><i class="fas fa-archive"></i></span>
                  <span><tra slug="view_request_alt_version"></tra></span>
              </button>
              <div>
                  <button *ngIf="!altRequestsIsEmpty()" class="button is-small has-icon" (click)="exportAltRequests()">
                      <span><tra slug="sa_classrooms_export"></tra></span>
                      <span class="icon"><i class="fas fa-table"></i></span>
                  </button>
              </div>
          </div>
          <ag-grid-angular
              class="ag-theme-alpine ag-grid-fullpage"
              style="border: none;"
              [rowData]="altRequests"
              [gridOptions]="altRequestGridOptions"
              (gridReady)="onAltGridReady($event)"
              (rowSelected)="onAltRowSelected($event)"
          ></ag-grid-angular>
      </div>
  </div>
  <hr>
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div [ngSwitch]="cModal().type" class="modal-contents" style="width:42em;">
      <div *ngSwitchCase="Modal.ALT_REQUEST">
          <sa-alt-version-print [config]="cmc()"></sa-alt-version-print>
      </div>
      <modal-footer class="modal-refund-policy" 
          [pageModal]="pageModal" 
          [confirmButton]="false" 
          [closeMessage]="'ie_close_modal'">
      </modal-footer>
  </div>
</div>