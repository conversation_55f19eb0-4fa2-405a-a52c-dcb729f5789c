@import '../../element-config-interactive-diagram.component.scss';

.target-container {
  margin-bottom: 1em;
}

.cell-input {
  width: 6em;
}

.list {
  width: 200px;
}

.table-container {
  &.is-reordering {
    display: grid;
    grid-template-columns: auto auto;
  }
  overflow-x: auto;
}

tr td.selectable-cell { 
    cursor: pointer;
    background-color:#ccc;
    color: #fff;
    text-align:center;
    vertical-align:middle;
    &:hover {
        background-color:rgb(170, 170, 170);
    }
}

tr td.min-col-width {
    min-width:4em;
}

.cell-target-info {
  width: 100%;
  /* padding-left: 0.5em; */
  font-weight: bold;
}

.target-cell {
  background-color: rgba(0,0,0,0.6);
  color: white;
}

.target-option-textarea {
  display: inline-block;
}

.table-reorder-column-container {
  display: flex;
  flex-direction: row;
  .table-reorder-handle {
    height: 2.5em;
  }
}
.table-reorder-row-container {
  display: flex;
  flex-direction: column;
  .table-reorder-handle {
    width: 2.5em;
  }
}
.table-reorder-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;
  color: white;
  font-weight: bold;
  background-color: #aaaaaa;
  &:hover {
    background-color: #ccc;
  }
  border-radius: 5px;
  border: 2px solid white;
}