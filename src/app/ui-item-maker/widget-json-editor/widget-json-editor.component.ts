import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { JsonEditorComponent, JsonEditorOptions} from 'ang-jsoneditor';

@Component({
  selector: 'widget-json-editor',
  templateUrl: './widget-json-editor.component.html',
  styleUrls: ['./widget-json-editor.component.scss']
})
export class WidgetJsonEditorComponent implements OnInit {
  public editorOptions: JsonEditorOptions;
  @Input() data: any;
  @Input() isUpdateDisabled: any;
  @Input() btnCaption: string = 'Update Style Profile';
  @Output() submitData? : EventEmitter<any> = new EventEmitter() // Promise<any>;
  // @Input() schema : any;
  @ViewChild(JsonEditorComponent, { static: false }) editor: JsonEditorComponent;

  constructor() { }

  ngOnInit(): void {
    this.editorOptions = new JsonEditorOptions();
    this.editorOptions.mode = 'code';
    this.editorOptions.modes = ['code', 'text', 'tree', 'view'];
    // this.editorOptions.modes = ['code', 'text', 'tree', 'view']; // set all allowed modes
    // this.editorOptions.schema = this.schema
    // this.editorOptions.onChange = () => console.log(this.editor.get());
  }

  submitJson = () => {
    const errors = this.validate();
    if(errors && errors.length > 0){
      console.error(errors);
      return;
    }
    this.submitData.emit(this.editor.get())
  };

  validate = () => {
    return [];
    // #TODO build and pass schema to this component
    // const editorObj = this.editor.getEditor();
    // editorObj.validate();
    // const errors = editorObj.validateSchema.errors;
    // return errors;
  }

}
