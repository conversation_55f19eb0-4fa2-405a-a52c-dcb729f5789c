import { Component, Input, OnChanges, ElementRef, SimpleChanges, ViewChild } from '@angular/core';
import katex from 'katex';

@Component({
  selector: 'katex-display',
  templateUrl: './katex-display.component.html',
  styleUrls: ['./katex-display.component.scss']
})
export class KatexDisplayComponent implements OnChanges {
  @Input() expression: string;
  @Input() displayMode: boolean = false;
  @ViewChild('katexElement') katexElement: ElementRef;
  
  private isViewInitialized = false;

  ngAfterViewInit() {
    this.isViewInitialized = true;
    this.renderMath();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (this.isViewInitialized && (changes['expression'] || changes['displayMode'])) {
      this.renderMath();
    }
  }

  private renderMath() {
    if (this.katexElement && this.expression) {
      katex.render(this.expression, this.katexElement.nativeElement, {
        displayMode: this.displayMode,
        throwOnError: false
      });
    }
  }
}
